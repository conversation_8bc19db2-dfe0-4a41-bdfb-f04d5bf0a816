<template>
  <div class="left-video-map" style="height: 100%">
    <put-away
      style="height: 100%"
      v-model="isOpen"
      :styles="{ left: 0, bottom: 0 }"
      :buttonStyle="buttonStyle"
      :tooltipText="language.title"
      :isShowAway="isShowAway"
    >
      <template v-slot:elseMain>
        <transition-group tag="div" name="group">
          <div
            v-for="(item, index) in componentsList"
            :key="'item-' + index"
            style="width: 100%"
            :style="item.style"
            :class="[{ 'amplification-style': openIndex == item.ref }]"
            class="video-map-item"
            v-show="isShowAway || openIndex == item.ref"
          >
            <components
              style="height: 100%; z-index: 23"
              v-show="isOpen || openIndex == item.ref"
              :openIndex="openIndex"
              :autoPlay="item.autoPlay"
              :is="item.name"
              :videoId="item.id"
              :title="item.title"
              :url="item.url"
              :currentTime.sync="item.currentTime"
              :isFill="item.isFill"
              :ref="item.ref"
              :griddingType="item.griddingType"
              :cover="item.cover"
              :isTemp="item.isTemp"
              :cameraInfo="staveTwoData"
              :tempStyle="item.tempStyle"
              @clickEvent="clickMap(item, index)"
              @clickVideo="fullEvent(item, index, $event)"
              @dblClickVideo="dblClickVideo(item, index, $event)"
              @mouseEvent="mouseEvent"
              :showFooter="openIndex == item.ref ? false : true"
              :style="{ background: openIndex == item.ref ? '#000' : '' }"
            >
            </components>
          </div>
        </transition-group>
      </template>

      <template v-slot:showContent>
        <transition name="el-fade-in">
          <div class="mt5 ml-away" v-show="isShowAway">
            <img-icon name="path02" width="20"></img-icon>
            <img-icon name="map" width="12"></img-icon>
          </div>
        </transition>
      </template>

      <template v-slot:awayIcon>
        <i class="el-icon-arrow-left"></i>
      </template>
    </put-away>
  </div>
</template>

<script>
import putAway from "../components/putAway.vue";
import imgIcon from "@/components/imgIcon/index";
import leftMap from "../components/leftMap.vue";
import webrtcVideo from "@/components/video/webrtcVideo.vue";
import webrtcVideoHttps from "@/components/video/webrtcVideoHttps.vue";

export default {
  props: {
    dblClickVideoEvent: Function,
    type: [String, Number],

    sortList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    websocket: [Function, Object],
  },
  components: {
    imgIcon,
    putAway,
    leftMap,
    webrtcVideo,
    webrtcVideoHttps,
  },
  data() {
    return {
      isOpen: true,
      isLive: false, // 是否直播流
      isShowUnfold: true,
      openIndex: "uavVideo",
      isShowAway: true,

      buttonStyle: {
        right: "-24px",
        "border-top-right-radius": "5px",
        "border-bottom-right-radius": "5px",
      },
      componentsList: [],
      cameraZoom: 0,
      time: "",
    };
  },
  computed: {
    // 无人机
    stream_uav_list: function () {
      return this.$store.state.equipment.equipmentInfo.stream_uav_list || [];
    },
    uavVideoUrl: function () {
      return this.$store.state.equipment.uavVideoUrl;
    },
    // 舱外
    stream_out_list: function () {
      return this.$store.state.equipment.equipmentInfo.stream_out_list || [];
    },
    // 舱内
    stream_in_list: function () {
      return this.$store.state.equipment.equipmentInfo.stream_in_list || [];
    },
    equipmentLinkState: function () {
      return this.$store.state.equipment.equipmentLinkState;
    },
    screenClass: function () {
      return this.$store.state.equipment.screenZIndex;
    },
    // 网格类型
    gridTypeVal: function () {
      return this.$store.state.camera.gridTypeVal;
    },
    videoStyle: function () {
      return {};
    },
    language() {
      return this.$languagePackage.navigation.leftVideoMap;
    },
    staveTwoData() {
      return this.$store.state.equipment.staveTwoData;
    },
  },
  watch: {
    // 监听无人机连接状态
    equipmentLinkState: function (val) {
      if (val) {
        let data = this.componentsList.map((item) => {
          return item.key;
        });

        let uav = data.indexOf("uav");
        let cabin = data.indexOf("cabin");
        let outboard = data.indexOf("outboard");

        if (this.componentsList[uav]) {
          this.componentsList[uav].url = this.uavVideoUrl;
        }

        if (this.componentsList[cabin]) {
          this.componentsList[cabin].url = this.stream_in_list[0];
        }

        if (this.componentsList[outboard]) {
          this.componentsList[outboard].url = this.stream_out_list[0];
        }
      }
    },
    //
    gridTypeVal: function (val) {
      let data = this.componentsList.map((item) => {
        return item.key;
      });
      let index = data.indexOf("uav");
      this.$set(this.componentsList[index], "griddingType", val);
    },
  },
  created() {
    this.dataDispose();
    this.$store.commit("setWsMmessageFun", {
      key: "leftVideoMap",
      message: this.disposeData,
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.$emit("update:sortList", this.componentsList);
    });

    // this.$store.commit(
    //   "setUavVideoUrl",
    //   "http://localhost:8080/navigationEnvironment/opjp/pkd"
    // );
    // let index = 0;
    // setInterval(() => {
    //   index++;
    //   this.disposeData(207, {
    //     cno: index,
    //   });
    // }, 2000);
  },
  methods: {
    disposeData: function (msg_id, data) {
      if (msg_id == 207) {
        let list = this.uavVideoUrl.split("/");

        if (list[list.length - 1] !== data.cno) {
          list[list.length - 1] = data.cno;
        }

        let url = list.join("/") + "&type=play";

        // 更新飞机端拉流地址
        let components = this.componentsList.map((item) => {
          return item.key;
        });
        let uav = components.indexOf("uav");
        if (this.componentsList[uav]) {
          this.componentsList[uav].url = url;
        }
        this.$store.commit("setUavVideoUrl", url);

        // 修改保存的值
        try {
          let obj = this.$store.state.equipment.equipmentInfo;
          obj.stream_uav_list[0] = url;
          setTimeout(() => {
            this.$store.commit("setEquipmentContent", obj);
          }, 0);
        } catch (e) {
          console.log("设备未链接----->", e);
        }
      }
    },
    dataDispose: function () {
      let data = [
        {
          name: "webrtcVideoHttps",
          ref: "uavVideo",
          url: "",
          id: "videoId",
          key: "uav",
          title: this.language.uavTitle,
          autoPlay: true,
          isFill: true,
          isTemp: true,
          griddingType: "3",
        },
        {
          name: "webrtcVideoHttps",
          ref: "cabinVideo",
          url: "",
          id: "video-top",
          key: "cabin",
          title: this.language.cabinTitle,
          autoPlay: this.type == 12 ? true : false,
          isFill: true,
        },
        {
          name: "webrtcVideoHttps",
          ref: "outboardVideo",
          url: "",
          id: "video-bot",
          key: "outboard",
          title: this.language.outboardTitle,
          autoPlay: true,
          currentTime: 0,
          isFill: true,
        },
        {
          name: "leftMap",
          ref: "map",
          url: "",
          id: "map",
          key: "map",
        },
      ];

      // 10\12 固定  100 移动  200 单兵
      let typeInfo = {
        10: {
          map: {
            style: {
              position: "absolute",
              bottom: 0,
              left: 0,
              height: "156px",
            },
          },
          outboardVideo: {
            style: {
              position: "absolute",
              top: "calc(33.3333% + 3px)",
              left: 0,
              height: "156px",
            },
          },
          cabinVideo: {
            style: {
              position: "absolute",
              top: 0,
              left: 0,
              height: "156px",
            },
          },
          uavVideo: {},
        },
        100: {
          map: {
            style: {
              position: "absolute",
              bottom: 0,
              left: 0,
              // height: "calc(50% - 10px)",
              height: "156px",
            },
          },
          outboardVideo: {
            style: {
              position: "absolute",
              top: 0,
              left: 0,
              // height: "calc(50% - 10px)",
              height: "156px",
            },
          },
          uavVideo: {},
        }, // 移动
        200: {
          map: {
            style: {
              position: "absolute",
              bottom: 0,
              left: 0,
              height: "100%",
            },
          },
          uavVideo: {},
        }, // 单兵
      };

      let type =
        this.type == 10 ||
        this.type == 100 ||
        this.type == 50 ||
        this.type == 45
          ? 100
          : this.type == 12
          ? 10
          : this.type;

      let list = [];
      let item = typeInfo[type];
      for (let k in item) {
        let row = data.filter((a) => {
          return a.ref == k;
        })[0];

        if (row) {
          let obj = Object.assign({}, row);
          obj.style = item[k].style;
          list.push(obj);
        }
      }
      this.componentsList = list;

      this.cutItemMagnify("outboard");
    },
    // 切换某一项放大
    cutItemMagnify: function (key) {
      this.$nextTick(() => {
        let index = null;
        for (let i = 0; i < this.componentsList.length; i++) {
          if (this.componentsList[i].key == key) {
            index = i;
            break;
          }
        }
        if (!index) {
          // 如果默认飞机视频是大屏，则切换温度
          if (this.openIndex == "uavVideo") {
            for (let i = 0; i < this.componentsList.length; i++) {
              if (this.componentsList[i].key == "uav") {
                this.$set(this.componentsList[i], "tempStyle", { top: "80px" });
                break;
              }
            }
          }
          return false;
        }
        this.fullEvent(this.componentsList[index], index);
      });
    },

    clickMap: function (item, index) {
      this.fullEvent(item, index);
    },
    // 双击视频
    dblClickVideo: function (item, index, screen) {
      let openIndex = 0;
      for (let i = 0; i < this.componentsList.length; i++) {
        if (this.componentsList[i].ref == this.openIndex) {
          openIndex = i;
          break;
        }
      }

      let open = this.componentsList[openIndex];
      if (item.key == open.key && item.key == "uav") {
        this.dblClickVideoEvent && this.dblClickVideoEvent(this.isOpen);
        console.log("双击触发------------------------------------->", item);
      }
    },
    dblClickChange: function (state) {
      this.isShowAway = state;
    },

    // 点击视频
    fullEvent: function (item, index, screen) {
      if (screen) {
        return;
      }
      let openIndex = 0;
      for (let i = 0; i < this.componentsList.length; i++) {
        if (this.componentsList[i].ref == this.openIndex) {
          openIndex = i;
          break;
        }
      }

      // 打开数据
      let openItem = this.componentsList[openIndex];

      // 如果打开或者收起的为飞机，则需要特殊处理
      if (this.componentsList[index].key == "uav") {
        this.$set(this.componentsList[index], "tempStyle", { top: "80px" });
        window.addEventListener("mousewheel", this.handleScroll);
        this.cameraZoom = this.staveTwoData.camera_zoom_value || 0;
      } else {
        window.removeEventListener("mousewheel", this.handleScroll);
      }

      let data = Object.assign({}, item); // 打开对象信息
      this.$set(this.componentsList[openIndex], "style", data.style); // 设置收回样式
      this.$set(this.componentsList[index], "style", {}); // 设置打开样式

      // 设置地图收回样式
      if (openItem.key == "map") {
        this.$refs[openItem.ref][0].partRefresh(false);
      }

      let video = this.$refs[item.ref][0];
      // 当显示网格时,视频放大，需要进行局部刷新
      if (item.griddingType) {
        video.updareGridding && video.updareGridding();
      }

      // 当显示网格时,视频缩小，需要进行局部刷新
      let openVideo = this.$refs[openItem.ref][0];
      if (openVideo.griddingType) {
        openVideo.updareGridding && openVideo.updareGridding();
      }

      this.openIndex = item.ref;
      if (this.componentsList[openIndex].key == "uav") {
        if (this.openIndex != "uavVideo")
          this.$set(this.componentsList[openIndex], "tempStyle", {
            top: "20px",
          });
      }

      if (item.key == "map") {
        this.$nextTick(() => {
          this.$refs[item.ref][0].magnifyMap([60, 100, 20, 20]);
        });
        return false;
      }

      let state = video && !video.isPlay;
      if (this.isLive && state) {
        video.reconnection && video.reconnection(true);
      } else if (!this.isLive && state) {
        video.playVideo && video.playVideo(true);
      } else if (!this.isLive && !state) {
        video.isPlay = true;
      }

      this.$emit("update:sortList", this.componentsList);
    },
    mouseEvent(e) {
      if (this.openIndex == "uavVideo") {
        if (e) {
          window.addEventListener("mousewheel", this.handleScroll);
        } else {
          window.removeEventListener("mousewheel", this.handleScroll);
        }
      }
    },
    //鼠标滚动事件
    handleScroll(e) {
      if (this.time) {
        clearTimeout(this.time);
      }
      this.time = setTimeout(() => {
        if (e.deltaY > 0) {
          let minZoom = this.cameraZoom <= 1 ? 1 : this.cameraZoom--;
          this.websocket.manualSend(
            {
              cmd_type: 3,
              zoom_multiple: minZoom,
            },
            402
          );
        } else {
          let maxZoom = this.cameraZoom >= 30 ? 30 : this.cameraZoom++;
          this.websocket.manualSend(
            {
              cmd_type: 3,
              zoom_multiple: maxZoom,
            },
            402
          );
        }
      }, 50);
    },
  },
};
</script>

<style lang="less" scoped>
.amplification-style {
  position: fixed !important;
  left: 0;
  top: 0;
  height: 100vh !important;
  width: 100%;
  z-index: 1 !important;
  margin-top: 0 !important;
  opacity: 1 !important;
}

.ml-away {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.custom-map {
  width: 100%;
  // background-color: #000;
}

.zindex-style {
  position: relative;
  z-index: 22;
}
</style>
