<template>
  <div class="mobileNest">
    <div class="content-item-1">
      <div class="content-item-1-state">
        <el-row class="content-item-1-state-row">
          <el-col :span="12">{{ equipLanguage.uavStatus.lng }}</el-col>
          <el-col :span="12">{{ equipLanguage.uavStatus.lat }}</el-col>
        </el-row>
        <el-row class="content-item-1-state-row-1">
          <el-col :span="12">{{ uavItemList.longitude / 1e7 || 0 }}</el-col>
          <el-col :span="12">{{ uavItemList.latitude / 1e7 || 0 }}</el-col>
        </el-row>
        <el-row class="content-item-1-state-row">
          <el-col :span="12">{{ equipLanguage.uavStatus.direction }}</el-col>
          <el-col :span="12">{{ equipLanguage.uavStatus.beaconValue }}</el-col>
        </el-row>
        <el-row class="content-item-1-state-row-1">
          <el-col :span="12">{{ uavItemList.vihicle_direction || 0 }}</el-col>
          <el-col :span="12">{{ uavItemList.fixtype || 0 }}</el-col>
        </el-row>
      </div>
      <div class="content-item-1-video" ref="contentItemVideo">
        <div
          :style="video.inCabin.style"
          class="inCabinStyle"
          v-if="equipItem && is_push_on"
        >
          <div class="tipinCabin">{{ equipLanguage.novideoTip }}</div>
          <div class="inCabinTitle">{{ equipLanguage.inCabin }}</div>
          <!-- <live-video
            v-if="equipItem"
            :title="equipLanguage.inCabin"
            :videoId="'inVideo'"
            :url="equipItem.stream_in_list ? equipItem.stream_in_list[0] : ''"
            :autoPlay="true"
            @clickVideo="changeVideo('inCabin')"
          ></live-video> -->
        </div>
        <div v-if="equipItem && !is_push_on" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>
      </div>

      <div class="content-item-1-video" ref="contentItemVideo1">
        <div :style="video.uav.style">
          <live-video
            v-if="equipItem && is_push_on"
            :title="equipLanguage.uav"
            @clickVideo="changeVideo('uav')"
            :videoId="'uavVideo'"
            :url="equipItem.stream_uav_list ? equipItem.stream_uav_list[0] : ''"
            :autoPlay="true"
          ></live-video>
          <div v-if="equipItem && !is_push_on" class="videoOutTip">
            <div class="titleTip">
              {{ equipLanguage.equipInfo.equipStateOut1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-item-2" ref="contentVideo">
      <div :style="video.outCabin.style">
        <live-video
          v-if="equipItem && is_push_on"
          :title="equipLanguage.outCabin"
          :videoId="'outVideo'"
          :url="equipItem.stream_out_list ? equipItem.stream_out_list[0] : ''"
          :autoPlay="true"
          :isFill="true"
          @clickVideo="changeVideo('outCabin')"
        ></live-video>
        <div v-if="equipItem && !is_push_on" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>
        <!-- :url="equipItem.stream_out_list ? equipItem.stream_out_list[0] : ''" -->
      </div>
    </div>
    <div class="content-item-3">
      <stable-operation
        ref="stableOperation"
        :websocket="websocket"
        :deviceItem="deviceItem"
      ></stable-operation>
    </div>
  </div>
</template>
<script>
import { Websockets } from "@/utils/websocketUntil";
import baseUrl from "@/utils/global";
import liveVideo from "@/components/video/webrtcVideoHttps.vue";

import stableOperation from "./stableOperation.vue";
export default {
  props: {
    deviceItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    liveVideo,
    stableOperation,
  },
  computed: {
    equipLanguage() {
      return this.$languagePackage.equipment;
    },
    is_push_on() {
      return this.deviceItem.is_push_on;
    },
  },
  data() {
    return {
      websocket: "",
      uavItemList: {},
      equipItem: "",

      video: {
        uav: {
          style: {},
        },
        outCabin: {
          style: {},
        },
        inCabin: {
          style: {},
        },
      },
      onKey: {
        close: 1,
        open: 0,
      },
      showOnKeyError: false,
      openCode: false,
      closeCode: false,
    };
  },
  mounted() {
    this.initWebsocket();
    this.$nextTick(() => {
      this.originVideoLayout();
    });
  },
  methods: {
    //建立设备websocket链接
    initWebsocket() {
      this.websocket = new Websockets(baseUrl.WS_URL, {
        equipmentVerify: {
          sn_id: this.deviceItem.sn_id,
          type: this.deviceItem.type,
          vst: 40,
        },
        heartbeat: 20000,
        message: this.returnMessage,
      });
    },
    //接受websokcet返回的数据
    returnMessage(e) {
      let msg_id = e.msg_id;
      let data = e.data;
      this.$refs.stableOperation &&
        this.$refs.stableOperation.getMessage(msg_id, data);
      switch (msg_id) {
        case 200:
          this.equipItem = data;
          break;
        case 432:
          this.uavItemList = data || {};
          this.$emit("returnMessage", msg_id, data);
          break;
        case 434:
          this.$emit("returnMessage", msg_id, data);
          break;
        case 435:
          this.cameraList = data;
          this.zoomValue = data.camera_zoom_value;
          break;

        default:
          break;
      }
    },
    //点击切换视频窗口
    changeVideo(item) {
      if (this.video[item].code !== 2) {
        let keys;
        for (let key in this.video) {
          if (this.video[key].code == 2) {
            keys = key;
          }
        }
        this.video[keys].code = this.video[item].code;
        this.video[item].code = 2;
        let style = this.video[item].style;
        this.video[item].style = this.video[keys].style;
        this.video[keys].style = style;
      }
    },
    //初始化视频盒子样式
    originVideoLayout() {
      this.video.inCabin.style = {
        top: this.$refs.contentItemVideo.offsetTop + "px",
        left: this.$refs.contentItemVideo.offsetLeft + "px",
        width: this.$refs.contentItemVideo.offsetWidth + "px",
        height: this.$refs.contentItemVideo.offsetHeight * 0.95 + "px",
        marginTop: this.$refs.contentItemVideo.offsetHeight * 0.05 + "px",
        position: "absolute",
      };
      this.video.inCabin.code = 0;
      this.video.uav.style = {
        top: this.$refs.contentItemVideo1.offsetTop + "px",
        left: this.$refs.contentItemVideo1.offsetLeft + "px",
        width: this.$refs.contentItemVideo1.offsetWidth + "px",
        height: this.$refs.contentItemVideo1.offsetHeight * 0.95 + "px",
        marginTop: this.$refs.contentItemVideo1.offsetHeight * 0.05 + "px",
        position: "absolute",
      };
      this.video.uav.code = 1;
      this.video.outCabin.style = {
        top: this.$refs.contentVideo.offsetTop + "px",
        left: this.$refs.contentVideo.offsetLeft + "px",
        width: this.$refs.contentVideo.offsetWidth + "px",
        height: this.$refs.contentVideo.offsetHeight + "px",
        position: "absolute",
      };
      this.video.outCabin.code = 2;
    },
    //修改视频盒子样式
    changeVideoLayout() {
      for (let key in this.video) {
        switch (this.video[key].code) {
          case 0:
            this.video[key].style = {
              top: this.$refs.contentItemVideo.offsetTop + "px",
              left: this.$refs.contentItemVideo.offsetLeft + "px",
              width: this.$refs.contentItemVideo.offsetWidth + "px",
              height: this.$refs.contentItemVideo.offsetHeight * 0.95 + "px",
              marginTop: this.$refs.contentItemVideo.offsetHeight * 0.05 + "px",
              position: "absolute",
            };

            break;
          case 1:
            this.video[key].style = {
              top: this.$refs.contentItemVideo1.offsetTop + "px",
              left: this.$refs.contentItemVideo1.offsetLeft + "px",
              width: this.$refs.contentItemVideo1.offsetWidth + "px",
              height: this.$refs.contentItemVideo1.offsetHeight * 0.95 + "px",
              marginTop:
                this.$refs.contentItemVideo1.offsetHeight * 0.05 + "px",
              position: "absolute",
            };
            break;
          case 2:
            this.video[key].style = {
              top: this.$refs.contentVideo.offsetTop + "px",
              left: this.$refs.contentVideo.offsetLeft + "px",
              width: this.$refs.contentVideo.offsetWidth + "px",
              height: this.$refs.contentVideo.offsetHeight + "px",
              position: "absolute",
            };
            break;
        }
      }
    },
  },
  beforeDestroy() {
    if (this.websocket) {
      this.websocket.manualClone();
      this.websocket = "";
    }
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .mobileNest {
    .content-item-1 {
      .content-item-1-state {
        // display: flex;
        border-radius: @zoomIndex * 6px !important;
        .content-item-1-state-row {
          .el-col {
            font-size: @zoomIndex * 12px !important;
          }
        }
        .content-item-1-state-row-1 {
          .el-col {
            font-size: @zoomIndex * 16px !important;
          }
        }
      }
      .content-item-1-video {
        .videoOutTip {
          .titleTip {
            font-size: @zoomIndex * 18px !important;
          }
        }
        .inCabinStyle {
          .tipinCabin {
            font-size: @zoomIndex * 16px !important;
          }
          .inCabinTitle {
            padding: @zoomIndex * 3px @zoomIndex * 10px !important;
          }
        }
      }
    }
    .content-item-2 {
      .videoOutTip {
        .titleTip {
          font-size: @zoomIndex * 18px !important;
        }
      }
    }
    .content-item-3 {
      border-radius: @zoomIndex * 6px !important;
    }
  }
}
.mobileNest {
  width: 100%;
  height: 100%;
  display: flex;
  .content-item-1 {
    width: 20%;
    margin: 2% 0 2% 1%;
    .content-item-1-state {
      width: 100%;
      height: 18%;
      margin-bottom: 2%;
      background-color: #000000;
      // display: flex;
      border-radius: 6px;
      color: white;
      .content-item-1-state-row {
        width: 96%;
        height: 25%;
        margin: 0 2%;
        .el-col {
          font-size: 12px;
          text-align: center;
          color: #6b6b6b;
          font-weight: 650;
        }
      }
      .content-item-1-state-row-1 {
        width: 96%;
        height: 25%;
        margin: 0 2%;
        .el-col {
          font-size: 16px;
          text-align: center;
        }
      }
    }
    .content-item-1-video {
      width: 100%;
      height: 40%;
      .video-module {
        height: 100%;
        width: 100%;
      }
      .videoOutTip {
        width: 100%;
        height: 100%;
        color: white;
        background-color: #3d4348;
        position: relative;
        .titleTip {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 18px;
        }
      }
      .inCabinStyle {
        background-color: rgba(61, 67, 72, 1);
        position: relative;
        .tipinCabin {
          width: 100%;
          color: white;
          text-align: center;
          vertical-align: middle;
          padding-top: 25%;
          font-size: 16px;
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-80%);
        }
        .inCabinTitle {
          position: absolute;
          top: 0;
          left: 0;
          background-color: #010101;
          color: #fff;
          border: none;
          padding: 3px 10px;
        }
      }
    }
  }
  .content-item-2 {
    margin: 2% 1%;
    width: 56%;
    .video-module {
      height: 100%;
      width: 100%;
    }
    .videoOutTip {
      width: 100%;
      height: 100%;
      color: white;
      background-color: #3d4348;
      position: relative;
      .titleTip {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
      }
    }
  }
  .content-item-3 {
    width: 20%;
    margin: 2% 1% 2% 0;
    background-color: #060606;
    border-radius: 6px;
  }
}
</style>
