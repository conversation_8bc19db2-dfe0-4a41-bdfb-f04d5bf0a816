<template>
  <video-layout
    ref="videoLayout"
    :videoId="videoId"
    :griddingType="griddingType"
    :title="title"
    :showFooter="showFooter"
    :autoPlay="autoPlay"
    :isPlay.sync="isPlay"
    :isNetworking="isNetworking"
    :linkErrorType="linkErrorType"
    @playVideo="playVideo"
    @clickVideo="clickVideoEvent"
    @dblClickVideo="dblClickVideo"
  >
    <template v-slot:video>
      <video
        ref="webrtcVideo"
        muted
        :style="videoStyle"
        :controls="controls"
        loop="loop"
        autoplay="autoplay"
        style="text-align: left; width: 100%; height: 100%"
        v-if="isShowVideo"
      />

      <!-- 不拉流且有图片时显示 -->
      <div class="img-show" v-show="imgUrl">
        <el-image style="width: 100%; height: 100%" :src="imgUrl" />
      </div>

      <!-- 封面图 -->
    </template>

    <template v-slot:error v-if="imgUrl">
      <slot name="error" :linkErrorType="linkErrorType">
        <div></div>
      </slot>
    </template>

    <template v-slot:temperature>
      <div class="" v-if="isTemp && showMaxMinTemp">
        <slot name="temperature">
          <!-- 温度 -->
          <div class="temperature" :style="tempStyle">
            <div
              class="temperature-item"
              v-for="(item, index) in temperatureList"
              :key="index"
            >
              <div class="top">
                <div
                  class="top-color"
                  :style="{ backgroundColor: item.color }"
                ></div>
                <div class="top-label">{{ item.label }}</div>
              </div>
              <div class="bot">{{ item.value }}°c</div>
            </div>
          </div>

          <!-- 最高最低点 -->
          <div class="">
            <div class="max-temperature" ref="maxTemp"></div>
            <div
              class="min-temoerature"
              ref="minTemp"
              style="background-color: rgb(5, 249, 17)"
            ></div>
          </div>
        </slot>
      </div>
    </template>
  </video-layout>
</template>

<script>
import ZLMRTCClient from "@/assets/js/ZLMRTCClient.js";
import videoLayout from "./videoLayout.vue";
export default {
  name: "webrtc-video-https",
  components: {
    videoLayout,
  },
  props: {
    controls: {
      type: Boolean,
      default: false,
    },
    title: String, // 标题
    url: String, // 视频链接
    videoId: String, // video标签id
    griddingType: [String, Number], // 视频网格类型
    // 显示底部操作
    showFooter: {
      type: Boolean,
      default: true,
    },
    autoPlay: Boolean, // 是否自动播放
    isFill: Boolean, // 播放器是否充满
    resolution: {
      type: Object,
      default: () => {
        return {
          w: 1920,
          h: 1080,
        };
      },
    },
    // 是否显示温度
    isTemp: Boolean,
    // 相机信息，如果显示温度，必传
    cameraInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 温度高度
    tempStyle: Object,
  },
  data() {
    return {
      isPlay: false,
      isNetworking: false,
      linkErrorType: 1, // 链接错误状态
      imgUrl: null,
      isShowVideo: true,
      rtcPlay: null,

      temperatureList: [
        { label: "最大", color: "red", value: "0.00", key: "max_temperature" },
        {
          label: "最小",
          color: "rgb(0, 255, 34)",
          value: "0.00",
          key: "min_temperature",
        },
        {
          label: "平均",
          color: "rgb(2, 81, 227)",
          value: "0.00",
          key: "area_average_temperature",
        },
      ],
    };
  },
  watch: {
    url: function () {
      let video = this.$refs.webrtcVideo;
      if (video) {
        video.srcObject = null;
        video.load();
      }

      this.autoPlay && this.playVideo(true);
    },
    cameraInfo: {
      deep: true,
      handler: function (val) {
        // 如果显示温度，则处理数据
        if (this.isTemp) {
          for (let i = 0; i < this.temperatureList.length; i++) {
            let item = this.temperatureList[i];
            item.value = Number(val[item.key]) / 10;
          }
          this.$nextTick(() => {
            this.location();
          });
        }
      },
    },
  },

  computed: {
    videoStyle() {
      return {
        "object-fit": this.isFill ? "fill" : "", // 充满拉伸、会变形
      };
    },
    showMaxMinTemp() {
      let item = this.cameraInfo || {};
      return item.pip_mode == 1 || item.pip_mode == 2 || item.pip_mode == 4;
    },
  },
  mounted() {
    this.autoPlay && this.initVideo();
  },
  methods: {
    initVideo: function () {
      // 每次加载前，删除前一个实例
      this.rtcPlay && this.rtcPlay.close && this.rtcPlay.close();

      this.$nextTick(() => {
        this.linkErrorType = 2;

        if (!this.url) {
          this.imgUrl = null;
          this.isNetworking = false;
          this.linkErrorType = 4;
          return;
        }

        let player = new ZLMRTCClient.Endpoint({
          element: this.$refs.webrtcVideo, // video 标签
          debug: false, // 是否打印日志
          zlmsdpUrl: this.url, //流地址
          simulcast: false,
          useCamera: true,
          audioEnable: true,
          videoEnable: true,
          recvOnly: true,
          resolution: this.resolution,
          usedatachannel: true,
        });
        this.rtcPlay = player;

        player.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED, function (s) {
          // 获取本地流失败
          console.log("获取本地流失败");
          console.error("本地拉流失败------------", s);
        });
        let self = this;
        let video = this.$refs.webrtcVideo;
        if (video) {
          video.oncanplay = () => {
            this.isNetworking = true;
            this.linkErrorType = 0;
            this.imgUrl = null;
            this.isPlay = true;
          };
          video.onwaiting = () => {
            this.isNetworking = false;
            this.linkErrorType = 2;
            this.imgUrl = null;
          };
          // offer anwser 交换失败
          player.on(
            ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,
            (e) => {
              this.isNetworking = false;
              this.linkErrorType = 3;
              this.imgUrl = null;
            }
          );

          // RTC 状态变化 ,详情参考 https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/connectionState
          player.on(
            ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE,
            (state) => {
              this.isNetworking = false;
              this.imgUrl = null;
              if (state == "disconnected") {
                this.linkErrorType = 2;
                console.log("rtc状态发生改变，---卡顿---", state);
              }
              if (state == "connecting") {
                this.linkErrorType = 2;
              }
              if (state == "failed") {
                this.linkErrorType = 3;
              }
              if (state == "connected") {
                self.isNetworking = true;
                self.linkErrorType = 0;
                self.imgUrl = null;
                self.isPlay = true;
              }
            }
          );
        }
      });
    },
    // 1120
    playVideo: function (state) {
      this.isPlay = state;
      this.isShowVideo = false;

      // 取消rtc播放
      this.rtcPlay && this.rtcPlay.close && this.rtcPlay.close();

      this.isNetworking = false;
      if (state) {
        setTimeout(() => {
          this.isShowVideo = true;
          this.$nextTick(() => {
            this.initVideo();
          });
        }, 0);
      } else {
        this.getVideoImg();
        this.linkErrorType = 2;

        let video = this.$refs.webrtcVideo;
        if (video) {
          video.srcObject = null;
          video.load();
        }
      }
    },
    clickVideoEvent: function (row) {
      this.$emit("clickVideo", row);
    },
    dblClickVideo: function (row) {
      this.$emit("dblClickVideo", row);
    },
    // 获取视频播放中的某一个页面
    getVideoImg: function () {
      var canvas = document.createElement("canvas");
      let video = this.$refs.webrtcVideo;
      if (!video) {
        return false;
      }
      canvas.width = video.clientWidth;
      canvas.height = video.clientHeight;
      canvas
        .getContext("2d")
        .drawImage(video, 0, 0, canvas.width, canvas.height);
      var dataURL = canvas.toDataURL("image/jpeg");
      this.imgUrl = dataURL;
    },
    // 重新连接/局部刷新
    reconnection: function (state) {
      this.isPlay = false;
      this.playVideo(state);
    },
    destroyVideo: function () {},
    // 更新网格
    updareGridding: function () {
      let gridding = this.$refs.videoLayout.$refs.gridding;
      gridding && gridding.partialRenewal(); // 更新网格，适应当前范围
    },
    // 计算最高、最低温度位置
    location: function () {
      // 宽 1120 1920 1920 / 2
      // 高 880

      let item = this.cameraInfo;
      let pip_mode = item.pip_mode;
      // 不存在，不做处理
      if (!pip_mode) {
        return false;
      }

      let ratioListW = {
        1: 1920,
        2: 1920 / 2,
        4: 1120,
      };

      let ratioListH = {
        1: 1080,
        2: 880,
        4: 1080,
      };

      // 热成像和视频宽度比例
      const ratio = ratioListW[pip_mode] / 1920;
      let uavVideo = this.$refs.videoLayout.$el;
      let uavW = uavVideo.offsetWidth;
      let uavH = uavVideo.offsetHeight;

      let tempW = uavW * ratio;
      let tempH = uavH * (ratioListH[pip_mode] / 1080);
      // 热成像距离左边
      let uavL = (uavW - tempW) / 2;
      let uavT = (uavH - tempH) / 2;

      let maxX, maxY, minX, minY;

      let unit = 10000;
      let max_temper_x_pos = Number(item.max_temper_x_pos) / unit,
        max_temper_y_pos = Number(item.max_temper_y_pos) / unit,
        min_temper_x_pos = Number(item.min_temper_x_pos) / unit,
        min_temper_y_pos = Number(item.min_temper_y_pos) / unit;

      if (pip_mode == 2 || pip_mode == 1) {
        uavL = 0;
      }

      this.$nextTick(() => {
        try {
          let iconW = this.$refs.maxTemp.offsetWidth;

          maxX = uavL + max_temper_x_pos * tempW;

          maxY = uavT + tempH * max_temper_y_pos;

          this.$refs.maxTemp.style.left = maxX + "px";
          this.$refs.maxTemp.style.top = maxY + "px";

          minX = uavL + min_temper_x_pos * tempW;
          minY = uavT + tempH * min_temper_y_pos;

          this.$refs.minTemp.style.left = minX + "px";
          this.$refs.minTemp.style.top = minY + "px";
        } catch (error) {}
      });
    },
  },
};
</script>

<style lang="less" scoped>
.img-show {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  // height: 100%;
}

.temperature {
  position: absolute;
  left: 50%;
  top: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  width: 140px;
  height: 40px;
  display: flex;
  border-radius: 5px;
  margin-left: -70px;
  z-index: 2;
  .temperature-item {
    flex-grow: 1;
    width: 100%;
    color: #fff;
    font-size: 12px;
    // justify-content: center;
    .top {
      display: flex;
      height: 20px;
      align-items: center;
      justify-content: center;
      .top-color {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: rgb(5, 249, 17);
        margin-right: 3px;
      }
    }
    .bot {
      font-size: 12px;

      display: flex;
      height: 20px;
      align-items: center;
      justify-content: center;
    }
  }
}

.max-temperature,
.min-temoerature {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: red;
  position: absolute;
  left: 20px;
  top: 20px;
}
</style>