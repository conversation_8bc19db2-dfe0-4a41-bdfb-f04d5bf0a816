import {
    pointsConvert
} from '../coordinateConvert'
//绘制点坐标
function drawPoint(point, config = {}) {
    let {
        name = '',
            id = '',
            fontSize = 14,
            fillColor = "#ffffff",
            color = "#0000008a",
            text = "",
            imageUrl = "",
            x = 0,
            y = 0,
            widthImg = 40,
            heightImg = 40

    } = config
    return {
        id,
        name,
        position: point,
        billboard: {
            // 图像地址，URI或Canvas的属性
            image: imageUrl,
            // 设置颜色和透明度
            color: Cesium.Color.WHITE.withAlpha(1),
            // 高度（以像素为单位）
            height: heightImg,
            // 宽度（以像素为单位）
            width: widthImg,
            // 逆时针旋转
            rotation: 0,
            // 大小是否以米为单位
            sizeInMeters: false,
            // 相对于坐标的垂直位置
            verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
            // 相对于坐标的水平位置
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
            pixelOffset: new Cesium.Cartesian2(x, y),
            // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
            scale: 1.0,
            // 显示在距相机的距离处的属性，多少区间内是可以显示的
            // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(-1000, 1000),
            // 是否显示
            show: true,
        },
        label: {
            // 文本。支持显式换行符“ \ n”
            text,
            // 字体样式，以CSS语法指定字体
            font: `${fontSize}px Source Han Sans CN`,
            // 字体颜色
            fillColor: new Cesium.Color.fromCssColorString(fillColor),
            // 背景颜色
            backgroundColor: new Cesium.Color.fromCssColorString(color),
            // 是否显示背景颜色
            showBackground: true,
            // 字体边框
            outline: false,
            // 字体边框颜色
            outlineColor: Cesium.Color.WHITE,
            // 字体边框尺寸
            outlineWidth: 0,
            // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
            scale: 1.0,
            // 设置样式：FILL：填写标签的文本，但不要勾勒轮廓；OUTLINE：概述标签的文本，但不要填写；FILL_AND_OUTLINE：填写并概述标签文本。
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            // 相对于坐标的水平位置
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            // 相对于坐标的水平位置
            horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
            // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
            pixelOffset: new Cesium.Cartesian2(x, y),
            // pixelOffset: new Cesium.Cartesian2(0, 15), // 中心位置
            // 显示在距相机的距离处的属性，多少区间内是可以显示的
            // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 3000),
            // eyeOffset: new Cesium.Cartesian3(0, 0, -point.height),
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
            // 是否显示
            show: true
        }
    };
}
//绘制模型
function drawModel(point, config = {}) {
    let {
        name = '',
            url = "",
            id = '',
            heading = 0,
            pitch = 0,
            roll = 0

    } = config
    return {
        id,
        name,
        position: point,
        model: {
            // uri: process.env.VUE_APP_STATIC_URL + "/glbModel/Cesium_Air.glb",
            uri: process.env.VUE_APP_STATIC_URL + "/glbModel/uav_3D.glb",
            scale: 0.1,
            minimumPixelSize: 100,
        },
        orientation: Cesium.Transforms.headingPitchRollQuaternion(
            point,
            new Cesium.HeadingPitchRoll(
                Cesium.Math.toRadians(heading), // 设置这个属性即可（顺时针旋转的角度值）
                Cesium.Math.toRadians(pitch),
                Cesium.Math.toRadians(roll)
            )
        )
    };
}

function getImage(text) {
    let size = 30
    let canvas = document.createElement("canvas");
    canvas.width = size;
    canvas.height = size;
    let ctx = canvas.getContext("2d");
    ctx.textAlign = "center";
    ctx.fillStyle = "#0092f8";
    ctx.font = "14px Microsoft YaHei";
    ctx.fillText(text, size / 2, size / 2 + 7);
    ctx.beginPath();
    ctx.strokeStyle = '#0092f8';
    ctx.lineWidth = 3;
    ctx.arc(15, 15, 13.5, 0 * Math.PI, 2 * Math.PI);
    ctx.stroke();
    return canvas;
}

function drawPointLabel(point, config = {}) {
    let {
        className = "",
            name = '',
            pixelSize = 24, //点的大小
            color = "#ffffff", //点的颜色
            fillColor = "#0092f8", //字体颜色
            backgroundColor = "#ffffffff", //背景颜色
            outlineWidth = 3, //边框宽度
            outlineColor = "#0092f8", //边框颜色
            label = true,
            text = "",
            id = '',
            fontSize = 20,
            x = -4,
            y = 0
    } = config
    switch (className) {
        case "marker-fence":
            outlineColor = "#07ff0e"
            fillColor = "#000000"
            color = "#ffffff"
            break;
        case "marker-fence-center":
            outlineColor = "#eeff00"
            fillColor = "#000000"
            color = "#ffffff"
            outlineWidth = 2
            pixelSize = 18
            fontSize = 16
            x = -3
            y = 1
            break;
        case "marker-route":
            outlineColor = "#0092f8"
            fillColor = "#0092f8"
            color = "#ffffff"
            x = id > 9 ? -8 : x
            break;
        case "marker-route-center":
            outlineColor = "#0728fc"
            fillColor = "#0728fc"
            color = "#ffffff"
            outlineWidth = 2
            pixelSize = 18
            fontSize = 16
            x = -3
            y = 1
            break;
        case "marker-break":
            pixelSize = 12
            fillColor = "#ff0000"
            color = "#ffffff"
            outlineWidth = 1
            outlineColor = "#ff0000"
            fontSize = 10
            x = -2
            y = 0
            break;
        case "marker-start-end":
            pixelSize = 12
            fillColor = "#0015a1"
            color = "#ffffff"
            outlineWidth = 1
            outlineColor = "#0728fc"
            fontSize = 10
            x = -2
            y = 0
            break;
        default:
            break;
    }
    return {
        id,
        name,
        position: changeLatLnag(point),
        point: {
            pixelSize,
            color: new Cesium.Color.fromCssColorString(color),
            outlineWidth,
            outlineColor: new Cesium.Color.fromCssColorString(outlineColor)
        },
        // 文字
        label: {
            // 文本。支持显式换行符“ \ n”
            text: text.toString(),
            // 字体样式，以CSS语法指定字体
            font: `${fontSize}px Source Han Sans CN`,
            // 字体颜色
            fillColor: new Cesium.Color.fromCssColorString(fillColor),
            // 背景颜色
            backgroundColor: new Cesium.Color.fromCssColorString(backgroundColor),
            // 是否显示背景颜色
            showBackground: false,
            // 字体边框
            outline: false,
            // 字体边框颜色
            outlineColor: new Cesium.Color.fromCssColorString(fillColor),
            // 字体边框尺寸
            outlineWidth: 0,
            // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
            scale: 1.0,
            // 设置样式：FILL：填写标签的文本，但不要勾勒轮廓；OUTLINE：概述标签的文本，但不要填写；FILL_AND_OUTLINE：填写并概述标签文本。
            style: Cesium.LabelStyle.FILL,
            // 相对于坐标的水平位置
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            // 相对于坐标的水平位置
            horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
            // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
            pixelOffset: new Cesium.Cartesian2(x, y),
            // pixelOffset: new Cesium.Cartesian2(0, 15), // 中心位置
            // 显示在距相机的距离处的属性，多少区间内是可以显示的
            // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 3000),
            eyeOffset: new Cesium.Cartesian3(0, 0, -point.height),
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
            // 是否显示
            show: label
        }
    }
}

function drawLabel(point, config = {}) {
    let {
        id = '',
            name = "",
            text = '',
            fontSize = 14,
            fillColor = "#ffffff",
            color = "#0000008a",
            verticalOrigin = Cesium.VerticalOrigin.BOTTOM,
            horizontalOrigin = Cesium.HorizontalOrigin.CENTER,
            x = 0,
            y = 0
    } = config
    return {
        id,
        name,
        position: point,
        // 文字
        label: {
            // 文本。支持显式换行符“ \ n”
            text,
            // 字体样式，以CSS语法指定字体
            font: `${fontSize}px Source Han Sans CN`,
            // 字体颜色
            fillColor: new Cesium.Color.fromCssColorString(fillColor),
            // 背景颜色
            backgroundColor: new Cesium.Color.fromCssColorString(color),
            // 是否显示背景颜色
            showBackground: true,
            // 字体边框
            outline: false,
            // 字体边框颜色
            outlineColor: Cesium.Color.WHITE,
            // 字体边框尺寸
            outlineWidth: 0,
            // 应用于图像的统一比例。比例大于会1.0放大标签，而比例小于会1.0缩小标签。
            scale: 1.0,
            // 设置样式：FILL：填写标签的文本，但不要勾勒轮廓；OUTLINE：概述标签的文本，但不要填写；FILL_AND_OUTLINE：填写并概述标签文本。
            style: Cesium.LabelStyle.FILL_AND_OUTLINE,
            // 相对于坐标的水平位置
            verticalOrigin: verticalOrigin,
            // 相对于坐标的水平位置
            horizontalOrigin: horizontalOrigin,
            // 该属性指定标签在屏幕空间中距此标签原点的像素偏移量
            pixelOffset: new Cesium.Cartesian2(x, y),
            // pixelOffset: new Cesium.Cartesian2(0, 15), // 中心位置
            // 显示在距相机的距离处的属性，多少区间内是可以显示的
            // distanceDisplayCondition: new Cesium.DistanceDisplayCondition(0, 3000),
            // eyeOffset: new Cesium.Cartesian3(0, 0, -point.height),
            // heightReference: Cesium.HeightReference.RELATIVE_TO_GROUND,
            // 是否显示
            show: true
        }
    }

}
//绘制线段
function drawLine(paths, config = {}) {
    let {
        width = 5.0,
            color = '#0092f8',
            clampToGround = false,
            noHavePaths = false //为true不传坐标，外部定义
    } = config
    if (!noHavePaths && paths.length < 1) return
    return {
        positions: noHavePaths ? [] : changeLatlangArrayHeight(paths),
        width,
        // material: new Cesium.PolylineGlowMaterialProperty({
        //     color: new Cesium.Color.fromCssColorString(color),
        //     glowPower: 1,
        //     taperPower: 1
        // }),
        material: new Cesium.ColorMaterialProperty(new Cesium.Color.fromCssColorString(color)),
        depthFailMaterial: new Cesium.ColorMaterialProperty(new Cesium.Color.fromCssColorString(color)),
        arcType: Cesium.ArcType.NONE,
        clampToGround,
    }
}
//绘制圆形
function drawCircle(paths, config = {}) {
    let {
        fillColor = '#00b0ff8a',
            outlineWidth = 10,
            outlineColor = "#80d8ff",
    } = config
    return {
        // 半短轴（画圆：半短轴和半长轴一致即可）
        semiMinorAxis: 5000,
        // 半长轴
        semiMajorAxis: 5000,
        // 填充色
        material: new Cesium.Color.fromCssColorString(fillColor),
        // 是否有边框
        outline: true,
        // 边框颜色
        outlineColor: new Cesium.Color.fromCssColorString(outlineColor),
        // 边框宽度
        outlineWidth: outlineWidth
    }
}
//绘制多边形
function drawPolygon(paths, config = {}) {
    let {
        color = '#1357B1',
            opacity = 0.2,
            outlineColor = "#0092f8",
            outlineWidth = 700,
            outline = true,
            perPositionHeight = true,
            fill = true,
            height = 500,
            showMin = 0,
            showMax = 10000000,
            show = true,
            zIndex = 10,
            noHavePaths = false //为true不传坐标，外部定义
    } = config
    if (!noHavePaths && paths.length < 1) return
    return {
        // 获取指定属性（positions，holes（图形内需要挖空的区域））
        hierarchy: {
            positions: noHavePaths ? [] : changeLatlangArrayHeight(paths),
        },
        //使用每个位置的高度
        perPositionHeight,
        // 边框
        outline,
        // 边框颜色
        outlineColor: new Cesium.Color.fromCssColorString(outlineColor),
        // 边框尺寸
        outlineWidth,
        // 填充的颜色，withAlpha透明度
        material: new Cesium.Color.fromCssColorString(color).withAlpha(opacity),
        // 是否被提供的材质填充
        fill,
        // 恒定高度
        // height,
        // 显示在距相机的距离处的属性，多少区间内是可以显示的
        distanceDisplayCondition: new Cesium.DistanceDisplayCondition(showMin, showMax),
        // 是否显示
        show,
        // 顺序,仅当`clampToGround`为true并且支持地形上的折线时才有效。
        zIndex
    }
}
//聚合点
function drawCluster(deviceList, map) {
    map.dataSources.removeAll();
    const dataSource = new Cesium.CustomDataSource("cluster");
    let inLineImage = require("@/assets/img/inLineHome.png")
    let outLineImage = require("@/assets/img/outLineHome.png")
    let workLineImgae = require("@/assets/img/workingHome.png")
    for (let index = 0; index < deviceList.length; index++) {
        if (deviceList[index].type !== 200 && deviceList[index].type !== 100) {
            let point = pointsConvert({
                    point: deviceList[index],
                    devicePoint: true
                })
                // wgs84_to_gcj02(
                //   deviceList[index].lon_int / 1e7,
                //   deviceList[index].lat_int / 1e7
                // );
            dataSource.entities.add({
                id: deviceList[index].sn_id,
                name: deviceList[index].name,
                position: Cesium.Cartesian3.fromDegrees(point[0], point[1], 1),
                // point: {
                //     color: Cesium.Color.RED,
                // },
                // label: {
                //   text: "车辆编号-" + i,
                //   pixelOffset: new Cesium.Cartesian2(10, -70), // 偏移方向
                //   fillColor: Cesium.Color.BLACK.withAlpha(0.65),
                //   backgroundColor: Cesium.Color.YELLOW,
                //   showBackground: true,
                //   font: "12px;",
                // },
                description: "isDevice",
                properties: {
                    state: deviceList[index].is_pull_on ? 2 : deviceList[index].is_push_on ? 1 : 0,
                    type: deviceList[index].type,
                    position: Cesium.Cartesian3.fromDegrees(point[0], point[1], 20), //用于缩放
                },
                billboard: {
                    image: deviceList[index].is_pull_on ? workLineImgae : deviceList[index].is_push_on ? inLineImage : outLineImage,
                    width: 40,
                    height: 40,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // //相对于对象的原点（注意是原点的位置）的水平位置
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                    pixelOffset: new Cesium.Cartesian2(12.5, 0), // 偏移
                },
            });
        }
    }
    const dataSourcePromise = map.dataSources.add(dataSource);
    dataSourcePromise.then(function(dataSource) {
        const pixelRange = 8;
        const minimumClusterSize = 2;
        const enabled = true;
        dataSource.clustering.enabled = enabled; //是否聚合
        dataSource.clustering.pixelRange = pixelRange;
        dataSource.clustering.minimumClusterSize = minimumClusterSize;
        // const pinBuilder = new Cesium.PinBuilder();
        var removeListener;
        // customStyle(pinBuilder, dataSource, removeListener);
        customStyle(dataSource, removeListener);
    });
}
//聚合点样式
function customStyle(dataSource, removeListener) {
    if (Cesium.defined(removeListener)) {
        removeListener && removeListener();
        removeListener = undefined;
    } else {
        removeListener = dataSource.clustering.clusterEvent.addEventListener(
            function(clusteredEntities, cluster) {
                cluster.label.show = false;
                cluster.billboard.show = true;
                cluster.billboard.id = cluster.label.id;
                cluster.billboard.verticalOrigin = Cesium.VerticalOrigin.BOTTOM;
                cluster.billboard.image = combineIconAndLabel(
                    clusteredEntities.length,
                    30
                );
            }
        );
    }
    const pixelRange = dataSource.clustering.pixelRange;
    dataSource.clustering.pixelRange = 0;
    dataSource.clustering.pixelRange = pixelRange;
}
//绘制聚合点样式画布
function combineIconAndLabel(label, size) {
    // 创建画布对象
    let canvas = document.createElement("canvas");
    canvas.width = size;
    canvas.height = size;
    canvas.className = "marker";
    let ctx = canvas.getContext("2d");
    //   ctx.fillStyle = Cesium.Color.WHITE.toCssColorString();
    //   ctx.font = "bold 20px Microsoft YaHei";
    //第一层
    ctx.fillStyle = "#c84141";
    ctx.fillRect(0, 0, size, size);
    //第二层
    ctx.fillStyle = "#ffffff";
    ctx.fillRect(1, 1, size - 2, size - 2);
    // 绘制第三个图层
    ctx.fillStyle = "#c84141";
    ctx.fillRect(3, 3, size - 6, size - 6);
    ctx.textAlign = "center";
    ctx.fillStyle = "#ffffff";
    ctx.font = "20px Microsoft YaHei";
    ctx.fillText(label, size / 2, size / 2 + 7);
    return canvas;
}
//坐标转换
function changeLatLnag(point) {
    return new Cesium.Cartesian3.fromDegrees(point.lng, point.lat, point.height ? point.height : 0)
}

function changeLatlangArray(points) {
    let paths = []
    for (let index = 0; index < points.length; index++) {
        paths.push(points[index].lng)
        paths.push(points[index].lat)
    }
    return new Cesium.Cartesian3.fromDegreesArray(paths)
}

function changeLatlangArrayHeight(points) {
    // Cesium.Cartesian3.fromDegreesArrayHeights([经度1, 纬度1, 高度1, 经度2, 纬度2, 高度2])
    let paths = []
    for (let index = 0; index < points.length; index++) {
        paths.push(Number(points[index].lng))
        paths.push(Number(points[index].lat))
        paths.push(Number(points[index].height))
    }
    return new Cesium.Cartesian3.fromDegreesArrayHeights(paths)
}

function changeLatLng(position, viewer) {
    let cartesian = viewer.scene.globe.pick(
        viewer.camera.getPickRay(position),
        viewer.scene
    );
    var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    //弧度转经纬度
    let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
    let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
    return {
        lat,
        lng
    }

}

//获取屏幕中心点坐标
function getCenterPosition(viewer) {
    let centerResult = viewer.camera.pickEllipsoid(
        new Cesium.Cartesian2(
            viewer.canvas.clientWidth / 2,
            viewer.canvas.clientHeight / 2,
        ),
    )
    if (!centerResult) {
        return null
    }
    let curPosition = Cesium.Ellipsoid.WGS84.cartesianToCartographic(centerResult);
    if (curPosition) {
        let curLongitude = (curPosition.longitude * 180) / Math.PI;
        let curLatitude = (curPosition.latitude * 180) / Math.PI;
        return {
            lon: curLongitude,
            lat: curLatitude,
        }
    }
}
export default {
    drawPoint,
    drawLine,
    drawPolygon,
    drawCluster,
    drawPointLabel,
    changeLatLng,
    drawLabel,
    drawCircle,
    getCenterPosition,
    drawModel
}