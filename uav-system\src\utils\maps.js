import {
    geolocation
} from '@/utils/getLocation'
import Vue from 'vue'
var map = ""
    //初始化地图(先定位绘制地图)
function initMap(tagId, config = {}) {
    let {
        locationCode = true
    } = config
    return new Promise((resolve, reject) => {
        if (locationCode) {
            if (window.locationSite) {
                init(tagId, config)
                return resolve(map)
            } else {
                geolocation().then(() => {
                    init(tagId, config)
                    return resolve(map)
                }).catch(() => {
                    init(tagId, config)
                    return resolve(map)

                })
            }

        } else {
            init(tagId, config)
            return resolve(map)
        }

    })
}

function init(tagId, config) {
    let {
        mapStyle = undefined,
            viewMode = undefined,
            position = 'RB',
            layer = undefined,
            showLayerType = undefined,
            zoom = undefined,
            locationCode = true
    } = config;
    map = new AMap.Map(tagId, { //设置地图容器id
        zoom: zoom ? zoom : 15,
        resizeEnable: true,
        mapStyle: mapStyle,
        viewMode: viewMode, //是否为3D地图模式
        // terrain: true,
        pitch: 0,
        rotation: 0,
        zoomEnable: true,
        doubleClickZoom: false,
        // viewMode: '3D', //开启3D视图,默认为关闭
        buildingAnimation: false, //楼块出现是否带动画
        animateEnable: false, //地图平移过程中是否使用动画,默认为true
        showLabel: !layer, //	是否展示地图文字和 POI 信息
        jogEnable: false, //地图是否使用缓动效果,默认为true
        zooms: [3, 30],
        // labelRejectMask: true, //文字是否拒绝掩模图层进行掩模
        WebGLParams: { preserveDrawingBuffer: true }
    });
    if (locationCode) {
        if (typeof(window.locationSite) == "string") {
            map.setCity(window.locationSite, function() {
                map.setZoom(15)
            })
        } else {
            map.setCenter(window.locationSite)
        }
    }
    if (layer) {
        let layers = setLayers()
        map.setLayers([layers])
    } else {
        AMapUI.loadUI(['control/BasicControl'], function(BasicControl) {
            let controls = new BasicControl.LayerSwitcher({
                position: position,
                // baseLayers: [{
                //     id: 'tile', //图层id，需唯一
                //     enable: true, //是否启用
                //     name: '<i class="iconfont icon-tilelayer">标准图', //显示名称，html
                //     layer: new AMap.TileLayer() // 图层实例
                // }, {
                //     id: 'Satellite', //图层id，需唯一
                //     enable: false, //是否启用
                //     name: '卫星图', //显示名称，html
                //     layer: new AMap.TileLayer.Satellite()
                // }]
            })
            if (showLayerType) {
                controls.opts.baseLayers[0].enable = false
                controls.opts.baseLayers[1].enable = true
            }
            //图层切换控件
            map.addControl(controls);
            if (Vue.prototype.$language != "chinese") {
                // let b = controls.opts.baseLayers[0].name.split("</i>")[0]
                // controls.opts.baseLayers[0].name = b + "</i> TileLayer"
                // let c = controls.opts.baseLayers[1].name.split("</i>")[0]
                // controls.opts.baseLayers[1].name = c + "</i> Satellite"
                let b = document.getElementsByClassName("amap-ui-control-layer-base-item-tile")[0].innerHTML.split("</i>")
                document.getElementsByClassName("amap-ui-control-layer-base-item-tile")[0].innerHTML = b[0] + "</i>TileLayer</span></label>"
                let c = document.getElementsByClassName("amap-ui-control-layer-base-item-satellite")[0].innerHTML.split("</i>")
                document.getElementsByClassName("amap-ui-control-layer-base-item-satellite")[0].innerHTML = c[0] + "</i>Satellite</span></label>"
                let e = document.getElementsByClassName("amap-ui-control-layer-overlay-item-traffic")[0].innerHTML.split("</i>")
                document.getElementsByClassName("amap-ui-control-layer-overlay-item-traffic")[0].innerHTML = e[0] + "</i>Traffic</span></label>"
                let f = document.getElementsByClassName("amap-ui-control-layer-overlay-item-roadNet")[0].innerHTML.split("</i>")
                document.getElementsByClassName("amap-ui-control-layer-overlay-item-roadNet")[0].innerHTML = f[0] + "</i>RoadNet</span></label>"

            }

        })

    }

    // AMap.plugin([
    //     'AMap.ControlBar',
    // ], function() {
    //     // 添加 3D 罗盘控制
    //     map.addControl(new AMap.ControlBar({
    //         position: {
    //             right: "10px",
    //             bottom: "5px"
    //         }
    //     }));
    // });

}

function setLayers() {
    return new AMap.TileLayer({
        getTileUrl: function(x, y, z) {
            // let q = change(x, y, z)
            // return 'http://dynamic.t0.tiles.ditu.live.com/comp/ch/' + a + '?it=G,VE,BX,L,LA&mkt=zh-cn,syr&n=z&og=111&ur=CN'
            // let a = (x + 2 * y) % 4
            // return `http://t7.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX=${z}&TILEROW=${y}&TILECOL=${x}&tk=fb9784ae90381164a3bc83974d7f7ad4` //天地图
            // return `http://ecn.t${a}.tiles.virtualearth.net/tiles/a${q}.jpeg?n=${z}&g=99&mkt=zh-cn` //bing影像
            // return `http://ecn.t${a}.tiles.virtualearth.net/tiles/r${q}.jpeg?n=${z}&g=99&mkt=zh-cn` //bing街道
            return ` https://mt3.google.cn/maps/vt?lyrs=s&hl=en-US&gl=US&x=${x}&y=${y}&z=${z}`
        },
        zIndex: 10,
    });
}
//绘制多边形
function drawPolypon(path) {
    return new AMap.Polygon({
        path: path,
        bubble: true,
        fillColor: '#1357B1',
        fillOpacity: 0.2,
        strokeOpacity: 1,
        strokeColor: '#0092f8',
        strokeWeight: 6,
        strokeStyle: 'soild',
    })
}
//绘制禁飞区多边形
function drawPolyponZone(param = {}) {
    let {
        stroke_color,
        solid_color,
        stroke_width,
        id,
        paths,
        type
    } = param
    return new AMap.Polygon({
        path: paths,
        bubble: true,
        fillColor: solid_color,
        fillOpacity: 0.5,
        strokeOpacity: 1,
        strokeColor: stroke_color,
        strokeWeight: stroke_width,
        strokeOpacity: 0.5,
        strokeStyle: 'soild',
        extData: {
            id: id,
            type: type
        }
    })
}
//绘制线段
function drawPolyline(points, param = {}) {
    let {
        borderWeight,
        strokeColor,
    } = param
    return new AMap.Polyline({
        path: points,
        isOutline: true,
        outlineColor: '#0092f8',
        borderWeight: borderWeight ? borderWeight : 1,
        strokeColor: strokeColor ? strokeColor : "#0092f8",
        strokeOpacity: 1,
        strokeWeight: 3,
        // 折线样式还支持 'dashed'
        strokeStyle: "solid",
        // strokeStyle是dashed时有效
        strokeDasharray: [10, 5],
        lineJoin: 'round',
        lineCap: 'round',
        zIndex: 50,
    })
}
//绘制点
function drawMarker(index, center, className, param = {}) {
    let content = "<div class='" + className + "'><span class='text'>" + index + "</span></div>"
    let {
        offset,
        clickable,
        draggable,
        zIndex
    } = param
    return new AMap.Marker({
        position: center,
        offset: new AMap.Pixel(offset, offset),
        content: content,
        clickable: clickable,
        draggable: draggable,
        zIndex: zIndex
    })
}
//每个点的文本标记
function textMarker(text, point, rotate) {
    let pad = -10
    if ((rotate > 90 && rotate < 180) || (rotate < 0 && rotate > -50)) {
        pad = 15
    } else if ((rotate < -160 && rotate > -180) || (rotate > 0 && rotate < 20)) {
        pad = -20
    }
    rotate = rotate > 0 ? rotate - 90 : rotate + 90
    return new AMap.Text({
        text: text,
        anchor: 'bottom-center', // 设置文本标记锚点
        cursor: 'pointer',
        angle: 0,
        draggable: false,
        bubble: true,
        offset: new AMap.Pixel(pad, -5),
        style: {
            'background-color': 'transparent',
            'border-width': 0,
            'text-align': 'center',
            'font-size': '20px',
            'color': 'black',
            'transform': 'rotate(' + rotate + 'deg)'

        },
        position: point
    });
}
//绘制点与标记
function drawMarkerText(point, param = {}) {
    let { iconImg = require("@/assets/img/routeplan/pldot.png"), offset = -5, text = '', direction = 'bottom', className = "" } = param
    let icon = new AMap.Icon({
        size: new AMap.Size(12, 12),
        image: iconImg,
        imageOffset: new AMap.Pixel(-12, 0),
    });
    return new AMap.Marker({
        position: point,
        icon: icon,
        offset: new AMap.Pixel(offset, offset),
        title: text
            // label: {
            //     content: `<div class='${className}'>${text}</div>`,
            //     direction: direction,
            //     angle: 0,
            //     offset: new AMap.Pixel(pad, -5),
            // }
    });
}
//绘制聚合点
function markerMerge(maps, points) {
    let a = ''
    AMap.plugin(["AMap.MarkerCluster"], function() {
        a = new AMap.MarkerCluster(maps, points, {
            maxZoom: 18,
            gridSize: 60,
            clusterByZoomChange: true,
            renderClusterMarker: renderClusterMarker,
            renderMarker: renderMarker
        });
    });
    return a

}
//未聚合点的样式
function renderMarker(context) {
    let img = ''
    let title = ""
    let online = Vue.prototype.$language == 'english' ? "online" : "在线"
    let offline = Vue.prototype.$language == 'english' ? "offline" : "离线"
    if (context.data[0].state) {
        img = require("../assets/img/inLineHome.png")
        title = online;
    } else {
        img = require("../assets/img/outLineHome.png")
        title = offline;
    }
    let icon = new AMap.Icon({
        // 图标的取图地址
        image: img,
        imageSize: new AMap.Size(50, 50),
    });
    let offset = new AMap.Pixel(-15, -30)
    context.marker._opts.bubble = true
    context.marker.setIcon(icon)
    context.marker.setOffset(offset)
    title = context.data[0].name + " \n" + context.data[0].id + '\n' + title
    context.marker.setTitle(title)
}
//聚合点的样式设置
function renderClusterMarker(context) {
    let content = "<div class='renderMarker'><span>" + context.count + "</span></div>"
    context.marker.setContent(content)
}
//创建信息窗口
function setInfoWindow(infoList) {
    let str = '<div class="divider"></div>'

    let outImg = require("../assets/img/outLineHome.png")
    let inImg = require("../assets/img/inLineHome.png")
    for (let index = 0; index < infoList.length; index++) {

        let img = infoList[index].state ? inImg : outImg
        let online = Vue.prototype.$language == 'english' ? "online" : "在线"
        let offline = Vue.prototype.$language == 'english' ? "offline" : "离线"
        let title = infoList[index].state ? online : offline
        str += '<div class="info-item-content"><img class="imageStyle" align="top" src=' + img + '></img>' + infoList[index].name + '&nbsp;&nbsp;' + infoList[index].id + '（' + title + '）</div>'
    }
    return new AMap.InfoWindow({
        content: str,
        anchor: 'bottom-center',
        offset: new AMap.Pixel(10, 5),
        zIndex: 130,
        // closeWhenClickMap: true,
        autoMove: true,
    });


}
//可按需要添加地图操作
export default {
    initMap,
    drawPolypon,
    drawPolyline,
    textMarker,
    drawMarker,
    drawPolyponZone,
    markerMerge,
    setInfoWindow,
    setLayers,
    drawMarkerText
}