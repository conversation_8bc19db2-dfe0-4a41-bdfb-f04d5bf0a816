<template>
  <div class="userDialog">
    <el-dialog
      :title="title"
      :visible.sync="userManageCode"
      :close-on-click-modal="false"
      :show-close="false"
      :destroy-on-close="true"
      center
    >
      <el-divider></el-divider>
      <el-form
        :model="userForm"
        :rules="rules"
        ref="userForm"
        class="userForm"
        label-position="right"
      >
        <el-form-item :label="userLanguage.dialogInfo.name" prop="nick">
          <el-input
            v-model="userForm.nick"
            :placeholder="userLanguage.dialogInfo.placeholder"
          ></el-input>
        </el-form-item>
        <el-form-item :label="userLanguage.dialogInfo.company" prop="in_com_id">
          <el-cascader
            v-model="userForm.in_com_id"
            :options="options"
            :props="cascaderProps"
            clearable
            :placeholder="userLanguage.dialogInfo.placeholder1"
          ></el-cascader>
        </el-form-item>
        <!-- <el-form-item :label="userLanguage.dialogInfo.company" prop="company">
          <el-select
            v-model="userForm.company"
            :placeholder="userLanguage.dialogInfo.placeholder1"
            popper-class="selectUserType"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item :label="userLanguage.dialogInfo.account" prop="account">
          <el-input
            v-model="userForm.account"
            :placeholder="userLanguage.dialogInfo.placeholder2"
            oninput="value=value.replace(/[\u4E00-\u9FA5]/ig,'')"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="userLanguage.dialogInfo.password"
          prop="password"
          :rules="[
            {
              required: type == 'add',
              message: userLanguage.dialogInfo.message,
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="userForm.password"
            :placeholder="
              type == 'add'
                ? userLanguage.dialogInfo.placeholder3
                : userLanguage.dialogInfo.placeholder4
            "
            oninput="value=value.replace(/[^\x20-\x7E]/ig,'')"
          ></el-input>
        </el-form-item>
        <el-form-item :label="userLanguage.dialogInfo.power" prop="num">
          <el-checkbox-group v-model="checkedfun">
            <el-checkbox
              v-for="item in funList"
              :label="item.fun_id"
              :key="item.fun_id"
              >{{
                $language == "english" ? item.name_en : item.name
              }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="userLanguage.dialogInfo.term" prop="term">
          <el-radio-group v-model="userForm.term">
            <el-radio :label="1">{{
              userLanguage.dialogInfo.permanent
            }}</el-radio>
            <el-radio :label="2">
              {{ userLanguage.dialogInfo.limite }}
            </el-radio>
            <el-date-picker
              v-if="userForm.term == 2 ? true : false"
              v-model="dateTime"
              type="datetimerange"
              range-separator="--"
              :start-placeholder="userLanguage.dialogInfo.startTime"
              :end-placeholder="userLanguage.dialogInfo.endTime"
              class="chooseDate"
              popper-class="choose-date-poper"
            >
            </el-date-picker>
          </el-radio-group>
        </el-form-item>
        <div class="fromButton">
          <el-button
            class="saveBut"
            @click="sureUser('userForm')"
            :class="saveCode ? 'checked' : ''"
            >{{ userLanguage.allBtn.save }}</el-button
          >
          <el-button
            class="closeBut"
            @click="closeEditEvent"
            :class="closeCode ? 'checked' : ''"
            >{{ userLanguage.allBtn.cancel }}</el-button
          >
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
import { getCode } from "@/utils/rsa";
export default {
  props: {
    funList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    clickCode: {
      type: Boolean,
      default: false,
    },
    u_id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      title: "",
      userManageCode: false,
      checkedfun: [],
      userForm: {
        nick: "",
        account: "",
        password: "000000",
        // company: "",
        term: 1,
        in_com_id: "",
        // notes:'',
      },
      rules: {
        nick: [{ required: true, message: "", trigger: "blur" }],
        in_com_id: [{ required: true, message: "", trigger: "change" }],
        account: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
          { validator: this.accountFormat, trigger: "blur" },
        ],
        password: [
          {
            min: 6,
            max: 32,
            message: "",
            trigger: "blur",
          },
        ],
        term: [
          { required: true, message: "", trigger: "change" },
          { validator: this.chooseTerm, trigger: "change" },
        ],
      },
      saveCode: false,
      closeCode: false,
      dateTime: "",
      item: "",
      type: "add",
      options: [],
      cascaderProps: {
        checkStrictly: true,
      },
      requestCode: false,
    };
  },
  computed: {
    userLanguage() {
      return this.$languagePackage.userManage;
    },
    userFunList() {
      return this.$store.state.user.userInfo.fun_list;
    },
    admin() {
      return (
        this.userFunList &&
        this.userFunList.indexOf("super_administrator") !== -1
      );
    },
  },
  created() {
    // this.options = this.userLanguage.options;
    this.rules.nick[0].message = this.userLanguage.dialogInfo.placeholder;
    this.rules.in_com_id[0].message = this.userLanguage.dialogInfo.placeholder1;
    this.rules.account[0].message = this.userLanguage.dialogInfo.placeholder2;
    this.rules.password[0].message = this.userLanguage.dialogInfo.placeholder5;
    this.rules.term[0].message = this.userLanguage.dialogInfo.placeholder6;
  },
  methods: {
    getComList() {
      this.requestCode = false;
      let param = {
        os_timestampCode: true,
      };

      requestHttp("allGetTeamList", param)
        .then((res) => {
          let list = res.data && res.data.list ? res.data.list : [];
          let arr = [];
          for (let index = 0; index < list.length; index++) {
            const element = list[index];
            if (this.admin && element.parent_info) {
              continue;
            }
            let obj = {
              value: element.id,
              label: element.name,
              children:
                element.son_list && element.son_list.length
                  ? this.getChildren(element)
                  : "",
            };
            this.options.push(obj);
          }
        })
        .finally(() => {
          this.requestCode = true;
        });
    },
    getChildren(element) {
      return element.son_list.map((item) => {
        return {
          value: item.id,
          label: item.name,
          children:
            item.son_list && item.son_list.length
              ? this.getChildren(element)
              : "",
        };
      });
    },
    open(type, item) {
      this.options = [];
      this.getComList();
      this.title = this.userLanguage.dialogInfo.title;
      this.type = type;
      this.userManageCode = true;
      this.checkedfun = [];
      let psw = parseInt(Math.random() * 1000000);
      this.userForm = {
        nick: "",
        account: "",
        password: psw.toString().padStart(6, "0"),
        in_com_id: "",
        // notes:'',
        againpassword: "",
        term: 1,
      };
      this.dateTime = "";
      this.time = 0;

      if (type == "edit") {
        this.userForm = {
          nick: item.nick,
          account: item.phone ? item.phone : item.email,
          password: "",
          in_com_id: "",
          // company: item.company,
          term: item.end_time >= ********** ? 1 : 2,
          // notes:'',
        };
        this.title = this.userLanguage.dialogInfo.editTitle;
        this.userForm.u_id = item.u_id;
        if (this.userForm.term == 1) {
          this.dateTime = "";
        } else {
          this.dateTime = [item.start_time * 1000, item.end_time * 1000];
        }
        this.checkedfun = item.fun_list;
        this.getComId(item.in_com);
      }
      this.$nextTick(() => {
        this.$refs.userForm.clearValidate();
      });
    },
    getComId(in_com) {
      if (!this.requestCode) {
        setTimeout(() => {
          this.getComId(in_com);
        }, 200);
        return false;
      }
      this.userForm.in_com_id = this.computerId(this.options, in_com.id);
    },
    computerId(array, id) {
      for (let index = 0; index < array.length; index++) {
        let item = array[index];
        if (item.value == id) {
          return [item.value];
        } else {
          if (item.children) {
            let result = this.computerId(item.children, id);
            if (result) {
              return [item.value, ...result];
            }
          }
        }
      }
    },
    //提交用户信息
    sureUser(e) {
      let now = new Date().getTime();
      if (this.time == 0 || now - this.time > 2000) {
        this.saveCode = true;
        this.time = now;
        this.$refs[e].validate((valid) => {
          if (valid) {
            if (this.type == "add") {
              let data = {
                nick: this.userForm.nick,
                in_com_id:
                  this.userForm.in_com_id[this.userForm.in_com_id.length - 1],
                state: 10,
                fun_json: JSON.stringify(this.checkedfun),
                // notes:'',
              };
              if (this.userForm.term == 1) {
                data.start_time = 0;
                data.end_time = **********;
              } else {
                data.start_time = this.dateTime[0] / 1000;
                data.end_time = this.dateTime[1] / 1000;
              }
              data.password = getCode(md5(this.userForm.password));
              if (this.userForm.account.indexOf("@") == -1) {
                data.phone = this.userForm.account;
              } else {
                data.email = this.userForm.account;
              }
              data.pmd =
                data.state.toString() +
                data.start_time.toString() +
                data.end_time.toString() +
                this.userForm.account.toString() +
                data.password +
                data.in_com_id.toString() +
                data.nick +
                data.fun_json;
              data.os_timestampCode = true;
              requestHttp("userAdd", data).then((res) => {
                this.$emit("refresh");
                this.$message.success({
                  message: this.userLanguage.dialogInfo.successAdd,
                  customClass: "message-info-tip",
                });
                setTimeout(() => {
                  this.saveCode = false;
                  this.userManageCode = false;
                  this.$emit("update:clickCode", false);
                  this.$emit("update:u_id", "");
                }, 200);
              });
            } else {
              let data = {
                u_id: this.userForm.u_id,
                nick: this.userForm.nick,
                state: 10,
                fun_json: JSON.stringify(this.checkedfun),
                in_com_id:
                  this.userForm.in_com_id[this.userForm.in_com_id.length - 1],
                // notes:'',
              };
              if (this.userForm.term == 1) {
                data.start_time = 0;
                data.end_time = **********;
              } else {
                data.start_time = this.dateTime[0] / 1000;
                data.end_time = this.dateTime[1] / 1000;
              }
              if (this.userForm.password) {
                data.password = getCode(md5(this.userForm.password));
              }
              if (this.userForm.account.indexOf("@") == -1) {
                data.phone = this.userForm.account;
              } else {
                data.email = this.userForm.account;
              }
              data.pmd =
                data.u_id +
                data.state.toString() +
                data.start_time.toString() +
                data.end_time.toString() +
                this.userForm.account.toString() +
                (data.password ? data.password : "") +
                data.in_com_id.toString() +
                data.nick +
                data.fun_json;
              data.os_timestampCode = true;
              requestHttp("userEdit", data).then((res) => {
                this.$emit("refresh");
                this.$message.success({
                  message: this.userLanguage.dialogInfo.successEdit,
                  customClass: "message-info-tip",
                });
                setTimeout(() => {
                  this.saveCode = false;
                  this.userManageCode = false;
                  this.$emit("update:clickCode", false);
                  this.$emit("update:u_id", "");
                }, 200);
              });
            }
          }
        });
      }
    },
    //取消新增、编辑用户
    closeEditEvent() {
      this.closeCode = true;
      setTimeout(() => {
        this.userManageCode = false;
        this.$emit("update:clickCode", false);
        this.$emit("update:u_id", "");
        this.closeCode = false;
      }, 200);
    },
    //校验手机号跟邮箱格式
    accountFormat(rule, value, callback) {
      if (value.indexOf("@") == -1) {
        const regMobile = /^1[345678]\d{9}$/;
        if (!regMobile.test(value)) {
          callback(new Error(this.userLanguage.dialogInfo.errorMessage));
        } else callback();
      } else {
        const regEmail =
          /^[A-Za-z0-9]+([_.][A-Za-z0-9]+)*@([A-Za-z0-9-]+\.)+[A-Za-z]{2,6}$/;
        if (!regEmail.test(value)) {
          callback(new Error(this.userLanguage.dialogInfo.errorMessage1));
        } else callback();
      }
    },
    //权限切换限制
    chooseTerm(rule, value, callback) {
      if (value == 2 && !this.dateTime) {
        callback(new Error(this.userLanguage.dialogInfo.errorMessage2));
      } else {
        callback();
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .userDialog {
    .el-dialog {
      .userForm {
        .fromButton {
          .el-button {
            font-size: @zoomIndex * 18px !important;
            padding: @zoomIndex * 12px @zoomIndex * 30px !important;
            border-radius: @zoomIndex * 8px !important;
          }
        }
      }
      .delButDiv {
        .el-button {
          font-size: @zoomIndex * 18px !important;
          padding: @zoomIndex * 12px 0 !important;
          border-radius: @zoomIndex * 8px !important;
        }
      }
    }
  }
}
.userDialog {
  .el-dialog {
    .userForm {
      .el-form-item {
        margin-bottom: 4%;
        .el-input {
          width: 100%;
        }
        .el-select,
        .el-cascader,
        .el-radio-group {
          width: 100%;
        }
        .el-checkbox-group {
          float: left;
          width: 100%;
        }
        .chooseDate {
          width: 100%;
          .el-range-input {
            width: 40%;
          }
        }
      }
      .fromButton {
        width: 100%;
        text-align: center;
        margin-top: 5%;
        .el-button {
          font-size: 18px;
          padding: 12px 30px;
          border-radius: 8px;
        }
        .saveBut {
          margin-right: 15%;
        }
      }
    }
    .delButDiv {
      width: 100%;
      text-align: center;
      margin-top: 10%;
      .el-button {
        font-size: 18px;
        padding: 12px 0;
        border-radius: 8px;
        width: 35%;
      }
      .saveBut {
        margin-right: 10%;
      }
    }
    .deltext {
      margin: 0 10%;
    }
  }
}
</style>
<style lang="less">
.userDialog {
  .el-dialog {
    width: 800px !important;
    margin-top: 8vh !important;
    border-radius: 8px !important;
    .el-form {
      .el-form-item {
        .el-form-item__label {
          line-height: 20px !important;
          padding-right: 12px !important;
          font-size: 14px !important;
        }
        .el-form-item__content {
          font-size: 14px !important;
          line-height: 40px !important;
          .el-input {
            font-size: 14px !important;
            line-height: 40px !important;
            .el-input__inner {
              border-width: 1px !important;
              border-radius: 4px !important;
              height: 40px !important;
              line-height: 40px !important;
              padding: 0 15px !important;
            }
            .el-input__suffix {
              right: 5px !important;
              .el-select__caret {
                font-size: 14px !important;
                width: 25px !important;
                line-height: 40px !important;
              }
            }
            .el-cascader {
              .el-input__suffix {
                right: 5px !important;
                .el-input__icon {
                  font-size: 14px !important;
                  width: 25px !important;
                  line-height: 40px !important;
                }
              }
            }
          }
          .el-form-item__error {
            font-size: 12px !important;
            padding-top: 4px !important;
          }
          .el-radio-group {
            display: flex;
            align-items: center;
            .el-radio {
              display: flex;
              align-items: center;
              margin-right: 30px !important;
              .el-radio__label {
                font-size: 14px !important;
                padding-left: 10px !important;
              }
              .el-radio__inner {
                width: 14px !important;
                height: 14px !important;
                border-width: 1px !important;
                &::after {
                  width: 4px !important;
                  height: 4px !important;
                }
              }
            }
          }
          .el-checkbox-group {
            .el-checkbox {
              font-size: 14px !important;
              margin-right: 30px !important;
              .el-checkbox__label {
                padding-left: 10px !important;
                line-height: 19px !important;
                font-size: 14px !important;
              }
              .el-checkbox__inner {
                border: 1px solid #dcdfe6 !important;
                border-radius: 2px !important;
                width: 14px !important;
                height: 14px !important;
              }
            }
          }
          .chooseDate {
            &.el-range-editor.el-input__inner {
              padding: 3px 10px !important;
              height: 40px !important;
              line-height: 40px !important;
              border-radius: 4px !important;
              border-width: 1px !important;
            }
            .el-range__icon {
              font-size: 14px !important;
              margin-left: -5px !important;
              line-height: 32px !important;
              width: 25px !important;
            }
            .el-range-input,
            .el-range-separator {
              font-size: 14px !important;
            }
            .el-range__close-icon {
              font-size: 14px !important;
              width: 25px !important;
              line-height: 32px !important;
            }
            .el-range-separator {
              padding: 0 5px !important;
              line-height: 32px !important;
            }
          }
        }
      }
    }
  }
  .el-dialog__wrapper {
    .el-dialog__header {
      margin: 0 100px !important;
      padding: 20px 20px 10px !important;
      .el-dialog__title {
        font-size: 24px !important;
        font-weight: 600 !important;
      }
    }
    .el-dialog__body {
      padding: 0 25px 30px 25px !important;
      margin-top: 0 !important;
      margin: 0 100px !important;
      .el-divider {
        margin: 0 !important;
        height: 5px !important;
        border-radius: 2.5px !important;
        margin-bottom: 2% !important;
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .userManage {
    .el-dialog {
      width: @zoomIndex * 800px !important;
      border-radius: @zoomIndex * 8px !important;
      .el-form {
        .el-form-item {
          .el-form-item__label {
            line-height: @zoomIndex * 20px !important;
            padding-right: @zoomIndex * 12px !important;
            font-size: @zoomIndex * 14px !important;
          }
          .el-form-item__content {
            font-size: @zoomIndex * 14px !important;
            line-height: @zoomIndex * 40px !important;
            .el-input {
              font-size: @zoomIndex * 14px !important;
              line-height: @zoomIndex * 40px !important;
              .el-input__inner {
                border-width: @zoomIndex * 1px !important;
                border-radius: @zoomIndex * 4px !important;
                height: @zoomIndex * 40px !important;
                line-height: @zoomIndex * 40px !important;
                padding: 0 @zoomIndex * 15px !important;
              }
              .el-input__suffix {
                right: @zoomIndex * 5px !important;
                .el-select__caret {
                  font-size: @zoomIndex * 14px !important;
                  width: @zoomIndex * 25px !important;
                  line-height: @zoomIndex * 40px !important;
                }
              }
            }
            .el-cascader {
              .el-input__suffix {
                right: @zoomIndex * 5px !important;
                .el-input__icon {
                  font-size: @zoomIndex * 14px !important;
                  width: @zoomIndex * 25px !important;
                  line-height: @zoomIndex * 40px !important;
                }
              }
            }
            .el-form-item__error {
              font-size: @zoomIndex * 12px !important;
              padding-top: @zoomIndex * 4px !important;
            }
            .el-radio-group {
              .el-radio {
                margin-right: @zoomIndex * 30px !important;
                .el-radio__label {
                  font-size: @zoomIndex * 14px !important;
                  padding-left: @zoomIndex * 10px !important;
                }
                .el-radio__inner {
                  width: @zoomIndex * 14px !important;
                  height: @zoomIndex * 14px !important;
                  border-width: @zoomIndex * 1px !important;
                  &::after {
                    width: @zoomIndex * 4px !important;
                    height: @zoomIndex * 4px !important;
                  }
                }
              }
            }
            .el-checkbox-group {
              .el-checkbox {
                font-size: @zoomIndex * 14px !important;
                margin-right: @zoomIndex * 30px !important;
                .el-checkbox__label {
                  padding-left: @zoomIndex * 10px !important;
                  line-height: @zoomIndex * 19px !important;
                  font-size: @zoomIndex * 14px !important;
                }
                .el-checkbox__inner {
                  border: @zoomIndex * 1px solid #dcdfe6 !important;
                  border-radius: @zoomIndex * 2px !important;
                  width: @zoomIndex * 14px !important;
                  height: @zoomIndex * 14px !important;
                }
              }
            }
            .chooseDate {
              &.el-range-editor.el-input__inner {
                padding: @zoomIndex * 3px @zoomIndex * 10px !important;
                height: @zoomIndex * 40px !important;
                line-height: @zoomIndex * 40px !important;
                border-radius: @zoomIndex * 4px !important;
                border-width: @zoomIndex * 1px !important;
              }
              .el-range__icon {
                font-size: @zoomIndex * 14px !important;
                margin-left: @zoomIndex * -5px !important;
                line-height: @zoomIndex * 32px !important;
                width: @zoomIndex * 25px !important;
              }
              .el-range-input,
              .el-range-separator {
                font-size: @zoomIndex * 14px !important;
              }
              .el-range__close-icon {
                font-size: @zoomIndex * 14px !important;
                width: @zoomIndex * 25px !important;
                line-height: @zoomIndex * 32px !important;
              }
              .el-range-separator {
                padding: 0 @zoomIndex * 5px !important;
                line-height: @zoomIndex * 32px !important;
              }
            }
          }
        }
      }
    }

    .el-dialog__wrapper {
      .el-dialog__header {
        margin: 0 @zoomIndex * 100px !important;
        padding: @zoomIndex * 20px @zoomIndex * 20px @zoomIndex * 10px !important;
        .el-dialog__title {
          font-size: @zoomIndex * 24px !important;
        }
      }
      .el-dialog__body {
        padding: 0 @zoomIndex * 25px @zoomIndex * 30px @zoomIndex * 25px !important;
        margin: 0 @zoomIndex * 100px !important;
        .el-divider {
          height: @zoomIndex * 5px !important;
          border-radius: @zoomIndex * 2.5px !important;
        }
      }
    }
  }
}
</style>