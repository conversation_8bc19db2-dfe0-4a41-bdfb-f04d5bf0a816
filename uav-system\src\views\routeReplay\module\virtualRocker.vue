<template>
  <div class="virtual-rocker">
    <div class="rocker-left rocker-item">
      <div class="rocker-left-block" style="height: 100px">
        <sliding-block :vertical="true" ref="accelerator"></sliding-block>
      </div>

      <div class="item-right">
        <disc ref="leftDisc">
          <template #circle-center>
            <div class="circle-center"></div>
          </template>
        </disc>
        <div class="" style="width: 1px; height: 10px"></div>
        <sliding-block ref="uavLeftRight"></sliding-block>
        <div class="uav-left-title">{{ language.uav }}</div>
      </div>
    </div>

    <div class="rocker-right rocker-item">
      <div class="item-right">
        <disc ref="rightDisc">
          <template #circle-center>
            <div class="right-circle-center">
              {{ language.payTilt }}
              <!-- 云台 -->
            </div>
          </template>
        </disc>
      </div>
      <div class="rocker-left-block ml10" style="height: 100px">
        <sliding-block :vertical="true" ref="cameraZoom"></sliding-block>
      </div>
    </div>
  </div>
</template>

<script>
import disc from "./disc.vue";
import slidingBlock from "./slidingBlock.vue";
export default {
  components: {
    disc,
    slidingBlock,
  },
  data() {
    return {
      data: [
        { ch1: 1500, ch2: 1500, ch3: 1500, ch4: 1500, ch15: 1500 },
        { ch1: 1510, ch2: 1510, ch3: 1510, ch4: 1510, ch15: 1510 },
        { ch1: 1400, ch2: 1400, ch3: 1400, ch4: 1400, ch15: 1400 },
        { ch1: 1600, ch2: 1600, ch3: 1600, ch4: 1600, ch15: 1600 },
        { ch1: 1600, ch2: 1600, ch3: 1600, ch4: 1600, ch15: 1600 },
        { ch1: 1300, ch2: 1600, ch3: 1300, ch4: 1600, ch15: 1200 },
      ],
      index: 0,
      time: null,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.navigation.uavOperation;
    },
  },
  methods: {
    // 更新值
    uploadRocker: function (item) {
    //   if (this.data[this.index]) {
    //     item = this.data[this.index];
    //   }

      // 油门
      let accelerator = (1500 - item.ch3) / 5;
      this.$refs.accelerator.echoLocation(`${50 + accelerator}%`);

      // 偏航，机身左转右转
      let uavLeftRight = (item.ch4 - 1500) / 5;
      this.$refs.uavLeftRight.echoLocation(`${50 + uavLeftRight}%`);

      // 飞机左右
      let uavType = "";
      // 前后
      if (item.ch2 > 1500) {
        uavType = "bottom";
      } else if (item.ch2 < 1500) {
        uavType = "top";
      }

      // 左右
      if (item.ch1 > 1500) {
        uavType = "right";
      } else if (item.ch1 < 1500) {
        uavType = "left";
      }
      this.$refs.leftDisc.echoSelect(uavType);

      // 云台
      let panTilt = "";
      if (item.ch15 > 1500) {
        panTilt = "top";
      } else if (item.ch15 < 1500) {
        panTilt = "bottom";
      }
      this.$refs.rightDisc.echoSelect(panTilt);

    //   if (!this.time) {
    //     this.time = setInterval(() => {
    //       this.index++;
    //     }, 3000);
    //   }
    },
  },
};
</script>

<style lang="less" scoped>
.virtual-rocker {
  display: flex;
  //   align-items: center;
  justify-content: space-between;
  width: 276px;
  //   height: 150px;
  background-image: linear-gradient(
    to bottom,
    rgba(4, 11, 43, 0.8),
    rgba(13, 134, 255, 0.5)
  );
  padding: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  .rocker-item {
    width: 50%;
    height: 100%;
    display: flex;

    .rocker-left-block {
      width: 20px;
      min-width: 20px;
      height: 100%;
    }
  }

  .circle-center {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -8px 0 0 -8px;
    background-color: #1b3956;
  }
  .right-circle-center {
    width: 30px;
    height: 30px;
    border: 1px solid rgba(27, 57, 86, 1);
    border-radius: 50%;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -15px 0 0 -15px;
    transform: rotate(-45deg);
    font-size: 12px;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(76, 166, 255, 1);
    font-weight: 700;
  }

  .uav-left-title {
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    text-align: center;
    width: 100%;
    // letter-spacing: 6px;
  }
}
</style>