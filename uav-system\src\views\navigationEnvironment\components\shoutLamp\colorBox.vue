<template>
  <div class="color-box">
    <div class="color-show-box" @click="operateColor">
      <div
        class="lamp-item-box-item"
        :style="{ backgroundColor: colorList[led_pc1] || '#F64134' }"
      ></div>
      <div
        class="lamp-item-box-item"
        :style="{ backgroundColor: colorList[led_pc2] || '#2380F8' }"
      ></div>
    </div>
    <div class="choose-color" :style="{ width }">
      <div class="color-box">
        <div class="color-item" v-for="(item, key) in colorList" :key="key">
          <div
            class="item-box"
            :class="{ active: led_pc1 == key }"
            :style="{ backgroundColor: item }"
            @click="changeColor(key, 'led_pc1')"
          ></div>
          <div
            class="item-box"
            :class="{ active: led_pc2 == key }"
            :style="{ backgroundColor: item }"
            @click="changeColor(key, 'led_pc2')"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    sendWs: {
      type: Function,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      led_pc1: 1,
      led_pc2: 2,
      colorList: {
        1: "#F64134",
        2: "#ABF63F",
        3: "#2380F8",
        4: "#E7B91E",
        5: "#F1FCFF",
      },
      width: 0,
    };
  },
  methods: {
    disposeData: function (data) {
      this.led_pc1 = data.led_pc1;
      this.led_pc2 = data.led_pc2;
    },
    operateColor: function () {
      this.width = this.width == 0 ? "138px" : 0;
    },
    changeColor: function (key, name) {
      this[name] = key;
      this.sendWs({ action: 65, value: `${this.led_pc1}#${this.led_pc2}` });
    },
  },
};
</script>
<style lang="less" scoped>
.color-box {
  flex: 1;
  padding: 0 5px;
  .color-show-box {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    .lamp-item-box-item {
      width: 26px;
      height: 26px;
      border-radius: 50%;
    }
  }

  .choose-color {
    position: absolute;
    top: 0;
    left: calc(100% + 18px);
    height: 100%;
    background-color: rgba(71, 71, 71, 0.5);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.1s linear;
    .color-box {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      box-sizing: border-box;
      padding: 10px;
      .color-item {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        .item-box {
          width: 26px;
          height: 26px;
          box-sizing: border-box;
          border-radius: 50%;
          cursor: pointer;
          &.active {
            border: 3.5px solid #fff;
          }
        }
      }
    }
  }
}
</style>