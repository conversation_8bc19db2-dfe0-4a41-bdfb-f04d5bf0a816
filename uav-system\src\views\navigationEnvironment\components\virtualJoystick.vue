<template>
  <div class="virtual-joystick">

    <!-- 左侧控制条 -->
    <div class="mr10 ml10" :style="{height: leftProgressHiehg}" v-if="leftSliding">
      <sliding-block :vertical="true" v-model="config.leftValue" @up="upEvent('left', $event)"  @onChange="slidingChange('left', $event)" @onInput="slidingInput('left', $event)" ></sliding-block>
    </div>

    <div class="">

      <!-- 圆盘控制 -->
      <disc-control width="100" @operation="operation" @up="mouseupEvent">
        <template v-slot:circle-center>
          <slot name="circle-center"></slot>
        </template>
      </disc-control>

      <!-- 底部控制条 -->
      <div class="sliding-block">
        <slot name="bottom">
           <sliding-block v-model="config.botValue"  @up="upEvent('bottom', $event)" @onChange="slidingChange('bootom', $event)"  @onInput="slidingInput('bottom', $event)"></sliding-block>
        </slot>
      </div>

      <!-- 标题 -->
      <div class="circle-text">
        <slot name="title"></slot>
      </div>

    </div>

    <div class="mr10 ml10" :style="{height: leftProgressHiehg}" v-if="!leftSliding">
      <slot name="right">
        <sliding-block :interval="interval" :vertical="true" v-model="config.leftValue"  @up="upEvent('right', $event)" @onChange="slidingChange('right', $event)"  @onInput="slidingInput('right', $event)"></sliding-block>
      </slot>
    </div>
  </div>
</template>

<script>
import slidingBlock from './slidingBlock.vue'
import discControl from './discControl.vue'
export default {
  components: { slidingBlock, discControl },
  props: {
    leftProgressHiehg: {
      type: String,
      default: '100%',
    },
    config: {
      type: Object,
      default: () => {
        return {
          leftValue: 50,
          botValue: 50,
        }
      },
    },
    leftSliding: {
        type: Boolean,
        default: true
    },

    width: {
      type: [String, Number],
      default: 100,
    },
    interval: Number
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  methods: {
    // 虚拟遥感控制
    operation: function (key) {
      this.$emit('operation', key)
    },
    // 鼠标移动滚动条时触发
    slidingInput: function(key, val){
      this.$emit("slidingInput", key, val);
    },
    // 鼠标松开鼠标时触发
    slidingChange: function(key, val){
      this.$emit("slidingChange", key, val);
    },
    mouseupEvent: function(key){
      this.$emit("undo", key);
    },
    upEvent: function(type, val){
      this.$emit("slidingUp", type, val);
    }
  },
}
</script>

<style lang="less" scoped>
.virtual-joystick {
  position: relative;
  display: flex;
  .circle-text {
    text-align: center;
    // color: #fff;
    height: 30px;
    line-height: 30px;
  }
  @circleWidth: 120px;
  .cell-circle {
    // width: @circleWidth;
    // height: @circleWidth;
    position: relative;
    transform: rotate(45deg);

    // @circleCellHeight: @circleWidth / 2;
    .circle-row {
      //   width: @circleCellHeight;
      //   height: @circleCellHeight;
      //   border-radius: @circleCellHeight 0 0;
      position: absolute;
      border-right: none;
      display: flex;
      align-items: center;
      justify-content: center;
      // background-color: #fff;
      font-size: 24px;
      // color: #000;
      cursor: pointer;
      i {
        transform: rotate(-45deg);
      }

      &:nth-child(2) {
        transform: rotate(90deg);
        // left: @circleCellHeight;
        top: 0;
        i {
          transform: rotate(215deg);
        }
      }

      &:nth-child(3) {
        transform: rotate(-90deg);
        left: 0px;
        // top: @circleCellHeight;
        i {
          transform: rotate(45deg);
        }
      }

      &:nth-child(4) {
        transform: rotate(180deg);
        // left: @circleCellHeight;
        // top: @circleCellHeight;
        i {
          transform: rotate(135deg);
        }
      }
    }
  }

  .select-icon-style {
    // color: rgba(76, 166, 255, 1) !important;
  }

  .sliding-block {
    height: 22px;
    display: flex;
    width: 100%;
    align-items: center;
    margin-top: 5px;
  }
}
</style>