/**
 * 判断点是否在多边形内
 * @param {Object} point 点的经纬度坐标
 * @param {Array} polygon 多边行坐标集合
 * @returns {Boolean} 在多边形内返回true，否则false
 */
function pointInPolygon({ point, polygon }) {
    let pt = turf.point(point);
    let polyArr = []
    for (let index = 0; index < polygon.length; index++) {
        polyArr.push([polygon[index].lng, polygon[index].lat])
    }
    polyArr.push([polygon[0].lng, polygon[0].lat]) //首尾坐标需一致
    let poly = turf.polygon([polyArr]);
    return turf.booleanPointInPolygon(pt, poly);

}
/**
 * 判断线与线是否交叉/线与面
 * @param {object} point 组成线的点坐标
 * @param {object} point1 组成线的点坐标
 * @param {Array} fence 与线判断的集合
 * @param {Boolean} type 判断是否首尾相连
 * @returns {Boolean} 有相交返回true,否则false
 */
function lineCross({ point, point1, fence, type }) {
    let flag = false
    var line = turf.lineString([
        [point.lng, point.lat],
        [point1.lng, point1.lat]
    ]);
    for (let index = 0; index < fence.length - 1; index++) {
        let line2 = turf.lineString([
            [fence[index].lng, fence[index].lat],
            [fence[index + 1].lng, fence[index + 1].lat]
        ]);
        var intersects = turf.lineIntersect(line, line2);
        if (intersects.features && intersects.features.length) {
            flag = true
            break;
        }
    }
    if (!flag && type) {
        let line1 = turf.lineString([
            [fence[fence.length - 1].lng, fence[fence.length - 1].lat],
            [fence[0].lng, fence[0].lat]
        ])
        var intersects = turf.lineIntersect(line, line1);
        if (intersects.features && intersects.features.length) {
            flag = true
        }
    }
    return flag
}
/**
 * 经纬度计算中心点
 * @param {Array} lnglatArr 经纬度数组
 * @returns {Object} 返回中心点的经纬度坐标以及高度
 */
function computeCenter({ lnglatArr }) {
    var total = lnglatArr.length;
    var X = 0,
        Y = 0,
        Z = 0,
        height = 0;
    lnglatArr.map((item) => {
        var lng = (item.lng * Math.PI) / 180;
        var lat = (item.lat * Math.PI) / 180;
        var x, y, z;
        x = Math.cos(lat) * Math.cos(lng);
        y = Math.cos(lat) * Math.sin(lng);
        z = Math.sin(lat);
        X += x;
        Y += y;
        Z += z;
        height += item.height
    });
    X = X / total;
    Y = Y / total;
    Z = Z / total;
    height = height / total;
    var Lng = Math.atan2(Y, X);
    var Hyp = Math.sqrt(X * X + Y * Y);
    var Lat = Math.atan2(Z, Hyp);
    return {
        lat: (Lat * 180) / Math.PI,
        lng: (Lng * 180) / Math.PI,
        height: height
    }
}
/**
 * 计算两点的距离
 * @param {Object} point1 第一个点的经纬度坐标 
 * @param {Object} point2 第二个点的经纬度坐标 
 * @param {Boolean} defaultHeight 是否不计算高度，如果为true，高度为0，否则高度为坐标高度
 * @returns {number} 返回距离，单位为米
 */
function computedDistance({ point1, point2, defaultHeight }) {
    let point1XYZ = Cesium.Cartesian3.fromDegrees(point1.lng, point1.lat, defaultHeight ? 0 : (point1.height ? point1.height : 0))
    let point2XYZ = Cesium.Cartesian3.fromDegrees(point2.lng, point2.lat, defaultHeight ? 0 : (point2.height ? point2.height : 0))
    return Cesium.Cartesian3.distance(point1XYZ, point2XYZ)
}
/**
 * 计算区域面积
 * @param {Array} positions 组成区域的点坐标集合
 * @returns {Number} 返回面积，单位m²
 */
function computedArea({ positions }) {
    let points = []
    for (let index = 0; index < positions.length; index++) {
        points.push([positions[index].lng, positions[index].lat])
    }
    points.push(points[0])
    var polygon = turf.polygon([points]);
    return turf.area(polygon);
}
/**
 * 计算测量时多边形的面积
 * @param {Array} arrCartesian3 传入Cartesian3类型数组，xyz
 * @returns {Number} 返回面积，单位m²/km²
 */
function getAreaFromArrayCartesians({ arrCartesian3 }) {
    let areaAll = 0;
    let cartesianFirst = arrCartesian3[0];
    for (let i = 1; i < arrCartesian3.length - 1; i++) {
        let cartesianStart = arrCartesian3[i];
        let cartesianEnd = arrCartesian3[i + 1];
        let vecStart = Cesium.Cartesian3.subtract(
            cartesianStart,
            cartesianFirst,
            new Cesium.Cartesian3()
        );
        let vecEnd = Cesium.Cartesian3.subtract(
            cartesianEnd,
            cartesianFirst,
            new Cesium.Cartesian3()
        );
        let vecCross = Cesium.Cartesian3.cross(
            vecStart,
            vecEnd,
            new Cesium.Cartesian3()
        );
        let areaTriangle = Cesium.Cartesian3.magnitude(vecCross) * 0.5;
        areaAll += areaTriangle;
    }
    if (areaAll > 10000) {
        return (areaAll / 1000000).toFixed(3) + "km²"
    }
    return areaAll.toFixed(2) + "m²";
}
/**
 * 计算方位角，区分参数为坐标点还是平面坐标
 * @param {Array} points 点的集合，根据 paramsPoint为true传入经纬度坐标，为false传入Cartesian3类型坐标，xyz
 * @param {Object} map 地图实例
 * @param {Boolean} paramsPoint 判断坐标类型
 * @returns {Number} 返回方位角
 */
function calcAngle({ points, map, paramsPoint = false }) {
    let start = points[0],
        end = points[1]
    if (paramsPoint) {
        let start_1 = Cesium.Cartesian3.fromDegrees(points[0].lng, points[0].lat, 0)
        let end_1 = Cesium.Cartesian3.fromDegrees(points[1].lng, points[1].lat, 0)
        start = start_1
        end = end_1
    }
    let p_start = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
            map.scene,
            start
        ),
        p_end = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
            map.scene,
            end
        );
    var diff_x = p_end.x - p_start.x,
        diff_y = p_end.y - p_start.y;
    let a = (360 * Math.atan2(diff_y, diff_x)) / (2 * Math.PI) + 90;
    if (a > 180) {
        a = -(360 - a);
    }
    return a;
}
let obj = {
    "pointInPolygon": pointInPolygon,
    "lineCross": lineCross,
    "computeCenter": computeCenter,
    "computedDistance": computedDistance,
    "computedArea": computedArea,
    "getAreaFromArrayCartesians": getAreaFromArrayCartesians,
    "calcAngle": calcAngle
}
export function computedMapMethods(name, params) {
    return obj[name](params)
}