const userManage = {
    allBtn: {
        search: '搜\xa0\xa0\xa0\xa0索',
        newUser: '新\xa0建\xa0用\xa0户',
        set: '设置',
        del: '删除',
        recovery: '恢复',
        save: '保\xa0\xa0\xa0\xa0\xa0\xa0存',
        cancel: '取\xa0\xa0\xa0\xa0\xa0\xa0消',
        del1: '删\xa0\xa0\xa0\xa0\xa0\xa0除',
        recover1: '恢\xa0\xa0\xa0\xa0\xa0\xa0复'

    },
    tipInfo: {
        placeholder: '请输入用户手机号/邮箱',
        placeholder1: '该用户已删除，无法操作'
    },
    dialogInfo: {
        title: '新增用户',
        editTitle: '编辑用户',
        name: '昵\xa0称',
        placeholder: '请输入账户昵称',
        company: '选择所属公司/部门',
        placeholder1: "请选择用户所属公司/部门",
        account: "手机号\xa0/\xa0邮箱",
        placeholder2: '请输入用户手机号\xa0/\xa0邮箱',
        password: '密\xa0码',
        message: '请输入用户密码',
        placeholder3: '请输入用户密码（长度为6-32位数字或字母）',
        placeholder4: '请输入用户密码，不填为原来密码',
        placeholder5: '密码长度在 6 到 32 个字符',
        placeholder6: '请选择用户期限',
        power: '用户权限',
        term: '用户期限',
        permanent: '永久有效',
        limite: '限时有效',
        startTime: '开始日期',
        endTime: '结束日期',
        delTitle: '用户删除',
        recoverTitle: '用户恢复',
        delText: '删除该用户后，该用户关联的所有数据也随着删除，并且不可恢复，请谨慎操作。',
        recoverText: '是否恢复用户的状态为正常？',
        successAdd: '用户添加成功！',
        successEdit: "用户编辑成功！",
        successDel: '删除成功！',
        successRecover: '用户恢复成功！',
        cancelDel: '已取消删除！',
        cancelRecover: '已取消恢复！',
        errorMessage: '请输入正确的手机号',
        errorMessage1: '请输入正确的邮箱',
        errorMessage2: '请选择限时时间段',
    },
    options: [{
        label: "华科尔",
        value: "wk",
    }, {
        label: "广州港航工程研究所",
        value: "gzGh",
    }, {
        label: "南瑞",
        value: "zrhk",
    }],
    column: [{
            label: "昵称",
            prop: "nick",
        },
        {
            label: "手机号/邮箱",
            prop: "phone",
        },
        {
            label: "所属公司/部门",
            prop: "company",
        },
        {
            label: "功能权限",
            prop: "fun_list",
        },
        {
            label: "状态",
            prop: "state",
        },
        {
            label: "开始时间",
            prop: "start_time",
        },
        {
            label: "结束时间",
            prop: "end_time",
        },
        {
            label: "操作",
            prop: "operation",
            width: "180",
        },
    ],

}
export default userManage