<!-- 视频、地图 -->
<template>
  <div class="video-map instructions-video-map" style="width: 100%">
    <layouts
      :title="componentsList[item].title"
      :class="index < showList.length - 1 ? 'mb10' : ''"
      class="video-map-item"
      v-for="(item, index) in showList"
      :key="index"
    >
      <template v-slot:content>
        <div class="item-text">
          {{ componentsList[item].content }}
        </div>
      </template>
    </layouts>
  </div>
</template>

<script>
import layouts from "./layout.vue";
export default {
  components: {
    layouts,
  },
  props: {
    title: String,
    content: String,
    type: {
      type: [Number, String],
      default: 10,
    },
    sortList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      componentsList: {
        uav: { title: "飞机视频", content: "飞机实时视频回传画面" },
        cabin: {
          title: "舱内监控",
          content: "视频监测的方式实时监测固定机场内部的变化情况",
        },
        outboard: {
          title: "舱外监控",
          content: "视频监测的方式实时监测固定机场内部的变化情况",
        },
        map: {
          title: "切换地图功能区",
          content: "展示任务围栏，航线，飞机实时位置",
        },
      },
      showList: [],
    };
  },
  watch: {
    sortList: {
      deep: true,
      handler: function (list) {
        this.showList = [];
        let data = ["", "", ""];
        for (let i = 0; i < list.length; i++) {
          let item = list[i];
          if (item.style && item.style.left === 0) {
            this.showList.push(item.key);
          }

          if (item.style && item.style.top === 0) {
            data[0] = item.key;
          }

          if (item.style && item.style.bottom === 0) {
            data[2] = item.key;
          }

          if (
            item.style &&
            item.style.top !== 0 &&
            item.style.top !== undefined
          ) {
            data[1] = item.key;
          }
        }

        // 判断是否需要重新排序
        if (this.showList.length > 1) {
          this.showList = data.filter((item) => {
            return item;
          });
        }
      },
    },
  },
  computed: {
    uavMap() {
      return this.$languagePackage.navigation.instructions.uavMap;
    },
  },
  created() {
    for (let k in this.uavMap) {
      this.componentsList[k] = Object.assign({}, this.uavMap[k]);
    }
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .video-map {
    .video-map-item {
      height: @radio * 156px !important;
      .item-text {
        font-size: @radio * 12px !important;
      }
    }
  }
}

.video-map {
  display: flex;
  flex-direction: column;
  .video-map-item {
    height: 156px;
    .item-text {
      font-size: 12px;
    }
  }
}
</style>