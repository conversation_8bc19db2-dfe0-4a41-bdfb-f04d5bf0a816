/**
 * 团队管理相关
 */
const team = {
    addTeam: 'Add department',
    editTeam: 'Editorial Department',
    edit: 'Edit',
    formLable: {
        parent_id: 'Department',
        name: 'Name',
        description: 'Description',
        notes: 'Notes',
        state: 'Status'
    },
    placeholder: {
        parent_id: 'Please select the superior department. If left blank, it represents a first level department',
        name: 'Please enter the department name(company, department, unit)',
        description: 'Please enter department description',
        notes: 'Please enter department remarks'
    },
    submit: 'Confirm',
    cancel: 'Cancel',
    successAdd: 'Successfully added department',
    successEdit: 'Editorial department successful',
    normal: 'Normal',
    delete: 'Delete',
    deleted: 'Deleted',
    column: {
        name: 'Name',
        state: 'Status',
        parent_info: 'Superior department',
        description: 'Description',
        notes: 'Notes',
        operation: 'Operation'
    },
}
export default team