## 使用场景为滚动到底部进行加载数据

## 导入
## import scrollList from "@/components/scrollList/index.vue"



## 使用(注：使用的时候必须给外层或者scrollList组件指定高度，否则不会触发到滚动)
<scroll-list>
    <template v-slot:content="scope">
        插入要显示的内容
        scope参数如下：
            scope.data：数据列表
            scope.current：当前页
    </template>
    <template v-slot:footer="scope">
        如果需要自定义底部加载内容，则使用卡槽footer即可
        scope参数如下：
            scope.loadStatus：加载状态，1加载错误，2加载中
    </template>
    <template v-slot:noData>
        没有数据时自定义显示插槽
    </template>
</scroll-list>

## 属性值
## data：要切割显示的数据列表，优先级比urlName低
## urlName：请求url名称，如果填写该属性，则不会处理data数据
## params：请求参数
## pmd：要rsa加密的数据字段名称，注：按照顺序填写， 如：sort + page写为 "sort,page"即可
## isJson：如果有此属性，则会使用jsonTest请求接口
## distanceBootm：滚动距离底部的距离，默认15px，小于等于时会触发加载下一页数据
## time：在多久的时间内只能触发一次加载下一页数据
## noDataText：无数据时显示的文本
## pageSize 每页请求数据，默认十条

## 事件
#### onEarth：滚动到底部时触发

## 注：如果需要外部刷新，调用refresh即可