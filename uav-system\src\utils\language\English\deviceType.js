const deviceType = {
  column: [{
      label: "Type",
      prop: "cla_type",
    },
    {
      label: "Type Name",
      prop: "cla_name"
    },
    {
      label: "Type Value",
      prop: "value"
    },
    {
      label: "Number Of Video Streams",
      prop: "stream"
    },
    {
      label: "State",
      prop: "state"
    },
    {
      label: "Notes",
      prop: "notes"
    },
    {
      label: "Creation Time",
      prop: "create_time"
    }, {
      label: "Modified Time",
      prop: "modified_time"
    }, {
      label: "Operation",
      prop: "operation"
    }
  ],
  addTitle: 'Add Device Type',
  editTitle: 'Edit Device Type',
  formLable: {
    cla_type: 'Type',
    cla_name: "Type Name",
    value: "Type Value",
    stream: "Number Of Video Streams",
    notes: "Notes",
    state: 'State'

  },
  placeholder: {
    cla_type: 'Please select the device type type',
    cla_name: 'Please enter the device type type name',
    value: 'Please enter a device type value',
    stream: 'Please choose device type Number of video streams',
    stream_1: 'The number of video streams is between 0 and 3',
    notes: 'Please enter remarks',
  },
  loading: 'Submitting, please wait...',
  successTipAdd: 'The device type is added successfully.',
  successTipEdit: 'The device type is successfully edited.',
  successTipDel: 'The device type is deleted successfully.',
  deleteContent: 'Are you sure to delete the device type?',
  delTip: 'Delete Tip',
  confirm: 'confirm',
  cancel: 'cancel',
  disabledContent: 'Are you sure to disable this device type?',
  disabledTip: 'Disable Tip',
  deleted: 'Deleted',
  addBtn: 'Add Type',
  editBtn: "Edit"


}
export default deviceType
