<template>
  <div class="teamManage">
    <div class="teamManage-form">
      <div class="type-choose"></div>
      <div class="operate-btn">
        <el-button @click="openDialog('add')" type="primary">
          <i class="el-icon-plus"></i> {{ language.addTeam }}
        </el-button>
      </div>
    </div>
    <custom-table
      :column="column"
      :urlName="admin ? 'allGetTeamList' : ''"
      ref="customTable"
      :isShowPage="false"
      :data="tableData"
      :os_timestampCode="true"
    >
      <template #state="scope">
        {{ scope.row.state === 1 ? language.normal : language.deleted }}
      </template>
      <template #parent_info="scope">
        {{ scope.row.parent_info && scope.row.parent_info.name }}
      </template>
      <template #operation="scope">
        <el-button
          type="text"
          :title="language.editTeam"
          @click="openDialog('edit', scope.row)"
          >{{ language.edit }}</el-button
        >
      </template>
    </custom-table>
    <team-dialog ref="teamDialog" @refresh="refresh"></team-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
import customTable from "@/components/customTable/index.vue";
import teamDialog from "./components/teamDialog.vue";
export default {
  data() {
    return {
      column: [
        { label: "上级团队", prop: "parent_info" },
        { label: "名称", prop: "name" },
        { label: "状态", prop: "state" },
        { label: "描述", prop: "description" },
        { label: "备注", prop: "notes" },
        { label: "操作", prop: "operation", width: 180 },
      ],
      tableData: [],
    };
  },
  components: {
    customTable,
    teamDialog,
  },
  computed: {
    language() {
      return this.$languagePackage.team;
    },
    userFunList() {
      return this.$store.state.user.userInfo.fun_list;
    },
    admin() {
      return (
        this.userFunList &&
        this.userFunList.indexOf("super_administrator") !== -1
      );
    },
  },
  created() {
    for (let index = 0; index < this.column.length; index++) {
      this.column[index].label = this.language.column[this.column[index].prop];
    }
    if (!this.admin) {
      this.getComList();
    }
  },
  methods: {
    getComList() {
      let param = {
        os_timestampCode: true,
      };
      requestHttp("allGetTeamList", param).then((res) => {
        this.tableData = res.data && res.data.list ? res.data.list : [];

        for (let index = 0; index < this.tableData.length; index++) {
          this.getChildren(this.tableData[index]);
        }
      });
    },
    getChildren(item) {
      if (item.son_list && item.son_list.length) {
        this.tableData = [...this.tableData, ...item.son_list];
        item.son_list.forEach((element) => {
          this.getChildren(element);
        });
      }
    },
    openDialog: function (type, row) {
      this.$refs.teamDialog.open(type, row);
    },
    refresh() {
      this.$refs.customTable.refresh();
    },
  },
};
</script>
<style lang="less" scoped>
.teamManage {
  background-color: rgba(0, 0, 0, 1);
  padding: 20px;
  padding-bottom: 0;
  height: calc(100% - 20px);
  .teamManage-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .operate-btn {
      .el-button {
        font-size: 16px;
        background-color: white;
        color: #397ff8;
        border: none;
      }
    }
  }
}
</style>