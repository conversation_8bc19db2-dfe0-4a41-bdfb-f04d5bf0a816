/**
 * 用户信息存放
 */
import require from "@/utils/api";
import {
  getCookie
} from "@/utils/storage";

let getUserInfoSate = false;

const user = {
  state: {
    userInfo: {},
    dataOverview: {}
  },
  mutations: {
    setUserInfo(state, val) {
      state.userInfo = val;
    }
  },
  actions: {
    getUserInfo(content) {
      let token = getCookie("token");
      if(getUserInfoSate){
        return false;
      }
      getUserInfoSate = true;
      require("userInfo", {
        token: token,
        pmd: ""
      }).then((res) => {
        content.commit("setUserInfo", res.data);
        getUserInfoSate = false;
      }).catch(()=>{
        getUserInfoSate = false;
      })
    }
  },
}



export default user
