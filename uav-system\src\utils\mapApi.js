//高德地图服务接口，搜索提示，搜索地址，以及经纬度搜索地址，天气查询
import axios from '../../node_modules/axios'
//搜索提示
export function searchTip(keywords) {
  return new Promise((result, reject) => {
    axios.get("https://restapi.amap.com/v3/assistant/inputtips", {
      params: {
        key: "",
        keywords: keywords,
      },
    }).then(res => {
      return result(res)
    }).catch(() => {
      return reject(false)
    })
  })
}

//poi搜索
export function searchPoi(keywords) {
  return new Promise((result, reject) => {
    axios.get("https://restapi.amap.com/v5/place/text", {
      params: {
        key: "",
        keywords: keywords,
      },
    }).then(res => {
      return result(res)
    }).catch(() => {
      return reject(false)
    })
  })
}
//经纬度搜索地址
export function searchLnglat(location) {
  return new Promise((result, reject) => {
    axios.get("https://restapi.amap.com/v3/geocode/regeo?parameters", {
      params: {
        key: "",
        location: location,
      },
    }).then((res) => {
      return result(res)
    }).catch(() => {
      return reject(false)
    })
  })

}
//天气查询
export function searchSky(adcode) {
  return new Promise((result, reject) => {
    axios.get("https://restapi.amap.com/v3/weather/weatherInfo", {
      params: {
        key: "",
        city: adcode,
        extensions: "base"
      },
    }).then(res => {
      return result(res)
    }).catch(() => {
      return reject(false)
    })
  })

}
//天地图
//经纬度搜索地址
export function searchLnglat1(point) {
  return new Promise((result, reject) => {
    // http://api.tianditu.gov.cn/geocoder?postStr={'lon':116.37304,'lat':39.92594,'ver':1}&type=geocode&tk=您的密钥
    let postStr = {

      lon: point[0],
      lat: point[1],
      ver: 1,
    }
    axios.get("https://api.tianditu.gov.cn/geocoder", {
      params: {

        postStr: JSON.stringify(postStr),
        type: "geocode",
        tk: "015e7ad151c24812409565d76fa85724",
      },
    }).then((res) => {
      return result(res)
    }).catch(() => {
      return reject(false)
    })
  })

}
//搜索提示
export function searchTip1(keywords) {
  // http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"北京大学","level":12,"mapBound":"116.02524,39.83833,116.65592,39.99185","queryType":1,"start":0,"count":10}&type=query&tk=您的密钥
  let postStr = {
    keyWord: keywords,
    level: 8,
    mapBound: "-180,90,180,90",
    queryType: 1,
    start: 0,
    count: 10
  }
  return new Promise((result, reject) => {
    axios.get(`https://api.tianditu.gov.cn/v2/search?postStr=${JSON.stringify(postStr)}&type=query&tk=015e7ad151c24812409565d76fa85724`).then(res => {
      return result(res)
    }).catch(() => {
      return reject(false)
    })
  })
}
//poi搜索
export function searchPoi1(keywords) {
  // http://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"北京大学","level":12,"mapBound":"116.02524,39.83833,116.65592,39.99185","queryType":1,"start":0,"count":10}&type=query&tk=您的密钥
  let postStr = {
    keyWord: keywords,
  }
  return new Promise((result, reject) => {
    axios.get(`https://api.tianditu.gov.cn/geocoder?ds=${JSON.stringify(postStr)}&tk=015e7ad151c24812409565d76fa85724`).then(res => {
      return result(res)
    }).catch(() => {
      return reject(false)
    })
  })
}
