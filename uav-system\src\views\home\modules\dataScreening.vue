<!-- 数据总览 -->
<template>
  <div class="data-screening">
    <div class="data-screening-main">
      <!-- 背景 -->
      <div class="main-setting">
        <div class="setting-cell setting-left">
          <!-- 背景图 -->
          <img :src="dataBg6" alt="" />

          <!-- 左右两侧 -->
          <img :src="dataBg7" alt="" class="cell-bg7" />
        </div>

        <!-- 底部闪闪发光的线 -->
        <div class="setting-footer">
          <div class="footer-main">
            <div class="line-item line-left"></div>
            <div class="line-item line-right"></div>
          </div>
        </div>
      </div>

      <!-- 数据 -->
      <div class="main-content">
        <div class="show-data">
          <!-- 总巡检次数 -->
          <div class="content-cell left">
            <div class="content-top">
              <div class="cell-title" :style="titleStyle">
                {{ language.time.title }}
              </div>
              <div class="row-explain content-top-explain">
                <div
                  class="explain-title"
                  :class="$language == 'chinese' ? '' : 'explain-title-1'"
                >
                  {{ language.lastMonthTotal }}
                </div>
                <div class="explain-num">{{ polling.last_month_times }}</div>
              </div>
            </div>

            <div class="cell-main left-cell-main">
              <div class="row-total">{{ polling.total_times }}</div>
            </div>
          </div>

          <!-- 总巡检里程 -->
          <div class="content-cell center">
            <div class="content-top">
              <div
                class="cell-title"
                :class="$loadingEnUI ? 'cell-title-en' : ''"
                :style="titleStyle"
              >
                {{ language.mileage.title }}
              </div>
              <div
                class="row-explain content-top-explain"
                style="top: 30px"
                :style="$loadingEnUI ? '' : { left: '70%' }"
              >
                <div
                  class="explain-title"
                  :class="$loadingEnUI ? 'explain-title-1' : ''"
                >
                  {{ language.lastMonthTotal }}
                </div>
                <div class="explain-num">{{ polling.last_month_mileage }}</div>
              </div>
            </div>
            <div class="cell-main center-cell-main">
              <div class="row-total">{{ polling.total_mileage }}</div>
            </div>
          </div>

          <!-- 巡检成果 -->
          <div class="content-cell right">
            <div class="content-top">
              <div class="cell-title" :style="titleStyle">
                {{ language.achievement.title }}
              </div>
              <div class="row-explain content-top-explain">
                <div
                  class="explain-title"
                  :class="$language == 'chinese' ? '' : 'explain-title-1'"
                >
                  {{ language.lastMonthTotal }}
                </div>
                <div class="explain-num">{{ polling.last_month_result }}</div>
              </div>
            </div>
            <div class="cell-main right-cell-main">
              <div class="row-total">{{ polling.total_result }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dataBg6 from "@/assets/img/home/<USER>";
import dataBg7 from "@/assets/img/home/<USER>";
export default {
  data() {
    return {
      dataBg6,
      dataBg7,
      polling: {
        total_result: 0,
        last_month_result: 0,
        total_mileage: 0,
        last_month_mileage: 0,
        total_times: 0,
        last_month_times: 0,
      },
      timeInterval: null,
    };
  },
  computed: {
    ws() {
      return this.$store.state.websocket.ws;
    },
    language() {
      return this.$languagePackage.home.dataOverview;
    },
    titleStyle() {
      let type = this.$language;
      return {
        "letter-spacing": type == "chinese" ? "3px" : 0,
      };
    },
  },
  watch: {
    ws: {
      handler: function () {
        this.getDataInfo();
      },
      deep: true,
    },
  },
  beforeDestroy() {
    clearInterval(this.timeInterval);
  },
  created() {
    this.$store.commit("setMultiMessage", {
      key: "dataScreening",
      message: this.getPushData,
    });
  },
  mounted() {
    this.getDataInfo();
  },
  methods: {
    //
    getDataInfo: function () {
      if (this.ws && this.ws.manualSend) {
        this.ws.manualSend({}, 102);
        this.timeInterval = setInterval(() => {
          if (this.ws && this.ws.manualSend) {
            this.ws.manualSend({}, 102);
          }
        }, 60000);
      }
    },
    getPushData: function (msg_id, data) {
      if (msg_id == 102) {
        data.total_mileage = data.total_mileage / 1000;
        data.last_month_mileage = data.last_month_mileage / 1000;
        this.polling = data;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.data-content-media;

.dynamic-style () {
  .data-screening {
    .data-screening-main {
      margin-top: -17px !important;
    }

    .main-setting {
      .setting-cell {
        img {
          height: 360px !important;
        }
      }

      .setting-footer {
        bottom: 49px !important;
      }
    }

    .main-content {
      width: calc(100% - 40px) !important;
      padding: 0 20px 0 20px !important;

      .show-data {
        .left {
          width: 50%;
          bottom: 60px !important;
        }
        .center {
          left: 0% !important;
          width: 100% !important;
        }
        .right {
          width: 50%;
          bottom: 60px !important;
        }
      }
    }
  }
}

// 适配小于1750情况下的样式
@media screen and(max-width: 1750px) {
  .dynamic-style();
}

// 大于1920的样式
@media screen and(min-width: 1921px) {
  @ratio: 100vw / 1920px;

  .left,
  .right {
    .cell-title {
      font-size: @ratio * 16px !important;
    }
  }

  .center {
    .cell-title {
      font-size: @ratio * 24px !important;
    }
    .cell-title-en {
      font-size: @ratio * 20px !important;
    }
  }

  .cell-main {
    .row-total {
      font-size: @ratio * 40px !important;
    }
  }

  .row-explain {
    .explain-title {
      font-size: @ratio * 12px !important;
    }
    .explain-num {
      font-size: @ratio * 18px !important;
    }
  }

  .main-setting {
    .setting-footer {
      bottom: @ratio * 49px !important;
    }
  }

  .data-screening-main {
    margin-top: @ratio * -17px !important;
  }
}

.data-content-media() {
  .data-screening {
    width: 100%;
    margin-top: 0;
    transform-origin: center top;

    .data-screening-main {
      width: 100%;
      height: 100%;
      position: relative;
      margin-top: -17px;

      // 线条和
      .main-setting {
        position: relative;
        height: 100%;
        display: flex;
        justify-content: center;
        .setting-cell {
          // position: absolute;
          height: 100%;
          animation-name: settingCell;
          animation-duration: 1.2s;
          animation-fill-mode: forwards;
          width: 100%;
          opacity: 0;
          img {
            width: 100%;
          }
          .cell-bg7 {
            position: absolute;
            left: 0;
            top: 0;
          }
        }
        @keyframes settingCell {
          0% {
            transform: rotateY(90deg);
            opacity: 0;
          }
          100% {
            transform: rotateY(0deg);
            opacity: 1;
          }
        }

        .setting-footer {
          position: absolute;
          height: 6px;
          width: 100%;
          bottom: 29px;
          left: 0;
          .footer-main {
            width: 100%;
            position: relative;

            @keyframes lineRightA {
              0% {
                left: 63%;
              }
              100% {
                left: 83%;
              }
            }
            @keyframes lineLeftA {
              0% {
                left: 31%;
              }
              100% {
                left: 11%;
              }
            }

            .line-left {
              left: 31%;
              animation-name: lineLeftA;
            }
            .line-right {
              left: 63%;
              animation-name: lineRightA;
            }
            .line-item {
              animation-duration: 4s;
              animation-timing-function: linear;
              animation-iteration-count: infinite;
              width: 64px;
              height: 6px;
              position: absolute;
              top: -1px;
            }
          }
        }
      }

      @keyframes dataContent {
        0% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
      }

      .main-content {
        width: calc(100% - 280px);
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        padding: 0 150px 0 130px;
        opacity: 0;
        animation-name: dataContent;
        animation-duration: 1.5s;
        animation-fill-mode: forwards;
        animation-delay: 0.8s;

        .show-data {
          width: 100%;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          position: relative;

          .left {
            left: 0;
            bottom: 26px;
          }
          .center {
            top: 40px;
            left: 33.33333333%;
          }
          .right {
            right: 0;
            bottom: 26px;
          }
        }

        .left,
        .right {
          .cell-title {
            font-size: 16px;
          }
        }
        .center {
          .cell-title {
            font-size: 24px;
          }
          .cell-title-en {
            font-size: 20px;
          }
        }

        .content-cell {
          width: 33.33333333333%;
          position: absolute;

          .cell-title {
            text-align: center;
            font-weight: 700;
            letter-spacing: 3px;
          }

          .right-cell-main,
          .left-cell-main {
            margin-top: 30px;
          }
          .center-cell-main {
            margin-top: 35px;
          }
          .cell-main {
            display: flex;
            display: flex;
            width: 100%;
            justify-content: center;
            position: relative;
            @font-face {
              font-family: SourceHanSansCNBold;
              src: url("../../../../static/fontStyle/SourceHanSansCN-Bold.otf");
            }
            .row-total {
              align-items: flex-end;
              display: flex;
              font-size: 40px;
              // color: rgba(0, 204, 255, 1);
              // text-shadow: 5px 0 4px #069f4c;
              font-weight: 900;
              letter-spacing: 4px;
              font-family: SourceHanSansCNBold;
            }
          }

          .row-explain {
            .explain-title {
              // color: rgba(0, 204, 255, 1);
              font-size: 12px;
              font-weight: 700;
              letter-spacing: 2px;
            }
            .explain-title-1 {
              letter-spacing: 0;
            }
            .explain-num {
              // color: rgba(1, 229, 105, 1);
              font-size: 18px;
            }
          }

          .content-top {
            position: relative;
            .content-top-explain {
              position: absolute;
              left: 70%;
              top: 20px;
            }
          }
        }
      }
    }
  }
}
</style>