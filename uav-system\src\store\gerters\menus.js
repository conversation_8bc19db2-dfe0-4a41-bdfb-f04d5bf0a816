/**
 * 保存菜单信息
 */
import request from "@/utils/http"
import router from "@/router/index"
import language from "@/utils/language/index"
import routerComponents from '@/utils/routerComponents'

const user = {
    state: {
        menuList: [],
        openRouter: ""
    },
    mutations: {
        setMenuList(state, val) {
            state.menuList = val;
        },

        setOpenRouter(state, val) {
            state.openRouter = val;
        }
    },
    actions: {
        setMenuList(content, {
            to = {},
            form,
            next,
            fun_list
        }) {
            request({
                url: `./static/json/menus.json?v=${new Date().getTime()}`
            }).then((res) => {
                let data = res.data.list || [];
                // 排序
                data = data.sort((a, b) => {
                    return a.sort - b.sort;
                })

                let menu = language.layout.menu;
                let admin = fun_list.indexOf("super_administrator") !== -1;
                let addRouters = (parentName, list) => {
                    for (let i = 0; i < list.length; i++) {
                        let item = Object.assign({}, list[i]);
                        item.title = menu[item.name];


                        if (item.filePath) {
                            let name = item.parentName || parentName;
                            let power = false;
                            if (item.fun_id) {
                                power = fun_list.indexOf(item.fun_id) === -1
                            }
                            if (admin || !power) {
                                router.matcher.addRoute(name, {
                                    path: item.path, // 路径
                                    name: item.name, // 名称
                                    meta: item.meta || {
                                        title: item.title
                                    },
                                    component: routerComponents[item.name],
                                })

                            }

                        }

                        // 如果有子集，则继续添加
                        if (item.children && item.children.length > 0) {
                            addRouters(parentName, item.children);
                        }
                    }
                }
                addRouters("", data);

                content.commit("setMenuList", data);

                // 重定向
                router.push({
                    path: to.path,
                    query: to.query
                })

            })
        }
    }
}



export default user