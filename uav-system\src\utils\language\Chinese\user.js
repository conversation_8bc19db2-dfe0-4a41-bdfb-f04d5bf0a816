/**
 * 用户相关
 */
const user = {
    userInfo: {
        title: "个人信息",
        label: {
            nick: "账户",
            account: "手机号/邮箱",
            startTime: "开通时间",
            endTime: "到期时间",
        },
        placeholder: {
            nick: "请输入账号",
            account: "请输入手机号/邮箱",
            startTime: "请选择开通时间",
            endTime: "请选择到期时间"
        },
        validity: "永久有效"
    },

    passSet: {
        title: "密码设置",
        label: {
            password: "旧密码",
            newPass: "新密码",
            affirmPass: "确认密码"
        },
        placeholder: {
            password: "请输入旧密码",
            newPass: "请输入新密码",
            affirmPass: "请再次输入新密码"
        },
        verify: {
            password: [
                "请输入旧密码",
                "长度在 6 到 32个字符"
            ],
            newPass: [
                "请输入新密码",
                "长度在 6 到 32个字符"
            ],
            affirmPass: [
                "请再次输入新密码",
                "长度在 6 到 32个字符",
                "两次输入密码不一致!"
            ]
        },
        confirm: {
            content: "密码修改成功，请重新登录。",
            title: "提示",
            cancelText: "取 消",
            confirmText: "确 定"
        }
    },

    accountSet: {
        title: "账户信息",
        label: {
            nick: "账户",
            account: "手机号/邮箱",
            in_com: '所属公司/单位',
            startTime: "开通时间",
            endTime: "到期时间"
        },
        placeholder: {
            nick: "请输入账户",
            account: "请输入手机号码/邮箱"
        },
        validity: "永久有效",
        verify: {
            nick: ["请输入账户昵称"],
            account: [
                "请输入用户手机号 / 邮箱",
                "请输入正确的手机号码或邮箱"
            ]
        },
        hint: {
            success: "修改成功"
        }
    },

    systemSet: {
        title: "语言设置",
        label: {
            langue: "语言",
            theme: "主题"
        },
        message: {
            cut: "正在切换语言..."
        }
    }

}

export default user;