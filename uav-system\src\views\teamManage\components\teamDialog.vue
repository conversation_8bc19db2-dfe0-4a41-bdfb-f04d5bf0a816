<template>
  <div class="">
    <el-dialog
      :title="type == 'edit' ? language.editTeam : language.addTeam"
      :visible.sync="visible"
      :center="true"
      custom-class="team-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :destroy-on-close="true"
      width="650px"
    >
      <el-form label-width="120px" :model="form" :rules="rules" ref="form">
        <el-form-item
          :label="language.formLable.parent_id"
          v-if="!(type == 'edit' && !form.parent_id)"
        >
          <el-cascader
            v-model="form.parent_id"
            :options="options"
            :props="{
              checkStrictly: true,
            }"
            clearable
            :placeholder="language.placeholder.parent_id"
          ></el-cascader>
        </el-form-item>
        <el-form-item :label="language.formLable.state" v-if="type == 'edit'">
          <el-radio-group v-model="form.state">
            <el-radio :label="1">{{ language.normal }}</el-radio>
            <el-radio :label="4">{{ language.delete }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="language.formLable[item.id]"
          v-for="item in formList"
          :key="item.id"
          :prop="item.id"
        >
          <el-input
            v-model="form[item.id]"
            :placeholder="language.placeholder[item.id]"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">{{
          language.submit
        }}</el-button>
        <el-button @click="close">{{ language.cancel }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
export default {
  data() {
    return {
      type: "add",
      visible: false,
      form: {
        parent_id: "",
        name: "",
        description: "",
        notes: "",
        state: 1,
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入团队名（公司、部门、单位）",
            trigger: "blur",
          },
        ],
      },
      formList: [{ id: "name" }, { id: "description" }, { id: "notes" }],
      options: [],
      requestCode: false,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.team;
    },
    userFunList() {
      return this.$store.state.user.userInfo.fun_list;
    },
    admin() {
      return (
        this.userFunList &&
        this.userFunList.indexOf("super_administrator") !== -1
      );
    },
  },
  created() {
    for (const key in this.rules) {
      this.rules[key][0].message = this.language.placeholder[key];
    }
  },
  methods: {
    getComList() {
      this.requestCode = false;
      let param = {
        os_timestampCode: true,
      };
      requestHttp("allGetTeamList", param)
        .then((res) => {
          let list = res.data && res.data.list ? res.data.list : [];
          for (let index = 0; index < list.length; index++) {
            const element = list[index];
            if (this.admin && element.parent_info) {
              continue;
            }
            let obj = {
              value: element.id,
              label: element.name,
              children:
                element.son_list && element.son_list.length
                  ? this.getChildren(element)
                  : "",
            };
            this.options.push(obj);
          }
        })
        .finally(() => {
          this.requestCode = true;
        });
    },
    getChildren(element) {
      return element.son_list.map((item) => {
        return {
          value: item.id,
          label: item.name,
          children:
            item.son_list && item.son_list.length
              ? this.getChildren(element)
              : "",
        };
      });
    },
    open: function (type, item) {
      this.getComList();
      this.type = type;
      this.visible = true;
      if (type == "edit") {
        for (const key in this.form) {
          this.form[key] = item[key];
        }
        this.form.id = item.id;
        if (item.parent_info) {
          // this.form.parent_id = item.parent_info.id;
          this.getComId(item.parent_info.id);
        }
      }
    },
    getComId(id) {
      if (!this.requestCode) {
        setTimeout(() => {
          this.getComId(id);
        }, 200);
        return false;
      }
      this.form.parent_id = this.computerId(this.options, id);
      this.delSameOptions();
    },
    delSameOptions() {
      let indexs = this.computedSame(this.options, this.form.id);
      this.delOptions(this.options, indexs);
    },
    delOptions(options, indexs) {
      if (indexs.length > 1) {
        let arr = options[indexs[0]].children;
        indexs.shift();
        this.delOptions(arr, indexs);
      } else {
        options.splice(indexs[0], 1);
      }
    },
    computedSame(array, id) {
      for (let index = 0; index < array.length; index++) {
        let item = array[index];
        if (item.value == id) {
          return [index];
        } else {
          if (item.children) {
            let result = this.computedSame(item.children, id);
            if (result) {
              return [index, ...result];
            }
          }
        }
      }
    },
    computerId(array, id) {
      for (let index = 0; index < array.length; index++) {
        let item = array[index];
        if (item.value == id) {
          return [item.value];
        } else {
          if (item.children) {
            let result = this.computerId(item.children, id);
            if (result) {
              return [item.value, ...result];
            }
          }
        }
      }
    },
    // getFirstTeamList: function () {
    //   requestHttp("getFirstTeamList").then((res) => {
    //     this.options = res.data && res.data.list ? res.data.list : [];
    //   });
    // },
    submit: function () {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let param = Object.assign({}, this.form);
          if (param.parent_id) {
            param.parent_id = param.parent_id[param.parent_id.length - 1];
          }
          delete param.parent_id;
          let url = "addTeam";
          let message = this.language.successAdd;
          param.pmd = param.name.toString();
          param.os_timestampCode = true;
          if (this.type == "edit") {
            url = "editTeam";
            param.pmd =
              param.name.toString() +
              param.id.toString() +
              param.state.toString();
            message = this.language.successEdit;
          } else {
            delete param.state;
          }
          requestHttp(url, param).then((res) => {
            this.$emit("refresh");
            this.$message.success(message);
            this.close();
          });
        }
      });
    },
    close: function () {
      this.visible = false;
      setTimeout(() => {
        this.type = "add";
        this.form = {
          parent_id: "",
          name: "",
          description: "",
          notes: "",
          state: 1,
        };
        this.options = [];
        this.requestCode = false;
      }, 300);
    },
  },
};
</script>
<style lang="less" scoped>
.team-dialog {
  .el-form {
    .el-form-item {
      .el-select,
      .el-cascader,
      .el-input {
        width: 100%;
      }
    }
  }
}
</style>
<style lang="less">
.team-dialog {
  .el-dialog__header {
    border-bottom: 1px solid #0c31ac;
    .el-dialog__title {
      font-size: 24px !important;
      font-weight: 550;
    }
  }
  .el-dialog__body {
    padding-bottom: 0 !important;
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .team-dialog {
    .el-dialog__header {
      .el-dialog__title {
        font-size: @zoomIndex * 24px !important;
      }
    }
    input::-webkit-input-placeholder {
      color: rgb(167, 167, 167) !important;
    }
    input::-moz-input-placeholder {
      color: rgb(167, 167, 167) !important;
    }
    input::-ms-input-placeholder {
      color: rgb(167, 167, 167) !important;
    }
  }
}
</style>