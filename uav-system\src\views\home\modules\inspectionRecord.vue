<!-- 巡检记录 -->
<template>
  <bg-layout :title="language.title" class="inspection-record">
    <template v-slot:content>
      <!-- 日期 -->
      <div class="date">
        <div class="date-icon el-icon-date"></div>
        <div class="date-start">
          <el-date-picker
            popper-class="date-poper"
            style="width: 120px"
            v-model="form.start_tms"
            size="mini"
            type="date"
            :placeholder="language.time.start"
            prefix-icon=""
            ref="starttms"
            @change="starttmsChange"
            format="yyyy-MM-dd"
            value-format="timestamp"
          >
          </el-date-picker>
        </div>
        <div class="date-end">
          <el-date-picker
            popper-class="date-poper"
            style="width: 120px"
            v-model="form.end_tms"
            size="mini"
            type="date"
            :placeholder="language.time.end"
            prefix-icon=""
            ref="endtms"
            format="yyyy-MM-dd"
            value-format="timestamp"
            @change="endtmsChange"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </div>
      </div>

      <!-- 数据预览 -->
      <div class="data-preview">
        <div class="view-row">
          <div
            class="view-icon el-icon-help"
            style="color: rgb(69, 99, 250)"
          ></div>
          <div class="view-content">
            <div class="title">{{ language.type.total }}</div>
            <div class="total" style="color: rgb(69, 99, 250)">
              {{ range_times }}
            </div>
          </div>
        </div>
        <div class="view-row">
          <div class="view-icon el-icon-receiving"></div>
          <div class="view-content">
            <div class="title" :style="titleStyle">{{ language.type.day }}</div>
            <div class="total">{{ today_times }}</div>
          </div>
        </div>
      </div>

      <!-- 表格数据 -->
      <div class="table-data">
        <scroll-list
          :data="tableData"
          :pageSize="8"
          class="table-tbody scrollbar-style"
          :params="form"
          pmd="page"
          ref="scrollList"
        >
          <template v-slot:content="scope">
            <div
              class="table-tbody-tr"
              :style="{ animationDelay: tableTrStyle(scope, index) }"
              v-for="(item, index) in scope.data"
              :key="index"
            >
              <div style="width: 28px" class="tbody-cell">
                <img src="@/assets/icon/uav-home.png" alt="" />
              </div>
              <div
                style="width: 60%; cursor: pointer"
                class="tbody-cell"
                @click="taskPlayback(item)"
                :title="language.prpo.flightLog"
              >
                <div class="cell-top">{{ item.mission_name }}</div>
                <div class="cell-bot mt5">
                  <span>{{ item.uav_id }}</span>
                  <span class="ml5">{{ item.start_time }}</span>
                </div>
              </div>
              <div
                style="width: 62px; cursor: pointer"
                class="tbody-cell achievement-echarts"
                @click="openAchievement(item)"
              >
                <div
                  class="canvas-click"
                  :title="language.prpo.achievement"
                ></div>
                <div
                  class="canvas-echarts"
                  :id="'canvas-' + index"
                  v-if="echartsInit(index, item)"
                ></div>
              </div>
            </div>
          </template>
        </scroll-list>
      </div>
    </template>
  </bg-layout>
</template>

<script>
import bgLayout from "../components/bgLayout.vue";
import scrollList from "@/components/scrollList/index.vue";
import { setLocalStorage } from "@/utils/storage.js";
import requestHttp from "@/utils/api";
import { getBeforeDate } from "@/utils/date";

export default {
  components: {
    bgLayout,
    scrollList,
  },
  data() {
    return {
      form: {
        search: "",
        sn_id: "",
        type: 0,
        start_tms: "",
        end_tms: "",
      },
      planTypeList: {},
      range_times: 0,
      today_times: 0,
      pickerOptions: {
        disabledDate: (time) => {
          return this.form.start_tms > time.getTime();
        },
      },
      tableData: [],
    };
  },
  computed: {
    ws() {
      return this.$store.state.websocket.ws;
    },
    language() {
      return this.$languagePackage.home.inspectionRecord;
    },
    titleStyle() {
      let type = this.$language;
      return {
        "letter-spacing": type == "chinese" ? "3px" : 0,
      };
    },
  },
  watch: {
    ws: {
      handler: function () {
        this.refresh();
      },
      deep: true,
    },
  },
  created() {
    this.getPlanTypeList();

    this.$store.commit("setMultiMessage", {
      key: "inspectionRecord",
      message: this.getPushData,
    });
  },
  mounted() {
    this.refresh();
  },
  methods: {
    refresh: function (params) {
      if (!params) {
        let start = getBeforeDate(30).start;
        let startTime = new Date(start).getTime();

        let end = new Date().getTime();
        params = {
          start_time: startTime,
          end_time: end,
        };
        this.form.start_tms = startTime;
        this.form.end_tms = end;
      }

      this.ws && this.ws.manualSend && this.ws.manualSend(params, 103);
    },
    getPushData: function (msg_id, data) {
      if (msg_id == 103) {
        this.range_times = data.range_times;
        this.today_times = data.today_times;
        this.tableData = data.list;
      }
    },
    taskPlayback: function (row) {
      const route = this.$router.resolve({
        path: `/routeReplay`,
        query: {
          sn_id: row.sn_id,
          sort_id: row.sort_id,
        },
      });
      row.mission_type_label = this.planTypeList[row.mission_type];
      window.open(route.href, "_blank");
      setLocalStorage(row.sort_id, row);
    },
    openAchievement: function (row) {
      this.$router.push({
        name: "totalResultList",
        query: { sort_id: row.sort_id, count: row.count },
      });
    },
    tableTrStyle: function (scope, index) {
      return {
        animationDelay:
          this.isInit || scope.current != 1 ? "0s" : 1 + 0.2 * index + "s",
      };
    },

    getPlanTypeList: function () {
      requestHttp("missionType").then((res) => {
        res.data.forEach((item) => {
          this.$set(this.planTypeList, item.value, item.name_cn);
        });
      });
    },
    /**
     * echarts初始化
     * @param {id} 挂载dom ID
     * @param {config} 相关配置
     */
    echartsInit: function (index, config) {
      let id = "canvas-" + index;
      let elem = document.getElementById(id);
      if (elem) {
        return true;
      }

      this.$nextTick(() => {
        let { count = 0, total = 1, fontColor } = config || {};
        let colorStops = [
          { offset: 0, color: "#1f4ef0" },
          { offset: 0.5, color: "#167bc8" },
          { offset: 1, color: "#109ea9" },
        ];

        let option = {
          title: {
            show: true,
            text: count,
            x: "center",
            y: "center", // 通过x,y将标题(进度)定位在圆环中心
            textStyle: {
              fontSize: "14",
              color: fontColor || "#ffffff",
              fontWeight: "700",
              fontFamily: "DINPro, DINPro-Regular",
            },
          },
          grid: { left: "0px", right: "0px", top: "0px", bottom: "0px" },
          tooltip: {
            trigger: "item",
            formatter: "{d}%",
            show: false,
          },
          legend: {
            orient: "vertical",
            x: "left",
            show: false,
          },
          series: {
            emphasis: false,
            name: "",
            type: "pie",
            radius: ["80%", "100%"],
            avoidLabelOverlap: true,
            label: {
              show: false,
              position: "center",
              // emphasis: {
              //   show: false,
              // },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                // 当前值
                value: count,
                name: "",
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 1,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: colorStops,
                    global: false, // 缺省为 false
                  },
                },
              },
              // {
              //   // 剩余
              //   value: 0,
              //   name: "",
              //   itemStyle: {
              //     color: "rgb(2,5,68)",
              //   },
              // },
            ],
          },
        };

        var myChart = this.$echarts.init(document.getElementById(id));
        myChart.setOption(option);
      });
      return true;
    },
    starttmsChange: function (time) {
      this.form.end_tms = "";
      if (time) {
        this.$refs.endtms.focus();
      } else {
        this.form.start_tms = "";
      }

      this.timeChnage();
    },
    endtmsChange: function (time) {
      if (time) {
        let ss = (23 * 60 * 60 + 59 * 60 + 59) * 1000;
        this.form.end_tms = time + ss;
      }

      this.timeChnage();
    },
    timeChnage: function () {
      let end = this.form.end_tms,
        startTime = this.form.start_tms;

      if (!end && !startTime) {
        let start = getBeforeDate(30).start;
        startTime = new Date(start).getTime();
        end = new Date().getTime();
      }

      if ((startTime && end) || (!end && !startTime)) {
        this.refresh({
          start_time: startTime,
          end_time: end,
        });
      }
    },
  },
};
</script>

<style lang="less">
.inspection-record {
  .el-input__inner {
    // background-color: rgba(15, 12, 68, 1);
    border: none;
    // color: #fff;
    font-weight: 700;
    font-size: 14px;
  }
  .el-input__prefix {
    display: none;
  }
  .el-input--suffix .el-input__inner {
    padding: 0 10px !important;
    text-align: center;
  }
  input::-webkit-input-placeholder {
    // color: rgb(106, 112, 169) !important;
  }
  input::-moz-input-placeholder {
    // color: rgb(106, 112, 169) !important;
  }
  input::-ms-input-placeholder {
    // color: rgb(106, 112, 169) !important;
  }
}
</style>

<style lang="less" scoped>
.inspection-record {
  .date {
    display: flex;
    // color: #fff;
    align-items: center;
    justify-content: center;
    background-color: rgba(18, 15, 79, 0.7);
    padding: 8px 0;
    margin: 0 10px;
    .date-start {
      margin-left: 30px;
    }
    .date-end {
      margin-left: 30px;
    }
  }

  .data-preview {
    display: flex;
    // color: #fff;
    height: 60px;
    // background-color: rgba(13, 11, 51, 0.7);
    margin: 4px 10px 0 10px;
    .view-row {
      height: 100%;
      flex-grow: 1;
      // justify-content: center;
      padding-left: 8px;
      display: flex;
      align-items: center;
      .view-icon {
        font-size: 24px;
        // color: rgb(2, 224, 110);
      }
      .view-content {
        text-align: center;
        margin-left: 10px;
        font-weight: 700;
        letter-spacing: 2px;
        .title {
          font-size: 12px;
          // color: rgb(106, 112, 169);
        }
        .total {
          font-size: 26px;
          // color: rgb(2, 224, 110);
          font-weight: 700;
        }
      }
    }
  }

  .table-data {
    height: calc(100% - 162px);
    position: relative;
    margin: 4px 10px 10px 10px;
    .table-tbody {
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      .table-tbody-tr {
        animation-name: tbodyTr;
        animation-duration: 0.4s;
        animation-fill-mode: forwards; // 保留动画最后的状态
        opacity: 0;
        width: 100%;
        display: flex;
        margin-bottom: 5px;
      }
      @keyframes tbodyTr {
        0% {
          transform: rotateX(90deg);
        }
        50% {
          opacity: 1;
        }
        100% {
          transform: rotateX(0deg);
          opacity: 1;
        }
      }
    }
    .achievement-echarts {
      position: relative;
      .canvas-click {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        cursor: pointer;
        z-index: 10;
      }
    }
    .tbody-cell {
      padding: 8px;
      font-size: 12px;
      // color: #fff;
      // background-color: rgba(25, 56, 171, 0.4);
      align-items: center;
      height: 46px;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      .cell-top {
        font-size: 14px;
        width: 100%;
        letter-spacing: 2px;
      }
      .cell-bot {
        width: 100%;
        font-size: 12px;
      }
      .canvas-echarts {
        width: 46px;
        height: 46px;
      }
    }
  }
}
</style>