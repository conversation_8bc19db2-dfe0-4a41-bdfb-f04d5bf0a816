<template>
  <div class="shout-file" :style="{ width, left }">
    <div class="shout-file-box" v-show="fileShow">
      <div class="header-box">
        <div class="title">{{ language.broadcast }}</div>
        <div class="title-icon">
          <el-image
            :src="titleIcon"
            style="height: 24px; margin-top: 6px; cursor: pointer"
            fit="contain"
            @click="open(false)"
          ></el-image>
        </div>
      </div>
      <div class="content-box">
        <div class="operate-item" v-for="item in operateList" :key="item.id">
          <div class="operate-item-icon" @click="item.fun(item)">
            <el-image
              :src="item.icon"
              fit="contain"
              :style="{ width: item.width }"
            ></el-image>
          </div>
          <div class="operate-item-title" @click="item.fun(item)">
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <div class="shout-file-box" v-show="textInputShow">
      <div class="header-box">
        <div class="title">{{ language.textToAudio }}</div>
        <div class="title-icon">
          <el-image
            :src="titleIcon"
            style="height: 24px; margin-top: 6px; cursor: pointer"
            fit="contain"
            @click="closeInput"
          ></el-image>
        </div>
      </div>
      <div class="content-box">
        <el-input
          v-model="text"
          :placeholder="language.textPlaceholder"
          type="textarea"
          show-word-limit
          maxlength="300"
          ref="elInputBox"
        ></el-input>
      </div>
      <div class="foot-btn">
        <el-button @click="clearText">{{ language.clear }}</el-button>
        <el-button @click="saveText">{{ language.save }}</el-button>
      </div>
    </div>
    <div class="shout-file-box" v-show="textListShow">
      <div class="header-box">
        <div class="header-box-item" @click="addText">
          <el-image
            :src="addIcon"
            style="height: 24px; margin-right: 6px"
            fit="contain"
          ></el-image>
          <div class="icon-title">{{ language.add }}</div>
        </div>
      </div>
      <div class="text-box">
        <div
          class="text-list-item"
          v-for="(item, index) in textList"
          :key="item.id"
        >
          <el-image
            v-show="!item.isPlay"
            :src="playIcon"
            style="height: 26px; margin-right: 6px"
            fit="contain"
            @click="sendStart(item)"
          ></el-image>
          <div
            class="el-image-pause"
            v-show="item.isPlay"
            @click="pause()"
          ></div>
          <div class="text-list-item-text" :title="item.text">
            {{ item.text }}
          </div>
          <el-image
            :src="delIcon"
            style="height: 26px; margin-left: 6px"
            fit="contain"
            @click="delText(index)"
          ></el-image>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
export default {
  props: {
    sendWs: {
      type: [Function, String],
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      width: 0,
      left: "calc(100% + 18px)",
      titleIcon: require("@/assets/img/left.png"),
      addIcon: require("@/assets/img/add.png"),
      delIcon: require("@/assets/img/del.png"),
      playIcon: require("@/assets/img/play.png"),
      operateList: [
        // {
        //   id: 1,
        //   title: "录制",
        //   icon: require("@/assets/img/transcribe.png"),
        //   width: "31px",
        // },
        {
          id: 2,
          title: "文本转语音",
          icon: require("@/assets/img/textToAudio.png"),
          width: "39.48px",
          fun: this.openTextList,
        },
        // {
        //   id: 3,
        //   title: "录音文件",
        //   icon: require("@/assets/img/recording.png"),
        //   width: "41.32px",
        // },
      ],
      text: "",
      textList: [],
      fileShow: false,
      textListShow: false,
      textInputShow: false,
      operateItem: null,
      startResponse: false,
      textResponse: false,
      endResponse: false,
      playback: "",
      currentItem: null,
      page: 0,
      total_page: 0,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.navigation.shout;
    },
  },
  watch: {
    playback(newValue, oldValue) {
      if (oldValue == 0x20 && newValue == 0x00) {
        this.pause(true);
      }
    },
  },
  created() {
    for (let index = 0; index < this.operateList.length; index++) {
      const item = this.operateList[index];
      item.title = this.language.operateList[item.id];
    }
    let query = this.$route.query || {};
    this.sn_id = query.sn_id;
    this.textList = [];
  },
  methods: {
    getDataObj: function (msg_id, data) {
      if (msg_id == 701 && data.model_connect) {
        this.playback = data.playback;
      }
      if (msg_id == 705) {
        if (this.endResponse) {
          this.sendStart(this.currentIndex);
          this.endResponse = false;
        }
        if (this.startResponse) {
          this.sendText();
          this.startResponse = false;
        }
        if (this.textResponse) {
          this.play();
          this.textResponse = false;
        }
      }
    },
    open: function (code) {
      this.width = this.width == 0 ? "100%" : 0;
      this.fileShow = code ? true : false;
      if (this.textListShow) {
        this.textListShow = !this.textListShow;
      }
      if (this.textInputShow) {
        this.textInputShow = !this.textInputShow;
      }
      if (!this.fileShow) {
        this.$emit("close");
      }
    },
    //点击设置开始传输文本
    sendStart: function (item) {
      if (this.operateItem) {
        this.currentItem = item;
        this.pause();
        return false;
      }
      this.currentItem = null;
      this.operateItem = item;
      this.sendWs({ action: 33 });
      this.startResponse = true;
    },
    //设置传输文本数据
    sendText: function () {
      this.sendWs({ action: 35, value: this.operateItem.text });
      this.textResponse = true;
    },
    //设置结束传输文本数据，并播放
    play: function () {
      if (this.operateItem) {
        this.operateItem.isPlay = true;
      }
      this.sendWs({ action: 37 });
    },
    //设置播放暂停
    pause: function (code) {
      if (this.operateItem) {
        this.operateItem.isPlay = false;
      }

      this.sendWs({ action: 40 });
      this.operateItem = null;
      this.endResponse = code ? true : false;
    },
    //删除文本
    delText: function (index) {
      this.$confirm("是否删除该文本信息", "删除提示", {
        type: "warning",
      }).then((res) => {
        if (this.textList[index].isPlay) {
          this.pause(true);
        }
        let data = {
          id: this.textList[index].id,
          pmd: "id",
          jointPmd: true,
          os_timestampCode: true,
        };
        requestHttp("megTextDel", data).then((res) => {
          this.textList = [];
          this.page = 0;
          this.getTextList();
        });
      });
    },
    openTextList: function () {
      this.fileShow = false;
      this.textListShow = true;
      this.getTextList();
    },
    closeInput: function () {
      this.textListShow = true;
      this.textInputShow = false;
    },
    addText: function () {
      this.textListShow = false;
      this.textInputShow = true;
      this.text = "";
    },
    getTextList: function () {
      let data = {
        sn_id: this.sn_id,
        page: this.page,
        size: 100,
        pmd: "page,sn_id",
        jointPmd: true,
        os_timestampCode: true,
      };
      requestHttp("megTextList", data).then((res) => {
        if (res.data) {
          console.log(res);
          let list = res.data.list || [];
          list.forEach((item) => {
            item.isPlay = false;
            if (this.operateItem && item.id == this.operateItem.id) {
              item.isPlay = true;
            }
            this.textList.push(item);
          });
          if (this.page < res.data.total_page - 1) {
            this.page++;
            this.getTextList();
          }
        }
      });
    },
    clearText: function () {
      this.text = "";
      this.$refs.elInputBox && this.$refs.elInputBox.focus();
    },
    saveText: function () {
      let data = {
        text: this.text,
        sn_id: this.sn_id,
        pmd: "sn_id,text",
        jointPmd: true,
        os_timestampCode: true,
      };
      requestHttp("megTextAdd", data).then((res) => {
        this.closeInput();
        this.textList = [];
        this.page = 0;
        this.getTextList();
      });
    },
    changeLeft: function (width) {
      if (width) {
        this.left = "calc(200% + 36px)";
      } else {
        this.left = "calc(100% + 18px)";
      }
    },
    clearPlay: function () {
      this.operateItem = null;
      this.textList.forEach((item) => {
        if (item.isPlay) {
          item.isPlay = false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.shout-file {
  position: absolute;
  top: 0;
  height: 100%;
  background-color: rgba(71, 71, 71, 0.5);
  overflow: hidden;
  transition: all 0.1s linear;
  border-radius: 8px;
  .shout-file-box {
    width: 100%;
    height: 100%;
    background-color: transparent;
    border-radius: 8px;
    padding: 28px 23px;
    box-sizing: border-box;
    color: #fff;
    font-size: 24px;
    display: flex;
    flex-direction: column;
    .header-box {
      width: 100%;
      position: relative;
      flex-shrink: 0;
      .title {
        width: 100%;
        text-align: center;
      }
      .title-icon {
        position: absolute;
        left: 0;
        top: 0;
      }
      .header-box-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        cursor: pointer;
        width: max-content;
      }
    }
    .content-box {
      flex: 1;
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;
      .operate-item {
        width: 50%;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        padding-top: 16px;

        .operate-item-icon {
          background-color: #24272b;
          width: 78px;
          height: 78px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
        .operate-item-title {
          font-size: 18px;
          margin-top: 10px;
          cursor: pointer;
        }
      }
    }
    .text-box {
      flex: 1;
      overflow-y: auto;
      .text-list-item {
        display: flex;
        align-items: center;
        background-color: #24272b;
        border-radius: 5px;
        padding: 17px 10px;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }

        .text-list-item-text {
          font-size: 18px;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .el-image {
          cursor: pointer;
          flex-shrink: 0;
        }
        .el-image-pause {
          cursor: pointer;
          flex-shrink: 0;
          width: 22px;
          height: 22px;
          margin-right: 6px;
          border-radius: 6px;
          background-color: #fff;
        }
      }
    }
    .foot-btn {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .el-button {
        width: 46%;
        background-color: #24272b;
        font-size: 24px;
        color: #fff;
        border: none;
        border-radius: 8px;
      }
    }
  }
}
</style>

<style lang="less">
.shout-file {
  .shout-file-box {
    .content-box {
      .el-textarea {
        margin: 5% 0;
        height: 90% !important;
        .el-textarea__inner {
          height: 100% !important;
          min-height: 100% !important;
          resize: none !important;
          background-color: #24272b;
          font-size: 19px;
          border: none;
          color: #fff;
          &::-webkit-scrollbar {
            width: 3px;
          }

          &::-webkit-scrollbar-track {
            background-color: #ccc;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #777777;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }
        }
        .el-input__count {
          font-size: 19px;
          color: #686868;
          background-color: transparent;
        }
      }
    }
    .text-box {
      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #ccc;
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #777777;
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }
    }
  }
}
</style>