const videoList = {
  upload: 'upload',
  play: 'play',
  uploading: 'uploading',
  column: [{
      label: "Sn_id",
      prop: "sn_id"
    },
    {
      label: "Video Cover",
      prop: "thumb_url",
      width: 120
    },
    {
      label: "Video Type",
      prop: "vr_type"
    },
    {
      label: "Start Time",
      prop: "start_time"
    },
    {
      label: "End Time",
      prop: "end_time"
    },
    {
      label: "File Name",
      prop: "file_name"
    },
    {
      label: "File Size(kb)",
      prop: "size_kb"
    },
    {
      label: "Creation Time",
      prop: "create_time"
    },
    {
      label: "Modified Time",
      prop: "modified_time"
    },
    {
      label: "Operation",
      prop: "operation"
    },
  ],
  vr_type: {
    1: 'Video from outside camera one',
    2: 'Video from outside camera two'
  }
}
export default videoList
