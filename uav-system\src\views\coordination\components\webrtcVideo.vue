<template>
  <div
    class="webrtcVideo"
    :class="isScreen ? 'fullClass' : ''"
    v-loading.lock="videoLoading"
    element-loading-spinner="el-icon-loading"
    :element-loading-text="videoLanguage.videoLoading"
    element-loading-background="transparent"
  >
    <video
      class="videoDiv"
      :id="videoItem.sn_id"
      :autoplay="autoplay"
      ref="webVideo"
    ></video>
    <div class="item-title">{{ videoItem.name }}</div>
    <div class="item-button">
      <el-button
        :class="{ active: videoItem.videoType == 3 }"
        @click="changeVideo(3)"
        v-if="videoItem.stream_uav_list"
        >{{ videoLanguage.uavVideo }}</el-button
      >
      <el-button
        :class="{ active: videoItem.videoType == 2 }"
        @click="changeVideo(2)"
        v-if="videoItem.stream_in_list"
        >{{ videoLanguage.inVideo }}</el-button
      >
      <el-button
        :class="{ active: videoItem.videoType == 1 }"
        @click="changeVideo(1)"
        v-if="videoItem.stream_out_list"
        >{{ videoLanguage.outVideo }}</el-button
      >
    </div>
    <div class="videoTip" v-if="errorCode">
      <i class="el-icon-error"></i>{{ videoLanguage.errorVideo
      }}<el-button type="text" @click="reconnect">{{
        videoLanguage.reconnect
      }}</el-button>
    </div>
    <div class="item-footer" v-show="hoverShow">
      <i
        :class="playCode ? 'el-icon-video-pause' : 'el-icon-video-play'"
        @click.stop="playVideo"
      ></i>
      <i class="el-icon-full-screen iconDiv" @click.stop="fullScreen"></i>
    </div>
  </div>
</template>
<script>
import ZLMRTCClient from "../../../assets/js/ZLMRTCClient";
export default {
  name: "webrtcVideo",
  props: {
    videoItem: {
      type: Object,
      default() {
        return {};
      },
    },
    videoLanguage: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      videoLoading: true,
      autoplay: true,
      errorCode: false,
      player: "",
      isScreen: false,
      hoverShow: true,
      playCode: false,
      videoDiv: "",
      tipsCode: false,
    };
  },
  mounted() {
    this.initVideo();
  },
  methods: {
    //切换视频
    changeVideo(num) {
      this.videoItem.videoType = num;
      if (this.player) {
        if (this.videoLoading) {
          this.autoplay = false;
          this.videoLoading = false;
          this.errorCode = true;
          this.playCode = false;
          // self.player=''
        }
        document.getElementById(this.videoItem.sn_id).srcObject = null;
        document.getElementById(this.videoItem.sn_id).load();
        this.tipsCode = false;
        this.player = "";
        this.autoplay = true;
        this.videoLoading = true;
        this.errorCode = false;
        this.initVideo();
      }
    },
    //播放视频
    initVideo() {
      let videoUrl = "";
      switch (this.videoItem.videoType) {
        case 1:
          videoUrl = this.videoItem.stream_out_list[0];
          // videoUrl =
          //   "https://app.walkera.cn/ms/index/api/webrtc?app=/wk/a/6563f34a72f1fa58/&stream=o1&type=play";
          //   // "http://***********/ms/index/api/webrtc?app=/wk/a/6563f34a72f1fa58/&stream=o1&type=play"
          break;
        case 2:
          videoUrl = this.videoItem.stream_in_list[0];
          break;
        case 3:
          videoUrl = this.videoItem.stream_uav_list[0];
          // videoUrl =
          //   "https://app.walkera.cn/ms/index/api/webrtc?app=/wk/a/6563f34a72f1fa58/&stream=u1&type=play";
          // "http://***********/ms/index/api/webrtc?app=/wk/a/6563f34a72f1fa58/&stream=u1&type=play"
          break;
        default:
          break;
      }
      // videoUrl="https://app.walkera.cn/ms/index/api/webrtc?app=/wk/a/6563f34a72f1fa58/&stream=o1&type=play"
      // videoUrl="https://app.walkera.cn/ms/index/api/webrtc?app=/wk/a/6563f34a72f1fa58/&stream=u1&type=play"
      this.player = new ZLMRTCClient.Endpoint({
        element: document.getElementById(this.videoItem.sn_id), // video 标签
        debug: false, // 是否打印日志
        // zlmsdpUrl:
        //   "https://ab.walkera.cn/ms/index/api/webrtc?app=live&stream=abc&type=play",
        zlmsdpUrl: videoUrl,
        simulcast: false,
        useCamera: true,
        audioEnable: true,
        videoEnable: true,
        recvOnly: true,
        resolution: { w: 1920, h: 1080 },
        usedatachannel: true,
      });
      console.log(this.player);
      this.videoDiv = document.getElementById(this.videoItem.sn_id);
      this.player.on(
        ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,
        function (e) {
          // ICE 协商出错
          console.log("ICE 协商出错");
        }
      );
      let self = this;
      this.player.on(
        ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE,
        function (state) {
          // RTC 状态变化 ,详情参考 https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/connectionState
          console.log("当前状态==>", state);
          if (state == "connected") {
            self.videoLoading = false;
            self.errorCode = false;
            self.playCode = true;
            self.tipsCode = true;
          }
          if (state == "disconnected") {
            self.videoLoading = true;
            self.playCode = false;
          }
          if (state == "failed") {
            // self.player.close();
            self.autoplay = false;
            self.videoLoading = false;
            self.errorCode = true;
            self.playCode = false;
            // self.player=''
            document.getElementById(self.videoItem.sn_id).srcObject = null;
            document.getElementById(self.videoItem.sn_id).load();
          }
        }
      );

      this.player.on(
        ZLMRTCClient.Events.WEBRTC_ON_REMOTE_STREAMS,
        function (s) {
          //获取到了远端流，可以播放
          // console.log(s)
          // self.videoDiv.srcObject = s;
          self.videoDiv.muted = true;
          console.log("获取到远程流，可以播放", s);
          // console.log('播放成功', e.streams)
        }
      );

      this.player.on(
        ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,
        function (e) {
          // offer anwser 交换失败
          // self.player.close();
          if (!self.tipsCode) {
            self.autoplay = false;
            self.videoLoading = false;
            self.errorCode = true;
            self.playCode = false;
            console.log("offer anwser 交换失败", e);
          }

          // stop();
        }
      );
      this.player.on(ZLMRTCClient.Events.WEBRTC_ON_LOCAL_STREAM, function (s) {
        // 获取到了本地流
        // document.getElementById(self.videoItem.sn_id).srcObject = s;
        self.videoDiv.muted = true;
        // console.log("offer anwser 交换失败", s);
      });

      this.player.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED, function (s) {
        // 获取本地流失败
        console.log("获取本地流失败");
        this.$message.error("获取本地流失败");
      });

      this.player.on(
        ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_OPEN,
        function (event) {
          console.log("rtc datachannel 打开 :", event);
        }
      );

      this.player.on(
        ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_MSG,
        function (event) {
          console.log("rtc datachannel 消息 :", event.data);
          // document.getElementById('msgrecv').value = event.data
        }
      );
      this.player.on(
        ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_ERR,
        function (event) {
          console.log("rtc datachannel 错误 :", event);
          this.$message.error("rtc datachannel 错误 :" + event);
        }
      );
      this.player.on(
        ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_CLOSE,
        function (event) {
          console.log("rtc datachannel 关闭 :", event);
        }
      );
    },
    reconnect() {
      this.videoLoading = true;
      this.autoplay = true;
      this.controls = true;
      this.errorCode = false;
      this.player = "";
      this.initVideo();
    },
    //全屏按钮
    fullScreen() {
      this.isScreen = !this.isScreen;
    },
    //点击播放暂停
    playVideo() {
      if (this.errorCode || !this.videoLoading) {
        if (this.playCode) {
          this.videoDiv.pause();
        } else {
          setTimeout(() => {
            this.videoDiv.play();
          }, 10);
        }
        this.playCode = !this.playCode;
      }
    },
  },
  beforeDestroy() {
    if (this.player) {
      this.player.close();
    }
  },
};
</script>
<style lang="less" scoped>
.webrtcVideo {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  &:hover .item-footer {
    bottom: 0;
  }
  &.fullClass {
    position: fixed !important;
    top: 0;
    left: 0;
    z-index: 2200;
    height: 100vh;
  }
  .videoDiv {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    object-fit: fill;
  }
  .item-title {
    position: absolute;
    top: 0;
    left: 0;
    width: 96%;
    height: auto;
    padding: 10px 2%;
    font-size: 16px;
    letter-spacing: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
  .item-button {
    position: absolute;
    top: 40px;
    left: 1%;
    height: auto;
    // z-index: 2200;
    .el-button {
      display: block;
      margin: 0;
      border-radius: 8px;
      padding: 12px 5px;
      min-width: 90px;
      width: auto;
      text-align: left;
      margin: 5px 0;
      opacity: 0.2;

      //

      &:hover {
        opacity: 1;
        // z-index: 2200;
      }
    }
  }
  .videoTip {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-30%, -50%);
    width: 60%;
    white-space: normal;
    i {
      margin-right: 2%;
    }
    .el-button {
      padding: 0;
      font-size: 16px;
      margin-left: 2px;
      text-decoration: underline;
    }
  }
  .item-footer {
    height: 30px;
    transition: 0.5s;
    position: absolute;
    bottom: -30px;
    left: 0;
    width: calc(100% - 20px);
    padding: 0 10px;
    font-size: 20px;
    letter-spacing: 2px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    z-index: 2100;
    i {
      cursor: pointer;
      padding-top: 4px;
    }
    .iconDiv {
      float: right;
    }
  }
}
</style>
<style lang="less">
.webrtcVideo {
  .el-loading-mask {
    border-radius: 8px !important;
    width: 50%;
    height: 50%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1800;
  }
}
</style>