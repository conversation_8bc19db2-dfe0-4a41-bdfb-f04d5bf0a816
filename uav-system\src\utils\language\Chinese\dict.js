const dict = {
    funState: {
        10: "正常",
        20: "已删除"
    },
    alarmLevel: {
        30: "高",
        20: "中",
        10: "低"
    },
    processState: {
        10: "待处理",
        20: "已处理"
    },
    checkingType: {
        10: "按出勤次数",
        20: "按巡检里程",
        30: "按成果数"
    },
    achievementType: {
        10: "图片",
        20: "视频",
        15: "全景图"
    },
    defoggingType: {
        0: "关 闭",
        1: "普 通",
        2: "自 动"
    },
    gridType: {
        1: "网格线",
        2: "中心点",
        3: "网格线和中心点"
    },

    // 飞行模式
    flightModeList: {
        // 2: "姿态模式",
        2: "姿态模式",
        3: "自动巡航模式",
        4: "跟随模式",
        5: "GPS模式",
        6: "返航模式",
        9: "降落飞行模式",
        13: "运动模式",
    },

    // rtk状态
    rtkStatus: {
        0: "未连接",
        1: "未定位",
        2: "单点定位",
        3: '浮动解',
        4: "固定解"
    },

    // 拍照模式
    pictureMode: {
        1: "单拍",
        2: "连拍",
        3: "定时拍摄",
        4: "延时拍摄"
    },

    // 语言
    languageList: {
        chinese: "中文",
        english: "英文"
    },

    themeList: {
        default: "默认",
        concise: "简洁"
    },

    careraPcolor: {
        0: "白热",
        1: "熔岩",
        2: "铁红",
        3: "热红",
        4: "琥珀1",
        5: "琥珀2",
        6: "蓝红黄",
        7: "黑红",
        8: "红1",
        9: "红2",
    },

    whiteBalance: {
        0: "自动",
        1: "烛光",
        2: "钨丝灯",
        3: "荧光灯",
        4: "日光",
        5: "多云",
        6: "阴天",
        7: "天蓝",
        8: "烟雾",
    }

}

export default dict;