<template>
  <div class="shout-lamp-box">
    <div class="shout-lamp-btn" v-if="lampShow">
      <div class="shout-lamp-btn-item" @click="openBox('lamp')">
        <el-image :src="lampCode ? lampActive : lamp" fit="contain"></el-image>
      </div>
      <div class="shout-lamp-content">
        <lamp-box v-show="lampBox" :sendWs="sendWs" ref="lampBox"></lamp-box>
      </div>
    </div>
    <div class="shout-lamp-btn last" v-if="megaphoneShow">
      <div class="shout-lamp-btn-item" @click="openBox('megaphone')">
        <el-image
          :src="megaphoneCode ? megaphoneActive : megaphone"
          fit="contain"
        ></el-image>
      </div>
      <div class="shout-lamp-content">
        <megaphone-box
          v-show="megaphoneBox"
          :sendWs="sendWs"
          ref="megaphoneBox"
        ></megaphone-box>
      </div>
    </div>
  </div>
</template>
<script>
import { ShoutSend } from "./shoutSend";
export default {
  props: {
    websocket: [Function, Object],
  },
  components: {
    LampBox: () => import("../components/shoutLamp/lampBox.vue"),
    MegaphoneBox: () => import("../components/shoutLamp/megaphoneBox.vue"),
  },
  data() {
    return {
      lamp: require("@/assets/img/lamp.png"),
      megaphone: require("@/assets/img/megaphone.png"),
      lampActive: require("@/assets/img/lamp-active.png"),
      megaphoneActive: require("@/assets/img/megaphone-active.png"),
      lampCode: false,
      megaphoneCode: false,
      lampBox: false,
      megaphoneBox: false,
      megaphoneShow: false,
      lampShow: false,
      sendWs: () => {},
      time: 0,
      timeLoop: "",
    };
  },
  computed: {
    language() {
      return this.$languagePackage.navigation.shout;
    },
  },
  watch: {
    time(val) {
      if (val >= 5) {
        this.megaphoneShow = false;
        this.lampShow = false;
      }
    },
  },
  mounted() {
    let shoutSend = new ShoutSend(this.websocket);
    this.sendWs = shoutSend.sendWs;
  },
  methods: {
    openBox: function (type) {
      if (type == "lamp") {
        if (this.megaphoneBox) {
          this.openBox("megaphone");
        }
        this.lampBox = !this.lampBox;
      } else {
        if (this.lampBox) {
          this.openBox("lamp");
        }
        this.megaphoneBox = !this.megaphoneBox;
        this.megaphoneCode = !this.megaphoneCode;
      }
    },
    //获取websocket数据
    disposeData: function (msg_id, data) {
      let megaphoneBox = this.$refs.megaphoneBox;
      megaphoneBox && megaphoneBox.disposeData(msg_id, data);
      let lampBox = this.$refs.lampBox;
      lampBox && lampBox.disposeData(msg_id, data);
      if (msg_id === 701) {
        this.timeLoop && clearInterval(this.timeLoop);
        this.time = 0;
        if (data.model_connect) {
          this.megaphoneShow = true;
          // if (data.meg_tdf < 5000) {
          //   this.megaphoneShow = true;
          // } else {
          //   this.megaphoneShow = false;
          // }
        }
        if (data.led_mo || data.led_mo == 0) {
          this.lampShow = true;
          // if (data.led_tdf < 5000) {
          //   this.lampShow = true;
          // } else {
          //   this.lampShow = false;
          // }
        }
        this.lampCode = data.led_mo || data.led_pse;
        this.timeLoop = setInterval(() => {
          this.time++;
        }, 1000);
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width: 1200px) {
  @zoomIndex: 100vw / 1920px;
  .shout-lamp-box {
    .shout-lamp-btn {
      .shout-lamp-btn-item {
        width: @zoomIndex * 65px !important;
        height: @zoomIndex * 65px !important;
        padding: 0 @zoomIndex* 8px !important;
        border-radius: @zoomIndex * 12px !important;
      }
      .shout-lamp-content {
        left: @zoomIndex * 91px !important;
      }
      &:first-child {
        margin-bottom: @zoomIndex * 50px !important;
      }
    }
  }
}
.shout-lamp-box {
  .shout-lamp-btn {
    .shout-lamp-btn-item {
      width: 65px;
      height: 65px;
      padding: 0 8px;
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(71, 71, 71, 0.5);
      border-radius: 12px;
      .el-image {
        width: 100%;
      }
    }
    .shout-lamp-content {
      width: 100%;
      position: absolute;
      top: 0;
      left: 91px;
    }
    &:first-child {
      margin-bottom: 50px;
    }
    &.last {
      .el-image {
        width: 80%;
      }
    }
  }
}
</style>