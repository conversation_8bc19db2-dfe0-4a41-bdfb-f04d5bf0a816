<template>
  <div class="rtkset">
    <div class="video-item" v-for="(item, key) in rtkList" :key="key">
      <div class="item-content">
        <div class="content-title">{{ item.label }}</div>
        <div class="content-main">
          <el-select
            v-if="key == 'endpoint'"
            v-model="item.value"
            placeholder=""
            popper-class="endpoint_select"
            @change="changeEndpoint"
          >
            <el-option v-for="item in endpointList" :key="item" :value="item">{{
              item
            }}</el-option>
          </el-select>
          <el-input
            v-else
            v-model="item.value"
            :disabled="item.disabled"
            @focus="settingCode = true"
          ></el-input>
        </div>
      </div>
    </div>
    <div class="video-item">
      <el-button :class="btnCode ? 'active' : ''" @click="getMountpoint">{{
        language.getMountpoint
      }}</el-button>
    </div>
    <div class="video-item">
      <el-button :class="btnCode1 ? 'active' : ''" @click="rtkSet">{{
        language.set
      }}</el-button>
    </div>
  </div>
</template>
<script>
import baseUrl from "@/utils/global";
import axios from "axios";
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rtkList: {},
      btnCode: false,
      btnCode1: false,
      settingCode: false,
      endpointList: [],
      noRefresh: false,
    };
  },
  computed: {
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS;
    },
    rtkState() {
      return this.$store.state.equipment.rtkState;
    },
    language() {
      return this.$languagePackage.navigation.cameraConfig.rtkset;
    },
  },
  watch: {
    rtkState: {
      deep: true,
      handler: function (item) {
        if (!this.settingCode) {
          for (const key in this.rtkList) {
            // console.log(this.rtkList[key])
            switch (key) {
              case "url":
                this.rtkList[key].value = item.url;
                break;
              case "port":
                this.rtkList[key].value = item.port;
                break;
              case "user":
                this.rtkList[key].value = item.user;
                break;
              case "password":
                this.rtkList[key].value = item.password;
                break;
              case "endpoint":
                if (!this.noRefresh) {
                  this.rtkList[key].value = item.mount_point;
                }
                break;
              case "state":
                this.rtkList[key].value = item.account_active
                  ? this.language.inLine
                  : this.language.outLine;
                break;
              // case "network":
              //   this.rtkList[key].value=item.network
              //   break;
              case "ntrip":
                this.rtkList[key].value =
                  this.language.ntrip_state[item.ntrip_state];
                break;

              default:
                break;
            }
          }
        }
      },
    },
    isShow(val) {
      if (val) {
        this.settingCode = false;
      }
    },
  },
  created() {
    this.rtkList = {
      url: {
        label: this.language.url,
        value: this.rtkState.url,
        disabled: false,
      },
      port: {
        label: this.language.port,
        value: this.rtkState.port,
        disabled: false,
      },
      user: {
        label: this.language.user,
        value: this.rtkState.user,
        disabled: false,
      },
      password: {
        label: this.language.password,
        value: this.rtkState.password,
        disabled: false,
      },
      endpoint: {
        label: this.language.endpoint,
        value: this.rtkState.mount_point,
        disabled: false,
      },
      state: {
        label: this.language.state,
        value: this.rtkState.account_active
          ? this.language.inLine
          : this.language.outLine,
        disabled: true,
      },
      network: {
        label: this.language.network,
        value: this.language.nodata,
        disabled: true,
      },
      ntrip: {
        label: this.language.ntrip,
        value: this.language.ntrip_state[this.rtkState.ntrip],
        disabled: true,
      },
    };
  },
  methods: {
    getMountpoint() {
      if (this.btnCode) {
        return false;
      }
      this.btnCode = true;
      setTimeout(() => {
        this.btnCode = false;
      }, 200);
      if (!this.rtkList.url.value) {
        this.$message.error(this.language.urlEmpty);
        return false;
      }

      if (!this.rtkList.port.value) {
        this.$message.error(this.language.portEmpty);
        return false;
      }
      this.sendRequest();
    },
    sendRequest() {
      let request =
        baseUrl.BASE_URL + baseUrl.USER_PROT + "/ntrip/getMountPoint";
      let url =
        request +
        "?url=" +
        this.rtkList.url.value +
        ":" +
        parseInt(this.rtkList.port.value);
      axios.get(url).then((res) => {
        if (res.status == 200) {
          this.endpointList = [];
          let str = res.data;
          let arr = str.split(/[\r\n]/);
          arr.forEach((item) => {
            let a = item.split(";")[1];
            a && this.endpointList.push(a);
          });
          this.noRefresh = true;
          this.rtkList.endpoint.value = this.endpointList[0];
        } else {
          this.$message.error(this.language.getMountPointError);
        }
      });
    },
    changeEndpoint() {
      this.noRefresh = true;
    },
    rtkSet() {
      this.btnCode1 = true;
      let data = {
        url: this.rtkList.url.value,
        port: parseInt(this.rtkList.port.value),
        mount_point: this.rtkList.endpoint.value,
        user: this.rtkList.user.value,
        password: this.rtkList.password.value,
      };
      this.equipmentWS.manualSend(data, 404);
      this.noRefresh = false;
      setTimeout(() => {
        this.btnCode1 = false;
        this.settingCode = false;
      }, 200);
    },
  },
};
</script>
<style lang="less" scoped>
.rtkset {
  padding: 0 50px;
  display: flex;
  flex-wrap: wrap;
  .video-item {
    width: 50%;
    .item-content {
      margin-bottom: 10px;
      .content-title {
        font-size: 12px;
        // color: #fff;
        margin: 2px 0 5px 0;
      }
      .content-main {
        // border: 1px solid #c2c3c3;
        width: 221px;
        border-radius: 6px;
        // padding: 0 35px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    .el-button {
      margin-bottom: 10px;
      padding: 5px 10px;
    }
  }
}
</style>
<style lang="less">
.rtkset {
  .video-item {
    .item-content {
      .content-main {
        .el-input__inner {
          height: 30px;
        }
        .el-input__icon {
          line-height: 30px;
        }
      }
    }
  }
}
.endpoint_select {
  background-color: rgb(75, 75, 75);
  border: none;
  .popper__arrow {
    border-bottom-color: rgb(75, 75, 75) !important;
    &::after {
      border-bottom-color: rgb(75, 75, 75) !important;
    }
  }
  .el-select-dropdown__item {
    color: #fff;
    background-color: transparent;
    &:hover {
      background-color: rgba(0, 183, 255, 0.493);
    }
    &.selected {
      color: #409eff;
    }
  }
}
</style>