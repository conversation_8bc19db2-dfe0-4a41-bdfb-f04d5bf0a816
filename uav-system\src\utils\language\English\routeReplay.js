/**
 * 飞行回放
 */
const routeReplay = {
    mode: "Airplane mode",
    modeTitle: "Flight data map change",
    basic: {
        mission_type_label: "Task type",
        sn_id: 'Device number',
        uav_id: "Drone number",
        start_time: "Departure time",
        end_time: "Landing time"
    },
    point: {
        lng: "longitude",
        lat: "latitude",
        hs: "speed",
        height: "altitude",
        yaw: "course angle",
        roll: "roll angle",
        pitch: "angle of pitch",
        distance: "distance"
    },
    chart: {
        altitude: "Altitude",
        horizontalVelocity: "Horizontal velocity",
        verticalVelocity: "Vertical velocity"
    },
    unit: {
        m: "m"
    },
    message: {
        getDataError: "Data acquisition failure",
        dataParsingFailure: "Data analysis failure",
        taskPlayConfirm: {
            title: "Hint",
            content: "This operation will play again",
            confirmText: "Confirm",
            camceLText: "Cancel"
        },
        refresh: {
            title: "Hint",
            content: "This operation will reset the trajectory information, please confirm？",
            confirmText: "Confirm",
            camceLText: "Cancel"
        }
    }
}

export default routeReplay;