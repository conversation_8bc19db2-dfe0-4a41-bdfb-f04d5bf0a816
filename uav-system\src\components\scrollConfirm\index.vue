<!-- 滚动确认 -->
<template>
  <div class="scroll-confirm" ref="scrollConfirm">
    <!-- 显示文本 -->
    <div class="show-text">{{ showText }}</div>

    <!-- 滚动按钮 -->
    <div class="scroll-button" @mousedown="mouserDown" ref="scrollButtom">
      <div class="scroll-button-mian">
        <div class="main-item"></div>
        <div class="main-item"></div>
        <div class="main-item"></div>
      </div>
    </div>

    <!-- 滚动之后的颜色 -->
    <div class="scroll-later" ref="scrollLater"></div>
  </div>
</template>

<script>
export default {
  name: "scroll-confirm",
  data() {
    return {
      isDown: false,
      mouserStartX: 0, // 鼠标按下时位置
      mainW: 0,
      scrollButtomW: 0,
      maxMoveW: 0,
      verifyState: 0, // 校验状态， 1=>失败，2=>成功
      showText: this.$languagePackage.components.scrollConfirm.showText
    };
  },
  created(){

  },
  mounted() {
    this.$nextTick(() => {});
  },
  methods: {
    mouserDown: function (event) {
      this.mainW = this.$refs.scrollConfirm.offsetWidth;
      this.scrollButtomW = this.$refs.scrollButtom.offsetWidth;
      this.maxMoveW = this.mainW - this.scrollButtomW;

      this.isDown = true;
      this.mouserStartX = event.pageX;
      window.addEventListener("mousemove", this.mouseMove);
      window.addEventListener("mouseup", this.mouseUp);
    },
    // 鼠标移动
    mouseMove: function (event) {
      if (!this.isDown) {
        return false;
      }
      let x = event.pageX - this.mouserStartX;

      if (x <= 0) {
        this.$refs.scrollButtom.style.left = 0 + "px";
        this.$refs.scrollLater.style.width = 0 + "px";
      } else if (x >= this.maxMoveW) {
        this.$refs.scrollButtom.style.left = this.maxMoveW + "px";
        this.$refs.scrollLater.style.width =
          this.maxMoveW + this.scrollButtomW + "px";
      } else {
        this.$refs.scrollButtom.style.left = x + "px";
        this.$refs.scrollLater.style.width = x + this.scrollButtomW + "px";
      }
    },
    // 鼠标松开
    mouseUp: function (event) {
      if (!this.isDown) {
        return false;
      }
      this.isDown = false;
      window.removeEventListener("mousemove", this.mouseMove);
      window.removeEventListener("mouseup", this.mouseUp);
      let x = event.pageX - this.mouserStartX;

      let state = null;
      if (x < this.maxMoveW) {
        this.$refs.scrollButtom.style.left = 0 + "px";
        this.$refs.scrollLater.style.width = 0 + "px";
        state = false;
      } else if (x >= this.maxMoveW) {
        this.verifyState = 2;
        state = true;
      }
      
      this.$emit("change", state);
    },
  },
};
</script>

<style lang="less" scoped>
.text-style {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 14px;
}

.scroll-confirm {
  width: 100%;
  height: 24px;
  background-color: #4ca6ff;
  border-radius: 18px;
  position: relative;
  .text-style;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  .show-text {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    .text-style;
  }

  .scroll-button {
    width: 34px;
    height: calc(100% - 2px);
    border: 1px solid #fff;
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    border-radius: 18px;
    overflow: hidden;
    background-image: linear-gradient(
      to right,
      rgba(6, 122, 238, 0.9),
      rgba(76, 166, 255, 0.6)
    );
    cursor: move;
    .scroll-button-mian {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
      .main-item {
        width: 2px;
        height: calc(100% - 10px);
        background-color: #fff;
        margin: 0 2px;
        border-radius: 2px;
      }
    }
  }

  .scroll-later {
    height: 100%;
    background-color: #1df50e;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 18px;
  }
}
</style>