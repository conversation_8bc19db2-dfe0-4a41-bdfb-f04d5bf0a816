<template>
  <div class="dialog-components" ref="dialogComponents">
    <div class="tabel-list">
      <div
        class="device-item"
        v-for="item in list"
        :key="item.id"
        @click="clickItem(item)"
      >
        <el-image
          :src="
            item.state
              ? item.state == 2
                ? imgWorkLine
                : imgInLine
              : imgOutLine
          "
        ></el-image>

        <div class="item-content">
          {{
            language.equipName +
            item.name +
            ";" +
            language.equipSn +
            item.id +
            " (" +
            foramt(item.state) +
            ")"
          }}
        </div>
      </div>
      <div class="close-icon">
        <i class="el-icon-close" @click="closeInfo"></i>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    close: {
      type: Function,
      default: () => {
        return {};
      },
    },
    clickItem: {
      type: Function,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      imgInLine: require("@/assets/img/inLineHome.png"),
      imgOutLine: require("@/assets/img/outLineHome.png"),
      imgWorkLine: require("@/assets/img/workingHome.png"),
    };
  },
  computed: {
    language() {
      return this.$languagePackage.components.dialogInfo;
    },
  },
  methods: {
    closeInfo() {
      this.close();
    },
    foramt(state) {
      let inLine = this.$language == "english" ? "online" : "在线";
      let outLine = this.$language == "english" ? "offline" : "离线";
      if (state) {
        return inLine;
      } else {
        return outLine;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.dialog-components {
  width: 340px;
  position: relative;
  bottom: 0;
  left: 0;
  background-color: rgba(8, 16, 39, 0.8);
  padding-top: 25px;
  padding-bottom: 2px;
  border-radius: 2px;
  display: block !important;
  //   transform: translate(-50%,-50%);
  .tabel-list {
    border-top: 1px solid #0092f8;
    .device-item {
      display: flex;
      align-items: flex-start;
      border-bottom: 1px solid rgb(231, 231, 231);
      margin: 1% 5%;
      padding: 1%;
      .el-image {
        width: 22px;
        height: 22px;
        margin-right: 5px;
      }

      .item-content {
        font-size: 14px;
        color: #ffffff;
      }
    }
  }
  .close-icon {
    position: absolute;
    top: 0;
    right: 10px;
    font-size: 18px;
    color: #ffffff;
    cursor: pointer;
  }
}
</style>