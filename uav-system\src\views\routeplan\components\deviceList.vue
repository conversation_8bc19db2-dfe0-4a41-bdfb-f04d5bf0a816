<template>
  <div class="deviceList">
    <el-dialog
      :title="deviceLanguage.title"
      :visible.sync="chooseDevice"
      :close-on-click-modal="true"
      :show-close="false"
      width="50%"
      @close="closeEvent"
    >
      <div class="contentDiv">
        <div class="check-group">
          <el-button
            v-for="item in deviceList"
            :key="item.sn_id"
            @click="changeDevice(item)"
            :class="checked == item.sn_id ? 'active' : ''"
          >
            <el-image :src="item.imgSrc"></el-image>
            <div class="nameDiv">{{ item.name }}</div>
            <div class="idDiv">
              <span style="color: #acacac; font-weight: 500">ID: </span
              >{{ item.sn_id }}
            </div>
            <div
              class="time"
              :style="item.estTime ? '' : { visibility: 'hidden' }"
            >
              {{ deviceLanguage.flightTime
              }}<span class="time-em">{{ item.estTime | estTimeFormat }}</span>
            </div>
          </el-button>
        </div>
        <div class="entryDevice" v-if="deviceList.length > 0 ? false : true">
          {{ deviceLanguage.noInEquip }}
        </div>
      </div>
      <div class="pageDiv">
        <el-pagination
          layout="prev, pager, next"
          :pager-count="5"
          :page-size="6"
          :total="total_page"
          @current-change="changePage"
          v-show="deviceList.length > 0 ? true : false"
        >
        </el-pagination>
      </div>
      <div class="btnDiv">
        <el-button class="sureBtn" @click="sureToplan">{{
          deviceLanguage.save
        }}</el-button>
        <el-button class="closeBtn" @click="closeToplan">{{
          deviceLanguage.cancel
        }}</el-button>
      </div>
    </el-dialog>
    <dialog-upload
      ref="dialogUpload"
      :waypoint_percent="waypoint_percent"
      @close="closeWebsocket"
    ></dialog-upload>
  </div>
</template>
<script>
import requestHttp from "../../../utils/api";
import { Websockets } from "@/utils/websocketUntil";
import baseUrl from "@/utils/global";
import { getRouteParam } from "@/utils/getRouteParam.js";
import dialogUpload from "./dialogUpload.vue";
import { wgs84_to_gcj02 } from "../../../utils/wgs84_to_gcj02";
import { computedMethod } from "@/utils/computedMap";
import { pointsConvert } from "@/utils/coordinateConvert";
import { computedMapMethods } from "@/utils/cesium/computedMapMethods";
import { typeJudge } from "@/utils/deviceTypeJudge";
export default {
  name: "deviceList",
  props: {
    planCode: {
      type: [Number, String],
      default: "",
    },
    deviceLanguage: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
  },
  components: {
    dialogUpload,
  },
  data() {
    return {
      chooseDevice: false,
      deviceList_total: [],
      deviceList: [],
      total_page: 0,
      closeCode: false,
      clickCode: true,
      websocket: "",
      deviceItem: "",
      fenceItem: "",
      checked: "",
      websocket1: "",
      waypoint_percent: 0,
      sendCode: false,
      waitLoading: "",
    };
  },
  computed: {
    routeItem() {
      return this.$store.state.route.routeItem;
    },
    estTime() {
      return this.$store.state.route.estTime;
    },
    firstPoint() {
      return this.$store.state.route.firstPoint;
    },
    lastPoint() {
      return this.$store.state.route.lastPoint;
    },
    maps() {
      return this.$store.state.equipment.maps;
    },
    leafletMaps() {
      return this.$store.state.equipment.leafletMaps;
    },
  },
  watch: {
    lastPoint: {
      handler(val) {
        if (this.chooseDevice) {
          this.getDeviceData();
        }
      },
      deep: true,
    },
  },
  // created() {
  //   this.$store.dispatch("requestTypeList");
  // },
  methods: {
    //获取在线设备列表数据
    // getDeviceData() {
    //   requestHttp("deviceOnline").then((res) => {
    //     this.deviceList_total = res.data.list;
    //     this.total_page=this.deviceList_total.length
    //     this.pageData(1)
    //   });
    // },
    //获取设备列表
    getDeviceData() {
      if (!(this.lastPoint && this.lastPoint.lat)) {
        return false;
      }
      this.waitLoading && this.waitLoading.close();
      let data = {
        page: 0,
        size: 200,
        type: 0,
      };
      data.pmd = data.page.toString() + data.type.toString();
      requestHttp("deviceList", data).then((res) => {
        let deviceList_total = res.data.list ? res.data.list : [];
        this.deviceList_total = [];
        for (let index = 0; index < deviceList_total.length; index++) {
          let item = typeJudge(deviceList_total[index]);
          if (item.is_push_on && !item.is_pull_on) {
            if (item.isStable) {
              item.estTime = this.computedTime(item);
            }
            this.deviceList_total.push(item);
          }
          // this.deviceList_total.push(deviceList_total[index]);
        }
        this.total_page = this.deviceList_total.length;
        this.pageData(1);
      });
    },
    computedTime(item) {
      // let path = wgs84_to_gcj02(item.lon_int / 1e7, item.lat_int / 1e7);
      let path = pointsConvert({
        point: [item.lon_int / 1e7, item.lat_int / 1e7],
        type: 10,
      });
      let distanceFirst = computedMapMethods("computedDistance", {
        point1: { lng: path[0], lat: path[1] },
        point2: this.firstPoint,
        defaultHeight: true,
      });
      let distanceLast = computedMapMethods("computedDistance", {
        point1: { lng: path[0], lat: path[1] },
        point2: this.lastPoint,
        defaultHeight: true,
      });
      let auto_speed = this.routeItem.auto_speed / 100;
      return (
        this.estTime +
        distanceFirst / auto_speed +
        distanceLast / auto_speed +
        120
      );
    },
    //点击选择设备
    changeDevice(item) {
      this.checked = item.sn_id;
    },
    //打开选择
    openChoose() {
      this.chooseDevice = true;
      this.deviceItem = "";
      this.waitLoading = this.$loading({
        lock: true,
        text: "正在计算时间跟距离，请稍候。。。",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.5)",
        customClass: "wait-loading",
      });
      this.getDeviceData();
    },
    //关闭选择
    closeEvent() {
      this.checked = "";
      if (!this.closeCode) {
        this.$message.info({
          message: this.routeItem.is_timed_task
            ? this.deviceLanguage.cancelMessage1
            : this.deviceLanguage.cancelMessage,
          customClass: "message-info-tip",
        });
      }
      this.closeCode = false;
      this.$emit("update:planCode", 0);
    },
    //取消按钮
    closeToplan() {
      this.chooseDevice = false;
      this.closeCode = true;
      this.checked = "";
      this.$message.info({
        message: this.routeItem.is_timed_task
          ? this.deviceLanguage.cancelMessage1
          : this.deviceLanguage.cancelMessage,
        customClass: "message-info-tip",
      });
      this.$emit("update:planCode", 0);
    },
    //确定按钮
    sureToplan() {
      if (this.clickCode) {
        this.clickCode = false;
        if (this.checked) {
          this.deviceItem = "";
          for (let index = 0; index < this.deviceList.length; index++) {
            if (this.deviceList[index].sn_id == this.checked) {
              this.deviceItem = this.deviceList[index];
            }
          }
          let fence = this.$store.state.route.fence;
          let f_id = this.routeItem.f_id;
          this.fenceItem = "";
          for (let index = 0; index < fence.length; index++) {
            if (fence[index].f_id == f_id) {
              this.fenceItem = fence[index];
            }
          }
          if (this.routeItem.is_timed_task) {
            this.issuedRoute();
            // this.$refs.dialogUpload.openDialog();
          } else {
            // this.websocket.manualClone();

            this.toNext();
            this.chooseDevice = false;
            this.closeCode = true;
            this.checked = "";
            this.$emit("update:planCode", 0);
          }

          // if (this.deviceItem.type == 10) {
          //   this.$refs.dialogUpload.openDialog();
          //   this.websocket1 = new Websockets(baseUrl.WS_URL, {
          //     equipmentVerify: {
          //       sn_id: this.deviceItem.sn_id,
          //       type: this.deviceItem.type,
          //       vst: 40,
          //     },
          //     heartbeat: 20000,
          //     message: this.returnMessage,

          //   });
          // } else {

          // }
        } else {
          this.$message.warning({
            message: this.routeItem.is_timed_task
              ? this.deviceLanguage.noSelectMessage1
              : this.deviceLanguage.noSelectMessage,
            customClass: "message-info-tip",
          });
        }
        setTimeout(() => {
          this.clickCode = true;
        }, 400);
      }
    },
    // createConnect() {
    //   this.websocket1 = new Websockets(baseUrl.WS_URL, {
    //     equipmentVerify: {
    //       sn_id: this.deviceItem.sn_id,
    //       type: this.deviceItem.type,
    //       vst: 40,
    //     },
    //     heartbeat: 20000,
    //     message: this.returnMessage,
    //   });
    // },
    // returnMessage(e) {
    //   console.log(e);
    //   if (e.msg_id === 200 && !this.sendCode) {
    //     this.issuedRoute();
    //     this.sendCode = true;
    //   }
    //   if (e.msg_id == 432) {
    //     this.waypoint_percent = e.data.waypoint_percent;
    //     //     console.log("数据反馈",e.data)
    //     //     console.log("上传进度", e.data.waypoint_percent)
    //   }
    // },
    issuedRoute() {
      let data = getRouteParam(
        this.routeItem,
        this.fenceItem,
        this.maps,
        this.leafletMaps
      );
      data.uav_id = this.deviceItem.uav_sn;
      let userInfo = this.$store.state.user.userInfo;
      data.user_id = userInfo.u_id;
      data.user_name = userInfo.nick;
      data.cmd_type = 8;
      //方位角
      data.standing_direction = this.deviceItem.direction_angle;
      data.alternate_rtl_latitude = this.deviceItem.alternate_lat_int;
      data.alternate_rtl_longitude = this.deviceItem.alternate_lon_int;
      data.place_rtl_latitude = this.deviceItem.lat_int;
      data.place_rtl_longitude = this.deviceItem.lon_int;
      if (this.routeItem.cal_alt_json) {
        data.elevation_array = this.sendElevation();
      }
      let uploadParams = {
        msg_id: 401,
        tms: new Date().getTime().toString(),
        version: 0,
        data: data,
      };
      let param = {
        to_push_json: JSON.stringify(uploadParams), //上传航线信息，json字符串
        task_json: this.routeItem.task_json, //时间搓数组字符串
        sn_id: this.deviceItem.sn_id, //设备id
        m_id: this.routeItem.m_id,
        os_timestampCode: true,
      };
      param.pmd =
        param.m_id.toString() +
        param.task_json +
        param.sn_id +
        param.to_push_json;
      requestHttp("taskAdd", param).then((res) => {
        this.chooseDevice = false;
        this.closeCode = true;
        this.checked = "";
        this.$emit("update:planCode", 0);
        this.$message.success(this.deviceLanguage.sendSuccessMsg);
      });
    },
    sendElevation() {
      let routeItem = this.routeItem;
      if (routeItem.cal_alt_json) {
        let cal_alt_json = JSON.parse(routeItem.cal_alt_json);
        if (!cal_alt_json.isComputeHight) {
          return [];
        }
        let arr = this.returnElevationArr(cal_alt_json.elevationData);
        return arr;
      } else {
        return [];
      }
    },
    returnElevationArr(elevationData, breakCode) {
      let arr = [];
      for (const key in elevationData) {
        let s_Point = breakCode && Number(key) == 1 ? -1 : Number(key);
        let obj = {
          s_point: s_Point,
          e_point: Number(key) + 1,
          height_arr: [],
          distance: elevationData[key].distance,
        };
        for (let index = 0; index < elevationData[key].arr.length; index++) {
          obj.height_arr.push(elevationData[key].arr[index].height);
        }
        arr.push(obj);
      }
      return arr;
    },
    // returnMessage(e) {
    //   // console.log(e)
    //   if (e.msg_id == 200 && !this.sendCode) {
    //     this.uploadRoute();
    //     this.sendCode = true;
    //   }
    //   if (e.msg_id == 432) {
    //     this.waypoint_percent = e.data.waypoint_percent;
    //     console.log("数据反馈",e.data)
    //     console.log("上传进度", e.data.waypoint_percent)
    //   }
    //   if (e.msg_id == 442) {
    //     // this.waypoint_percent = e.data.waypoint_percent;
    //     console.log("下载航线", e.data);
    //     // console.log(123,e.data)
    //   }
    // },
    // uploadRoute() {
    //   let data = getRouteParam(
    //     this.routeItem,
    //     this.fenceItem
    //   );
    //   data.uav_id = this.deviceItem.uav_sn;
    //   let userInfo = this.$store.state.user.userInfo;
    //   data.user_id = userInfo.u_id;
    //   data.user_name = userInfo.nick;
    //   console.log("上传航线", data);
    //   this.websocket1.manualSend(data, 440);
    //   // let flightParam = sessionStorage.getItem("flightParam") || {};
    //   // let params = JSON.parse(flightParam);

    //   // data.uav_id = params.uav_id;
    //   // let userInfo = this.$store.state.user.userInfo;
    //   // data.user_id = userInfo.u_id;
    //   // data.user_name = userInfo.nick;

    //   //
    // },
    closeWebsocket() {
      if (this.websocket1) {
        this.websocket1.manualClone();
        this.websocket1 = "";
      }
      this.waypoint_percent = 0;
      this.sendCode = false;
    },
    // downRoute() {
    //   this.websocket1.manualSend({}, 442);
    // },
    //上传航线后跳转
    toNext() {
      let f_id = this.routeItem.f_id;
      let m_id = this.routeItem.m_id;
      let obj = {
        route: this.routeItem,
        fence: this.fenceItem,
        point: [this.deviceItem.lon_int / 1e7, this.deviceItem.lat_int / 1e7],
        direction_angle: this.deviceItem.direction_angle,
        uav_point: [
          this.deviceItem.alternate_lon_int,
          this.deviceItem.alternate_lat_int,
        ],
        uav_id: this.deviceItem.uav_sn,
        equipmentType: this.deviceItem.isAlone ? 200 : this.deviceItem.type,
      };
      // sessionStorage.removeItem("flightParam");
      let flightParam = sessionStorage.getItem("flightParam");
      if (flightParam) {
        flightParam = JSON.parse(flightParam);
      } else {
        flightParam = {};
      }
      flightParam[this.deviceItem.sn_id] = obj;
      // if (flightParam) {
      //   flightParam = JSON.parse(flightParam);
      //   let i = flightParam.fence.findIndex((item) => item.f_id == f_id);
      //   if (i > -1) {
      //     flightParam.fence[i] = this.fenceItem;
      //   } else {
      //     flightParam.fence.push(this.fenceItem);
      //   }
      //   let j = flightParam.route.findIndex((item) => item.m_id == m_id);
      //   if (j > -1) {
      //     flightParam.route[j] = this.routeItem;
      //   } else {
      //     flightParam.route.push(this.routeItem);
      //   }
      //   let n = 0;
      //   for (let key in flightParam.point) {
      //     if (key == this.deviceItem.sn_id) {
      //       flightParam.point[key] = [
      //         (this.deviceItem.lon_int / 1e7).toFixed(7),
      //         (this.deviceItem.lat_int / 1e7).toFixed(7),
      //       ];
      //       flightParam.direction_angle[key] = this.deviceItem.direction_angle;
      //       flightParam.uav_point[key] = [
      //         this.deviceItem.alternate_lon_int,
      //         this.deviceItem.alternate_lat_int,
      //       ];
      //       n = 1;
      //     }
      //   }
      //   if (n == 0) {
      //     let sn_id = this.deviceItem.sn_id;
      //     flightParam.point[sn_id] = [
      //       this.deviceItem.lon_int / 1e7,
      //       this.deviceItem.lat_int / 1e7,
      //     ];

      //     flightParam.direction_angle[sn_id] = this.deviceItem.direction_angle;
      //     flightParam.uav_point[sn_id] = [
      //       this.deviceItem.alternate_lon_int,
      //       this.deviceItem.alternate_lat_int,
      //     ];
      //   }
      // } else {
      //   let sn_id = this.deviceItem.sn_id;
      //   let point = {};
      //   point[sn_id] = [
      //     this.deviceItem.lon_int / 1e7,
      //     this.deviceItem.lat_int / 1e7,
      //   ];
      //   let direction_angle = {};
      //   direction_angle[sn_id] = this.deviceItem.direction_angle;
      //   let uav_point = {};
      //   uav_point[sn_id] = [
      //     this.deviceItem.alternate_lon_int,
      //     this.deviceItem.alternate_lat_int,
      //   ];
      //   flightParam = {
      //     fence: [this.fenceItem],
      //     route: [this.routeItem],
      //     point: point,
      //     direction_angle: direction_angle,
      //     uav_point: uav_point,
      //   };
      // }
      // flightParam.uav_id = this.deviceItem.uav_sn;
      // flightParam.equipmentType = this.deviceItem.type;
      sessionStorage.setItem("flightParam", JSON.stringify(flightParam));
      setTimeout(() => {
        const newwin = this.$router.resolve({
          path: `/navigationEnvironment`,
          query: {
            sn_id: this.deviceItem.sn_id,
            type: this.deviceItem.isAlone ? 200 : this.deviceItem.type,
            // f_id: f_id,
            // m_id: m_id,
            state: 1,
          },
        });
        window.open(newwin.href, "_blank");
      }, 100);
    },
    //页码切换
    changePage(e) {
      this.pageData(e);
    },
    //分页数据
    pageData(e) {
      this.deviceList = [];
      let num =
        (e - 1) * 6 + 6 > this.deviceList_total.length
          ? this.deviceList_total.length
          : (e - 1) * 6 + 6;
      for (let index = (e - 1) * 6; index < num; index++) {
        this.deviceList.push(this.deviceList_total[index]);
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .deviceList {
    .el-dialog {
      .contentDiv {
        .check-group {
          .el-button {
            border-radius: @zoomIndex * 14px !important;
            .nameDiv {
              font-size: @zoomIndex * 16px !important;
            }
            .idDiv {
              // margin-top: 1%;
              font-size: @zoomIndex * 12px !important;
            }
            .time {
              font-size: @zoomIndex * 14px !important;
            }
          }
        }
        .entryDevice {
          letter-spacing: @zoomIndex * 4px !important;
          font-size: @zoomIndex * 24px !important;
        }
      }
    }
  }
}
.deviceList {
  .el-dialog {
    .contentDiv {
      width: 100%;
      height: 80%;
      overflow: auto;
      .check-group {
        .el-button {
          height: auto;
          width: 28%;
          margin: 1% 2%;
          margin-bottom: 2%;
          padding: 1%;
          text-align: center;
          border-radius: 14px;
          .el-image {
            width: 85%;
          }
          .nameDiv {
            font-size: 16px;
            margin: 2%;
          }
          .idDiv {
            // margin-top: 1%;
            font-size: 12px;
          }
          .time {
            font-size: 14px;
            margin: 5% 0;
            color: #acacac;
            margin-bottom: -6px;
            padding-bottom: 6px;
            // margin-top: 3%;
            .time-em {
              font-weight: 550;
              color: #0092f8;
            }
          }
        }
      }
      .entryDevice {
        text-align: center;
        padding-top: 20%;
        letter-spacing: 4px;
        font-size: 24px;
      }
    }
    .pageDiv {
      width: 94%;
      height: 5%;
      text-align: right;
    }
    .btnDiv {
      width: 95.5%;
      height: 8%;
      text-align: right;
      margin-top: 2%;
      .sureBtn {
        width: 15%;
        font-size: large;
        margin-right: 5%;
      }
      .closeBtn {
        width: 15%;
        font-size: large;
        margin-right: 2%;
      }
    }
  }
}
</style>
<style lang="less">
.deviceList {
  .el-dialog {
    height: 70% !important;
    top: 0 !important;
    margin-top: 15vh !important;
    border-radius: 8px !important;
    .el-dialog__header {
      text-align: left !important;
      height: 6% !important;
      padding: 2% !important;
      padding-bottom: 0 !important;
    }
    .el-dialog__body {
      height: 90% !important;
      padding: 1% 2% !important;
    }
    .pageDiv {
      .el-pagination {
        .btn-prev,
        .btn-next {
          padding-right: 6px !important;
          padding-left: 6px !important;
        }
      }
    }
  }
}
.wait-loading {
  z-index: 99999 !important;
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .deviceList {
    .el-dialog {
      border-radius: @zoomIndex * 8px !important;
      .pageDiv {
        .el-pagination {
          .btn-prev,
          .btn-next {
            padding-right: @zoomIndex * 6px !important;
            padding-left: @zoomIndex * 6px !important;
          }
        }
      }
    }
  }
}
</style>
