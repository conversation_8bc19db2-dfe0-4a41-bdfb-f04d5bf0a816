<template>
  <div class="error-info remote-header">
    <div class="info-left">
      <el-radio-group
        v-model="form_cor"
        @change="formCorChange"
        :disabled="!layerShow"
      >
        <el-radio :label="0">{{ link.auto }}</el-radio>
        <el-radio :label="10">{{ link.fpv }}</el-radio>
        <el-radio :label="20">{{ link.uav }}</el-radio>
      </el-radio-group>
    </div>
    <div class="info-right">
      <!-- <div class="right-cell">
        图传：{{trans_pt}}
      </div> -->
      <div class="right-cell error-cell">
        {{ this.severity_text }}
      </div>
      <div class="right-cell">
        {{ lineType(this.from_cno) }}
      </div>

      <!-- 飞行模式 -->
      <div class="right-cell">{{ flightMode }}</div>

      <!-- 飞行时间 -->
      <div class="right-cell">
        <img-icon name="time" class="mr10" :size="14"></img-icon>
        <span>{{ missionTime }}</span>
      </div>

      <!-- 飞行总距离 -->
      <div class="right-cell">
        <img-icon name="distance" class="mr10" :size="14"></img-icon>
        <span>{{ totalDistance }} m</span>
      </div>

      <!-- 电池 -->
      <div class="right-cell">
        <img-icon
          :name="batteryState"
          class="mr10"
          :size="14"
          :clickEvent="batteryClick"
        ></img-icon>
        <span>{{ percent }}%</span>
      </div>

      <!-- 电压 -->
      <div class="right-cell">
        <img-icon name="voltage-fff" class="mr10" :size="16"></img-icon>
        <span>{{ voltage }}</span>
      </div>

      <!-- 卫星 -->
      <div class="right-cell satellite">
        <img-icon name="satellite" class="mr5" :size="14"></img-icon>
        <img-icon :name="signalState" width="14"></img-icon>
        <span class="satellite-number">{{ numsv }}</span>
      </div>

      <!-- rtk -->
      <div class="right-cell satellite">
        <span :style="{ color: rtkStatus.color }">RTK{{ rtkStatus.text }}</span>
      </div>

      <!-- 4G网络信号值 -->
      <!-- <div class="right-cell">
        <img-icon name="telecontrol" class="mr5" :size="14"></img-icon>
        <img-icon :name="telecontrolState" width="14"></img-icon>
      </div> -->
      <div class="right-cell">
        <div class="">4G&nbsp;&nbsp;</div>
        <img-icon :name="telecontrolState" width="14"></img-icon>
      </div>

      <!-- 示例 -->
      <div class="right-cell" @click="layerShow ? openIntroduced() : ''">
        <img-icon name="what-fff" :size="14"></img-icon>
      </div>
      <div class="right-cell" @click="layerShow ? openKeyboard() : ''">
        <img-icon
          v-if="!openKeyboardCode"
          name="keyboard"
          :size="18"
          :title="language.openOperate"
          style="margin-top: 5px"
        ></img-icon>
        <img-icon
          v-if="openKeyboardCode"
          name="keyboard_1"
          :size="18"
          :title="language.closeOperate"
          style="margin-top: 5px"
        ></img-icon>
      </div>
    </div>
  </div>
</template>

<script>
import imgIcon from "@/components/imgIcon/index";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import operationIntroduced from "./operationIntroduced.vue";

import { timestampSwitch } from "@/utils/date.js";

import Audio from "@/utils/audio";

export default {
  components: {
    imgIcon,
    ElImageViewer,
    operationIntroduced,
  },
  props: {
    openIntroduced: Function,
  },
  data() {
    return {
      percent: 100,
      numsv: 0,
      telecontrol: 100,
      satelliteNum: 0,
      voltage: "N/A",
      rtkstatusNum: "",
      time: "00:00:00",
      missionTime: "00:00:00",
      showViewer: false,
      flightMode: "",
      // flightModeList: {
      //   2: "姿态模式",
      //   3: "自动模式",
      //   4: "跟随模式",
      //   5: "GPS模式",
      //   6: "返航模式",
      //   9: "降落飞行模式",
      //   13: "运动模式",
      // },
      totalDistance: 0,
      telecontrolState: "",
      form_cor: 0,
      from_cno: null,
      severity: 0,
      severity_text: "",
      textLoop: "",
      openKeyboardCode: false,
      layerShow: false,
      trans_pt:''
    };
  },
  computed: {
    // 电池状态
    batteryState: function () {
      if (this.percent >= 60) {
        return "battery80";
      } else if (this.percent >= 40 && this.percent < 60) {
        return "battery60";
      } else {
        return "battery40";
      }
    },
    // 卫星状态
    signalState: function () {
      if (this.numsv >= 16) {
        return "signal100";
      } else if (this.numsv > 12 && this.numsv < 16) {
        return "signal80";
      } else if (this.numsv > 8 && this.numsv <= 12) {
        return "signal60";
      } else if (this.numsv > 4 && this.numsv <= 8) {
        return "signal40";
      } else if (this.numsv > 0 && this.numsv <= 4) {
        return "signal20";
      } else {
        return "signal0";
      }
    },
    rtkStatus: function () {
      let info = {
        text: "",
        color: "",
      };
      let val = this.rtkstatusNum;
      let item = this.$languagePackage.dict.rtkStatus;
      if (val == 0) {
        info.text = item[0];
        info.color = "red";
      } else if (val == 1) {
        info.text = item[1];
        info.color = "red";
      } else if (val == 3) {
        info.text = item[2];
        info.color = "red";
      } else if (val === 4) {
        info.text = item[3];
        info.color = "yellow";
      } else if (val === 6) {
        info.text = item[4];
        info.color = "green";
      }

      return info;
    },
    flightModeList() {
      return this.$languagePackage.dict.flightModeList;
    },
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS;
    },
    link() {
      return this.$languagePackage.navigation.link;
    },
    language() {
      return this.$languagePackage.navigation.errorInfo;
    },
    cor() {
      return this.$store.state.equipment.form_cor;
    },
  },
  watch: {
    flightMode: function (val) {
      console.log("飞行模式发生变化--------->", val);
      if (val == "姿态模式") {
        this.audio.playSpeech("姿态模式，注意飞行安全");
      }else if(val=="Model error"){
        this.audio.playSpeech("Attitude mode, pay attention to flight safety!");
      } else {
        val && this.audio.playSpeech(val);
      }
    },
    cor: function (val) {
      if (val == 10) {
        this.form_cor = val;
      }
    },
  },
  created() {
    this.audio = new Audio();
    let query = this.$route.query || {};
    this.layerShow = query.state == 2 && query.type != 200 ? false : true;
  },
  methods: {
    disposeData: function (msg_id, data) {
      if (msg_id === 432) {
        this.numsv = data.numsv; // 卫星数量
        this.percent = data.battery_remaining || 0; // 电池 network_signal_value

        this.flightMode = this.flightModeList[data.flight_mode];

        this.rtkstatusNum = data.rtk_status;

        let time = data.current_consumed || 0;
        this.missionTime = timestampSwitch(time * 1000).time;
        this.voltage = data.voltage.toFixed(2) || "N/A";
        this.totalDistance = (data.total_distance / 100).toFixed(2);
        // this.severity=data.severity
        // this.severity_text=data.severity_text
      } else if (msg_id === 207) {
        this.form_cor = data.cor;
        this.from_cno = data.cno;
      } else if (msg_id === 200) {
        this.form_cor = data.cor;
        this.from_cno = data.cno;
      } else if (msg_id === 206) {
        let cnoList = data.list;
        let index = cnoList.findIndex((item) => {
          return item.cor == this.form_cor;
        });
        let params = {};
        if (this.form_cor !== 0) {
          params = {
            cor: this.form_cor,
            cno: cnoList[index].cno,
          };
        } else {
          params = {
            cor: this.form_cor,
          };
        }
        this.equipmentWS.manualSend(params, 207);
      } else if (msg_id === 500) {
        this.severity_text = data.msg;
        this.severity = data.level;
        if (this.textLoop) {
          clearTimeout(this.textLoop);
        }
        this.textLoop = setTimeout(() => {
          this.severity_text = "";
        }, 3000);
      } else if (msg_id === 436) {
        this.telecontrol = data.network_signal_value; // fixtype
        if (this.telecontrol < 0) {
          if (this.telecontrol >= -80 && this.telecontrol < -50) {
            this.telecontrolState = "signal100";
          } else if (this.telecontrol >= -90 && this.telecontrol < -80) {
            this.telecontrolState = "signal80";
          } else if (this.telecontrol >= -95 && this.telecontrol < -90) {
            this.telecontrolState = "signal60";
          } else if (this.telecontrol >= -105 && this.telecontrol < -95) {
            this.telecontrolState = "signal40";
          } else if (this.telecontrol >= -115 && this.telecontrol < -105) {
            this.telecontrolState = "signal20";
          } else {
            this.telecontrolState = "signal0";
          }
        }
        /**
         * 满格(5格):-80<=x<-50;
         * 4格:-90<=x<-80;
         * 3格:-95<=x<-90;
         * 2格:-105<=x<-95;
         * 1格:-115<=x<-105;
         * 没信号:x<-115;
         */
        this.trans_pt=data.trans_pt
      }
    },
    openImage: function () {
      this.showViewer = true;
    },
    closeViewer: function () {
      this.showViewer = false;
    },
    formCorChange: function () {
      this.$store.commit("setFormCor", this.form_cor);
      this.equipmentWS.manualSend({}, 206);
    },
    lineType(value) {
      if (value) {
        let a = this.$language == "english" ? "fpv" : "图传";
        let val = value.substr(1, 1);
        return val == 0 ? a : "4G";
      }
    },
    openKeyboard() {
      this.openKeyboardCode = !this.openKeyboardCode;
      this.$store.commit("setKeyboardOperate", this.openKeyboardCode);
      if (this.openKeyboardCode) {
        this.$message.success(this.language.openMsg);
      } else {
        this.$message.info(this.language.closeMsg);
      }
    },
    batteryClick() {
      this.$emit("showBattery", "");
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .error-info {
    height: @radio * 50px !important;
    padding: 0 @radio * 20px !important;

    .info-right {
      .right-cell {
        margin-right: @radio * 23px !important;
      }
      .satellite {
        .satellite-number {
          font-size: @radio * 12px !important;
          left: @radio * 19px !important;
          top: @radio * -10px !important;
        }
      }
    }
  }

  .custom-dialog {
    .dialog-body {
      .shut-icon {
        right: @radio * 40px !important;
        top: @radio * 40px !important;
        font-size: @radio * 48px !important;
        width: @radio * 30px !important;
        height: @radio * 30px !important;
      }
    }
  }
}

.error-info {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  // background-color: rgba(0, 0, 0, 0.7);
  .info-left {
    display: flex;
    // color: #fc0900;
    align-items: center;
  }
  .info-right {
    display: flex;
    // color: #fff;
    .right-cell {
      margin-right: 23px;
      display: flex;
      align-items: center;
      &.error-cell {
        color: red;
      }
      .img-icon {
        cursor: pointer;
      }
    }
    .satellite {
      position: relative;
      .satellite-number {
        font-size: 12px;
        position: absolute;
        left: 19px;
        top: -10px;
        transform: scale(0.8);
      }
    }
  }
}

.custom-dialog {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  //
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  .dialog-body {
    position: relative;
    max-width: 100%;
    height: 100%;
    overflow: hidden;
    // background-color: rgba(0, 0, 0, 0.5);
    // overflow-y: auto;
    img {
      max-width: 100%;
    }
    .shut-icon {
      position: absolute;
      right: 40px;
      top: 40px;
      // background-color: #ccc;
      color: #ccc;
      font-size: 48px;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover {
        // color: #409eff;
      }
    }
  }
}
</style>
<style lang="less">
.error-info {
  .info-left {
    .el-radio-group {
      .el-radio__input.is-disabled.is-checked .el-radio__inner {
        border-color: #409eff;
        background: #409eff;
      }
      .el-radio__input.is-checked+.el-radio__label{
        color: #409eff;
      }
    }
  }
}
</style>