<template>
  <div class="cell-circle" :style="cellCircleStyle">
    <div
      class="circle-row"
      :style="item.style"
      @mousedown.stop="cutOperation(item)"
      @mouseup.stop="stepOperation(item)"
      v-for="(item, index) in joustickList"
      :key="index"
      :class="downType == item.key ? 'select-icon-style' : ''"
    >
      <i :class="item.icon"></i>
    </div>
    <!-- 中间的点 -->
    <slot name="circle-center"></slot>
  </div>
</template>

<script>
export default {
  props: {
    width: {
      type: [Number, String],
      default: 100,
    },
  },
  data() {
    return {
      downType: "",
      joustickList: [
        {
          icon: "el-icon-arrow-up",
          key: "top",
          style: {},
        },
        {
          icon: "el-icon-arrow-right",
          key: "right",
          style: {
            left: `${this.width / 2}px`,
          },
        },
        {
          icon: "el-icon-arrow-left",
          key: "left",
          style: {
            top: `${this.width / 2}px`,
          },
        },
        {
          icon: "el-icon-arrow-down",
          key: "bottom",
          style: {
            left: `${this.width / 2}px`,
            top: `${this.width / 2}px`,
          },
        },
      ],
      dowmTime: null,
      item: "",
    };
  },
  computed: {
    cellCircleStyle() {
      let width = this.width;
      return {
        width: width + "px",
        height: width + "px",
      };
    },
    circleRowStyke() {
      let width = this.width / 2;
      return {
        width: width + "px",
        height: width + "px",
        "border-radius": `${width}px 0 0`,
      };
    },
  },
  created() {
    for (let i = 0; i < this.joustickList.length; i++) {
      let item = this.joustickList[i];
      item.style = Object.assign(item.style, this.circleRowStyke);
    }
  },
  mounted() {
    window.addEventListener("mouseup", this.mouseups);
  },
  methods: {
    cutOperation: function (item) {
      this.item = item;
      clearInterval(this.dowmTime);
      this.downType = item.key;
      this.$emit("operation", item.key);

      // 长按每200毫秒触发一下
      this.dowmTime = setInterval(() => {
        this.downType = item.key;
        this.$emit("operation", item.key);
      }, 200);
    },
    stepOperation: function (item) {
      clearInterval(this.dowmTime);
      this.downType = "";
      this.$emit("up", item.key);
    },
    mouseups() {
      if (this.item) {
        clearInterval(this.dowmTime);
        this.downType = "";
        this.$emit("up", this.item.key);
        this.item = "";
      }
    },
  },
};
</script>

<style lang="less" scoped>
.cell-circle {
  position: relative;
  transform: rotate(45deg);
  .circle-row {
    position: absolute;
    border-right: none;
    display: flex;
    align-items: center;
    justify-content: center;
    // background-color: #fff;
    font-size: 24px;
    // color: #000;
    cursor: pointer;
    i {
      transform: rotate(-45deg);
    }

    &:nth-child(2) {
      transform: rotate(90deg);
      top: 0;
      i {
        transform: rotate(215deg);
      }
    }

    &:nth-child(3) {
      transform: rotate(-90deg);
      left: 0px;
      i {
        transform: rotate(45deg);
      }
    }

    &:nth-child(4) {
      transform: rotate(180deg);
      i {
        transform: rotate(135deg);
      }
    }
  }
  .select-icon-style {
    // color: rgba(76, 166, 255, 1) !important;
  }
}
</style>