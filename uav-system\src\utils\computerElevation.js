import axios from 'axios'
import { computedMethod, calcAngle } from './computedMap'
import { getLonAndLat } from './getCoordinate'
let cancel = []
export function computerElevation(wgs84Points, oneRequestNum, progressFun, map) {
    cancel.forEach(x => {
        if (typeof x === 'function') {
            x("终止请求")
        }
    })
    return new Promise((resolve, reject) => {
            asyncPools(oneRequestNum, getComputedArray(wgs84Points, map),
                getZoomHeight, progressFun).then(res => {
                let maxHeight = []
                for (let index = 0; index < res.length; index++) {
                    maxHeight.push(res[index].data.stats.max)
                }
                resolve(Math.max(...maxHeight))
            })
        })
        // asyncPools(oneRequestNum, getComputedArray(wgs84Points, map),
        //     getZoomHeight, progress).then(res => {
        //     for (let index = 0; index < res.length; index++) {
        //         maxHeight.push(res[index].data.stats.max)
        //     }
        //     // console.log('区域高度：', maxHeight)
        //     // this.zoomMaxHeight = 
        //     return Math.max(...maxHeight)
        // })

}
//请求
function getZoomHeight(aPoints) {
    return new Promise((resolve, reject) => {
        axios({
            url: process.env.API_ROOT + "/carpet?points=" + aPoints,
            cancelToken: new axios.CancelToken(c => { cancel.push(c) })
        }).then(res => {
            console.log(res.status)
            if (res.status == 200) {

                // console.log(res.data.data)
                resolve(res.data)
                    // console.log(res.data)
                    // console.log(res.data)
                    // maxHeight.push(res.data.data.stats.max)
                    // console.log(maxHeight)
            } else {
                reject('失败')
            }
        }).catch(() => {
            reject('失败')
        })
    })
}
//计算外扩点是否需要切分
export function getNewPoint(point1, point2, map) {
    let limitArea = 8000000
    let limitWidth = 30
    let angle1 = calcAngle([point2, point1], map)
    let angle2 = calcAngle([point1, point2], map)
    angle1 = (angle1 + 45) > 180 ? (angle1 + 45 - 360) : (angle1 + 45);
    angle2 = (angle2 + 45) > 180 ? (angle2 + 45 - 360) : (angle2 + 45);
    // console.log(angle1, angle2)
    let a1 = getLonAndLat(point1.lng, point1.lat, angle1, 2 * limitWidth * Math.sqrt(2))
    let a2 = getLonAndLat(point2.lng, point2.lat, angle2, 2 * limitWidth * Math.sqrt(2))
    let dis = computedMethod(1, { point1, point2 })
        // if ((dis + 2 * limitWidth) < limitArea / (2 * limitWidth)) {
    if (dis <= 4000) {
        return [a1, a2]
    } else {
        let newPoint = {
            lng: (point1.lng + point2.lng) / 2,
            lat: (point1.lat + point2.lat) / 2
        }
        let arr = getNewPoint(point1, newPoint, map)
        let arr1 = getNewPoint(newPoint, point2, map)
        return [...arr, ...arr1]
    }
}
//获取计算数组
export function getComputedArray(points, map) {
    let arr = []
    let funArray = []
    for (let index = 1; index < points.length; index++) {
        let arr1 = getNewPoint(points[index - 1], points[index], map)
        arr = [...arr, ...arr1]
    }
    for (let index = 1; index < arr.length; index += 2) {
        let ap = arr[index - 1].lat + ',' + arr[index - 1].lng + ',' + arr[index].lat + ',' + arr[index].lng
        funArray.push(ap)
    }
    return funArray;
}
//限制并发数
export function asyncPools(poolLimit, array, fnInter, progressFun) {
    let doing = [];
    let i = 0;
    let ret = [];
    let progressIndex = 0;

    function pp() {
        if (i >= array.length) {
            return Promise.resolve(); //最后一个resolve状态，会进入外层返回Promise.then
        }
        let e = fnInter(array[i++]);
        e.then(() => {
            doing.splice(doing.indexOf(e), 1)
            progressIndex++;
            let progress = parseInt(progressIndex * 100 / array.length)
            progressFun(progress)
        })
        doing.push(e);
        ret.push(e);
        if (doing.length >= poolLimit) {
            return Promise.race(doing).then(pp); //return返回
        } else {
            return Promise.resolve().then(pp); //改写一下保证then链式调用
        }
    }
    return pp().then(() => Promise.all(ret)); //只有当array结束，最后一个resolve才会进入then
}