<template>
  <div class="analyse-canvas">
    <div class="title" :style="titleStyles" v-if="title">{{ title }}</div>
    <slot name="content"></slot>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    titleStyle: Object,
  },
  computed: {
    // titleStyles() {
    //   return {
    //     "letter-spacing": this.$language == "chinese" ? "3px" : 0,
    //   };
    // },
  },
  data() {
    return {
      titleStyles: null,
    };
  },
  watch: {
    titleStyle: {
      deep: true,
      handler: function () {
        let obj = Object.assign(
          { "letter-spacing": this.$language == "chinese" ? "3px" : 0 },
          this.titleStyle
        );
        this.titleStyles = obj;
      },
    },
  },
  created() {
    let obj = Object.assign(
      { "letter-spacing": this.$language == "chinese" ? "3px" : 0 },
      this.titleStyle
    );
    this.titleStyles = obj;
  },
};
</script>

<style lang="less" scoped>
.analyse-canvas {
  min-height: 150px;
  min-width: 250px;
  // border: 1px solid rgba(0, 255, 255, 1);
  border-radius: 5px;
  // box-shadow: 0px 0px 5px 5px rgba(0,0,255, 0.35) inset;
  padding: 5px;
  background-color: #eee;
  .title {
    font-weight: 700;
    // color: rgb(0, 255, 255);
    font-size: 18px;
    letter-spacing: 3px;
    margin-bottom: 12px;
    color: #333;
  }
}
</style>