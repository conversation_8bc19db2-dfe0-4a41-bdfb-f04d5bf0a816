<!-- 巡检排行榜 -->
<template>
  <bg-layout :title="language.title" class="lnspection-leaderboard">
    <template v-slot:content>
      <div class="" style="height: 100%; padding: 0 20px">
        <!--tab导航 -->
        <div class="tab-nav">
          <div
            class="tab-row"
            v-for="(item, index) in checkingType"
            :key="index"
            @click="cutTab(item)"
          >
            <img
              :src="
                params.sort !== item.value
                  ? imgList.paihangbTag
                  : imgList.paihangbTagSelect
              "
              alt=""
            />
            <div class="tab-row-title" :style="titleStyle">
              {{ item.label }}
            </div>
          </div>
        </div>

        <!-- 表格数据 -->
        <div class="table-data">
          <scroll-list
            class="table-tbody scrollbar-style"
            ref="scrollList"
            urlName="insRanking"
            pmd="page"
            :params="params"
            :pageSize="15"
          >
            <template v-slot:content="scope">
              <div
                class="table-tbody-tr"
                style="width: 100%; display: flex"
                :style="tableTrStyle(index, scope)"
                v-for="(item, index) in scope.data"
                :key="index"
              >
                <div style="width: 28px" class="tbody-cell">
                  <div class="table-index">
                    {{ index + 1 }}
                  </div>
                </div>
                <div
                  style="
                    width: 60%;
                    text-align: left;
                    color: rgb(36, 191, 223);
                    letter-spacing: 2px;
                  "
                  class="tbody-cell ml14"
                >
                  {{ item.name }}
                </div>
                <div style="width: 30%" class="tbody-cell">
                  <span v-show="params.sort == 10">
                    {{ item.flight_count }} {{ units.time }}
                  </span>
                  <span v-show="params.sort == 20">
                    {{ item.flight_mileage }} {{ units.m }}
                  </span>
                  <span v-show="params.sort == 30">
                    {{ item.result_count }} {{ units.PCs }}
                  </span>
                </div>
              </div>
            </template>
          </scroll-list>
        </div>
      </div>
    </template>
  </bg-layout>
</template>

<script>
import bgLayout from "../components/bgLayout.vue";
import paihangbTag from "@/assets/img/home/<USER>";
import paihangbTagSelect from "@/assets/img/home/<USER>";
import scrollList from "@/components/scrollList/index.vue";
export default {
  components: {
    bgLayout,
    scrollList,
  },
  data() {
    return {
      imgList: {
        paihangbTag,
        paihangbTagSelect,
      },
      params: {
        sort: 10,
      },
      isInit: false,
    };
  },
  computed: {
    checkingType() {
      return this.$store.state.dict.checkingType;
    },
    language() {
      return this.$languagePackage.home.rankingList;
    },
    units() {
      return this.$languagePackage.unit;
    },
    titleStyle() {
      return {
        "letter-spacing": this.$language == "chinese" ? "3px" : 0,
      };
    },
  },
  mounted() {},
  methods: {
    tableTrStyle: function (index, scope) {
      let botder = index % 2 == 0 ? "none" : "1px solid #2f6099";
      return {
        background: index % 2 == 0 ? "" : "rgba(5, 21, 45, 0.7)",
        borderTop: botder,
        borderBottom: botder,
        animationDelay:
          this.isInit || scope.current != 1 ? "0s" : 1 + 0.2 * index + "s",
      };
    },
    cutTab: function (item) {
      this.isInit = true;
      this.params.sort = item.value;
      this.$refs.scrollList.refresh();
    },
  },
};
</script>

<style lang="less" scoped>
.lnspection-leaderboard {
  //   padding: 0 20px;

  .tab-nav {
    display: flex;
    // color: #fff;
    .tab-row {
      flex-grow: 1;
      cursor: pointer;
      height: 34px;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
      .tab-row-title {
        letter-spacing: 3px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 34px;
      }
    }
  }

  .table-data {
    height: calc(100% - 94px);
    position: relative;
    // margin: 10px;
    margin-top: 10px;
    .table-tbody {
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      //   border: 1px solid #2f6099;
      border-bottom: none;
      .table-tbody-tr {
        animation-name: tbodyTr;
        animation-duration: 0.4s;
        animation-fill-mode: forwards; // 保留动画最后的状态
        opacity: 0;
      }
      @keyframes tbodyTr {
        0% {
          transform: rotateX(90deg);
        }
        50% {
          opacity: 1;
        }
        100% {
          transform: rotateX(0deg);
          opacity: 1;
        }
      }
    }

    .tbody-cell {
      padding: 8px 0;
      font-size: 12px;
      // color: #fff;
      text-align: center;
    }
  }
}
</style>