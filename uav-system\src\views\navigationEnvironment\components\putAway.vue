<template>
  <div class="put-away">
    <transition :name="transitionName">
      <div :style="{ height: contentHeight }" v-show="value" v-if="isShowMain">
        <slot name="main"></slot>
      </div>
    </transition>

    <slot name="elseMain"></slot>

    <!-- 收起 -->
    <el-tooltip
      class="item"
      effect="dark"
      :content="tooltipText"
      :placement="placement"
    >
      <div
        class="put-away-show"
        v-show="!value"
        @click="cutPutAwayState"
        :style="styles"
        v-if="isShowAway"
      >
        <img-icon name="pack-up" width="50" height="43"></img-icon>
        <slot name="showContent"></slot>
      </div>
    </el-tooltip>

    <!-- 收起按钮 -->
    <transition :name="transitionName">
      <div class="" v-show="isShowAway">
        <div
          class="put-away-button"
          v-show="value"
          @click="cutPutAwayState"
          :style="buttonStyle"
        >
          <slot name="awayIcon">
            <i class="el-icon-arrow-right"></i>
          </slot>
        </div>
      </div>
    </transition>
    
  </div>
</template>

<script>
import imgIcon from "@/components/imgIcon/index";
export default {
  components: {
    imgIcon,
  },
  props: {
    value: [Boolean, String],
    transitionName: {
      type: String,
      default: "el-fade-in-linear",
    },
    styles: {
      type: Object,
      default: () => {
        return {};
      },
    },
    contentHeight: {
      type: String,
      default: "100%",
    },
    buttonStyle: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isShowMain: {
      type: Boolean,
      default: true,
    },
    // 提示文本
    tooltipText: String,
    // 出现方向
    placement: String,
    isShowAway: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isOpen: this.value,
    };
  },

  methods: {
    cutPutAwayState: function () {
      this.$emit("input", !this.value);
    },
  },
};
</script>

<style lang="less" scoped>
.put-away {
  .put-away-show {
    padding: 10px;
    // background-color: rgba(18, 27, 36, 0.5);
    // color: rgb(141, 155, 171);
    width: 64px;
    text-align: center;
    font-size: 12px;
    position: absolute;
    z-index: 22;
    border-radius: 6px;
  }

  .put-away-button {
    position: absolute;
    width: 24px;
    height: 30px;
    display: flex;
    // background-color: #000;
    // color: rgba(27, 229, 13, 1);
    z-index: 999;
    // left: -24px;
    top: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 22;
  }
}
</style>