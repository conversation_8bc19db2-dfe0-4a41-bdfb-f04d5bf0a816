<!--  -->
<template>
  <div class="basic-inside">
    <!-- 抗闪烁 -->
    <div class="flicker" v-if="showType == 'flicker'">
      <div class="flicker-input">{{ flickerInfo.value }}</div>
      <div class="flicker-content">
        <div
          class="content-item"
          v-for="(item, index) in flickerInfo.list"
          :key="index"
          @click="flickerItem(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <!-- 去雾 -->
    <div class="defogging" v-if="showType == 'defogging'">
      <div
        class="defogging-item"
        v-for="(item, index) in defogging.list"
        :key="index"
      >
        <div
          class="item-main"
          :class="defogging.index == item.value ? 'select-item-style' : ''"
          @click="cutDefoggingType(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <!-- 网格 -->
    <div class="gridding" v-if="showType == 'gridding'">
      <div class="gridding-item">
        <div class="item-header">{{language.gridLine}}</div>
        <div
          class="item-content item-one"
          :class="gridding.value == 1 ? 'gridding-select-style' : ''"
          @click="cutGriddingType(1)"
        >
          <div class="content-row" v-for="item in 9" :key="item"></div>
        </div>
      </div>

      <div class="gridding-item ml50" style="width: calc(50% - 50px)">
        <div class="item-header">{{language.centerPoint}}</div>
        <div
          class="item-content item-center"
          :class="gridding.value == 2 ? 'gridding-select-style' : ''"
          @click="cutGriddingType(2)"
        >
        <el-image :src="gridding.value == 2?centerUrl_1:centerUrl"></el-image>
          <!-- <div class="item-center-circle"></div> -->
        </div>
      </div>

      <div class="gridding-item">
        <div class="item-header">{{language.gridlinesALines}}</div>
        <div
          class="item-content item-one"
          :class="gridding.value == 3 ? 'gridding-select-style' : ''"
          @click="cutGriddingType(3)"
        >
          <div class="content-row" v-for="item in 9" :key="item"></div>
          <div class="diagonal"></div>
          <div class="diagonal right"></div>
        </div>
      </div>

      <div class="gridding-item ml50" style="width: calc(50% - 50px)">
        <div class="item-header">{{language.control}}</div>
        <div class="item-content" style="width: 130px">
          <div class="shut-gridding ml50" @click="griddingShut">{{language.close}}</div>
        </div>
      </div>
    </div>

    <!-- 格式化 -->
    <div class="formatting" v-if="showType == 'formatting'">
      <div class="formatting-title">{{language.formatting}}</div>
      <div class="formatting-main" v-if="!formatLoading">
        {{language.formatTip}}
      </div>
      <div class="formatting-bot" v-if="!formatLoading">
        <el-button
          size="mini"
          type="danger"
          style="background-color: #ff0000"
          class="mr50"
          @click="formatSDcard"
        >
          {{language.submit}}
        </el-button>
        <el-button
          size="mini"
          style="background-color: rgba(0, 0, 0, 0); color: #ffffff"
          @click="returnNext"
        >
          {{language.cancel}}
        </el-button>
      </div>
      <div class="formatting-load" v-if="formatLoading">
        <i class="el-icon-loading"></i> {{language.formatLoad}}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // showType: "gridding",

      flickerInfo: {
        value: "22",
        list: [
          { label: 1, value: "1" },
          { label: 2, value: "2" },
          { label: 3, value: "3" },
          { label: 0, value: "0" },
          { label: 4, value: "4" },
          { label: 5, value: "5" },
          { label: 6, value: "6" },
          { label: "删除", value: "remove" },
          { label: 7, value: "7" },
          { label: 8, value: "8" },
          { label: 9, value: "9" },
          { label: "完成", value: "save" },
        ],
      },

      defogging: {
        index: "0",
        list: [],
      },

      // 网格
      gridding: {
        value: "3",
        shut: true,
      },
      centerUrl:require("@/assets/img/centerGridding.png"),
      centerUrl_1:require("@/assets/img/centerGridding_1.png")

    };
  },
  computed: {
    showType() {
      return this.$store.state.camera.moreInsideType;
    },
    formatLoading() {
      return this.$store.state.camera.formatLoading;
    },
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS || {};
    },
    formatState() {
      return this.$store.state.equipment.staveTwoData.sdcard_format_state;
    },
    language() {
      return this.$languagePackage.navigation.cameraConfig.more;
    },
  },
  watch: {
    formatLoading(val) {
      if (!val) {
        this.$store.commit("setShowComponents", "more");
        this.$store.commit("setHierarchy", 1);
      }
    },
    formatState(val, oldVal) {
      if (oldVal == 1) {
        if (val == 2) {
          this.$message.success(this.language.formatSuccess);
          this.$store.commit("setFormatLoading", false);
        } else if (val == 3) {
          this.$message.error(this.language.formatError);
          this.$store.commit("setFormatLoading", false);
        }
      }
    },
  },
  created() {
    // 去雾类型
    this.defogging.list = this.$store.state.dict.defoggingType;
    this.defogging.index = this.$store.state.camera.defoggingType;

    // 闪烁值
    this.flickerInfo.value = this.$store.state.camera.flickerVal;

    // 网格类型
    let val = this.$store.state.camera.gridTypeVal;
    this.gridding.value = val;
    this.gridding.shut = val && val != "0" && val !== 0 ? true : false;
    for (let index = 0; index < this.flickerInfo.list.length; index++) {
      if(this.flickerInfo.list[index].value=="remove"){
        this.flickerInfo.list[index].label=this.language.del
      }
      if(this.flickerInfo.list[index].value=="save"){
        this.flickerInfo.list[index].label=this.language.complete
      }
      
    }
  },
  methods: {
    flickerItem: function (item) {
      if (item.value == "remove") {
        this.flickerInfo.value = "";
      } else if (item.value == "save") {
        //   this.flickerInfo.value = 0;
        this.$store.commit("setFlickerVal", this.flickerInfo.value);
        this.$store.commit("setShowComponents", "more");
        this.$store.commit("setHierarchy", 1);
      } else {
        this.flickerInfo.value += item.value;
      }
    },

    // 切换去雾类型
    cutDefoggingType: function (item) {
      this.defogging.index = item.value;
      this.$store.commit("setDefoggingType", item.value);
      this.returnNext();
    },
    // 切换网格类型
    cutGriddingType: function (val) {
      this.gridding.value = val;
      this.$store.commit("setGridTypeVal", val);
      this.returnNext();
    },
    returnNext: function () {
      this.$store.commit("setShowComponents", "more");
      this.$store.commit("setHierarchy", 1);
    },
    formatSDcard: function () {
      if (this.$store.state.equipment.staveTwoData.sdcard_state==2) {
        this.$message.error(this.language.noSD);
        return false;
      }
      let params = {
        format: 0,
        cmd_type: 14,
      };
      this.equipmentWS &&
        this.equipmentWS.manualSend &&
        this.equipmentWS.manualSend(params, 402);
      this.$store.commit("setFormatLoading", true);
    },
    griddingShut: function () {
      this.cutGriddingType(0);
    },
  },
};
</script>

<style lang="less" scoped>
.basic-inside {
  width: 100%;
  .flicker {
    width: 398px;
    margin: 0 auto;

    .flicker-input {
      margin: 64px 0 36px 0;
      width: 394px;
      height: 26px;
      // background-color: #0b0f15;
      // border: 2px solid #787b7d;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      // color: #fff;
    }
    .flicker-content {
      display: flex;
      flex-wrap: wrap;
      .content-item {
        width: 95px;
        height: 28px;
        // background-color: #0b0f15;
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        // color: #fff;
        margin: 0 4px 4px 0;
        font-size: 12px;
        cursor: pointer;
      }
    }
  }
  .defogging {
    padding: 0 50px;
    display: flex;
    flex-wrap: wrap;
    .defogging-item {
      width: 50%;
      margin: 58px 0 16px 0;
      .item-main {
        width: 167px;
        height: 22px;
        // border: 1px solid #fff;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        // color: #fff;
        font-size: 12px;
        cursor: pointer;
      }
      .select-item-style {
        // color: #398bcc;
        // border-color: #398bcc;
      }
    }
  }
  .gridding {
    padding: 0 50px;
    padding-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    .gridding-item {
      width: 50%;
      .item-header {
        margin: 22px 0 11px 0;
        // color: #fff;
        font-size: 12px;
      }
      .gridding-select-style {
        // border-color: #398bcc !important;
        .content-row {
          // border-color: #398bcc !important;
        }

        .item-center-circle,
        .diagonal {
          // background-color: #398bcc !important;
        }
      }

      @border-style: 1px solid #fff;
      .item-one {
        border-right: @border-style;
        border-top: @border-style;
        margin-left: 50px;
      }
      .item-content {
        display: flex;
        position: relative;
        flex-wrap: wrap;
        width: 81px;
        height: 51px;
        overflow: hidden;
        .el-image{
          width: 120%;
          margin-top: -20%;
          // margin-top: -50px;
          // height: 100%;
        }

        .diagonal {
          height: 1px;
          width: 81px;
          position: absolute;
          top: 24.5px;
          left: 0;
          // background-color: #ffffff;
          transform: rotateZ(32deg) scale(1.2);
        }
        .right {
          transform: rotateZ(-32deg) scale(1.2) !important;
        }

        .content-row {
          width: 26px;
          height: 16px;
          border-left: @border-style;
          border-bottom: @border-style;
        }
      }

      .item-center {
        width: 81px;
        height: 51px;
        border: @border-style;
        margin-left: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        .item-center-circle {
          width: 7px;
          height: 7px;
          border-radius: 50%;
          // background-color: #fff;
        }
      }
    }
  }

  .shut-gridding {
    width: 180px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    // color: #fff;
    font-size: 12px;
    // border: 1px solid #fff;
    border-radius: 8px;
    cursor: pointer;
    &:hover {
      // color: #398bcc;
      // border-color: #398bcc;
    }
  }

  .formatting {
    .formatting-title {
      font-size: 16px;
      // color: #fff;
      font-weight: 700;
      text-align: center;
      // padding: 16px 0;
      margin: 32px 0 38px 0;
    }
    .formatting-main {
      margin: 0 125px 0 118px;
      // border: 1px solid #ff0000;
      padding: 12px 24px 14px 32px;
      // color: #ffffff;
      line-height: 2em;
      border-radius: 8px;
      font-size: 14px;
    }
    .formatting-bot {
      margin: 40px 0 0 0;
      text-align: center;
    }
    .formatting-load {
      width: 100%;
      color: #fff;
      font-size: 18px;
      text-align: center;
      margin: 50px 0;
    }
  }
}
</style>