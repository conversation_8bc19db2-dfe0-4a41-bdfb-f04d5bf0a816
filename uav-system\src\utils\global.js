//本地使用端口，线上不使用端口
// const BASE_URL = 'http://app.walkera.com' // 阿里云（线上环境）Host总地址
// const WS_URL = 'ws://app.walkera.com/' // 阿里云（线上环境）Host总地址
// const BASE_URL = 'https://app.walkera.cn/' //天翼云（测试环境）Host总地址
// const WS_URL = 'wss://app.walkera.cn/' //天翼云（测试环境）Host总地址
// const BASE_URL = 'http://************'; // 设置全局 URL
// const WS_URL = 'ws://************/'
// export default {
//     BASE_URL,
//     WS_URL,
// }

/**
 * 不同环境配置不同的信息
 * npm run dev：本地开发
 * npm run test：测试
 * npm run prod：打包
 */
let env = process.env.NODE_ENV;

let config = {
    // 开发环境
    development: {
        BASE_URL: "http://************", // 请求路径
        // BASE_URL: "http://*************", // 请求路径
        WS_URL: "ws://************:8192/", // websocket请求,
        DEVICE_PROT: ":8190", // 设备后缀
        USER_PROT: ":8188", // 用户后缀
        ZONE_PROT: ":8096", //禁飞区后缀
        MT_PROT: ':8160', //瓦片后缀
    },
    //nb环境
    // 测试环境
    // testing: {
    //     BASE_URL: "https://app.walkera.cn",
    //     BASE_URL_FLY: "https://fly.walkera.cn",
    //     WS_URL: "wss://app.walkera.cn/wb_pull/",
    //     DEVICE_PROT: "/ndb",
    //     USER_PROT: "/nub",
    //     ZONE_PROT: "/fc",
    //     MT_PROT: '/mt',
    //     VIDEO_PROT: '/fv-bate',
    // },
    // // 线上环境
    // production: {
    //     BASE_URL: "https://app.walkera.cn",
    //     BASE_URL_FLY: "https://fly.walkera.cn",
    //     WS_URL: "wss://app.walkera.cn/wb_pull/",
    //     DEVICE_PROT: "/ndb",
    //     USER_PROT: "/nub",
    //     ZONE_PROT: "/fc",
    //     MT_PROT: '/mt',
    //     VIDEO_PROT: '/fv-bate',
    // }

    //nest环境
    // 测试环境
    testing: {
        BASE_URL: "https://app.walkera.cn",
        BASE_URL_FLY: "https://fly.walkera.cn",
        WS_URL: "wss://app.walkera.cn/wn_pull/",
        // BASE_URL: "http://192.168.1.8",
        // WS_URL: "ws://192.168.1.8/wn_pull/",
        // BASE_URL: "https://5gdrone.eu",
        // WS_URL: "wss://5gdrone.eu/wn_pull/",
        DEVICE_PROT: "/nedv",
        USER_PROT: "/neus",
        ZONE_PROT: "/fc",
        MT_PROT: '/mt',
        VIDEO_PROT: '/fv-bate',
    },
    // 线上环境
    production: {
        // BASE_URL: "http://app.walkera.com",
        // WS_URL: "ws://app.walkera.com/wn_pull/",
        BASE_URL: "https://app.walkera.cn",
        BASE_URL_FLY: "https://fly.walkera.cn",
        WS_URL: "wss://app.walkera.cn/wn_pull/",
        WS_URL: "wss://app.walkera.cn/wn_pull/",
        // BASE_URL: "http://192.168.1.8",
        // WS_URL: "ws://192.168.1.8/wn_pull/",
        // BASE_URL: "https://5gdrone.eu",
        // WS_URL: "wss://5gdrone.eu/wn_pull/",
        DEVICE_PROT: "/nedv",
        USER_PROT: "/neus",
        ZONE_PROT: "/fc",
        MT_PROT: '/mt',
        VIDEO_PROT: '/fv',
    }
}

export default config[env];