<template>
  <custom-dialog :title="language.title" :visible.sync="isShow">
    <template v-slot:main>
      <el-form :model="form" label-width="100px">
        <el-form-item :label="language.label.langue">
          <el-radio-group v-model="form.language" @change="languageChange">
            <el-radio
              :label="item.value"
              v-for="item in languageList"
              :key="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- <el-form-item :label="language.label.theme">
          <el-radio-group v-model="form.theme" @change="themeChange">
            <el-radio
              :label="item.value"
              v-for="item in themeList"
              :key="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
    </template>
  </custom-dialog>
</template>

<script>
import customDialog from "@/components/customDialog/index.vue";
import { setLocalStorage, getLocalStorage } from "@/utils/storage.js";
export default {
  name: "system-set",
  components: {
    customDialog,
  },
  data() {
    return {
      isShow: false,
      form: {
        language: this.$language,
        theme: "default",
      },
    };
  },
  computed: {
    language() {
      return this.$languagePackage.user.systemSet;
    },
    languageList() {
      return this.$store.state.dict.languageList;
    },
    themeList() {
      return this.$store.state.dict.themeList;
    },
  },
  methods: {
    open: function () {
      this.isShow = true;

      let theme = getLocalStorage("theme");
      this.form.theme = theme || "default";
    },
    languageChange: function (item) {
      setLocalStorage("language", item);
      this.$loading({
        lock: true,
        text: this.language.message.cut,
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      setTimeout(() => {
        this.$router.go(0);
      });
      // this.$router.go(0);
    },
    themeChange: function (item) {
      setLocalStorage("theme", item);
      document.getElementById("app").setAttribute("class", `theme-${item}`);
    },
  },
};
</script>