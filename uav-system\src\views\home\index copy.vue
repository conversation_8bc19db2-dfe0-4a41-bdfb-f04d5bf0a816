<!-- 首页 -->
<template>
  <div id="home">
    <div class="home-map" id="home-map"></div>

    <!-- 左侧 -->
    <div class="home-left">
      <div class="home-left-main" ref="mainLeft">
        <!-- 无人机设备列表 -->
        <uav-equipment-list
          style="height: calc(50% - 10px)"
          @cutEquipment="cutEquipment"
          ref="uavEquipmentList"
        />
        <!-- 巡检记录 -->
        <inspection-record style="height: calc(50% - 20px); margin-top: 20px" />
      </div>
    </div>

    <div class="home-mian">
      <!-- 数据总览 -->
      <data-screening ref="dataScreening"></data-screening>
    </div>

    <!-- 右侧 -->
    <div class="home-right">
      <div class="home-right-main">
        <!-- 巡检排行榜 -->
        <!-- <inspection-ieaderboard style="height: calc(50% - 10px)" /> -->
        <!-- 定时任务列表 -->
        <task-list style="height: calc(50% - 10px)" />
        <!-- 飞行数据 -->
        <flight-data
          ref="flightData"
          style="height: calc(50% - 20px); margin-top: 20px"
        />
      </div>
    </div>

    <equip-list ref="equipList"></equip-list>
  </div>
</template>
    
<script>
import uavEquipmentList from "./modules/uavEquipmentList.vue";
import inspectionRecord from "./modules/inspectionRecord.vue";
import dataScreening from "./modules/dataScreening.vue";
import inspectionIeaderboard from "./modules/inspectionIeaderboard.vue";
import taskList from "./modules/taskList.vue";
import flightData from "./modules/flightData.vue";
import equipList from "./modules/equipList.vue";
import maps from "@/utils/maps";
import request from "@/utils/api";
import { wgs84_to_gcj02 } from "../../utils/wgs84_to_gcj02";

export default {
  name: "home",
  components: {
    uavEquipmentList,
    inspectionRecord,
    dataScreening,
    inspectionIeaderboard,
    flightData,
    equipList,
    taskList,
  },
  data() {
    return {
      // websocket: null,
      deviceList: [],
      deviceMarker: [],
      map: null,
      iconList: {},
      fireMarker: "",
      alarmList: "",
      markerCluster: "",
      imgList: {
        1: {
          imgSrc: require("@/assets/img/alarm.png"),
          state: "发现火情",
        },
        10: {
          imgSrc: require("@/assets/img/alarm1.png"),
          state: "处理中",
        },
        20: {
          imgSrc: require("@/assets/img/alarm2.png"),
          state: "处理完成",
        },
      },
    };
  },
  created() {
    this.$store.commit("setMultiMessage", {
      key: "changeDeviceItemState",
      message: this.changeDeviceItemState,
    });
  },
  mounted() {
    this.$nextTick(() => {
      this.initMap();
    });
  },
  methods: {
    initMap: function () {
      maps
        .initMap("home-map", {
          mapStyle: "amap://styles/bab278a9545b11927fb15bb5f7d47be2",
          locationCode: false,
        })
        .then((map) => {
          this.map = map;
          map.setZoom(15);
          this.getDeviceData();
        });
    },

    //获取设备列表数据
    async getDeviceData() {
      let data = {
        page: 0,
        size: 100,
        type: 0,
      };
      data.pmd = data.page.toString() + data.type.toString();
      await request("deviceList", data).then((res) => {
        this.deviceList = res.data.list ? res.data.list : [];
      });
      setTimeout(() => {
        this.drawIcon();
      }, 2000);
    },
    // 绘制设备图标
    drawIcon: function () {
      let points = [];

      let state = this.$languagePackage.home.index.state;
      let iconList = {
        0: {
          icon: require("@/assets/img/outLineHome.png"),
          title: state[2],
        },
        1: {
          icon: require("@/assets/img/inLineHome.png"),
          title: state[1],
        },
        2: {
          icon: require("@/assets/img/workingHome.png"),
          title: state[3],
        },
      };

      for (let i = 0; i < this.deviceList.length; i++) {
        let item = this.deviceList[i];
        if (item.type !== 200) {
          points.push({
            lnglat: wgs84_to_gcj02(item.lon_int / 1e7, item.lat_int / 1e7),
            id: item.sn_id,
            name: item.name,
            state: item.is_pull_on ? 2 : item.is_push_on ? 1 : 0,
            type: item.type,
          });

          // if (item.is_pull_on) {
          //   points[i].state = 2;
          // } else if (item.is_push_on) {
          //   points[i].state = 1;
          // }
        }
      }

      AMap.plugin(["AMap.MarkerCluster"], () => {
        let markerCluster = new AMap.MarkerCluster(this.map, points, {
          maxZoom: 12,
          gridSize: 60,
          clusterByZoomChange: true,
          // 自定义聚合点样式
          renderClusterMarker: (context) => {
            let content =
              "<div class='renderMarker'><span>" +
              context.count +
              "</span></div>";
            context.marker.setContent(content);
          },
          // 自定义非聚合点样式
          renderMarker: (context) => {
            let item = context.data[0];
            let iconItem = iconList[item.state];
            let icon = new AMap.Icon({
              // 图标的取图地址
              image: iconItem.icon,
              imageSize: new AMap.Size(50, 50),
            });
            let offset = new AMap.Pixel(-15, -30);
            context.marker.setIcon(icon);
            context.marker.setOffset(offset);
            let title = item.name + " \n" + iconItem.title;
            context.marker.setTitle(title);
            context.marker.setExtData(context.data[0]);
          },
        });
        markerCluster.on("click", this.markerClusterClick);
        this.markerCluster = markerCluster;
      });
    },
    markerClusterClick(e) {
      if (e.clusterData.length) {
        return false;
      }
      if (e.marker.getExtData().state) {
        const newwin = this.$router.resolve({
          path: `/navigationEnvironment`,
          query: {
            sn_id: e.marker.getExtData().id,
            type: e.marker.getExtData().type,
            state: e.marker.getExtData().state == 2 ? 2 : 1,
          },
        });
        window.open(newwin.href, "_blank");
      }
    },
    cutEquipment: function (item) {
      if (!item) {
        return false;
      }
      this.map.setCenter(
        wgs84_to_gcj02(item.lon_int / 1e7, item.lat_int / 1e7)
      );
      let zoom = this.map.getZoom();
      zoom = zoom > 18 ? zoom : 18;
      this.map.setZoom(zoom);
    },
    drawFireBehaviorIcon: function (obj) {
      let center = this.changeLngLat(obj);
      let img = this.imgList[obj.state].imgSrc;
      let icon = new AMap.Icon({
        // 图标的取图地址
        image: img,
        imageSize: new AMap.Size(35, 50),
      });
      let str = "";
      if (obj.file_list && obj.file_list[0]) {
        if (obj.file_list[0].file_type == 10) {
          str = `<img src="${obj.file_list[0].o_url}" style="width:100px;">`;
        } else {
          str = `<video style="width:120px;" autoplay loop><source src="${obj.file_list[0].o_url}"></video>`;
        }
      }
      // else{
      //   str=`<video style="width:120px;" autoplay loop><source src="https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.webm" type="video/mp4"></video>`
      // }
      // https://www.bilibili.com/video/BV1hR4y1U7Ld?t=1.7
      // let str = `<img src='https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fsafe-img.xhscdn.com%2Fbw%2Fb833c843-2c70-4f4d-8325-845c96d3c5d8%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fsafe-img.xhscdn.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1687481766&t=4e536b8e2a3df8012f797f90e6f6ac6c' style="width:100px;"/>`;
      let fireMarker = new AMap.Marker({
        position: center,
        icon: icon,
        offset: new AMap.Pixel(-15, -30),
        title: this.imgList[obj.state].state + "，经纬度：" + center,
        label: str ? { content: str, direction: "top" } : "",
        extData: {
          id: obj.wain_id,
        },
      });
      if (obj.state !== 20) {
        fireMarker.dom.classList.add("fireMarker");
        fireMarker.on("click", this.fireMarkerClick);
      }
      fireMarker.setMap(this.map);
      return fireMarker;
    },
    editFireBehaviorIcon(index, state) {
      let img = this.imgList[state].imgSrc;
      let icon = new AMap.Icon({
        // 图标的取图地址
        image: img,
        imageSize: new AMap.Size(35, 50),
      });
      let title =
        this.imgList[state].state +
        "，经纬度：" +
        this.fireMarker[index].getTitle().split("，经纬度：")[1];
      this.fireMarker[index].setIcon(icon);
      this.fireMarker[index].setTitle(title);
      if (state == 20) {
        this.fireMarker[index].dom.classList.remove("fireMarker");
        this.fireMarker[index].off("click", this.fireMarkerClick);
      }
    },
    fireMarkerClick(e) {
      let i = this.alarmList.findIndex((item) => {
        return item.wain_id == e.target.getExtData().id;
      });
      this.$refs.equipList.open(e, this.alarmList[i]);
    },
    changeLngLat(obj) {
      return obj.coo_type == 1
        ? wgs84_to_gcj02(obj.lon_int / 1e7, obj.lat_int / 1e7)
        : [obj.lon_int / 1e7, obj.lat_int / 1e7];
    },
    //获取告警信息列表
    getAlarmList(msg_id, data) {
      if (msg_id == 293) {
        if (data && data.list) {
          if (this.alarmList) {
            for (let index = 0; index < data.list.length; index++) {
              let i = this.alarmList.findIndex((item) => {
                return item.wain_id == data.list[index].wain_id;
              });
              if (i == -1) {
                let fireMarker = this.drawFireBehaviorIcon(data.list[index]);
                this.fireMarker.push(fireMarker);
                let lnglat = this.changeLngLat(data.list[index]);
                this.map.setCenter(lnglat);
              } else {
                if (this.alarmList[i].state !== data.list[index].state) {
                  let j = this.fireMarker.findIndex((item) => {
                    return item.getExtData().id == data.list[index].wain_id;
                  });
                  this.editFireBehaviorIcon(j, data.list[index].state);
                }
              }
            }
            this.alarmList = data.list;
          } else {
            this.fireMarker = [];
            let arr = [];
            let arr1 = [];
            for (let index = 0; index < data.list.length; index++) {
              let fireMarker = this.drawFireBehaviorIcon(data.list[index]);
              this.fireMarker.push(fireMarker);
              if (data.list[index].state == 1) {
                arr.push(data.list[index]);
              }
              if (data.list[index].state == 10) {
                arr1.push(data.list[index]);
              }
            }
            this.alarmList = data.list;
            let lnglat = [];
            if (arr.length) {
              lnglat = this.changeLngLat(arr[arr.length - 1]);
            } else if (arr1.length) {
              lnglat = this.changeLngLat(arr1[arr1.length - 1]);
            } else {
              lnglat = this.changeLngLat(data.list[data.list.length - 1]);
            }
            this.map.setCenter(lnglat);
          }
        }
      }
      if (msg_id == 291) {
        if (data) {
          let i = this.alarmList.findIndex((item) => {
            return item.wain_id == data.wain_id;
          });
          if (i == -1) {
            let fireMarker = this.drawFireBehaviorIcon(data);
            this.fireMarker.push(fireMarker);
            let lnglat = this.changeLngLat(data);
            this.map.setCenter(lnglat);
          } else {
            if (this.alarmList[i].state !== data.state) {
              let j = this.fireMarker.findIndex((item) => {
                return item.getExtData().id == data.wain_id;
              });
              this.editFireBehaviorIcon(j, data.state);
            }
          }
        }
      }
    },
    changeDeviceItemState(msg_id, data) {
      if (msg_id == 110) {
        if (this.markerCluster) {
          this.markerCluster.setMap(null);
          this.markerCluster = "";
          this.getDeviceData();
          this.$refs.uavEquipmentList &&
            this.$refs.uavEquipmentList.refreshList();
        }
      }
    },
  },
};
</script>

<style lang="less">
@sizePx: 16;
#home {
  padding: 20px;
  height: calc(100% - 40px);
  position: relative;
  display: flex;
  .home-mian {
    flex-grow: 1;
    height: 100%;
    flex-grow: 1;
    margin: 0 20 / @sizePx*1rem;
  }

  .home-map {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    .fireMarker {
      .amap-icon img {
        -webkit-animation-name: scaleDraw;

        /*关键帧名称*/

        -webkit-animation-timing-function: ease-in-out;

        /*动画的速度曲线*/

        -webkit-animation-iteration-count: infinite;

        /*动画播放的次数*/

        -webkit-animation-duration: 2s;

        /*动画所花费的时间*/
      }
      .amap-marker-label {
        background-color: transparent;
        border: none;
        padding: 0;
        img {
          background-color: #fff;
          padding: 2px;
          margin-bottom: 3px;
        }
      }
    }
  }

  .home-left {
    width: 20%;
    min-width: 350px;
    height: 100%;
    overflow: hidden;
    z-index: 2;

    .home-left-main {
      width: 100%;
      height: 100%;
    }
  }

  .home-right {
    width: 20%;
    min-width: 350px;
    height: 100%;
    overflow: hidden;
    z-index: 2;
    .home-right-main {
      width: 100%;
      height: 100%;
    }
  }
}
.renderMarker {
  border-radius: 4px;
  padding: 1px;
  span {
    display: inline-block;
    padding: 2px 4px;
    min-width: 32px;
    border-radius: 4px;
    text-align: center;
  }
}
@keyframes scaleDraw {
  /*定义关键帧、scaleDrew是需要绑定到选择器的关键帧名称*/

  0% {
    transform: scale(1);
    /*开始为原始大小*/
  }

  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style>