<template>
  <div class="">
    <div id="container" style="width: 100vw; height: 100vh"></div>
    <!-- 操作列表 -->
    <div class="panorama-operate">
      <div class="controlbar">
        <div class="controlbar-main">
          <div class="compass" ref="compass" @click="restore">
            <div class="pointers"></div>
          </div>
          <div class="pitchUp" @mousedown="pitchUp"></div>
          <div class="pitchDown" @mousedown="pitchDown"></div>
          <div class="rotateLeft" @mousedown="rotateLeft"></div>
          <div class="rotateRight" @mousedown="rotateRight"></div>
        </div>
      </div>

      <!-- 放大缩小 -->
      <div class="zooming">
        <div class="zooming-item" @mousedown="buttonWheels('min')">
          <span class="el-icon-zoom-out"></span>
        </div>
        <div class="zooming-item" @mousedown="buttonWheels('max')">
          <span class="el-icon-zoom-in"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Viewer } from "photo-sphere-viewer";
import "photo-sphere-viewer/dist/photo-sphere-viewer.css";

export default {
  name: "panorama",
  props: {
    src: String,
  },
  data() {
    return {
      bigImg: this.src, //全景图图片路径
      skyBox: null,
      time: null,
      viewer: null,
      zoomLevel: 0,
      position: {
        x: 0,
        y: 0
      }
    };
  },
  created() {
    if (!this.src) {
      let router = this.$route.query;
      this.bigImg = router.imgSrc;
      // this.bigImg = "http://localhost:8000/public/examples/textures/微信图片_20220929101118.jpg";
      // this.bigImg = require("../../assets/img/微信图片_20220929101118.jpg");
    }
  },
  mounted() {
    // 调用全景图函数
    this.$nextTick(() => {
      this.init();
    });
  },
  methods: {
    init: function () {
      const div = document.getElementById("container");
      this.viewer = new Viewer({
        panorama: this.bigImg,
        container: div,
        autorotateIdle: false,
        navbar: false
      });

      const position = this.viewer.getPosition();
      this.position.x = position.longitude;
      this.position.y = position.latitude;
      this.zoomLevel = this.viewer.getZoomLevel();

      // 监听全景图移动
      this.viewer.on("position-updated", (e, coord) => {
        let x = (coord.longitude * 360) / Math.PI;
        let y = (coord.latitude * 360) / Math.PI;
        this.$refs.compass.style.transform = `rotateX(${y}deg) rotateZ(${-x}deg)`;
        this.position.x = coord.longitude;
        this.position.y = coord.latitude;
        
      });
    },

    pitchUp: function () {
      this.upDownMove("up");
    },
    pitchDown: function () {
      this.upDownMove("down");
    },
    upDownMove: function (type) {
      let move = () => {
        let y =
          type == "up" ? (this.position.y += 0.05) : (this.position.y -= 0.05);
        this.viewer.animate({
          longitude: this.position.x,
          latitude: y,
          speed: "100rpm",
        });
      };
      move();
      let time = setInterval(move, 200);
      let mouseupEvent = () => {
        clearInterval(time);
        document.removeEventListener("mouseup", mouseupEvent);
      };
      document.addEventListener("mouseup", mouseupEvent);
    },
    leftRightMove: function (type) {
      let move = () => {
        let x =
          type == "left"
            ? (this.position.x += 0.05)
            : (this.position.x -= 0.05);
        this.viewer.animate({
          longitude: x,
          latitude: this.position.y,
          speed: "100rpm",
        });
      };

      move();
      let time = setInterval(move, 200);
      let mouseupEvent = () => {
        clearInterval(time);
        document.removeEventListener("mouseup", mouseupEvent);
      };
      document.addEventListener("mouseup", mouseupEvent);
    },

    rotateLeft: function () {
      this.leftRightMove("left");
    },
    rotateRight: function () {
      this.leftRightMove("right");
    },

    // 回正
    restore: function () {
      this.viewer.animate({
        longitude: 0,
        latitude: 0,
        speed: "10rpm",
      });
      this.position.x = 0;
      this.position.y = 0;
    },

    // 按钮控制物体大小
    buttonWheels: function (type) {
      let zoomEvent = () => {
        type == "min" ? this.zoomLevel-- : this.zoomLevel++;
        this.viewer.animate({
          zoom: this.zoomLevel,
          speed: "100rpm",
        });
      };
      zoomEvent();
      let time = setInterval(zoomEvent, 100);

      // 鼠标松开取消定时器
      document.addEventListener("mouseup", () => {
        clearInterval(time);
      });
    },

    onWindowResize() {},
  },
};
</script>

<style lang="less" scoped>
.psv-container {
  width: 100%;
  height: 100%;
  .psv-canvas-container {
    width: 100%;
    height: 100%;
  }
}
.panorama-operate {
  position: fixed;
  right: 30px;
  top: 30px;
  // height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 20;
  // display: flex;
  .zooming {
    margin-top: 20px;
    .zooming-item {
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      // color: aqua;
      font-weight: 700;
      border: 1px solid #ccc;

      height: 30px;
      width: 30px;
      font-size: 18px;
      font-weight: 700;
      &:hover {
        background-color: #409eff;
        color: #fff;
      }
      &:nth-child(1) {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        border-bottom: none;
      }
      &:nth-child(2) {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
      }
    }
  }

  .controlbar {
    width: 92px;
    height: 92px;
    .controlbar-main {
      position: relative;
      width: 100%;
      height: 100%;
      background: url("../../assets/img/ctb.png") -22px -30px no-repeat;
      background-size: 348px 270px;
      user-select: none;
      .compass {
        top: 46px;
        left: 50%;
        position: absolute;
        margin: -24px;
        width: 48px;
        height: 48px;
        z-index: 10;
        background: url("../../assets/img/ctb.png") -231px -26px no-repeat;
        background-size: 348px 270px;
        .pointers {
          position: absolute;
          width: 30px;
          height: 48px;
          top: 0;
          left: 9px;
          border: none;
          z-index: 2;
          background: url("../../assets/img/ctb.png") -281px -26px no-repeat;
          background-size: 348px 270px;
        }
      }
      .pitchUp {
        width: 30px;
        height: 25.5px;
        position: absolute;
        top: 3.5px;
        margin-left: -15px;
        left: 50%;
        z-index: 1;
        background: url("../../assets/img/ctb.png") -302.5px -49px no-repeat;
        background-size: 348px 270px;
        &:hover {
          background-position: -302.5px -23.5px;
          // left: 4px;
        }
      }
      .pitchDown {
        width: 30px;
        height: 25.5px;
        position: absolute;
        top: 66px;
        transform: rotate(180deg);
        margin-left: -15px;
        left: 50%;
        z-index: 1;
        background: url("../../assets/img/ctb.png") -302.5px -49px no-repeat;
        background-size: 348px 270px;

        &:hover {
          background-position: -302.5px -23.5px;
          // left: 4px;
        }
      }
      .rotateLeft {
        width: 21px;
        height: 52px;
        top: 19px;
        position: absolute;
        z-index: 2;
        background: url("../../assets/img/ctb.png") -301.5px -77px no-repeat;
        background-size: 348px 270px;

        &:hover {
          background-position: -280.5px -77px;
          // left: 4px;
        }
      }
      .rotateRight {
        width: 21px;
        height: 52px;
        top: 19px;
        right: 5px;
        position: absolute;
        z-index: 2;
        background: url("../../assets/img/ctb.png") -301.5px -77px no-repeat;
        background-size: 348px 270px;
        transform: rotateY(180deg);

        &:hover {
          background-position: -280.5px -77px;
          // right: 2px;
        }
      }
    }
  }
}
</style>