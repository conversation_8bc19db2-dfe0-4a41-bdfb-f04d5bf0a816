/**
 * websocket
 */
import {
    Message,
    // MessageBox
} from 'element-ui';

import {
    getCode
} from './rsa'

// import md5 from 'js-md5'

import {
    publicParameter
} from "./api"

import {
    getDateTime
} from "./date"
import Vue from 'vue';
// import store from "@/store/index";
// import {
//     removeCookie
// } from "../utils/storage"
// import router from "@/router/index"
/** 用户验证 */
export function userWebsocket(_this) {
    let row = publicParameter();
    let params = {
        token: row.token,
        app_ids: row.app_ids,
        app_code: row.app_code,
        licence: '',
        os_language: Vue.prototype.$language == "english" ? "en" : 'zh',
    }
    let item = params;
    params.licence = getCode(md5(item.token + item.app_ids + item.app_code));
    _this.manualSend(params, 100)
}

/** 心跳验证 */
export function wsHeartbeat(_this) {
    _this.manualSend({}, 101)
}

/**
 * 设备验证
 */
export function equipmentCertification(_this, data) {
    // 连接设备认证
    let row = publicParameter();
    let params = {
        token: row.token,
        sn_id: 'wk_03',
        // type: 10,
        pmd: '',
        vst: 40,
        sor: "100",
        os_language: Vue.prototype.$language == "english" ? "en" : 'zh',
        // sor: ""
    }
    params = Object.assign(params, data);

    let item = params
    params.pmd = md5(item.sn_id + item.token + item.vst + item.sor)
    _this.manualSend(params, 200);
}

export class Websockets {
    ws = null;
    url = null; // websocket url
    wsConfig = null; // 请求配置参数
    heartbeatTime = null;

    // 虚拟遥感定时器
    virtualTime = null

    /**
     * @param {*} url ws url
     * @param {*} data 请求配置参数
     * @param {data.userVerify} 用户验证,如果为 true，则连接成功时发起用户验证
     * @param {data.heartbeat} 心跳，如果为0或者false，则不进行心跳连接,单位：毫秒
     * @param {data.equipmentVerify} 设备认证，格式: { sn_id: 'wk_03', type: 10,}
     * @param {data.}
     * @param {data.open} 连接时触发
     * @param {data.success} 连接成功时触发
     * @param {data.message} 获得服务端推送触发
     * @param {data.clone} 连接关闭时触发
     * @param {data.fail} 发生错误时触发
     * @param {data.ruternFail} 返回错误时触发
     * @return
     */
    constructor(url, data = {}) {
        // 判断浏览器是否支持,如果不支持，则进行提示
        if (!(window.WebSocket || window.MozWebSocket)) {
            // fail && fail("抱歉，您的浏览器不支持webSocket")
            if (Vue.prototype.$language == "english") {
                Message({
                    type: "error",
                    message: "Sorry, your browser does not support webSocket"
                });
            } else {
                Message({
                    type: "error",
                    message: "抱歉，您的浏览器不支持webSocket"
                });
            }

            return false;
        }
        this.url = url;
        this.wsConfig = data;
        this.connect(url);
    }

    // 连接
    connect = (url) => {
        this.ws = new WebSocket(url);
        this.ws.onopen = this.connectOpen; // 打开时触发
        this.ws.onmessage = this.message; // 拿到推送信息触发
        this.ws.onerror = this.error; // 通信错误时触发
        this.ws.onclose = this.close; // 连接取消时
        return Promise.resolve(this.ws);
    }

    // 连接时触发 https://blog.csdn.net/weixin_42752574/article/details/122243459
    connectOpen = (res) => {
        // 连接建立时触发
        let {
            fail,
            success,
            open,
            userVerify,
            heartbeat,
            equipmentVerify
        } = this.wsConfig;
        let state = this.ws.readyState;
        open && open(state, res);
        if (state == 0) {
            fail && fail(Vue.prototype.$language == "english" ? "Connection not yet established" : "连接尚未建立");
        }
        // 连接成功
        else if (state == 1) {
            success && success(state);

            // 发起用户验证
            if (userVerify) {
                userWebsocket(this);
            }

            // 发起设备验证
            if (equipmentVerify) {
                equipmentCertification(this, equipmentVerify);
            }

            // 是否每个一段时间就发起一次心跳
            if (heartbeat) {
                clearInterval(this.heartbeatTime);
                wsHeartbeat(this);
                this.heartbeatTime = setInterval(() => {
                    wsHeartbeat(this);
                }, heartbeat)
            }
        }
        // 连接正在进行关闭
        else if (state == 2) {
            fail && fail(Vue.prototype.$language == "english" ? "Connection closing in progress" : "连接正在进行关闭");
        }
        // 连接已关闭或不能打开
        else if (state == 3) {
            fail && fail(Vue.prototype.$language == "english" ? "Connection closed or cannot be opened" : "连接已关闭或不能打开");
        }
    }

    // 获取到推送信息
    message = (msg) => {
        let {
            fail,
            message
        } = this.wsConfig;
        try {
            // console.log(msg.data)
            let data = JSON.parse(msg.data);
            // console.log(data)
            if (data.code == 2000 || data.code == 2201) {
                message && message(data);
            } else if (data.code == 2002) {
                console.log("发生错误--------->");
                if (data.msg_id == 473) {
                    let msg1 = ''
                    if (data.data.control_cmd == 1) {
                        msg1 = Vue.prototype.$language == "english" ? "Process pause failed" : "流程暂停失败"
                    } else if (data.data.control_cmd == 2) {
                        msg1 = Vue.prototype.$language == "english" ? "Process continuation failed" : "流程继续失败"

                    } else if (data.data.control_cmd == 3) {
                        msg1 = Vue.prototype.$language == "english" ? "Process termination failed" : "流程终止失败"
                    }
                    if (msg1) {
                        Message({
                            type: "error",
                            message: msg1
                        });
                    }
                }
                if (data.msg_id == 401) {
                    if (data.data.cmd_type == 8 && data.data.reason !== 0) {

                        Message({
                            type: "error",
                            message: Vue.prototype.$languagePackage.navigation.errorMsg[data.data.reason]
                                // message: Vue.prototype.$language == "english" ? "The azimuth angle of the machine nest is set incorrectly" : "机巢方位角设置有误"
                        });
                    }
                }
            } else {
                if (data.msg_id !== 101) {
                    if (data.code != 44020) {
                        fail && Message({
                            type: "error",
                            message: data.msg
                        });
                    }
                    fail && fail(data);
                }
                // if (data.msg_id == 101 && data.code == 46010) {
                //目前后端连接暂无法判定  
                // fail && fail(data);
                // (this.ws && this.ws.manualClone) && this.ws.manualClone();
                // store.commit('setCloseEquipWs', true)
                // let item = document.getElementsByClassName("login-40263");
                // if (item.length == 0) { // 保证只弹出一次
                //     MessageBox.confirm(Vue.prototype.$language == "english" ? 'Login has expired, please log in again' : '登录已过期，请重新登录', Vue.prototype.$language == "english" ? 'Tips' : '提示', {
                //         confirmButtonText: Vue.prototype.$language == "english" ? 'confirm' : '确定',
                //         cancelButtonText: Vue.prototype.$language == "english" ? 'cancel' : '取消',
                //         type: 'warning',
                //         customClass: "login-40263"
                //     }).then(() => {
                //         store.commit("setUserInfo", {});
                //         removeCookie("token");
                //         router.push({
                //             name: "login"
                //         });
                //     }).catch(() => {}).finally(() => {
                //         item && item[0].parentNode.remove()
                //         store.commit('setCloseEquipWs', false)
                //     })
                // }

                // }
            }
        } catch (err) {
            fail && fail(err);
        }
    }

    /** 通信错误 */
    error = (err) => {
        this.wsConfig.fail && this.wsConfig.fail(err);
    }

    /** 连接被关闭时触发 */
    close = (row) => {
        this.wsConfig.close && this.wsConfig.close(row);
    }

    /** 手动取消 */
    manualClone = () => {
        (this.ws && this.ws.close) && this.ws.close();
        clearInterval(this.heartbeatTime);
    };

    /** 虚拟遥感定时发送 */
    virtualUp = () => {
        clearInterval(this.virtualTime)
        this.virtualTime = setInterval(() => {
            this.manualSend({
                    cmd_type: 1,
                    action_cmd: 9,
                    value: 1500,
                },
                401
            )
        }, 200)
    }

    /**
     * 发送请求
     * @param {data} 请求参数
     * @param {msg_id} 消息类型
     */
    manualSend = (data, msg_id) => {
            // console.log(data, msg_id)
            if (this.ws.readyState != 1) {
                this.error({
                    readyState: this.ws.readyState,
                    msg: Vue.prototype.$language == "english" ? "The connection has been closed" : "连接已经被关闭"
                });
                return false;
            }

            let defaultData = {
                msg_id: msg_id,
                tms: getDateTime().toString(),
                version: 0,
                data: Object.assign({}, data)
            }
            this.ws.send(JSON.stringify(defaultData));
            return true;
        }
        /**
         * 注：
         * 0、页面卸载时需要手动断开ws连接
         * 1、可以手动获取ws实例，进行 close和send事件
         */
}