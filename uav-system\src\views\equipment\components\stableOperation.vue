<template>
  <div class="stableOperation">
    <div class="content-item-3-title">
      {{ equipLanguage.airportStatus.title }}
    </div>
    <div
      class="content-item-3-btn"
      ref="contentBtn"
      :class="equipLanguage.language == 'en-US' ? 'content-item-3-btn-en' : ''"
    >
      <el-button
        :class="!clearCode && (onKey.open == 3 || openCode) ? 'active' : ''"
        :loading="!clearCode && (onKey.open == 3 || openCode) ? true : false"
        :disabled="errorCode"
        @click="UpCloseEvent(1)"
        >{{
          !clearCode && (onKey.open == 3 || openCode)
            ? equipLanguage.airportBtn.starting
            : equipLanguage.airportBtn.onKeyState
        }}</el-button
      >
      <el-button
        :class="!clearCode && (onKey.close == 3 || closeCode) ? 'active' : ''"
        :loading="!clearCode && (onKey.close == 3 || closeCode) ? true : false"
        class="lastBtn"
        ref="allclose"
        :disabled="errorCode"
        @click="UpCloseEvent(0)"
        >{{
          !clearCode && (onKey.close == 3 || closeCode)
            ? equipLanguage.airportBtn.closing
            : equipLanguage.airportBtn.onKeyClose
        }}</el-button
      >
    </div>
    <div class="content-item-3-content">
      <div
        class="content-index"
        v-for="(item, index) in operationList"
        :key="index"
        :class="[
          deviceItem.type == 12 ? 'content-index-1' : '',
          item.value
            ? 'active'
            : item.value1 == 2 || item.value1 == 3
            ? 'active'
            : '',
        ]"
      >
        {{ item.title }}
        <el-switch
          v-if="item.value1 != 2 && item.value1 != 3 ? true : false"
          class="content-switch"
          v-model="item.value"
          active-color="#0A2550"
          inactive-color="#2C2C31"
          :disabled="
            onKey.open == 3 ||
            onKey.close == 3 ||
            openCode ||
            closeCode ||
            errorCode
              ? true
              : false
          "
          @change="changeState(item)"
        >
        </el-switch>

        <el-button
          type="text"
          :loading="loadingCode"
          v-if="item.value1 == 2 || item.value1 == 3 ? true : false"
          >{{ item.value1 | getTipTitle(item) }}</el-button
        >
      </div>
      <div
        class="content-index"
        :class="[
          deviceItem.type == 12 ? 'content-index-1' : '',
          uavState ? 'active' : '',
        ]"
      >
        {{ equipLanguage.airportStatus.drone }}
        <el-switch
          class="content-switch"
          v-model="uavState"
          active-color="#0A2550"
          inactive-color="#2C2C31"
          :disabled="!deviceItem.uavOpen"
          @change="changeUavState"
        >
        </el-switch>
      </div>
    </div>
    <div class="content-charge" v-if="deviceItem.fitOn">
      <el-button
        :class="nest_get_battery == 3 ? 'active' : ''"
        :loading="nest_get_battery == 3 ? true : false"
        :disabled="
          onKey.open == 3 ||
          onKey.close == 3 ||
          openCode ||
          closeCode ||
          errorCode
            ? true
            : false
        "
        @click="nest_get_battery !== 3 ? getBattery() : ''"
        >{{
          nest_get_battery == 3
            ? equipLanguage.airportBtn.fitOning
            : equipLanguage.airportBtn.fitOn
        }}</el-button
      >
      <el-button
        :class="nest_put_battery == 3 ? 'active' : ''"
        :loading="nest_put_battery == 3 ? true : false"
        :disabled="
          onKey.open == 3 ||
          onKey.close == 3 ||
          openCode ||
          closeCode ||
          errorCode
            ? true
            : false
        "
        @click="nest_put_battery !== 3 ? putBattery() : ''"
        >{{
          nest_put_battery == 3
            ? equipLanguage.airportBtn.unfitOning
            : equipLanguage.airportBtn.unfitOn
        }}</el-button
      >
      <!-- <el-button>{{ equipLanguage.airportBtn.removeError }}</el-button> -->
    </div>
    <div class="content-cell" v-if="deviceItem.cellList">
      <div
        class="content-cell-item"
        v-for="item in equipLanguage.cellList"
        :key="item.seq"
        ref="cellItem"
      >
        <div class="cell-title">
          {{ item.label }}
        </div>
        <div class="cell-value">
          <el-progress
            type="circle"
            :percentage="nest_battery_state[item.seq]"
            :stroke-width="progressWidth / 10"
            :width="progressWidth"
            define-back-color="rgb(255, 255, 255)"
            :color="colorFormat"
          ></el-progress>
        </div>
      </div>
    </div>
    <div class="content-item-3-btn">
      <el-button class="errorBtn" v-if="errorCode" @click="errorCode = false">{{
        equipLanguage.airportBtn.removeError
      }}</el-button>
      <el-button
        v-if="deviceItem.isMobile || deviceItem.stopBtn"
        @click="clickEvent(3)"
        :class="clickCode == 3 ? 'active' : ''"
        >{{ equipLanguage.airportBtn.stop }}</el-button
      >
      <el-button
        v-if="deviceItem.isMobile || deviceItem.stopBtn"
        @click="clickEvent(1)"
        :class="clickCode == 1 ? 'active' : ''"
        >{{ equipLanguage.airportBtn.continue }}</el-button
      >
      <el-button
        v-if="deviceItem.crashStop"
        class="dangerBtn"
        @click="clickEvent(4)"
        :class="clickCode == 4 ? 'active' : ''"
        >{{ equipLanguage.airportBtn.crashStop }}</el-button
      >
      <el-button
        @click="clickEvent(2)"
        :class="clickCode == 2 ? 'active' : ''"
        >{{ equipLanguage.airportBtn.reset }}</el-button
      >
      <el-button
        v-if="power && deviceItem.type == 12"
        @click="clearCode = true"
        class="errorBtn"
        >{{ equipLanguage.airportBtn.removeState }}</el-button
      >
    </div>
  </div>
</template>
<script>
import { errorMsg } from "@/utils/errorMsg";
export default {
  props: {
    websocket: {
      type: [String, Object],
      default: () => {
        return {};
      },
    },
    deviceItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      onKey: {
        close: 1,
        open: 0,
      },
      showOnKeyError: false,
      openCode: false,
      closeCode: false,
      time: 0,
      operationList: [],
      errorCode: false,
      changeStateCode: {
        num: "",
        state: false,
      },
      uavState: false,
      nest_battery_state: {},
      nest_get_battery: 1,
      nest_put_battery: 0,
      progressWidth: 50,
      clickCode: 0,
      watchError: 0,
      loadingCode: true,
      clearCode: false,
    };
  },
  created() {
    for (let index = 0; index < this.switchList.length; index++) {
      const item = Object.assign({}, this.switchList[index]);
      if (this.deviceItem.isMobile) {
        this.operationList.push(item);
      } else {
        if (item.id === "charger") {
          this.deviceItem.charger && this.operationList.push(item);
        } else if (item.id === "Lift") {
          this.deviceItem.lift && this.operationList.push(item);
        } else {
          this.operationList.push(item);
        }
      }
    }
  },
  mounted() {
    if (this.deviceItem.type == 12) {
      this.progressWidth = this.$refs.cellItem[0].offsetWidth - 20;
    }
  },
  computed: {
    equipLanguage() {
      return this.$languagePackage.equipment;
    },
    switchList() {
      return this.equipLanguage.operationList;
    },
    userList() {
      return this.$store.state.user.userInfo.fun_list || [];
    },
    power() {
      return (
        this.userList.indexOf("super_administrator") !== -1 ||
        this.userList.indexOf("test_01") !== -1
      );
    },
  },
  watch: {
    watchError(value) {
      if (value > 12 && value < 21 && !this.errorCode) {
        this.errorCode = true;
        if (
          this.changeStateCode.num === "" &&
          !this.changeStateCode.state &&
          !this.showOnKeyError
        ) {
          let msg = errorMsg(value, this.equipLanguage.language);
          this.$message.error({ message: msg, customClass: "message-info" });
        }
      }
    },
  },
  methods: {
    //获取机巢状态
    getMessage(msg_id, data) {
      switch (msg_id) {
        case 434:
          this.watchError = data.nest_error_code;
          this.oneKeyState(data);
          this.stepState(data);
          if (this.deviceItem.type == 12) {
            this.getBatteryState(data);
          }

          break;
        case 437:
          // console.log(data)
          if (data.flight_control_module == 1) {
            this.uavState = true;
          } else {
            this.uavState = false;
          }
          break;

        default:
          break;
      }
    },
    //一键启动状态改变
    oneKeyState(data) {
      //一键开舱
      if (
        data.nest_one_key_open == 1 &&
        this.onKey.open !== 1 &&
        this.onKey.open !== 0
      ) {
        this.$message.success({
          message: this.equipLanguage.onKeyStartSuccess,
          customClass: "message-info",
        });
      }
      this.onKey.open = data.nest_one_key_open;
      if (data.nest_one_key_open == 3) {
        this.openCode = false;
      } else if (data.nest_one_key_open == 4) {
        if (this.showOnKeyError) {
          let msg = errorMsg(data.nest_error_code, this.equipLanguage.language);
          this.$message.error({
            message: msg + this.equipLanguage.onKeyStartError,
            customClass: "message-info",
          });
          this.showOnKeyError = false;
        }
        this.openCode = false;
      }
      //一键关舱
      if (
        data.nest_one_key_close == 1 &&
        this.onKey.close !== 1 &&
        this.onKey.close !== 0
      ) {
        this.$message.success({
          message: this.equipLanguage.onKeyCloseSuccess,
          customClass: "message-info",
        });
      }
      this.onKey.close = data.nest_one_key_close;
      if (data.nest_one_key_close == 3) {
        this.closeCode = false;
      } else if (data.nest_one_key_close == 4) {
        if (this.showOnKeyError) {
          let msg = errorMsg(data.nest_error_code, this.equipLanguage.language);
          this.$message.error({
            message: msg + this.equipLanguage.onKeyCloseError,
            customClass: "message-info",
          });
          this.showOnKeyError = false;
        }
        this.closeCode = false;
      }
    },
    //一键启动一键关闭事件
    UpCloseEvent(index) {
      let time = new Date().getTime();
      if (time - this.time > 1000) {
        if (index == 0) {
          this.closeCode = true;
          if (this.clearCode) {
            this.showOnKeyError = true;
            let data = {
              nest_action_cmd: 116,
            };
            this.websocket.manualSend(data, 403);
            this.clearCode = false;
          } else {
            if (this.onKey.open != 3) {
              let a = 0;
              for (let index = 0; index < this.operationList.length; index++) {
                if (this.operationList[index].value1 == 2) {
                  this.$message.warning({
                    message:
                      this.operationList[index].title +
                      this.equipLanguage.airportBtn.closing,
                    customClass: "message-info",
                  });
                  a = 1;
                  this.closeCode = false;
                }
                if (this.operationList[index].value1 == 3) {
                  this.$message.warning({
                    message:
                      this.operationList[index].title +
                      this.equipLanguage.airportBtn.starting1,
                    customClass: "message-info",
                  });
                  a = 1;
                  this.closeCode = false;
                }
              }
              if (a == 0) {
                this.showOnKeyError = true;
                let data = {
                  nest_action_cmd: 116,
                };
                this.websocket.manualSend(data, 403);
              }
            } else {
              this.closeCode = false;
              this.$message.warning({
                message: this.equipLanguage.noClose,
                customClass: "message-info",
              });
            }
          }

          setTimeout(() => {
            this.closeCode = false;
          }, 3000);
        } else if (index == 1) {
          this.openCode = true;
          if (this.clearCode) {
            this.showOnKeyError = true;
            let data = {
              nest_action_cmd: 115,
            };
            this.websocket.manualSend(data, 403);
            this.clearCode = false;
          } else {
            if (this.onKey.close != 3) {
              let a = 0;
              for (let index = 0; index < this.operationList.length; index++) {
                if (this.operationList[index].value1 == 2) {
                  this.$message.warning({
                    message:
                      this.operationList[index].title +
                      this.equipLanguage.airportBtn.closing,
                    customClass: "message-info",
                  });
                  a = 1;
                  this.openCode = false;
                }
                if (this.operationList[index].value1 == 3) {
                  this.$message.warning({
                    message:
                      this.operationList[index].title +
                      this.equipLanguage.airportBtn.starting1,
                    customClass: "message-info",
                  });
                  a = 1;
                  this.openCode = false;
                }
              }
              if (a == 0) {
                this.showOnKeyError = true;
                let data = {
                  nest_action_cmd: 115,
                };
                this.websocket.manualSend(data, 403);
              }
            } else {
              this.openCode = false;
              this.$message.warning({
                message: this.equipLanguage.noOpen,
                customClass: "message-info",
              });
            }
          }

          setTimeout(() => {
            this.openCode = false;
          }, 3000);
        }
      }
      this.time = time;
    },
    //机巢步骤状态
    stepState(data) {
      for (let index = 0; index < this.operationList.length; index++) {
        if (this.operationList[index].id == "Hatch") {
          this.judgeState(
            data.nest_door_open,
            data.nest_door_close,
            index,
            data
          );
        }
        if (this.operationList[index].id == "Lift") {
          this.judgeState(
            data.nest_platform_up,
            data.nest_platform_down,
            index,
            data
          );
        }
        if (this.operationList[index].id == "center") {
          this.judgeState(
            data.nest_correct_open,
            data.nest_correct_close,
            index,
            data
          );
        }
        if (this.operationList[index].id == "charger") {
          this.judgeState(
            data.nest_charge_on,
            data.nest_charge_off,
            index,
            data
          );
        }
      }
    },
    //判断机巢信息状态
    judgeState(openValue, closeValue, index, data) {
      let result = "";
      let result1 = "";
      if (openValue == 0 || openValue == 1) {
        if (closeValue == 3) {
          result = true;
          result1 = 2;
        } else if (closeValue == 1) {
          result = false;
          result1 = 1;
        } else if (closeValue == 4) {
          if (
            this.operationList[index].value1 == 4 ||
            this.operationList[index].value1 == 0
          ) {
            result = true;
            result1 = 4;
            if (
              this.changeStateCode.num == index &&
              this.changeStateCode.state
            ) {
              let msg = errorMsg(
                data.nest_error_code,
                this.equipLanguage.language
              );
              let msg1 = "";
              if (data.nest_one_key_close == 4) {
                msg1 = this.equipLanguage.onKeyCloseError;
              }
              if (data.nest_one_key_open == 4) {
                msg1 = this.equipLanguage.onKeyStartError;
              }
              if (msg || msg1) {
                this.$message.error({
                  message: msg + msg1,
                  customClass: "message-info",
                });
              }
              result = true;
              result1 = 4;
              this.changeStateCode = {
                num: "",
                state: false,
              };
            }
          } else {
            let msg = errorMsg(
              data.nest_error_code,
              this.equipLanguage.language
            );
            let msg1 = "";
            if (data.nest_one_key_close == 4) {
              msg1 = this.equipLanguage.onKeyCloseError;
            }
            if (data.nest_one_key_open == 4) {
              msg1 = this.equipLanguage.onKeyStartError;
            }
            if (msg || msg1) {
              this.$message.error({
                message: msg + msg1,
                customClass: "message-info",
              });
            }
            result = true;
            result1 = 4;
            this.changeStateCode = {
              num: "",
              state: false,
            };
          }
        }
        //  else if(closeValue==2){
        //   if (
        //     this.operationList[index].value1 == 5 ||
        //     this.operationList[index].value1 == 0
        //   ) {
        //     result = true;
        //     result1 = 5;
        //   } else {
        //     this.$message.error(this.operationList[index].title + "操作超时！");
        //     result = true;
        //     result1 = 5;
        //   }
        // }
      }
      if (closeValue == 0 || closeValue == 1) {
        if (openValue == 3) {
          result = false;
          result1 = 3;
        } else if (openValue == 1) {
          result = true;
          result1 = 1;
        } else if (openValue == 4) {
          if (
            this.operationList[index].value1 == 4 ||
            this.operationList[index].value1 == 0
          ) {
            result = false;
            result1 = 4;
            if (
              this.changeStateCode.num == index &&
              this.changeStateCode.state
            ) {
              let msg = errorMsg(
                data.nest_error_code,
                this.equipLanguage.language
              );
              let msg1 = "";
              if (data.nest_one_key_close == 4) {
                msg1 = this.equipLanguage.onKeyCloseError;
              }
              if (data.nest_one_key_open == 4) {
                msg1 = this.equipLanguage.onKeyStartError;
              }
              if (msg || msg1) {
                this.$message.error({
                  message: msg + msg1,
                  customClass: "message-info",
                });
              }
              result = true;
              result1 = 4;
              this.changeStateCode = {
                num: "",
                state: false,
              };
            }
          } else {
            let msg = errorMsg(
              data.nest_error_code,
              this.equipLanguage.language
            );
            let msg1 = "";
            if (data.nest_one_key_close == 4) {
              msg1 = this.equipLanguage.onKeyCloseError;
            }
            if (data.nest_one_key_open == 4) {
              msg1 = this.equipLanguage.onKeyStartError;
            }
            if (msg || msg1) {
              this.$message.error({
                message: msg + msg1,
                customClass: "message-info",
              });
            }
            result = false;
            result1 = 4;
            this.changeStateCode = {
              num: "",
              state: false,
            };
          }
        }
        //  else if (openValue == 2) {
        //   if (this.operationList[index].value1 == 2) {
        //     result = false;
        //     result1 = 5;
        //   } else {
        //     this.$message.error(this.operationList[index].title + "操作超时！");
        //     result = false;
        //     result1 = 5;
        //   }
        // }
      }
      this.operationList[index].value = result;
      this.operationList[index].value1 = result1;
    },
    //点击改变状态
    changeState(item) {
      let stateCode = "";
      let index = this.operationList.findIndex((x) => {
        return x.id == item.id;
      });
      this.changeStateCode = {
        num: index,
        state: true,
      };
      if (item.id == "Hatch") {
        if (item.value) {
          stateCode = 100;
        } else {
          stateCode = 101;
        }
      } else if (item.id == "Lift") {
        if (item.value) {
          stateCode = 102;
        } else {
          stateCode = 103;
        }
      } else if (item.id == "center") {
        if (item.value) {
          stateCode = 105;
        } else {
          stateCode = 104;
        }
      } else if (item.id == "charger") {
        if (item.value) {
          stateCode = 117;
        } else {
          stateCode = 118;
        }
      }
      let data = {
        nest_action_cmd: stateCode,
      };
      this.websocket.manualSend(data, 403);
    },
    //改变飞机状态
    changeUavState() {
      if (!this.uavState) {
        this.websocket.manualSend(
          {
            nest_action_cmd: 111,
          },
          403
        );
      } else {
        this.websocket.manualSend(
          {
            nest_action_cmd: 110,
          },
          403
        );
      }
    },
    //获取飞机电池状态
    getBatteryState(data) {
      this.nest_get_battery = data.nest_get_battery;
      this.nest_put_battery = data.nest_put_battery;
      this.nest_battery_state = {};
      for (let index = 0; index < data.nest_battery_state.length; index++) {
        if (data.nest_battery_state[index].state) {
          this.nest_battery_state[data.nest_battery_state[index].seq] =
            data.nest_battery_state[index].percent;
        } else {
          this.nest_battery_state[data.nest_battery_state[index].seq] = 0;
        }
      }
    },
    //装载电池
    getBattery() {
      if (this.nest_get_battery == 1) {
        this.$message.warning(this.equipLanguage.onend);
        return false;
      }
      this.nest_get_battery = 3;
      let data = {
        nest_action_cmd: 106,
      };
      this.websocket.manualSend(data, 403);
    },
    //卸下电池
    putBattery() {
      if (this.nest_put_battery == 1) {
        this.$message.warning(this.equipLanguage.unonend);
        return false;
      }
      this.nest_put_battery = 3;
      let data = {
        nest_action_cmd: 107,
      };
      this.websocket.manualSend(data, 403);
    },
    //颜色返回
    colorFormat(value) {
      if (value < 25) {
        return "#ff4949";
      } else if (value < 50) {
        return "#e6a23c";
      } else if (value < 75) {
        return "#1989fa";
      } else if (value < 100) {
        return "#83c561";
      } else if (value == 100) {
        return "#67c23a";
      }
    },
    //暂停，继续，复位，急停
    clickEvent(index) {
      if (!this.clickCode) {
        let num = 114;
        switch (index) {
          case 1:
            num = 119;
            break;
          case 2:
            num = 108;
            break;
          case 3:
            num = 114;
            break;

          case 4:
            num = 109;
            break;

          default:
            break;
        }
        let data = {
          nest_action_cmd: num,
        };
        this.websocket.manualSend(data, 403);
        this.clickCode = index;
        setTimeout(() => {
          this.clickCode = 0;
        }, 1000);
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .stableOperation {
    .content-item-3-title {
      font-size: @zoomIndex * 14px !important;
      letter-spacing: @zoomIndex * 2px !important;
    }
    .content-item-3-btn {
      .el-button {
        min-width: @zoomIndex * 120px !important;
        font-size: @zoomIndex * 14px !important;
        letter-spacing: @zoomIndex * 2px !important;
        padding: @zoomIndex * 12px !important;
      }
    }
    .content-item-3-content {
      .content-index {
        font-size: @zoomIndex * 18px !important;
        letter-spacing: @zoomIndex * 2px !important;
        .el-button {
          padding: @zoomIndex * 6px 0 !important;
          &.el-button--text {
            font-size: @zoomIndex * 16px !important;
          }
        }
      }
    }
    .content-charge {
      .el-button {
        min-width: @zoomIndex * 120px !important;
        font-size: @zoomIndex * 14px !important;
        letter-spacing: @zoomIndex * 2px !important;
        padding: @zoomIndex * 12px !important;
      }
    }
    .content-cell {
      margin: 0 @zoomIndex * 10px !important;
      .content-cell-item {
        font-size: @zoomIndex * 14px !important;
        margin: @zoomIndex * 5px !important;
        .cell-title {
          border-radius: @zoomIndex * 2px !important;
        }
        .cell-value {
          .el-progress {
            margin-top: @zoomIndex * 5px !important;
          }
        }
      }
    }
  }
}
.stableOperation {
  .content-item-3-title {
    margin: 2% 8%;
    font-weight: 550;
    font-size: 14px;
    letter-spacing: 2px;
    color: white;
  }
  .content-item-3-btn {
    margin: 2% 8%;
    display: flex;
    flex-flow: row wrap;
    .el-button {
      white-space: normal;
      margin-left: 0;
      width: 45%;
      min-width: 120px;
      border: none;
      font-size: 14px;
      font-weight: 550;
      letter-spacing: 2px;
      margin-bottom: 1%;
      padding: 12px;
      background-color: #0b58de;
      color: white;
      border: none;
      &.dangerBtn {
        background-color: #f56c6c;
      }
      &.is-disabled {
        background-color: #638bd1;
      }

      &.active {
        background-color: white;
        color: #0b58de;

        &.is-disabled {
          background-color: #638bd1;
        }
      }
      &:nth-child(odd) {
        margin-right: 5%;
      }
      // &:nth-child(even){
      //   margin-left: 5%;
      // }
    }
    .errorBtn {
      background-color: #ee3434;
      color: white;
    }
  }
  .content-item-3-content {
    margin: 2% 8%;
    .content-index {
      margin: 10% 0;
      font-size: 18px;
      text-align: left;
      margin-left: 7%;
      font-weight: 500;
      letter-spacing: 2px;
      color: #79818e;

      &.active {
        color: #0b58de;
      }
      &.content-index-1 {
        margin: 5% 0;
      }
      .content-switch,
      .el-button {
        float: right;
        background-color: transparent;
        border: none;
        &.el-button--text {
          color: #0b58de !important;
        }

        &.is-loading:before {
          background-color: transparent !important;
        }
      }
      .el-switch {
        height: 50%;
      }
      .el-button {
        border: none;
        padding: 6px 0;
        &.el-button--text {
          font-size: 16px;
        }
      }
    }
  }
  .content-charge {
    margin: 2% 8%;
    .el-button {
      white-space: normal;
      margin-left: 0;
      width: 45%;
      min-width: 120px;
      border: none;
      font-size: 14px;
      font-weight: 550;
      letter-spacing: 2px;
      margin-bottom: 1%;
      padding: 12px;
      background-image: linear-gradient(
        to bottom,
        rgb(123, 197, 187),
        rgb(99, 99, 99)
      );
      color: white;
      border: none;

      &.active {
        background-image: linear-gradient(to bottom, white, white);
        // background-color: white;
        color: #0b58de;
      }

      &.is-disabled {
        background-color: #638bd1;
      }
      &:nth-child(odd) {
        margin-right: 4%;
      }
    }
  }
  .content-cell {
    margin: 0 10px;
    display: flex;
    align-items: center;
    text-align: center;
    .content-cell-item {
      flex: 1;
      font-size: 14px;
      text-align: center;
      margin: 5px;
      .cell-title {
        border-radius: 2px;
        background-color: rgba(38, 17, 155, 0.842);
        color: #fff;
      }
      .cell-value {
        .el-progress {
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
<style lang="less">
.stableOperation {
  .content-item-3-content {
    .el-switch {
      .el-switch__core {
        height: 36px !important;
        width: 100px !important;
        border-radius: 20px !important;
        &::after {
          height: 32px !important;
          width: 32px !important;
        }
      }
    }
    .el-switch.is-checked .el-switch__core::after {
      background-color: #0b58de !important;
    }
    .el-switch.is-checked .el-switch__core::after {
      margin-left: -30px !important;
    }
  }
  .content-cell {
    .content-cell-item {
      .cell-value {
        .el-progress {
          .el-progress__text {
            font-size: 14px !important;
            color: #fff !important;
          }
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .stableOperation {
    .content-item-3-content {
      .el-switch {
        .el-switch__core {
          height: @zoomIndex * 36px !important;
          width: @zoomIndex * 100px !important;
          border-radius: @zoomIndex * 20px !important;
          &::after {
            height: @zoomIndex * 32px !important;
            width: @zoomIndex * 32px !important;
          }
        }
      }
      .el-switch.is-checked .el-switch__core::after {
        margin-left: @zoomIndex * -30px !important;
      }
    }
    .content-cell {
      .content-cell-item {
        .cell-value {
          .el-progress {
            .el-progress__text {
              font-size: @zoomIndex * 14px !important;
            }
          }
        }
      }
    }
  }
}
</style>