<template>
  <div class="camera-params-config camera">
    <div class="camera-item">
      <div class="camera-item-main">
        <div class="main-title">{{language.mode}}</div>
        <div class="main-content">
          <div
            class="content-row"
            v-for="(item, index) in modelList"
            :key="index"
            @click="cutModel(item)"
          >
            <div class="row-top">
              <img-icon
                :name="modelIndex == item.value ? item.iconS : item.icon"
                :width="item.width"
                :height="item.height"
              />
            </div>
            <div
              class="row-bot"
              :style="{
                color: modelIndex == item.value ? 'rgb(57, 139, 204)' : '#fff',
              }"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="cap-continues" v-if="modelIndex == 2">
            <el-input-number v-model="cap_continues" @change="manualSend" size="mini"  :min="1" :max="10" label="连拍张数"></el-input-number>
          </div>
        </div>
      </div>
    </div>

    <div class="camera-item">
      <div class="camera-item-main">
        <div class="main-title">{{language.format.title}}</div>
        <div class="main-content">
          <el-popover
            width="205"
            v-model="isShowFormat"
            trigger="click"
            popper-class="cameta-set-popover"
          >
            <div
              class="popover-item"
              v-for="(item, index) in imgFormat"
              :key="index"
              :class="formatIndex == item.value ? 'select-item-style' : ''"
              @click="cutImgFormat(item)"
            >
              {{ item.label }}
            </div>

            <div class="img-format" slot="reference">
              <div class="format-left">{{language.format.front}}</div>
              <div class="format-right">
                <span style="color: #398bcc">{{ imgFormat[formatIndex-1].label }}</span>
                <span class="el-icon-arrow-down"></span>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
    </div>

    <div class="camera-item" style="margin: 20px 0">
      <div class="camera-item-main">
        <div class="main-title">{{language.size}}</div>
        <div class="main-content mina-size">
          <tab-scroll v-model="imgSizeIndex" :list="imgSizeList" @onChange="imgSizeChange"></tab-scroll>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import imgIcon from "@/components/imgIcon/index.vue";
import tabScroll from "@/components/tabScroll/index.vue";
export default {
  components: {
    imgIcon,
    tabScroll,
  },
  data() {
    return {
      modelIndex: "1",
      modelList: [
        {
          label: "单拍",
          value: 1,
          icon: "p-mode-1",
          iconS: "p-mode-1-s",
          width: "22",
          height: "22",
        },
        {
          label: "连拍",
          value: 2,
          icon: "p-mode-2",
          iconS: "p-mode-2-s",
          width: "26",
          height: "28",
        },
        {
          label: "定时拍摄",
          value: 3,
          icon: "p-mode-3",
          iconS: "p-mode-3-s",
          width: "31",
          height: "28",
        },
        {
          label: "延时拍摄",
          value: 4,
          icon: "p-mode-4",
          iconS: "p-mode-4-s",
          width: "33",
          height: "28",
        },
      ],

      // 照片大小
      imgSizeIndex: 1,
      imgSizeList: [
        { label: "8M", value: 1 },
        { label: "12M", value: 2 },
        { label: "16M", value: 3 },
        { label: "21M", value: 4 },
        { label: "32M", value: 5 },
        { label: "41M", value: 6 },
      ],

      // 照片格式
      isShowFormat: false,
      formatIndex: 1,
      imgFormat: [
        { label: "JPG", value: 1 },
        { label: "RAW", value: 2 },
        { label: "RAW&JPG", value: 3 },
      ],
      cap_continues: 1,
    };
  },
  computed: {
    // 无人机
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS;
    },
    formatLabel(){
      return
    },
    language(){
      return this.$languagePackage.navigation.cameraConfig.camera
    },
    // 状态二回传信息
    staveTwoData() {
      return this.$store.state.equipment.staveTwoData;
    },
  },
  watch: {
    staveTwoData: {
      deep: true,
      handler: function(val){
        this.modelIndex = val.cap_mode;
        this.cap_continues = val.cap_continues;
        this.formatIndex = val.cap_save_format;
        this.imgSizeIndex = val.cap_rotation
      }
    }
  },
  created(){
    this.init();
  },
  methods: {
    init: function(){
      let picture = this.$languagePackage.dict.pictureMode;
      for(let i=0; i<this.modelList.length; i++){
        let item = this.modelList[i].value;
        this.modelList[i].label = picture[item]
      }
    },
    cutImgSize: function (item) {
      this.imgSizeIndex = item.value;
    },
    cutImgFormat: function (item) {
      this.formatIndex = item.value;
      this.isShowFormat = false;
      this.manualSend();
    },
    cutModel: function (item) {
      this.modelIndex = item.value;
      this.manualSend();
     
    },
    imgSizeChange: function(val){
      this.manualSend();
    },

    manualSend: function(){
       let param = {
        cmd_type: 4,
        cap_param_mode: this.modelIndex,
        cap_param_continues: this.cap_continues,
        cap_save_format: this.formatIndex,
        cap_param_rotation: this.imgSizeIndex
      }

      this.equipmentWS.manualSend(param, 402);
    }
  },
};
</script>

<style lang="less" scoped>
.camera {
  padding: 0 50px;
  display: flex;
  flex-wrap: wrap;
  .camera-item {
    width: 50%;
    .camera-item-main {
      width: 200px;
      .main-title {
        margin: 25px 0 15px 0;
        font-size: 12px;
        color: #fff;
      }
      .main-content {
        display: flex;
        justify-content: space-between;
        position: relative;
        .content-row {
          .row-top {
            height: 28px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
          .row-bot {
            color: #fff;
            font-size: 12px;
            margin-top: 13px;
          }
        }
      }
      .mina-size {
        border: 1px solid #c2c3c3;
        width: 191px;
        height: 24px;
        border-radius: 6px;
        // padding: 0 35px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .main-row {
          color: #fff;
          font-size: 12px;
          cursor: pointer;
        }

        .select-row {
          position: relative;
          color: #398bcc;
          &::after {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid #398bcc;
            bottom: -5px;
            left: 50%;
            margin-left: -5px;
          }
        }
      }

      .img-format {
        border: 1px solid #c2c3c3;
        padding: 0 6px;
        width: 193px;
        height: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 6px;
        .format-left,
        .format-right {
          font-size: 12px;
          color: #fff;
        }
        .format-right {
        }
      }
    }
  }
}
</style>

<style lang="less">
.cameta-set-popover {
  padding: 0 !important;
  margin-top: 2px !important;
  border: none !important;
  border-radius: 6px !important;
  .popover-item {
    padding: 0 29px;
    height: 22px;
    // color: #000;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    cursor: pointer;
    font-size: 12px;
    font-weight: 700;
    border-radius: 6px;
    &:hover {
      // .select-item-style;
    }
  }
  .select-item-style {
    // background-color: #211e1e;
    // color: #398bcc;
  }

  .popper__arrow {
    display: none;
  }
}

.cap-continues{
  position: absolute;
  bottom: -34px;
  left: 0;
}
</style>