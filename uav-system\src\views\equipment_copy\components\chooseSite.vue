<template>
  <div class="choose-site">
    <div class="v-model" v-if="mapCode"></div>
    <div id="mapDiv">
      <div class="title" v-if="mapCode">
        {{
          deviceInfo.get_address
            ? equipLanguage.airportMap.title
            : equipLanguage.airportMap.title1
        }}
      </div>
      <div id="map"></div>
      <div class="searchSite" v-show="mapCode">
        <el-input
          :placeholder="equipLanguage.airportMap.placeholder"
          id="tipinput"
          v-model="searchValue"
          @input="search"
          @keyup.enter.native="chooseSite()"
          clearable
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="chooseSite()"
          ></el-button>
        </el-input>
        <div class="searchReturn" v-show="mapCode || result.length > 0">
          <el-button
            class="search-item"
            v-for="(item, index) in result"
            :key="index"
            @click="chooseSite(item)"
            >{{ item.name }}</el-button
          >
        </div>
      </div>
      <div class="foot-btn" v-show="mapCode">
        <el-button
          class="getsitebtn"
          @click="getUavSite"
          v-if="deviceInfo && deviceInfo.sn_id"
          :loading="getSiting"
          >{{
            getSiting
              ? equipLanguage.airportMap.reading
              : deviceInfo.get_address
              ? equipLanguage.airportMap.readAirport
              : equipLanguage.airportMap.readUav
          }}</el-button
        >
        <el-button @click="sureSite">{{
          equipLanguage.airportMap.sure
        }}</el-button>
        <el-button class="closebtn" @click="closeMap">{{
          equipLanguage.airportMap.cancel
        }}</el-button>
      </div>
    </div>
  </div>
</template>
<script>
// import initMaps from "../../../utils/maps";
import createMap from "@/utils/cesium/createMap";
import mapMethods from "@/utils/cesium/mapMethods";
import {
  searchTip,
  searchPoi,
  searchLnglat,
  searchLnglat1,
  searchTip1,
  searchPoi1,
} from "../../../utils/mapApi";
import { Websockets } from "../../../utils/websocketUntil";
import baseUrl from "../../../utils/global";
import { wgs84_to_gcj02, gcj02_to_wgs84 } from "../../../utils/wgs84_to_gcj02";
import { pointsConvert } from "@/utils/coordinateConvert";
export default {
  name: "chooseSize",
  props: {
    site: {
      type: Object,
      default() {
        return {};
      },
    },
    equipLanguage: {
      type: [Object, String],
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      center: "",
      mapCode: false,
      map: "",
      searchValue: "",
      result: "",
      marker: "",
      backData: {
        address: "",
        lat_int: 0,
        lng_int: 0,
      },
      deviceInfo: {},
      getSiting: false,
      websocket1: "",
      lnglat: "",
      msgCode: "",
      markerIcon: require("../../../assets/img/equipment/marker.png"),
      handler: "",
      leftDownCode: false,
    };
  },
  mounted() {
    //初始化地图
    this.init();
  },
  methods: {
    //初始化地图
    init() {
      // initMaps
      //   .initMap("map", {
      //     mapStyle: "amap://styles/6a6eb1b7b147e6a84a679c0aa1cdf130",
      //   })
      //   .then((res) => {
      //     this.map = res;
      //     this.center = [this.map.getCenter().lng, this.map.getCenter().lat];
      //   });
      this.map = createMap.createMap("map", { layerIndex: 1, modeIndex: 1 });
      this.center = [108.923611, 34.540833];
      // 113.2759952545166 纬度:23.117055306224895
    },
    //打开更改地图宽高
    openMap(data) {
      this.deviceInfo = data;
      this.mapCode = true;
      let a = document.getElementById("mapDiv");
      a.style.height = "70vh";
      let self = this;
      // AMap.plugin(["AMap.ToolBar"], function () {
      //   // 在图面添加工具条控件, 工具条控件只有缩放功能
      //   self.map.addControl(
      //     new AMap.ToolBar({
      //       position: "RT",
      //     })
      //   );
      // });
      this.handler = new Cesium.ScreenSpaceEventHandler(this.map.scene.canvas);
      this.handler &&
        this.handler.setInputAction(
          this.leftDownEvent,
          Cesium.ScreenSpaceEventType.LEFT_DOWN
        );
      window.addEventListener("mouseup", this.leftUpEvent);
      if (this.deviceInfo.get_address) {
        if (this.site.address) {
          this.searchValue = this.site.address;
          this.backData = {
            address: this.site.address,
            lat_int: parseInt(this.site.lat_int * 1e7),
            lng_int: parseInt(this.site.lng_int * 1e7),
          };
          // let ps = wgs84_to_gcj02(this.site.lng_int, this.site.lat_int);
          let ps = pointsConvert({
            point: [this.site.lng_int, this.site.lat_int],
            type: 10,
          });
          this.setCamera(ps);
          this.drawMarker(ps);
        } else {
          this.setCamera(this.center, true);
          this.drawMarker(this.center);
          searchLnglat1(this.center).then((res) => {
            if (res.data.status == 0) {
              this.searchValue = res.data.result.formatted_address;
              let po = this.getPointLngLat([108.923611, 34.540833]);
              this.backData = {
                address: this.searchValue,
                lat_int: parseInt(po.latitude),
                lng_int: parseInt(po.longitude),
              };
            }
          });
          // this.drawMarker(this.center);
          // this.map.setZoom(17);
          // this.map.setCenter(this.center, true);
          // let location = this.center[0] + "," + this.center[1];
          // searchLnglat(location).then(res => {
          //   if (res.data.status == 1) {
          //     this.searchValue = res.data.regeocode.formatted_address;
          //     if (this.marker) {
          //       this.marker.setTitle(this.searchValue);
          //     }
          //     let po = this.getPointLngLat([this.center[0], this.center[1]]);
          //     this.backData = {
          //       address: this.searchValue,
          //       lat_int: po.latitude,
          //       lng_int: po.longitude
          //     };
          //   }
          // });
        }
      } else {
        if (data.alternate_lon_int !== 0 && data.alternate_lat_int !== 0) {
          // let poi = wgs84_to_gcj02(
          //   data.alternate_lon_int,
          //   data.alternate_lat_int
          // );
          let poi = pointsConvert({
            point: [data.alternate_lon_int, data.alternate_lat_int],
            type: 10,
          });
          this.setCamera(poi);
          this.drawMarker(poi);
          searchLnglat1(poi).then((res) => {
            if (res.data.status == 0) {
              this.searchValue = res.data.result.formatted_address;
            }
          });
          this.lnglat = {
            latitude: parseInt(data.alternate_lat_int * 1e7),
            longitude: parseInt(data.alternate_lon_int * 1e7),
          };
        } else {
          if (this.site.address) {
            this.searchValue = this.site.address;
            // let ps = wgs84_to_gcj02(this.site.lng_int, this.site.lat_int);
            let ps = pointsConvert({
              point: [this.site.lng_int, this.site.lat_int],
              type: 10,
            });

            this.setCamera(ps);
            this.drawMarker(ps);
          } else {
            this.setCamera(this.center, true);
            this.drawMarker(this.center);
            searchLnglat1(this.center).then((res) => {
              if (res.data.status == 0) {
                this.searchValue = res.data.result.formatted_address;
              }
            });
            let p = this.getPointLngLat([108.923611, 34.540833]);
            this.lnglat = {
              latitude: parseInt(p[1]),
              longitude: parseInt(p[0]),
            };

            // this.drawMarker(this.center);
            // this.map.setZoom(17);
            // this.map.setCenter(this.center, true);
            // let location = this.center[0] + "," + this.center[1];
            // searchLnglat(location).then((res) => {
            //   if (res.data.status == 1) {
            //     this.searchValue = res.data.regeocode.formatted_address;
            //     if (this.marker) {
            //       let title =
            //         this.equipLanguage.uavStatus.lng +
            //         ":" +
            //         this.center[0] +
            //         "，" +
            //         this.equipLanguage.uavStatus.lat +
            //         ":" +
            //         this.center[1];
            //       this.marker.setTitle(title);
            //     }
            //   }
            // });
            // let p = gcj02_to_wgs84(this.center[0], this.center[1]);
            // this.lnglat = {
            //   latitude: parseInt(p[1] * 1e7),
            //   longitude: parseInt(p[0] * 1e7),
            // };
          }
        }
      }
    },
    //鼠标在地图按下
    leftDownEvent(e) {
      const pickedLabel = this.map.scene.pick(e.position);
      if (Cesium.defined(pickedLabel) && pickedLabel.id) {
        this.leftDownCode = true;
        this.map.scene.screenSpaceCameraController.enableTranslate = false;
        this.handler &&
          this.handler.setInputAction(
            this.leftUpEvent,
            Cesium.ScreenSpaceEventType.LEFT_UP
          );
        this.handler &&
          this.handler.setInputAction(
            this.mouseMoveEvent,
            Cesium.ScreenSpaceEventType.MOUSE_MOVE
          );
      }
    },
    //鼠标抬起
    leftUpEvent() {
      if (this.leftDownCode) {
        this.map.scene.screenSpaceCameraController.enableTranslate = true;
        this.handler &&
          this.handler.removeInputAction(
            Cesium.ScreenSpaceEventType.MOUSE_MOVE
          );
        this.handler &&
          this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
        this.leftDownCode = false;
      }
    },
    //鼠标移动
    mouseMoveEvent(e) {
      let markerEntity = this.map.entities.getById("siteMarker");
      if (!markerEntity) {
        return false;
      }
      let cartesian = this.map.camera.pickEllipsoid(
        e.endPosition,
        this.map.scene.globe.ellipsoid
      );
      var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      //弧度转经纬度
      var lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
      var lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
      let cartesian1 = new Cesium.Cartesian3.fromDegrees(lng, lat, 0);
      markerEntity.position = cartesian1;
      searchLnglat1([lng, lat]).then((res) => {
        if (res.data.status == 0) {
          this.searchValue = res.data.result.formatted_address;
          let po = this.getPointLngLat([lng, lat]);
          this.backData = {
            address: this.searchValue,
            lat_int: parseInt(po.latitude),
            lng_int: parseInt(po.longitude),
          };
        }
      });
    },
    //设置相机位置
    setCamera(arrPoint, code) {
      var camera = this.map.camera;
      camera.setView({
        destination: new Cesium.Cartesian3.fromDegrees(
          arrPoint[0],
          arrPoint[1],
          code ? 20000000 : 1000
        ),
        orientation: {
          heading: Cesium.Math.toRadians(0),
          pitch: Cesium.Math.toRadians(-90),
          roll: Cesium.Math.toRadians(0),
        },
      });
    },
    getPointLngLat(e) {
      let point =
        this.$coordinateType == "gcj02" ? gcj02_to_wgs84(e[0], e[1]) : e;
      return {
        latitude: parseInt(point[1] * 1e7),
        longitude: parseInt(point[0] * 1e7),
      };
    },
    //画marker
    drawMarker(point) {
      let points = new Cesium.Cartesian3.fromDegrees(point[0], point[1], 0);
      let marker = this.map.entities.getById("siteMarker");
      if (marker) {
        marker.position = points;
        return false;
      }
      let site = mapMethods.drawPoint(points, {
        id: "siteMarker",
        imageUrl: require("../../../assets/img/equipment/marker.png"),
      });
      this.map.entities.add(site);
    },
    //查找
    search() {
      searchTip1(this.searchValue).then((res) => {
        if (res.data.pois) {
          this.result = res.data.pois;
        } else this.result = [];
      });
      // searchLnglat1().then(res=>{})
      // searchTip(this.searchValue).then(res => {
      //   if (res.data.status == 1) {
      //     this.result = res.data.tips;
      //   }
      // });
    },
    //点击查找结果
    chooseSite(item) {
      this.result = "";
      this.searchValue = item ? item.name : this.searchValue;
      if (item) {
        let a = item.lonlat.split(",");
        let point = {
          lat: Number(a[1]),
          lng: Number(a[0]),
        };
        this.changeMarkerCenter(point);
        return false;
      }
      searchPoi1(this.searchValue).then((res) => {
        if (res.data.status == 0) {
          let point = {
            lat: Number(res.data.location.lat),
            lng: Number(res.data.location.lon),
          };
          this.changeMarkerCenter(point);
        }
      });
    },
    //修改标记点和中心点
    changeMarkerCenter(point) {
      // let points = wgs84_to_gcj02(point.lng, point.lat);
      let points = pointsConvert({ point: [point.lng, point.lat], type: 10 });
      let marker = this.map.entities.getById("siteMarker");
      marker.position = new Cesium.Cartesian3.fromDegrees(
        points[0],
        points[1],
        0
      );
      this.setCamera(points);
      if (!this.deviceInfo.get_address) {
        this.lnglat = {
          latitude: parseInt(point.lat * 1e7),
          longitude: parseInt(point.lng * 1e7),
        };
      } else {
        this.backData = {
          address: this.searchValue,
          lat_int: parseInt(point.lat * 1e7),
          lng_int: parseInt(point.lng * 1e7),
        };
      }
    },
    //点击取消
    closeMap() {
      this.mapCode = false;
      let a = document.getElementById("mapDiv");
      a.style.height = "0";
      this.searchValue = "";
      this.result = "";
      this.map.entities.removeById("siteMarker");
      // if (this.marker) {
      //   this.map.remove(this.marker);
      //   this.marker = "";
      // }
      this.backData = {
        address: "",
        lat_int: 0,
        lng_int: 0,
      };
      this.deviceInfo = "";
      this.getSiting = false;
      if (this.websocket1) {
        this.websocket1.manualClone();
        this.websocket1 = "";
      }
      this.lnglat = "";
      window.removeEventListener("mouseup", this.leftUpEvent);
      this.handler &&
        this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
      this.handler = "";
      // this.map.setCenter(this.center);
    },
    //拖拽结束触发的事件
    // markerDrag(e) {
    //   let location = e.lnglat.lng + "," + e.lnglat.lat;
    //   this.lnglatRequest(location).then(res=>{
    //     if (res.data.status == 1) {
    //         this.searchValue = res.data.regeocode.formatted_address;
    //         this.backData={
    //           address:this.searchValue,
    //           lat_int:parseInt(e.lnglat.lat*1e7),
    //           lng_int:parseInt(e.lnglat.lng*1e7)
    //         }
    //       }
    //   })
    // },
    //选好位置返回
    sureSite() {
      if (!this.deviceInfo.get_address) {
        this.$emit("siteData", this.lnglat, 1);
        this.mapCode = false;
        let a = document.getElementById("mapDiv");
        a.style.height = "0";
        this.searchValue = "";
        this.result = "";
        this.map.entities.removeById("siteMarker");
        this.lnglat = "";
      } else {
        this.$emit("siteData", this.backData);
        this.mapCode = false;
        let a = document.getElementById("mapDiv");
        a.style.height = "0";
        this.searchValue = "";
        this.result = "";
        this.map.entities.removeById("siteMarker");
        this.backData = {
          address: "",
          lat_int: 0,
          lng_int: 0,
        };
      }
    },
    markerDrag(e) {
      // let location = e.lnglat.lng + "," + e.lnglat.lat;
      // searchLnglat(location).then(res => {
      //   if (res.data.status == 1) {
      //     this.searchValue = res.data.regeocode.formatted_address;
      //     if (!this.deviceInfo.get_address) {
      //       if (this.marker) {
      //         let title =
      //           this.equipLanguage.uavStatus.lng +
      //           ":" +
      //           e.lnglat.lng +
      //           "，" +
      //           this.equipLanguage.uavStatus.lat +
      //           ":" +
      //           e.lnglat.lat;
      //         this.marker.setTitle(title);
      //       }
      //       let p = gcj02_to_wgs84(e.lnglat.lng, e.lnglat.lat);
      //       this.lnglat = {
      //         latitude: parseInt(p[1] * 1e7),
      //         longitude: parseInt(p[0] * 1e7)
      //       };
      //     } else {
      //       if (this.marker) {
      //         this.marker.setTitle(this.searchValue);
      //       }
      //       let po = this.getPointLngLat([e.lnglat.lng, e.lnglat.lat]);
      //       this.backData = {
      //         address: this.searchValue,
      //         lat_int: po.latitude,
      //         lng_int: po.longitude
      //       };
      //     }
      //   }
      // });
    },
    getUavSite() {
      if (!this.getSiting) {
        this.getSiting = true;
        this.websocket1 = new Websockets(baseUrl.WS_URL, {
          equipmentVerify: {
            sn_id: this.deviceInfo.sn_id,
            type: this.deviceInfo.type,
            vst: 40,
          },
          heartbeat: 10000,
          message: this.returnMessage,
        });
        let left = this;
        this.msgCode = setTimeout(() => {
          this.$message.error({
            message: left.deviceInfo.get_address
              ? this.equipLanguage.airportMap.errorMessage
              : this.equipLanguage.airportMap.errorMessage1,
            customClass: "message-info",
          });
          this.getSiting = false;
        }, 3000);
      }
    },
    returnMessage(e) {
      if (e.msg_id == 432) {
        clearTimeout(this.msgCode);
        let data = e.data;
        let left = this;
        if (data.rtk_status == 6) {
          if (!data.flight_arm) {
            this.lnglat = {
              latitude: data.latitude,
              longitude: data.longitude,
            };
            // let a = wgs84_to_gcj02(data.longitude / 1e7, data.latitude / 1e7);
            let a = pointsConvert({
              point: [data.longitude / 1e7, data.latitude / 1e7],
              type: 10,
            });
            this.setCamera(a);
            let markerEntity = this.map.entities.getById("siteMarker");
            let cartesian1 = new Cesium.Cartesian3.fromDegrees(a[0], a[1], 0);
            markerEntity.position = cartesian1;
            searchLnglat1(a).then((res) => {
              if (res.data.status == 0) {
                this.searchValue = res.data.result.formatted_address;
                this.backData = {
                  address: this.searchValue,
                  lat_int: data.latitude,
                  lng_int: data.longitude,
                };
              }
            });

            this.$message.success({
              message: this.deviceInfo.get_address
                ? this.equipLanguage.airportMap.successMessage
                : this.equipLanguage.airportMap.successMessage1,
              customClass: "message-info",
            });
          } else {
            this.$message.error({
              message: this.equipLanguage.airportMap.errorMessage2,
              customClass: "message-info",
            });
          }
        } else {
          this.$message.error({
            message: this.equipLanguage.airportMap.errorMessage3,
            customClass: "message-info",
          });
        }
        this.getSiting = false;
        if (this.websocket1) {
          this.websocket1.manualClone();
          this.websocket1 = "";
        }
      }
    },
  },
  // beforeDestroy() {
  //   this.map && this.map.destroy();
  //   this.map = "";
  // }
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .choose-site {
    #mapDiv {
      .foot-btn {
        .el-button {
          padding: @zoomIndex * 10px @zoomIndex * 20px !important;
          font-size: @zoomIndex * 18px !important;
          border-radius: @zoomIndex * 6px !important;
        }
        .getsitebtn {
          font-size: @zoomIndex * 16px !important;
          padding: 0 !important;
        }
      }
      .searchReturn {
        border-radius: @zoomIndex * 4px !important;
        .el-button {
          font-size: @zoomIndex * 16px !important;
          padding: 0 !important;
        }
      }
    }
  }
}
.choose-site {
  width: 100%;
  height: 0;
  .v-model {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 3000;
    opacity: 0.8;
    background-position: center;
    background-size: 100% 100%;
  }
  #mapDiv {
    position: absolute;
    top: -3vh;
    right: 20%;
    width: 60vw;
    height: 0;
    z-index: 3000;
    .title {
      width: 98%;
      font-size: x-large;
      padding: 1%;
    }
    #map {
      width: 100%;
      height: 83%;
    }
    .foot-btn {
      text-align: right;
      width: 98%;
      padding: 0.5% 1%;
      .el-button {
        padding: 10px 20px;
        font-size: 18px;
        border-radius: 6px;
      }
      .getsitebtn {
        font-size: 16px;
        padding: 0 !important;
      }
    }
    .searchSite {
      position: absolute;
      top: 11%;
      left: 1%;
      width: 35%;
    }
    .searchReturn {
      min-height: 50%;
      width: 100%;
      overflow: auto;

      border-radius: 4px;
      .el-button {
        width: 98%;
        margin: 1%;
        padding: 2%;
        text-align: left;
        font-size: 16px;
        border: none;
      }
    }
  }
}
</style>
<style lang="less">
.choose-site {
  #mapDiv {
    .searchSite {
      .el-input {
        font-size: 14px !important;
        .el-input__inner {
          font-size: 16px !important;
          // border-radius: 8px !important;
          border-width: 1px !important;
          height: 40px !important;
          line-height: 40px !important;
        }
        .el-input__suffix {
          .el-input__icon {
            width: 25px !important;
            font-size: 14px !important;
            line-height: 40px !important;
          }
        }
      }
    }
    #map {
      .amap-ui-control-layer {
        box-shadow: 0 1px 5px rgb(0 0 0 / 40%) !important;
        border-radius: 3px !important;
        .amap-ui-control-layer-toggle {
          width: 34px !important;
          height: 34px !important;
          line-height: 34px !important;
          font-size: 22px !important;
        }
      }
      .amap-ui-control-layer-list {
        font-size: 16px !important;
        .amap-ui-control-layer-selector {
          margin-top: 2px !important;
          top: 1px !important;
          width: 14px !important;
          height: 14px !important;
          &[type="radio"] {
            margin: 3px 3px 0px 5px !important;
          }
          &[type="checkbox"] {
            margin: 3px 3px 3px 4px !important;
          }
        }
        .amap-ui-control-layer-separator {
          border-top: 1px solid #ddd !important;
          // margin: 5px -10px 5px -6px !important;
        }
      }
      .amap-logo {
        bottom: 1.5px !important;
        left: 4px !important;
        height: 20px !important;
        img {
          width: 73px !important;
          height: 20px !important;
        }
      }
      .amap-copyright {
        left: 85px !important;
        height: 16px !important;
        bottom: 1.8px !important;
        padding-bottom: 2px !important;
        font-size: 11px !important;
      }
    }
  }
}
.message-info {
  z-index: 9999 !important;
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .choose-site {
    #mapDiv {
      .searchSite {
        .el-input {
          font-size: @zoomIndex * 14px !important;
          .el-input__inner {
            font-size: @zoomIndex * 16px !important;
            // border-radius: 8px !important;
            border-width: @zoomIndex * 1px !important;
            height: @zoomIndex * 40px !important;
            line-height: @zoomIndex * 40px !important;
          }
          .el-input__suffix {
            .el-input__icon {
              width: @zoomIndex * 25px !important;
              font-size: @zoomIndex * 14px !important;
              line-height: @zoomIndex * 40px !important;
            }
          }
        }
      }
      #map {
        .amap-ui-control-layer {
          box-shadow: 0 @zoomIndex * 1px @zoomIndex * 5px rgb(0 0 0 / 40%) !important;
          border-radius: @zoomIndex * 3px !important;
          .amap-ui-control-layer-toggle {
            width: @zoomIndex * 34px !important;
            height: @zoomIndex * 34px !important;
            line-height: @zoomIndex * 34px !important;
            font-size: @zoomIndex * 22px !important;
          }
        }
        .amap-ui-control-layer-list {
          font-size: @zoomIndex * 16px !important;
          .amap-ui-control-layer-separator {
            border-top: @zoomIndex * 1px solid #ddd !important;
            // margin: @zoomIndex * 5px @zoomIndex * -10px @zoomIndex * 5px
            //   @zoomIndex * -6px !important;
          }
          .amap-ui-control-layer-selector {
            margin-top: @zoomIndex * 2px !important;
            top: @zoomIndex * 1px !important;
            width: @zoomIndex * 14px !important;
            height: @zoomIndex * 14px !important;
            &[type="radio"] {
              margin: @zoomIndex * 3px @zoomIndex * 3px @zoomIndex * 0px
                @zoomIndex * 5px !important;
            }
            &[type="checkbox"] {
              margin: @zoomIndex * 3px @zoomIndex * 3px @zoomIndex * 3px
                @zoomIndex * 4px !important;
            }
            // &input[type="radio"] {
            //   margin: 3px 3px 0px 5px !important;
            // }
          }
        }
        .amap-logo {
          bottom: @zoomIndex * 1.5px !important;
          left: @zoomIndex * 4px !important;
          height: @zoomIndex * 20px !important;
          img {
            width: @zoomIndex * 73px !important;
            height: @zoomIndex * 20px !important;
          }
        }
        .amap-copyright {
          left: @zoomIndex * 85px !important;
          height: @zoomIndex * 16px !important;
          bottom: @zoomIndex * 1.8px !important;
          padding-bottom: @zoomIndex * 2px !important;
          font-size: @zoomIndex * 11px !important;
        }
      }
    }
  }
}
</style>
