<!-- 滚动加载更多 -->
<template>
  <div
    class="custom-scroll-list"
    style="position: relative; width: 100%; height: 100%"
  >
    <div
      @scroll="tableScroll"
      :style="outerStyle"
      class="scroll-list scrollbar-style"
      ref="scrollDiv"
    >
      <!-- 插入的内容 -->
      <slot
        name="content"
        :data="tableData"
        :current="current"
        v-if="tableData.length !== 0 && isShowMain"
      ></slot>

      <!-- 无数据时显示 -->
      <slot name="noData" v-if="tableData.length == 0 && loadStatus == 0">
        <div class="scroll-list-no-data">{{ noText }}</div>
      </slot>

      <!-- 底部显示内容 -->
      <slot name="footer" :loadStatus="loadStatus">
        <!-- 加载失败 -->
        <div class="load-cell fail-load" v-if="loadStatus == 1">
          <i class="el-icon-document-delete mr5"></i>
          <span class="mr5">{{ language.loadFail }}</span>
          <el-link
            style="font-size: 12px"
            :underline="false"
            type="primary"
            @click="initData"
          >
            {{ language.reload }}
          </el-link>
        </div>

        <!-- 加载中 -->
        <div class="load-cell loading" v-if="loadStatus == 2">
          <i class="el-icon-loading mr5"></i> {{ language.loading }}
        </div>
      </slot>
    </div>

    <!-- 回到头部 -->
    <slot name="returnTop">
      <div
        class="el-icon-s-flag return-top"
        :title="language.returnTop"
        v-if="getBackTop"
        @click="returnTopEvent"
        v-show="isShowTop"
      ></div>
    </slot>
  </div>
</template>

<script>
import request from "@/utils/api";
import { jsonApi } from "@/utils/jsonTestApi";
export default {
  props: {
    data: Array, // 数据
    // 滚动距离底部多少距离触发加载下一页数据
    distanceBootm: {
      type: Number,
      default: 15,
    },
    // 滚动到底部一定的时间内只触发一次
    time: {
      type: Number,
      default: 100,
    },
    urlName: String, // 请求名称，不填则不会进行请求
    // 请求参数
    params: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 分页参数字段
    pageProps: {
      type: Object,
      default: () => {
        return {
          current: "page", // 当前页
          pageSize: "size", // 每页大小字段
        };
      },
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pmd: String,
    isJson: Boolean,
    noDataText: {
      type: String,
      default: "",
    },
    // 自动发起请求
    isAutoHttp: {
      type: Boolean,
      default: true,
    },
    // 动画延时时间，相比前一个数据
    delayed: {
      type: Number,
      default: 0.2,
    },
    // 距离头部多少的时候显示回到顶部按钮
    top: {
      type: Number,
      default: 100,
    },
    // 是否显示回到顶部
    getBackTop: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      isLoading: false,
      noText: this.noDataText,
      current: 0,
      total: 0,
      loadStatus: 0, // 1加载失败，2加载中
      isShowMain: true,
      isShowTop: false,
    };
  },
  watch: {
    data: {
      deep: true,
      handler(val) {
        this.current = 1;
        this.tableData = [];
        this.tableData = val.slice(0, this.pageSize);
      },
    },
  },
  computed: {
    outerStyle: function () {
      let nodeData = this.tableData.length == 0 && this.loadStatus == 0;
      return {
        height: nodeData ? "100%" : "",
      };
    },
    language() {
      return this.$languagePackage.components.scrollList;
    },
  },
  created() {
    this.isAutoHttp && this.initData();
    if (!this.noText) {
      this.noText = this.language.noDataText;
    }
  },
  methods: {
    initData: function () {
      if (!this.urlName) {
        this.current = 1;
        this.tableData = this.data ? this.data.slice(0, this.pageSize) : [];
        return false;
      }

      this.loadStatus = 2;

      let params = Object.assign({}, this.params);
      let props = this.pageProps;
      params[props.current] = this.current;
      params[props.pageSize] = this.pageSize;
      // 添加pmd参数
      let pmd = "";
      if (this.pmd) {
        let list = this.pmd.split(",");
        list.forEach((item) => {
          pmd += params[item];
        });
      }
      params.pmd = pmd;
      let http = this.isJson ? jsonApi : request;
      http(this.urlName, params)
        .then((res) => {
          let data = res.data;
          let next = 0;
          if (data.list) {
            for (let i = 0; i < data.list.length; i++) {
              let item = data.list[i];
              item.delayed = next;
              next += this.delayed;
              if (this.urlName == "deviceList" && params.type == 0) {
                if (item.type !== 200 && item.type !== 100) {
                  this.tableData.push(item);
                }
              } else {
                this.tableData.push(item);
              }
            }
          } else {
            this.tableData = [];
          }
          this.current++; // 当前页

          this.total = data.total_page ? data.total_page * this.pageSize : 0; // 总数不准
          this.loadStatus = 0;
          this.isLoading = false;

          // 请求响应
          this.$emit("requestSuccess", data);
        })
        .catch(() => {
          this.loadStatus = 1;
          this.isLoading = false;
        });
    },
    tableScroll: function (e) {
      if (this.isLoading) {
        return false;
      }
      let parentH = e.srcElement.parentNode.offsetHeight; // 父元素的高度
      let scrollTop = e.srcElement.scrollTop; // 滚动的距离
      let elH = e.srcElement.scrollHeight; // 本身高度
      if (scrollTop > this.top) {
        this.isShowTop = true;
      } else {
        this.isShowTop = false;
      }

      if (scrollTop + parentH >= elH - this.distanceBootm) {
        this.$emit("onEarth");
        this.isLoading = true;
        //
        if (!this.urlName) {
          // 如果是自定义数据，则进行处理
          this.data && this.disposeData();
        } else {
          if (this.current >= this.total / this.pageSize) {
            this.isLoading = false;
            return "加载完毕，不继续加";
          }
          this.initData();
        }
      }
    },
    // 刷新
    refresh: function (current = 0) {
      this.current = current;
      this.tableData = [];
      if (this.urlName) {
        this.initData();
      } else {
        this.data && this.disposeData();
      }
    },

    disposeData: function () {
      if (this.tableData.length == this.data.length) {
        this.isLoading = false;
        return false;
      }
      let start = this.tableData.length;
      let end = this.pageSize + start - 1;
      let list = this.data.slice(start, end);
      this.tableData = this.tableData.concat(list);
      this.current++;
      setTimeout(() => {
        this.isLoading = false;
      }, this.time);
    },
    returnTopEvent: function () {
      let time = setInterval(() => {
        let top = this.$refs.scrollDiv.scrollTop;
        if (top <= 0) {
          clearInterval(time);
          this.$refs.scrollDiv.scrollTop = 0;
        }
        this.$refs.scrollDiv.scrollTop = top - 10;
      }, 3);
      this.isShowTop = false;
    },
  },
};
</script>

<style lang="less" scoped>
.load-cell {
  width: 100%;
  height: 40px;
  // color: #fff;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  .el-link--inner {
    font-size: 12px;
  }
}
.fail-load {
  // color: red;
}
.scroll-list-no-data {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  // color: #fff;
}
.scroll-list {
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
}

.return-top {
  position: absolute;
  bottom: 10px;
  right: 10px;
  cursor: pointer;
  // color: #fff;
  font-size: 24px;
  z-index: 20;
}
</style>