/**
 * 数据分析
 */
const dataAnalyse = {
    title: "无人机管理平台",
    name: "数据分析",
    createUser: "创建人",
    executor: "执行人",

    basicInfo: {
        title: "基本信息",
        uavInfo: {
            title: "设备信息",
            sn_id: "设备编号",
            code: "无人机编号",
            taskType: "任务类型",

        },
        flightTime: {
            title: "飞行时间概览",
            total: "总时长",
            start: "起飞",
            end: "着陆"
        },
        horizontalVelocity: {
            title: "水平速度概览",
            average: '平均水平速度',
            min: "最小水平速度",
            max: "最大水平速度"
        },
        verticalVelocity: {
            title: "垂直速度概览",
            average: "平均垂直速度",
            min: "最小垂直速度",
            max: "最大垂直速度"
        },
        flightHeight: {
            title: "飞行高度概览",
            average: "平均飞行高度",
            min: "最小飞行高度",
            max: "最大飞行高度"
        },
        flightDistance: {
            title: "飞行总距离"
        },
    },

    lineChart: {
        title: "高度、速度折线图",
        height: {
            title: "飞行高度",
            chartName: "飞行高度"
        },
        horizontalVelocity: {
            title: "飞行水平速度",
            chartName: "水平速度"
        },
        verticalVelocity: {
            title: "飞行垂直速度",
            chartName: "垂直速度"
        }
    },

    flightpath: {
        title: "飞行轨迹",
    },

    resultsShow: {
        title: "成果展示"
    },

    message: {
        loading: "数据加载中...",
        dataGetErr: "数据获取失败",
        filesAnalysisErr: "文件解析失败"
    }
}
export default dataAnalyse;