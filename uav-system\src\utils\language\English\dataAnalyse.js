/**
 * 数据分析
 */
const dataAnalyse = {
    title: "Uav management platform",
    name: "Data analysis",
    createUser: "Creator",
    executor: "Executor",

    // 
    basicInfo: {
        title: "Basic Information",
        uavInfo: {
            title: "Device Information",
            sn_id: "Device Number",
            code: "Drone Number",
            taskType: "Task Type"
        },
        flightTime: {
            title: "An overview of flight times",
            total: "Total time",
            start: "Take off",
            end: "Land"
        },
        horizontalVelocity: {
            title: "Horizontal velocity",
            average: 'Average',
            min: "Min",
            max: "Max"
        },
        verticalVelocity: {
            title: "Vertical velocity",
            average: "Average",
            min: "Min",
            max: "Max"
        },
        flightHeight: {
            title: "Flight altitude",
            average: "Average",
            min: "Min",
            max: "Max"
        },
        flightDistance: {
            title: "Flight distance"
        },
    },

    // 
    lineChart: {
        title: "Height, speed line chart",
        height: {
            title: "Flight altitude",
            chartName: "Flight altitude"
        },
        horizontalVelocity: {
            title: "Horizontal flight speed",
            chartName: "Horizontal velocity"
        },
        verticalVelocity: {
            title: "Vertical velocity of flight",
            chartName: "Vertical velocity"
        }
    },

    // 
    flightpath: {
        title: "Flightpath",
    },

    // 
    resultsShow: {
        title: "Results show"
    },

    // 
    message: {
        loading: "Data loading",
        dataGetErr: "Data acquisition failure",
        filesAnalysisErr: "File parsing failed"
    }
}
export default dataAnalyse;