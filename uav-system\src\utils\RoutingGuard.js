import router from "../router/index"
import {
    getCookie,
    removeCookie
} from "./storage"
import store from "../store"
import {
    getLocalStorage
} from "@/utils/storage";
import Vue from "vue";
/**
 * @param {to} 将要进入的路由信息
 * @param {form} 将要离开的路由信息
 * @param {next} 执行进入下一个路由
 */
router.beforeEach((to, form, next) => {
    let title = Vue.prototype.$language == "english" ? 'Drone Management Platform' : '无人机机巢系统'
    if (to.meta.title) {
        document.title = to.meta.title + "-" + title
    } else {
        document.title = title

    }
    let userInfo_1 = getLocalStorage('userInfo');
    let fun_list = userInfo_1 ? userInfo_1.fun_list : []

    // 不需要校验token就可以直接进入的页面 
    let isNoToken = ["login"]
    if (isNoToken.indexOf(to.name) !== -1) {
        store.commit("setMenuList", []);
        removeCookie("token");
        if (store.state.websocket.ws && store.state.websocket.like) {
            store.state.websocket.ws.close()
            console.log("卸载ws-------------")
        }
        store.commit("wsMessage", null); // 卸载单个
        store.commit("unintallMultiMessage", null); // 卸载多个
        next();
        return;
    }

    // 每次进入新页面时卸载ws接收参数事件
    if (store.state.websocket.ws && store.state.websocket.like) {
        store.commit("wsMessage", null); // 卸载单个
        store.commit("unintallMultiMessage", null); // 卸载多个
    }

    // 如果项目没有创建ws
    if (!store.state.websocket.ws && !store.state.websocket.like) {
        if (fun_list.indexOf("super_administrator") !== -1 || fun_list.indexOf('device_10') !== -1) {
            store.dispatch("createWS");
        }
    }

    // 校验token
    let token = getCookie("token");
    if (!token) {
        store.commit("setMenuList", []);
        router.push({
            name: "login"
        });
        return false;
    }

    // 判断没有用户信息，则从本地取出
    let user = store.state.user;
    if (!user.userInfo.token) {
        let userInfo = getLocalStorage('userInfo');
        store.commit('setUserInfo', userInfo);
    }

    // 获取菜单，动态注册路
    if (store.state.menus.menuList.length === 0) {
        store.dispatch("setMenuList", {
            to,
            form,
            next,
            fun_list
        });
        return false;
    }
    next();


})