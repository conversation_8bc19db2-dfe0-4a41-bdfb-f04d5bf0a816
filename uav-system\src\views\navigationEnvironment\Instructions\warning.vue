<!-- 警告信息 -->
<template>
  <layouts :title="warning.title">
    <template v-slot:content>
      <div class="content-main">
        <div class="mb8" style="font-size: 14px">{{ warning.content }}</div>
        <div
          class="content-main-item mb5"
          v-for="(item, index) in IMType"
          :key="index"
        >
          <div
            class="item-left mr5"
            :style="{ backgroundColor: colorList[item.value] }"
          ></div>
          <div class="item-right">{{ item.label }}</div>
        </div>
      </div>
    </template>
  </layouts>
</template>

<script>
import layouts from "./layout.vue";
export default {
  components: {
    layouts,
  },
  data() {
    return {
      IMType: [
        { label: "红色严重警告", value: "1" },
        { label: "黄色警告", value: "2" },
        { label: "绿色正常数据信息", value: "3" },
      ],
      colorList: {
        1: "rgba(29, 245, 14, 1)",
        2: "rgba(222, 245, 14, 1)",
        3: "#FC0900",
      },
    };
  },
  computed: {
    warning() {
      return this.$languagePackage.navigation.instructions.warning;
    },
  },
  created() {
    this.IMType[0].label = this.warning.type[0];
    this.IMType[1].label = this.warning.type[1];
    this.IMType[2].label = this.warning.type[2];
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .content-main {
    font-size: @radio * 12px !important;
    .content-main-item {
      .item-left {
        width: @radio * 10px !important;
        height: @radio * 10px !important;
        border-radius: @radio * 2px !important;
      }
    }
  }
}

.content-main {
  font-size: 12px;
  // color: #fff;
  .content-main-item {
    display: flex;
    align-items: center;
    .item-left {
      width: 10px;
      height: 10px;
      border-radius: 2px;
    }
  }
}
</style>