<template>
  <div class="editDialog">
    <el-dialog
      :title="language.editTask"
      :visible.sync="show"
      :center="true"
      custom-class="task-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :destroy-on-close="true"
      width="650px"
    >
      <el-form label-width="160px" :model="form" :rules="rules" ref="form">
        <el-form-item :label="language.taskTime" prop="type">
          <el-date-picker
            v-model="form.task_tms"
            type="datetime"
            :placeholder="language.placeholderTime"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item :label="language.taskStateLabel">
          <el-radio-group v-model="form.task_state">
            <el-radio
              v-for="item in taskState"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="language.dataStateLabel">
          <el-radio-group v-model="form.data_state">
            <el-radio
              v-for="item in dataState"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <slot name="footer">
          <el-button type="primary" @click="submit">{{
            language.submit
          }}</el-button>
          <el-button @click="closeDialog">{{ language.cancel }}</el-button>
        </slot>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
export default {
  data() {
    return {
      form: {
        id: "",
        task_tms: "",
        task_state: "",
        data_state: "",
      },
      rules: {},
      show: false,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.taskManage;
    },
    dataState() {
      return this.language.dataStateList;
    },
    taskState() {
      let list = this.language.taskState;
      let resule = list.filter((item) => {
        return item.value == 1 || item.value == 3;
      });
      return resule;
    },
  },
  methods: {
    open(row) {
      for (const key in this.form) {
        this.form[key] = row[key];
      }
      this.show = true;
      console.log(this.form);
    },
    submit() {
      let data = Object.assign({}, this.form);
      if (typeof data.task_tms !== "number") {
        data.task_tms = data.task_tms.getTime();
        data.pmd =
          data.id.toString() +
          data.task_tms.toString() +
          data.task_state.toString() +
          data.data_state.toString();
      } else {
        delete data.task_tms;
        data.pmd =
          data.id.toString() +
          data.task_state.toString() +
          data.data_state.toString();
      }
      data.os_timestampCode = true;
      requestHttp("taskEdit", data).then((res) => {
        this.$message.success(this.language.editSuccess);
        this.closeDialog();
        this.$emit("refresh", "");
      });
    },
    closeDialog() {
      this.show = false;
      this.form = {
        id: "",
        task_tms: "",
        task_state: "",
        data_state: "",
      };
    },
  },
};
</script>
<style lang="less">
.editDialog {
  .task-dialog {
    .el-dialog__header {
      border-bottom: 1px solid #0c31ac;
      .el-dialog__title {
        font-size: 24px !important;
        font-weight: 550;
      }
    }
    .el-dialog__body {
      padding-bottom: 0 !important;
      .el-date-editor.el-input,
      .el-date-editor.el-input__inner {
        width: 80%;
      }
      .el-radio-group {
        width: 100%;
        .el-radio {
          min-width: 15%;
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .editDialog {
    .task-dialog {
      .el-dialog__header {
        .el-dialog__title {
          font-size: @zoomIndex * 24px !important;
        }
      }
      input::-webkit-input-placeholder {
        color: rgb(167, 167, 167) !important;
      }
      input::-moz-input-placeholder {
        color: rgb(167, 167, 167) !important;
      }
      input::-ms-input-placeholder {
        color: rgb(167, 167, 167) !important;
      }
    }
  }
}
</style>