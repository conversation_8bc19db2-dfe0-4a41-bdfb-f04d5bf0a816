const firmware = {
    language: 'en-US',
    typeTitle: "Type:",
    uploadTitle: 'Upload Firmware',
    typeList: [
        { label: "All", value: "" },
        { label: "Machine Nest", value: 17 },
    ],
    normal: 'Normal',
    edit: 'Edit',
    down: 'Down',
    column: {
        type: "Type",
        version_code: "VERSION",
        version_name: "Version name",
        state: "State",
        description: "description",
        description_en: "description-en",
        create_time: 'CreateTime',
        operation: "Operation"
    },
    upgradeFirmware: {
        dialogTitle: 'Upgrade Firmware',
        dialogTitle1: 'Upload Firmware',
        dialogTitle2: 'Edit Firmware',
        fileTitle: 'Firmware Files',
        type: "Type",
        vCode: 'VERSION',
        vName: 'Version name',
        desc: 'description',
        desc_en: 'description-en',
        placeholder: 'Select File Automatic Generation',
        placeholder1: 'Please enter English upgrade instructions',
        errorFile: 'File selection error, please reselect',
        clickChoose: 'Click to select',
        uploadTip: 'Only wkimg suffix files can be uploaded',
        submit: 'Confirm',
        cancel: 'Cancel',
        errorTip: 'Please upload the firmware file',
        loadingText: 'Uploading firmware...',
        loadingText1: 'Updating firmware...',
        successTip: 'Firmware update successful',
        successTip1: 'Firmware upload successfu',
        reupgrade: 'Re-upload',
        upgraded: 'Uploaded',
        upgradeText: 'upgrade',
        tipText: "Are you sure to choose this version for upgrade?",
        Tip: 'Tips'

    },
}
export default firmware;