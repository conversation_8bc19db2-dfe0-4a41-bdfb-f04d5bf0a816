/**
 * 组件相应
 */
const components = {
    customDialog: {
        confirm: "确定",
        cancel: "取消"
    },
    customTable: {
        noData: "暂无可查询数据",
        index: "序号",
        loading: "拼命加载中..."
    },
    infoTip: {
        confirm: "确 定",
        cancel: "取 消"
    },
    pages: {
        prevText: "上一页",
        nextText: "下一页"
    },

    scrollConfirm: {
        showText: "向右滑动确认继续"
    },

    scrollList: {
        loadFail: "加载失败",
        refresh: "刷新",
        loading: "努力加载中...",
        returnTop: "回到顶部",
        noDataText: "暂无可查询数据",
        reload: "重新加载"
    },

    twiceDatePicker: {
        start: "开始日期",
        end: "结束日期",
        cancelText: "取消"
    },

    video: {
        errorInfo: {
            1: "等待连接",
            2: "正在连接",
            3: "网络连接错误",
            4: "播放地址不存在"
        },
        reconnection: "重新连接",
        videoDialog: {
            error: '你的浏览器不支持video标签',
            errorList: {
                1: '加载失败，视频链接不可用'
            }
        }
    },
    dialogInfo: {
        equipName: '设备名：',
        equipSn: '设备编号：'
    }
}

export default components;