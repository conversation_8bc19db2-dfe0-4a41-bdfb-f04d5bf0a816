let urls = {
    pc: "http://localhost:8081",
    phone: "http://localhost:8080"
}
export function context() {
    var ua = navigator.userAgent,
        isWindowsPhone = /(?:Windows Phone)/.test(ua),
        isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
        isAndroid = /(?:Android)/.test(ua),
        isFireFox = /(?:Firefox)/.test(ua),
        isChrome = /(?:Chrome|CriOS)/.test(ua),
        isTablet = /(?:iPad|PlayBook)/.test(ua) || (isAndroid && !/(?:Mobile)/.test(ua)) || (isFireFox &&
            /(?:Tablet)/.test(ua)),
        isPhone = /(?:iPhone)/.test(ua) && !isTablet,
        isPc = !isPhone && !isAndroid && !isSymbian;
    return {
        isTablet: isTablet,
        isPhone: isPhone,
        isAndroid: isAndroid,
        isPc: isPc
    };
};

export function redirect() {
    console.log("------->", navigator.userAgent);
    let os = context();
    if (os.isAndroid || os.isPhone) {
        console.log("手机")
            // window.location.href = urls.phone;

    } else if (os.isTablet) {
        console.log("平板")
    } else if (os.isPc) {
        console.log("电脑")
            // window.location.href = urls.pc;
            // window.open(urls.pc)
    }
}


//获取操作系统类型，判断是Android或者IOS
export function getOSType() {
    if (/(Android)/i.test(navigator.userAgent)) {
        return 0;
    } else if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
        return 1;
    } else {
        return 2;
    }
}

//判断当前环境是否是微信环境
export function is_weixin() {
    var ua = navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == "micromessenger") {
        return true;
    } else {
        return false;
    }
}