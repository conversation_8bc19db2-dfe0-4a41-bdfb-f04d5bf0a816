// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'

// 按需引入
import {
    initElement
} from "@/utils/elementUI/index"
initElement(Vue);

import App from './App'
import router from './router'
import * as filters from "./filters/filter"
Vue.config.productionTip = false
console.log("切换")
    // 引入语言包
import "./utils/language/index"

// 引入css预处理less
import less from 'less'
Vue.use(less)

// 加载md5
import "@/assets/js/md5.min.js"

//过滤器声明使用
Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
})

// 引入vuex
import store from "./store/index"

// 引入路由守卫
import "./utils/RoutingGuard"
import "./assets/css/common.less";

import {
    redirect
} from "./utils/context.js"
redirect();
// // 正式环境清除所有console.log
if (process.env.NODE_ENV === 'production') {
    if (window) {
        window.console.log = function() {};
    }
}
// 监听用户切换浏览器tab
if (document.hidden !== undefined) {
    document.addEventListener('visibilitychange', () => {
        // 当前页面被显示时判断ws是否连接成功
        // if(!document.hidden){
        //   if(!store.state.websocket.like && router.currentRoute.name !== "login"){
        //     store.dispatch("createWS");
        //   }else
        // }
        if (!document.hidden && !store.state.websocket.like && router.currentRoute.name !== "login" && router.currentRoute.name !== "navigationEnvironment") {
            store.dispatch("createWS");
        }
    })
}

// 粒子系统
import VueParticles from 'vue-particles'
Vue.use(VueParticles)

// 引入echarts，按需引入
import echarts from "./utils/echarts/index"
Vue.prototype.$echarts = echarts;
Vue.prototype.$coordinateType = 'gcj02' //gcj02,wgs84

//获取当前位置信息
// import {
//     geolocation
// } from './utils/getLocation'
// geolocation()
/* eslint-disable no-new */
new Vue({
    el: '#app',
    router,
    render: h => h(App),
    store,
    template: '<App/>'
})