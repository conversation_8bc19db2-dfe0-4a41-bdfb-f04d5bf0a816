<template>
  <div class="navigation-environment">
    <div class="navigation-environment-main">
      <!-- 头部 -->
      <div class="header">
        <error-info
          :class="screenClass"
          ref="errorInfo"
          @showBattery="showBattery"
          :openIntroduced="
            () => {
              this.isShow = true;
            }
          "
        />
      </div>

      <!-- 主要内容 -->
      <div class="main">
        <div class="main-left">
          <!-- 实时信息 -->
          <transition name="el-fade-in">
            <div
              class="mb20"
              v-show="isShowInfo"
              :style="{ height: `calc(100% - ${leftVideoMapHeight + 20}px)` }"
            >
              <components
                :is="leftTopModelName"
                style="height: 100%"
                :class="screenClass"
                :websocket="websocket"
                ref="shoutLampBox"
              />
            </div>
          </transition>

          <left-video-map
            class="main-left-bottom"
            ref="leftVideo"
            :dblClickVideoEvent="packUModules"
            :type="equipmentType"
            :style="leftVideoMapStyle"
            :sortList.sync="videoMapList"
            :websocket="websocket"
          />
        </div>
        <div class="main-center">
          <!-- <div class="capacity-TF">
            {{ language.TFcapacity
            }}{{ sdcard_state == 1 ? sdcardAvailableSize : language.noSD }}
          </div> -->
          <div class="lnglatBtn">
            <el-button
              :icon="showLngLat ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"
              @click="() => (showLngLat = !showLngLat)"
            ></el-button>
          </div>
          <div class="lnglatContent" v-show="showLngLat">
            <div
              class="lnglatItem"
              v-for="(item, key) in latLngList"
              :key="key"
            >
              <div class="label">{{ item }}</div>
              <div class="value">
                {{
                  key == "altitude"
                    ? (staveThreeData[key] / 100).toFixed(2)
                    : staveThreeData[key] / (1e7).toFixed(7)
                }}
              </div>
            </div>
          </div>
          <data-overview
            :class="screenClass"
            :style="{ height: centerH }"
            ref="dataOverview"
            style="width: 600px"
          />
        </div>
        <!-- 右侧 -->
        <div class="main-right">
          <transition name="el-fade-in">
            <div class="main-right" :class="screenClass" v-show="isShowInfo">
              <div
                class=""
                :style="{
                  height: `calc(100% - ${introduces.rightBot.style.height} - 20px)`,
                  flexShrink: 0,
                }"
              >
                <components :is="rightTopModelName" style="height: 100%" />
              </div>

              <!-- <uav-operation

              /> -->
              <component
                :is="equipmentType == 200 ? 'single-uav' : 'uav-operation'"
                ref="uavOperation"
                class="mt20"
                :websocket="websocket"
                @openFlightStep="openFlightStep"
                @openRouteStep="openRouteStep"
                v-show="layerShow"
                style="flex-grow: 1"
              ></component>
            </div>
          </transition>
        </div>
      </div>
    </div>

    <!-- 操作说明 -->
    <operation-introduced
      :show.sync="isShow"
      :config="introduces"
      :type="equipmentType"
      :sortList="videoMapList"
    ></operation-introduced>

    <div class="home-bg" v-show="isShow"></div>

    <!-- 飞行步骤 -->
    <component
      :is="
        equipmentType == 10
          ? 'flight-step'
          : equipmentType == 12
          ? 'flight-step-large'
          : 'flight-step-mini'
      "
      ref="flightStep"
      @cutMagnify="cutMagnify"
    ></component>
    <route-step ref="routeStep"></route-step>
    <battery-box ref="batteryBox"></battery-box>
    <warn-box v-if="warnCode"></warn-box>
  </div>
</template>

<script>
import errorInfo from "./modules/errorInfo.vue";
import realtimeMessage from "./modules/realtimeMessage.vue";
import ShoutLampBox from "./modules/shoutLampBox.vue";
import dataOverview from "./modules/dataOverview.vue";
import checkingType from "./modules/checkingType.vue";
import uavOperation from "./modules/uavOperation.vue";
import leftVideoMap from "./modules/leftVideoMap.vue";
import flightStep from "./modules/flightStep.vue";
import flightStepLarge from "./modules/flightStepLarge.vue";
import flightStepMini from "./modules/flightStepMini.vue";
import singleUav from "./modules/singleUav.vue";
import operationIntroduced from "./Instructions/index.vue";
import weatherModule from "./modules/weatherModule.vue";
import routeStep from "./modules/routeStep.vue";
import warnBox from "./components/warnBox.vue";

import baseUrl from "@/utils/global";
import { Websockets } from "@/utils/websocketUntil";
import batteryBox from "./modules/batteryBox.vue";
export default {
  components: {
    errorInfo,
    realtimeMessage,
    dataOverview,
    checkingType,
    uavOperation,
    leftVideoMap,
    operationIntroduced,
    flightStep,
    flightStepLarge,
    flightStepMini,
    singleUav,
    weatherModule,
    batteryBox,
    routeStep,
    warnBox,
    ShoutLampBox,
  },
  data() {
    return {
      centerH: "87px",
      isShowInfo: true,
      websocket: null,
      isShow: false,
      flightParam: {},
      equipmentType: 0,
      equipmentType: 10,
      videoMapList: [],
      sendTimeLoop: "",
      layerShow: true,
      introduces: {
        leftTop: {
          is: "warning",
          isShow: false,
          style: {},
        },
        leftBot: {
          is: "video-map",
          isShow: true,
          style: {
            height: 0,
          },
        },
        rightTop: {
          is: "achievement",
          isShow: false,
          style: {},
        },
        rightBot: {
          is: "instruct",
          isShow: true,
          style: {
            height: 0,
          },
        },
      },
      query: {},
      latLngList: {
        longitude: "经度：",
        latitude: "纬度：",
        altitude: "海拔高度（m）：",
      },
      showLngLat: false,
      warnCode: false,
      warnTip: false,
    };
  },
  computed: {
    screenClass: function () {
      return this.$store.state.equipment.screenZIndex;
    },
    // 左侧视频地图样式
    leftVideoMapStyle: function () {
      return {
        minHeight: this.leftVideoMapHeight + "px",
        height: this.leftVideoMapHeight + "px",
      };
    },
    leftVideoMapHeight: function () {
      let height = {
        10: 156 * 2 + 20,
        12: 156 * 3 + 20,
        100: 156 * 2 + 20,
        200: 156,
      };
      let type = this.equipmentType;
      // this.equipmentType == 50 || this.equipmentType == 45
      //   ? 10
      //   : this.equipmentType;
      return height[type] ? height[type] : height[10];
    },

    // 右上角模块名称
    rightTopModelName: function () {
      // let model = "checkingType";
      let model = "weatherModule";
      // let model = "";
      return model;
    },
    leftTopModelName: function () {
      // let model = "realtimeMessage";
      let model = "ShoutLampBox";
      return model;
    },
    sdcard_state() {
      return this.$store.state.equipment.staveTwoData.sdcard_state;
    },
    sdcardAvailableSize() {
      let size = this.$store.state.equipment.staveTwoData.sdcard_available_size;
      if (!size) {
        return "";
      }
      let num = (Number(size) / 1024).toFixed(2);
      return `${num}G`;
    },
    language() {
      return this.$languagePackage.navigation.cameraConfig.more;
    },
    languageBatteryBox() {
      return this.$languagePackage.navigation.batteryBox;
    },
    staveThreeData() {
      return this.$store.state.equipment.staveThreeData;
    },
    // closeEquipWs() {
    //   return this.$store.state.websocket.closeEquipWs;
    // },
  },
  // watch: {
  //   closeEquipWs(val) {
  //     if (val) {
  //       // console.log(this.websocket);
  //       // if (this.websocket) {
  //       //   this.websocket.manualClone();
  //       //   this.websocket = null;
  //       // }
  //     }
  //   },
  // },
  created() {
    this.websockerInit();
    // 获取缓存的数据
    let query = this.$route.query || {};
    this.query = query;
    this.equipmentType = query.type;
    this.layerShow =
      query.state == 2 && this.equipmentType != 200 ? false : true;
    this.latLngList = this.language.latLngList;
    if (query.state == 1) {
      // 获取航线
      let flightParam = sessionStorage.getItem("flightParam");
      try {
        if (!flightParam) {
          return false;
        }
        let data = JSON.parse(flightParam)[query.sn_id];
        // let flightCourse = data.route.filter((item) => {
        //   return query.m_id == item.m_id;
        // })[0];

        // let fence = data.fence.filter((item) => {
        //   return query.f_id == item.f_id;
        // })[0];

        // this.$store.commit("setFenceRoute", {
        //   flightCourse: flightCourse,
        //   fence: fence,
        // });
        this.$store.commit("setFenceRoute", {
          flightCourse: data.route,
          fence: data.fence,
        });
      } catch (error) {
        console.error(error);
      }
    }
  },
  mounted() {
    console.log(
      "码率",
      navigator.mediaDevices.getSupportedConstraints().sampleRate
    );
    document.addEventListener("visibilitychange", this.visibilitychange);
    window.addEventListener("beforeunload", (e) => this.beforeunloadHandler(e));
    this.$nextTick(() => {
      if (this.layerShow) {
        let operationH = this.$refs.uavOperation.$el.offsetHeight;
        this.introduces.rightBot.style.height = operationH + "px";
        this.introduces.rightBot.style.minHeight = operationH + "px";
      }
      // 动态获取各个组件高度，以自适应操作说明

      this.introduces.leftBot.style = this.leftVideoMapStyle;

      // this.$refs.flightStep.open(0);
      // let index = 0;
      // let time = setInterval(()=>{
      //   index ++
      //   if(index == 8){
      //     clearInterval(time);
      //   }

      //   this.$store.commit("setStaveTwoData", { start_one_key_step: index });
      // }, 2000)
    });
  },
  methods: {
    visibilitychange(e) {
      // if (e.target.visibilityState !== "hidden") {
      //   console.log(this.websocket)
      //   if (this.websocket) {
      //     if (!(this.websocket.ws && this.websocket.ws.readyState == 1)) {
      //       this.websocket.manualClone();
      //       this.websockerInit()
      //     }
      //   } else {
      //     this.websockerInit()
      //   }
      // }
    },
    // websocker初始化
    websockerInit: function () {
      let query = this.$route.query;
      this.websocket = new Websockets(baseUrl.WS_URL, {
        equipmentVerify: {
          sn_id: query.sn_id,
          type: query.type,
        },
        heartbeat: 30000,
        message: this.disposeData,
        close: this.websockeClose,
      });
      this.$store.commit("setEquipmentWS", this.websocket);
    },

    // 处理ws返回数据
    disposeData: function (val) {
      let msg_id = val.msg_id;
      let data = val.data;
      let staveTwoData = this.$store.state.equipment.staveTwoData;
      let newStaveTwoData = Object.assign({}, staveTwoData);

      let state = [434, 435, 436, 437];
      if (state.indexOf(msg_id) !== -1) {
        newStaveTwoData = Object.assign(newStaveTwoData, data);
        this.$store.commit("setStaveTwoData", newStaveTwoData);
        this.$store.commit("setCameraDefault", newStaveTwoData);
        // this.$store.commit("setStaveTwoData", newStaveTwoData);
        // this.$store.commit("setCameraDefault", {});
        // setTimeout(() => {
        //   newStaveTwoData = Object.assign(staveTwoData, data);
        //   this.$store.commit("setStaveTwoData", newStaveTwoData);
        //   this.$store.commit("setCameraDefault", newStaveTwoData);
        // }, 0);

        // return false;
      } else {
        switch (msg_id) {
          case 200: // 设备认证回传信息
            this.$store.commit("setEquipmentContent", data);
            this.$store.commit("setEquipmentLinkState", true);
            if (this.equipmentType !== "200" && this.query.state == 1) {
              this.websocket.virtualUp(); // 默认一直发虚拟遥感松开1500
              this.sendTimeWebcoket();
            }
            break;
          case 431: // 状态2回传信息
            this.$store.commit("setStaveTwoData", data);
            // this.$store.commit("setCameraDefault", data);
            break;
          case 432: // 状态3回传信息
            this.$store.commit("setStaveThreeData", data);
            if (data.flight_status == 2) {
              // this.$store.commit("setFlightTask", true);
            }
            this.warnCode = data.battery_remaining < 20;
            this.batteryTip(data.battery_remaining);
            break;
          case 442:
            console.log("下载航线-------->", data);
            break;
          case 438:
            this.$store.commit("setRtkState", data);
          default:
            break;
        }
      }
      let wsMmessageFun = this.$store.state.equipment.wsMmessageFun;
      for (let k in wsMmessageFun) {
        wsMmessageFun[k](msg_id, data);
      }

      // 下发给控制面板
      let uavOperation = this.$refs.uavOperation;
      uavOperation && uavOperation.disposeData(msg_id, data);

      // 下发给信息提示
      let errorInfo = this.$refs.errorInfo;
      errorInfo && errorInfo.disposeData(msg_id, data);

      let batteryBox = this.$refs.batteryBox;
      batteryBox && batteryBox.getMessage(msg_id, data);
      // 下发给实时信息
      // let realtimeMessage = this.$refs.realtimeMessage;
      // realtimeMessage && realtimeMessage.disposeData(msg_id, data);

      // 下发给成果
      // let checkingType = this.$refs.checkingType;
      // checkingType && checkingType.disposeData(msg_id, data);

      // 下发给信息预览
      let dataOverview = this.$refs.dataOverview;
      dataOverview && dataOverview.disposeData(msg_id, data);
      let routeStep = this.$refs.routeStep;
      routeStep && routeStep.disposeData(msg_id, data);
      let shoutLampBox = this.$refs.shoutLampBox;
      shoutLampBox && shoutLampBox.disposeData(msg_id, data);
    },
    //低电量提示
    batteryTip: function (val) {
      if (this.warnCode && !this.warnTip) {
        this.warnTip = true;
        this.$confirm(
          this.languageBatteryBox.content.replace("num", val),
          this.languageBatteryBox.tip,
          {
            confirmButtonText: this.languageBatteryBox.submit,
            type: "warning",
            showCancelButton: false,
          }
        ).then(() => {
          setTimeout(() => {
            this.warnTip = false;
          }, 600000);
        });
      }
    },
    websockeClose(e) {
      console.log("websocket断开连接", e);
      this.websocket.manualClone();
      this.websockerInit();
    },
    // 收起左右模块
    packUModules: function () {
      this.isShowInfo = !this.isShowInfo;
      this.$refs.leftVideo.dblClickChange(this.isShowInfo);
    },
    // 打开步骤
    openFlightStep: function (item) {
      this.$refs.flightStep.open(item);
    },
    openRouteStep: function () {
      this.$refs.routeStep.open();
    },
    cutMagnify: function () {
      this.$refs.leftVideo.cutItemMagnify("uav");
    },
    beforeunloadHandler(e) {
      if (this.websocket) {
        this.websocket.manualClone();
        this.websocket = "";
      }
    },
    sendTimeWebcoket() {
      clearInterval(this.sendTimeLoop);
      this.sendTimeLoop = setInterval(() => {
        let date = new Date();
        let data = {
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          day: date.getDate(),
          hour: date.getHours(),
          minute: date.getMinutes(),
          second: date.getSeconds(),
          time_zone: -(date.getTimezoneOffset() / 60),
        };
        this.websocket.manualSend(data, 405);
      }, 1000);
    },
    showBattery() {
      let batteryBox = this.$refs.batteryBox;
      batteryBox && batteryBox.showInfo();
    },
  },
  destroyed() {
    clearInterval(this.sendTimeLoop);
    window.removeEventListener("beforeunload", (e) =>
      this.beforeunloadHandler(e)
    );
    document.removeEventListener("visibilitychange", this.visibilitychange);
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .navigation-environment {
    .header {
      height: @radio * 50px !important;
    }

    .amplification-style {
      top: @radio * 100px !important;
      @heights: @radio * 100px;
      height: calc(100vh - @heights) !important;
    }

    .main {
      @heights: @radio * 90px;
      height: calc(100% - @heights);
      padding: @radio * 20px !important;
      .main-left {
        min-width: @radio * 282px !important;
        max-width: @radio * 282px !important;
      }

      .main-right {
        min-width: @radio * 282px !important;
        max-width: @radio * 282px !important;
      }
    }
  }
}
.navigation-environment {
  width: 100%;
  height: 100%;
  position: relative;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .uav-video {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    // background-color: #000;
  }

  .navigation-environment-main {
    height: 100%;
    width: 100%;
    position: relative;
    // z-index: 80;
  }
  .header {
    height: 50px;
  }
  .zindex-style {
    position: relative;
    z-index: 23;
  }

  .amplification-style {
    position: fixed;
    left: 0;
    top: 100px;
    height: calc(100vh - 100px) !important;
    width: 100%;
    z-index: 1;
    margin-top: 0 !important;
  }

  .main {
    display: flex;
    height: calc(100% - 90px);
    padding: 20px;
    justify-content: space-between;
    .main-left {
      height: 100%;
      min-width: 282px;
      max-width: 282px;
      display: flex;
      flex-direction: column;
      .main-left-bottom {
        position: relative;
      }
    }
    .main-center {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      .capacity-TF {
        background-color: rgba(0, 0, 0, 0.3);
        color: #fff;
        z-index: 23;
        padding: 0 20px;
        border-radius: 5px;
      }
      .lnglatBtn,
      .lnglatContent {
        background-color: rgba(0, 0, 0, 0.3);
        color: #fff;
        z-index: 23;
        padding: 0 20px;
        border-radius: 5px 5px 0 0;
        .el-button {
          background-color: transparent;
          border: none;
          padding: 0;
          color: #fff;
          font-size: 20px;
        }
      }
      .lnglatContent {
        padding: 0;
        padding-top: 4px;
        display: flex;
        align-items: center;
        width: 600px;
        // justify-content: space-between;
        .lnglatItem {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          .value {
            font-weight: bold;
          }
        }
      }
    }
    .main-right {
      min-width: 282px;
      max-width: 282px;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      z-index: 201;
    }
  }
}

.home-bg {
  // background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 22;
}
</style>
<style lang="less">
.navigation-environment {
  .main {
    .main-center {
      .lnglatBtn {
        [class*=" el-icon-"],
        [class^="el-icon-"] {
          font-weight: 900;
        }
      }
    }
  }
}
</style>