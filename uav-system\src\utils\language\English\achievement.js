const achievement = {
    form: {
        placeholder: {
            search: "Please enter task name/device ID",
            uav: "Select a device",
            type: "Please select a result",
            startTime: "Start date",
            endTime: "End date",
            searchButText: "Search",
        },

        listLabel: {
            sn_id: "Device Number",
            sort_id: "Flight Number",
            sort_name: "Mission Name",
            create_time: "Creation Time"
        },
        button: {
            search: "Search",
            submitDown: 'Confirm download',
            bulkDown: 'Bulk download',
            cancelbulkdown: 'Cancel bulk download',
            submitDel: 'Confirm delete',
            bulkDel: 'Bulk delete',
            cancelDel: 'Cancel delete',
            sortiesDown: 'Download',
            down: 'Download',
            del: 'Delete',
            sortieList: 'SortieList',
            imgLoading: 'loading...',
        },
        deleteInfo: {
            deleteTip: 'Are you sure to delete this achievement?',
            submitTip: 'Confirm Delete',
            cancel: 'Cancel',
            message: 'Successfully deleted',
            deleteTip1: 'Are you sure to delete the selected achievements?',
            downTip: 'Are you sure to download the selected results?',
            tip: 'Tips',
            submit: 'Confirm'
        },

    },
    flightLog: {
        table: {
            sort_id: "The flight number",
            mission_name: "Task name",
            sn_id: "Device ID",
            uav_id: "Drone",
            mission_type: "Task type",
            user_name: "Flight users",
            start_time: "Start time",
            end_time: "End time",
            count: "Number",
            operation: "Operation"
        },
        tipMessage: {
            sortieDelTip: "Are you sure to delete this sorty and the corresponding achievement data?",
            sortieDelTips: "Do you want to delete the selected sorties and corresponding achievement data?",
            achieveDelMsg: 'Are you sure to delete all achievements of this flight?',
            noChooseMsg: 'No sorty data selected!',
            deleteConf: 'Delete confirmation',
            confirm: 'Confirm',
            cancel: 'Cancel',
            successDel: 'Successfully deleted',
        },
        button: {
            search: "Search",
            data: "Data analysis",
            results: "Results",
            play: "Play",
            del: 'Delete achievements',
            down: 'Download',
            alternateRecord: 'AlternateRecord',
            sortieDel: 'Delete sorties',
            submitDel: 'Confirm delete',
            bulkDel: 'Bulk delete',
            cancelDel: 'Cancel delete',
        },
        recordList: [{
                prop: "sn_id",
                label: "Device ID",
                width: '80px'
            },
            // {
            // prop: "timestamp", label: "Alternate timestamp"
            // },
            {
                prop: "reason",
                label: "Alternate Reason"
            },
            {
                prop: "solution",
                label: "Treatment"
            },
            {
                prop: "timestamp",
                label: 'Alternate time',
                width: '90px'

            },
            {
                prop: "drone_int",
                label: "Drone coordinates"
            },
            {
                prop: "altn_int",
                label: "Coordinate of alternate landing point"
            },
            {
                prop: "place_int",
                label: "Airport coordinates"
            },
            {
                prop: "create_time",
                label: "Create Time",
                width: '90px'
            },
        ],
        altnReason: {
            0: "Not have",
            1: "Excessive position deviation",
            2: "One click cabin opening failure of the aircraft nest",
            3: "Visual recognition landing failed"
        },
        altnSolution: {
            0: "Invalid",
            1: "Perform only alternate flight",
            2: "Attempt to land after completing alternate flight path"
        },
        label: {
            title: 'Alternate Record',
            lat: "latitude:",
            lon: "longitude:"
        }
    }
}
export default achievement;