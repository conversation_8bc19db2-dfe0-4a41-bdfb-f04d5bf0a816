// import JSEncrypt from '../../node_modules/jsencrypt'
import "@/assets/js/jsencrypt.min.js"



//Rsa加密
export function getCode(str, name) {
  var publickey = ''
  if (name) {
    publickey = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDP+BKw2CM/h3F0+dUDdvCpPomH9UzNV+cHrTVmY+s2FAfgs0onKPa4DQHB0tiNv2sUEtKu02E4OW45yRg8iIajWpoGFnfG7YIDvRca2HMZb2wQVNQllg0OYCSMtsE6cZpaPdK0vVvc0yUNjmcrsiAuWGkrWzqQB6Ybrjl+cbFLawIDAQAB`

  } else {
    publickey = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDr7WHnU5Rv+XzDCrhsSB5svZUDMqpcB8bxrMFgmJfxKQ6kdimISeQazzwmxnrZZPI0Wg9PmlBkbmz27dJDU+/97lTEuXVFvoCKNyu14WOAHLJt/xdf4J9lIZnnBeG58kZy3QB+cqzCyy+kWy5RdU6e3kAP4Yb8EMYaXW7HaR2wZwIDAQAB`

  }

  var jes = new JSEncrypt()
  jes.setPublicKey(publickey)
  let data = jes.encrypt(str)
  return data
}

// // RSA解码
// export function getRsaDecode(val) {
//     var decrypt = new JSEncrypt()
//     var publickey = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDr7WHnU5Rv+XzDCrhsSB5svZUDMqpcB8bxrMFgmJfxKQ6kdimISeQazzwmxnrZZPI0Wg9PmlBkbmz27dJDU+/97lTEuXVFvoCKNyu14WOAHLJt/xdf4J9lIZnnBeG58kZy3QB+cqzCyy+kWy5RdU6e3kAP4Yb8EMYaXW7HaR2wZwIDAQAB`
//     decrypt.setPrivateKey(publickey) //解密公钥
//     var decryptMsg = decrypt.decrypt(val.toString()) //解密
//     return decryptMsg
// }
