import Vue from 'vue'
import {
  gcj02_to_wgs84,
  wgs84_to_gcj02
} from './wgs84_to_gcj02'

/**
 * 坐标转换
 */
let waypointCoordinateType = {
  10: "wgs84",
  20: "gcj02"
}
const coordinateType = Vue.prototype.$coordinateType
const typeObject = {
  "gcj02": wgs84_to_gcj02,
  "wgs84": gcj02_to_wgs84
}
export function pointsConvert(params) {
  let {
    point = [],
      type = 10,
      devicePoint = false //设备坐标，是否需要整除
  } = params
  if (devicePoint) {
    point = [point.lon_int / 1e7, point.lat_int / 1e7]
  }
  if (waypointCoordinateType[type] === coordinateType) {
    return point
  }
  return typeObject[coordinateType](point[0], point[1])
}
