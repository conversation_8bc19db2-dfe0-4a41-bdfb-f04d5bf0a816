<template>
  <div id="layout">
    <!-- 头部 -->
    <div class="layout-header">
      <layout-header></layout-header>
    </div>

    <!-- 内容 -->
    <div class="layout-main">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
import layoutHeader from "./components/header.vue";
export default {
  name: "layout",
  components: {
    layoutHeader,
  },
  data() {
    return {};
  },
  computed: {
    isShowMap() {
      return this.$store.state.map.isShowMap;
    },
  },
  created() {
    if (this.$route.name === "layout") {
      // this.$router.push({ name: 'home' })
    }
  },
  mounted() {
    this.$nextTick(() => {
      //   this.$store.dispatch('mapInit', {
      //     id: 'default-map',
      //   })
    });
  },
  beforeDestroy() {},
  methods: {},
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1200px) {
  @radio: 100vw / 1920px;

  @HEIGHT: @radio * 100px;
  #layout {
    .layout-header {
      width: 100%;
      height: @HEIGHT !important;
    }
    .layout-main {
      height: calc(100vh - @HEIGHT) !important;
      position: relative;
    }
  }
}
#layout {
  .layout-header {
    width: 100%;
    height: 100px;
  }
  .layout-main {
    height: calc(100vh - 100px);
    position: relative;
  }
}
</style>
