import store from "@/store"
const imgSrc = {
        10: require("@/assets/img/equipment/AC50.jpg"),
        12: require("@/assets/img/equipment/AC100.jpg"),
        50: require("@/assets/img/equipment/AC50MINI.jpg"),
        100: require("@/assets/img/equipment/move.jpg"),
        500: require("@/assets/img/equipment/R500.jpg"),
        1000: require("@/assets/img/equipment/R1000.jpg"),
        1800: require("@/assets/img/equipment/1800.jpg"),
        1900: require("@/assets/img/equipment/1900.jpg"),
        2000: require("@/assets/img/equipment/1900P.jpg"),
    }
    /** stable: 固定机巢
    stable_mini: 小机巢
    mobile: 移动机巢
    alone: 单兵
    */
let typeObject = {
    stable: {
        isStable: true,
        inCabin: true,
        lift: true,
        stopBtn: true,
    },
    stable_mini: {
        isStable: true,
        charger: true,
        stopBtn: true,
    },
    mobile: {
        isMobile: true
    },
    alone: {
        isAlone: true
    }
}

export function typeJudge(item) {
    let typeList = store.state.deviceType.typeList
    let index = typeList.findIndex(typeItem => typeItem.value === item.type)
    if (index !== -1) {
        let typeItem = typeList[index]
        item = Object.assign(item, typeObject[typeItem.cla_type])
        if (item.type === 12) {
            item.isExists = true
            item.uavOpen = true
            item.fitOn = true
            item.cellList = true
            item.stopBtn = false
            item.crashStop = true
        }
        if (item.type === 10) {
            item.charger = true
        }
        if (item.type == 45) {
            item.judgeVideo = true
        }
        if (item.isAlone) {
            item.imgSrc = imgSrc[item.uav_type] ? imgSrc[item.uav_type] : imgSrc[500]
        } else {
            item.imgSrc = imgSrc[item.type] ? imgSrc[item.type] : imgSrc[50]
        }
    }
    return item;
}