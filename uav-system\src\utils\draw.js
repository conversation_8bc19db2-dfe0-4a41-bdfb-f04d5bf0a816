import initMaps from "./maps";
import { renderPolyline } from './orthophoto'
import { orthoPhotoComputer } from './orthoPhotoComputer'
import { gcj02_to_wgs84, wgs84_to_gcj02 } from './wgs84_to_gcj02'
import { computedMethod, computeCenter, computedDistance } from './computedMap'
import { computerElevation } from './computerElevation'
import { computerElevationPoints, computerElevationArray } from './computerElevationData'
export default {
    methods: {
        //航线:绘制点
        drawMarker(text, center) {
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
                zIndex: 55,
            };
            let marker = initMaps.drawMarker(text, center, "marker-edit", params);
            marker.setMap(this.map);
            marker.id = text;
            this.markers.push(marker);
            this.markerClick(marker);
            if (!this.changeCode) {
                if (!this.importCode && !this.recallClick) {
                    this.changePoint = {
                        index: text - 1,
                        lat: parseFloat(marker.getPosition().kT.toFixed(7)),
                        lng: parseFloat(marker.getPosition().KL.toFixed(7)),
                        type: "add",
                    }
                }
                marker.on("dragstart", this.markerDragStart);
                marker.on("dragging", this.markerDrag);
                marker.on("dragend", this.markerDragEnd);
                marker.on("click", this.markerClick);
            }
            if (this.markers.length > 1) {
                let point1 = this.markers[this.markers.length - 1].getPosition()
                let point2 = this.markers[this.markers.length - 2].getPosition()
                this.distance += computedMethod(1, {
                    point1,
                    point2,
                });
                this.estTime = this.distance / this.$refs.routeEdit.routeForm.auto_speed + this.markers.length;
                this.computerElevationRoute(point1, point2, this.default_distance, text - 1)
                this.clearElevationCode = 0
            }
        },
        //航线、围栏：点点击事件
        markerClick(e) {
            if (this.drawend) {
                if (e.target) {
                    this.clickId = e.target.id;
                } else {
                    this.clickId = e.id;
                }
                // let back = "";
                if (this.code == 2) {
                    for (let index = 0; index < this.markers.length; index++) {
                        let content = this.markers[index].getContent();
                        if (this.clickId == this.markers[index].id) {
                            if (content.indexOf("active") == -1) {
                                let str = content.split("marker-o-edit");
                                content = str[0] + "marker-o-edit active" + str[1];
                            }
                        } else {
                            content = content.replace(" active", "");
                        }
                        this.markers[index].setContent(content);
                    }
                } else {
                    for (let index = 0; index < this.markers.length; index++) {
                        let content = this.markers[index].getContent();
                        if (this.clickId == this.markers[index].id) {
                            if (content.indexOf("active") == -1) {
                                let str = content.split("marker-edit");
                                content = str[0] + "marker-edit active" + str[1];
                            }
                        } else {
                            content = content.replace(" active", "");
                        }
                        this.markers[index].setContent(content);
                    }
                }
            }
            // else {
            //   // for (let index = 0; index < this.markers.length; index++) {
            //   //   let style = this.markers[index]._opts.style;
            //   //   style["background-color"] = "#FFFFFF";
            //   //   style["color"] = "#000000";
            //   //   this.markers[index].setStyle(style);
            //   // }
            // }
        },
        //航点拖拽开始
        markerDragStart(e) {
            this.routeFormCode = true;
            let index = this.markers.findIndex((item) => {
                return item.id == e.target.id;
            });
            this.startPoints = {
                lng: this.$refs.routeEdit.routeForm.point_json[index].lng,
                lat: this.$refs.routeEdit.routeForm.point_json[index].lat,
            }
            if (this.clearElevationCode < 50) {
                this.clearElevation(index)
                this.clearElevationCode++;
            }


        },
        //航点拖拽结束
        markerDragEnd(e) {
            let index = this.markers.findIndex((item) => {
                return item.id == e.target.id;
            });
            let a = computedMethod(2, {
                point1: e.lnglat,
                fence: this.fenceItem.paths,
            });
            if (!a) {
                this.$message.error(this.routeLanguage.placeholder4);
                this.markers[index].setPosition(
                    new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
                );
                this.changePoint = {
                    index: index,
                    lat: this.startPoints.lat,
                    lng: this.startPoints.lng,
                    type: "edit",
                };
                this.drawline();
                this.editCenterpoint(index);
                this.computedData();
                return false;
            }
            let point_json = this.$refs.routeEdit.routeForm.point_json;
            if (point_json.length > 1) {
                let b = "";
                let isCreate = false;
                if (index == 0) {
                    b = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index + 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    isCreate = computedDistance(
                        e.lnglat,
                        this.markers[index + 1].getPosition()
                    );
                } else if (index == point_json.length - 1) {
                    b = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    isCreate = computedDistance(
                        e.lnglat,
                        this.markers[index - 1].getPosition()
                    );
                } else {
                    let b1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index + 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let b2 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    b = b1 || b2;
                    let isCreate1 = computedDistance(
                        e.lnglat,
                        this.markers[index + 1].getPosition()
                    );
                    let isCreate2 = computedDistance(
                        e.lnglat,
                        this.markers[index - 1].getPosition()
                    );
                    isCreate = isCreate1 && isCreate2;
                }
                if (b) {
                    this.$message.error(this.routeLanguage.placeholder5);
                    this.markers[index].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.changePoint = {
                        index: index,
                        lat: this.startPoints.lat,
                        lng: this.startPoints.lng,
                        type: "edit",
                    };
                    this.drawline();
                    this.editCenterpoint(index);
                    this.computedData();
                    return false;
                }
                if (!isCreate) {
                    this.$message.warning(this.routeLanguage.errorMessage8);
                    this.markers[index].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.changePoint = {
                        index: index,
                        lat: this.startPoints.lat,
                        lng: this.startPoints.lng,
                        type: "edit",
                    };
                    this.drawline();
                    this.editCenterpoint(index);
                    this.computedData();
                    return false;
                }
            }
            this.computedData();
            this.routeFormCode = false
            this.deepCopy();
            this.changeElevation(index, e.lnglat)
        },
        //航线:点拖拽事件
        async markerDrag(e) {
            this.markerClick(e);
            let index = this.markers.findIndex((item) => {
                return item.id == e.target.id;
            });
            this.clearElevation(index)
            this.markers[index].setPosition(e.lnglat);
            this.changePoint = {
                index: index,
                lat: parseFloat(e.lnglat.kT.toFixed(7)),
                lng: parseFloat(e.lnglat.KL.toFixed(7)),
                type: "edit",
            };
            this.drawline();
            this.editCenterpoint(index);

        },
        //航线:绘制线段
        drawline() {
            let paths = [];
            for (let index = 0; index < this.markers.length; index++) {
                paths.push(this.markers[index].getPosition());
            }
            if (this.line) {
                this.line.setPath(paths)
            } else {
                this.line = initMaps.drawPolyline(paths);
                this.line.setMap(this.map);
            }
            // var polyEditor = new AMap.PolylineEditor(this.map, this.line);
        },
        //航线:设置两个点的中心点
        addCenterpoint(index) {
            let lat =
                (parseFloat(this.markers[index - 1].getPosition().lat) +
                    parseFloat(this.markers[index - 2].getPosition().lat)) /
                2;
            let lng =
                (parseFloat(this.markers[index - 1].getPosition().lng) +
                    parseFloat(this.markers[index - 2].getPosition().lng)) /
                2;
            let center = [lng, lat];
            let params = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker("+", center, "marker-edit-i", params);
            addMarker.setMap(this.map);
            addMarker.id = index - 1;
            this.addMarkers.push(addMarker);
            addMarker.on("click", this.clickAdd);
        },
        //航线:修改两点中的中心点
        editCenterpoint(index) {
            if (index == 0) {
                let lat =
                    (parseFloat(this.markers[index + 1].getPosition().lat) +
                        parseFloat(this.markers[index].getPosition().lat)) /
                    2;
                let lng =
                    (parseFloat(this.markers[index + 1].getPosition().lng) +
                        parseFloat(this.markers[index].getPosition().lng)) /
                    2;
                let center = [lng, lat];
                this.addMarkers[index].setPosition(center);
            } else {
                let lat =
                    (parseFloat(this.markers[index].getPosition().lat) +
                        parseFloat(this.markers[index - 1].getPosition().lat)) /
                    2;
                let lng =
                    (parseFloat(this.markers[index].getPosition().lng) +
                        parseFloat(this.markers[index - 1].getPosition().lng)) /
                    2;
                let center = [lng, lat];
                this.addMarkers[index - 1].setPosition(center);
                if (index < this.markers.length - 1) {
                    let lat =
                        (parseFloat(this.markers[index + 1].getPosition().lat) +
                            parseFloat(this.markers[index].getPosition().lat)) /
                        2;
                    let lng =
                        (parseFloat(this.markers[index + 1].getPosition().lng) +
                            parseFloat(this.markers[index].getPosition().lng)) /
                        2;
                    let center = [lng, lat];
                    this.addMarkers[index].setPosition(center);
                }
            }
            // if(index<this.markers.length-1)
        },
        editCenterpoint1(index) {
            let lat =
                (parseFloat(this.markers[index].getPosition().lat) +
                    parseFloat(this.markers[index - 1].getPosition().lat)) /
                2;
            let lng =
                (parseFloat(this.markers[index].getPosition().lng) +
                    parseFloat(this.markers[index - 1].getPosition().lng)) /
                2;
            let center = [lng, lat];
            this.addMarkers[index - 1].setPosition(center);

        },
        //航线:点击+触发添加事件
        clickAdd(e) {
            this.routeFormCode = true
            this.num++;
            let index = e.target.id;
            this.map.remove(this.addMarkers[index - 1]);
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
                zIndex: 55,
            };
            let marker = initMaps.drawMarker(
                index + 1,
                e.lnglat,
                "marker-edit",
                params
            );
            marker.id = index + 1;
            marker.setMap(this.map);
            marker.on("dragstart", this.markerDragStart);
            marker.on("dragging", this.markerDrag);
            marker.on("dragend", this.markerDragEnd);
            this.markers.splice(index, 0, marker);
            this.changePoint = {
                index: index,
                lat: marker.getPosition().kT.toFixed(7),
                lng: marker.getPosition().KL.toFixed(7),
                type: "editAdd",
            };
            for (let i = index + 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                    "<div class='marker-edit'><span class='text'>" +
                    (i + 1) +
                    "</span></div>";
                this.markers[i].setContent(content);
            }
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            this.drawline();
            for (let n = index; n < this.addMarkers.length; n++) {
                this.addMarkers[n].id = this.addMarkers[n].id + 1;
            }
            let lat =
                (parseFloat(this.markers[index - 1].getPosition().lat) +
                    parseFloat(this.markers[index].getPosition().lat)) /
                2;
            let lng =
                (parseFloat(this.markers[index - 1].getPosition().lng) +
                    parseFloat(this.markers[index].getPosition().lng)) /
                2;
            let center = [lng, lat];
            let params1 = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-edit-i",
                params1
            );
            addMarker.setMap(this.map);
            addMarker.id = index;
            this.addMarkers.splice(index - 1, 1, addMarker);
            addMarker.on("click", this.clickAdd);
            let lat1 =
                (parseFloat(this.markers[index].getPosition().lat) +
                    parseFloat(this.markers[index + 1].getPosition().lat)) /
                2;
            let lng1 =
                (parseFloat(this.markers[index].getPosition().lng) +
                    parseFloat(this.markers[index + 1].getPosition().lng)) /
                2;
            let center1 = [lng1, lat1];
            let addMarker1 = initMaps.drawMarker(
                "+",
                center1,
                "marker-edit-i",
                params1
            );
            addMarker1.setMap(this.map);
            addMarker1.id = index + 1;
            this.addMarkers.splice(index, 0, addMarker1);
            addMarker1.on("click", this.clickAdd);

            setTimeout(() => {
                this.reElevation()
                this.routeFormCode = false
                this.deepCopy()
            })
        },
        //计算两点间高程
        computerElevationRoute(point1, point2, distance, id) {
            if (!this.isComputeHight) {
                return false
            }
            if (this.elevationTime[id]) {
                clearTimeout(this.elevationTime[id])
            }
            if (this.elevationPoints[id] && this.elevationPoints[id].length) {
                this.map.remove(this.elevationPoints[id])
            }
            this.elevationPoints[id] = []
            this.elevationData[id] = [];
            this.elevationSuccess[id] = false
            computerElevationPoints(point1, point2, 120, this.progressFun, distance, this.map, id).then(res => {
                this.elevationData[id] = res
                if (this.showHeight && this.isComputeHight) {
                    this.elevationTime[id] = setTimeout(() => {
                        this.drawElevationPoint(res.arr, id)
                    })
                } else {
                    this.elevationSuccess[id] = true
                }
            })
        },
        //绘制高程点
        async drawElevationPoint(arr, id) {
            if (this.elevationPoints[id].length < arr.length) {
                let path = new AMap.LngLat(arr[this.elevationPoints[id].length].points.lng, arr[this.elevationPoints[id].length].points.lat);
                let params = {
                    className: "marker-elevation",
                    text: this.routeLanguage.elevation + arr[this.elevationPoints[id].length].height + "m"
                };
                let marker = await initMaps.drawMarkerText(path, params);
                marker.setMap(this.map);
                this.elevationPoints[id].push(marker)
                this.elevationTime[id] = setTimeout(() => {
                    this.drawElevationPoint(arr, id)
                }, 100)
            } else {
                this.elevationSuccess[id] = true
            }
        },
        //判断是否需要改变计算高程
        changeElevation(index, point) {
            if (this.markers.length > 1) {
                let before = index == 0 ? '' : this.markers[index - 1].getPosition()
                let after = this.markers[index + 1] ? this.markers[index + 1].getPosition() : ''
                if (before) {
                    this.computerElevationRoute(point, before, this.default_distance, index)
                }
                if (after) {
                    this.computerElevationRoute(point, after, this.default_distance, index + 1)
                }
            }

        },
        //重新全部计算高程
        reElevation() {
            this.clearElevationMarker()
            if (!this.isComputeHight) {
                return false
            }
            for (let index = 0; index < this.markers.length - 1; index++) {
                let point1 = this.markers[index].getPosition()
                let point2 = this.markers[index + 1].getPosition()
                this.computerElevationRoute(point1, point2, this.default_distance, index + 1)
            }

        },
        //清除正在绘制的高程点
        clearElevation(index) {
            if (this.markers.length > 1) {
                let before = index == 0 ? '' : true
                let after = this.markers[index + 1] ? true : ''
                if (before) {
                    if (this.elevationTime[index]) {
                        clearTimeout(this.elevationTime[index])
                    }
                    if (this.elevationPoints[index] && this.elevationPoints[index].length) {
                        this.map.remove(this.elevationPoints[index])
                    }
                    this.elevationPoints[index] = []
                    this.elevationData[index] = [];
                }
                if (after) {
                    if (this.elevationTime[index + 1]) {
                        clearTimeout(this.elevationTime[index + 1])
                    }
                    if (this.elevationPoints[index + 1] && this.elevationPoints[index + 1].length) {
                        this.map.remove(this.elevationPoints[index + 1])
                    }
                    this.elevationPoints[index + 1] = []
                    this.elevationData[index + 1] = [];
                }
            }
        },
        //清除高程点
        clearElevationMarker(code) {
            for (const key in this.elevationPoints) {
                if (this.elevationPoints[key] && this.elevationPoints[key].length) {
                    if (this.elevationTime[key]) {
                        clearTimeout(this.elevationTime[key]);
                    }
                    this.map.remove(this.elevationPoints[key]);
                    this.elevationPoints[key] = [];

                }
            }
            if (!code) {
                this.elevationData = {};
            }
        },
        //围栏：绘制标记点
        drawPoint(index, e) {
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
                zIndex: 55,
            };
            let marker = initMaps.drawMarker(index, e, "marker-o-edit", params);
            if (this.markers.length > 2) {
                let a = this.isLineCross(
                    this.markers[0].getPosition(),
                    marker.getPosition(),
                    0
                );
                let b = this.isLineCross(
                    this.markers[this.markers.length - 1].getPosition(),
                    marker.getPosition(),
                    this.markers.length - 1
                );
                if (a || b) {
                    this.$message.warning({
                        message: this.routeLanguage.messageInfo2,
                        duration: 1000,
                    });
                    this.num--;
                    return;
                }
            }
            marker.id = index;
            this.map.add(marker);
            this.markers.push(marker);
            if (!this.changeCode && !this.recallClick) {
                this.changePoint = {
                    index: index - 1,
                    lat: parseFloat(marker.getPosition().kT.toFixed(7)),
                    lng: parseFloat(marker.getPosition().KL.toFixed(7)),
                    type: "add",
                }
            }
            marker.on("dragstart", this.markerDragStart1);
            marker.on("dragging", this.markerDrag1);
            marker.on("dragend", this.markerDragEnd1);
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            if (this.markers.length > 1) {
                this.drawAddPoint();
            }
            return true
        },
        //围栏：绘制多边形
        drawPolypon() {
            let paths = [];
            for (let index = 0; index < this.markers.length; index++) {
                paths.push(this.markers[index].getPosition());
            }
            if (this.polypon) {
                this.polypon.setPath(paths);
            } else {
                this.polypon = initMaps.drawPolypon(paths);
                this.polypon.setMap(this.map);

            }

            // this.map.add(this.polypon)
        },
        //围栏：拖拽点开始
        markerDragStart1(e) {
            this.routeFormCode = true;
            let index = e.target.id - 1;
            this.startPoints = {
                lat: this.$refs.fenceEdit.fenceForm.point_json[index].lat,
                lng: this.$refs.fenceEdit.fenceForm.point_json[index].lng
            }
            this.markerClick(e);
        },
        //围栏：拖拽点拖拽中触发事件
        markerDrag1(e) {
            let index = e.target.id;
            this.position = this.markers[index - 1].getPosition();
            this.markers[index - 1].setPosition(e.lnglat);
            this.changePoint = {
                index: index - 1,
                lat: parseFloat(e.lnglat.kT.toFixed(7)),
                lng: parseFloat(e.lnglat.KL.toFixed(7)),
                type: "edit",
            };
            this.drawPolypon();
            if (this.markers.length > 1) {
                this.drawEditPoint(index);
            }
        },
        //围栏：拖拽点结束
        markerDragEnd1(e) {
            if (this.markers.length > 3) {
                let index = e.target.id;
                let a, b;
                if (index == 1) {
                    a = this.isLineCross1(
                        this.markers[0].getPosition(),
                        this.markers[index].getPosition(),
                        0
                    );
                    b = this.isLineCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[0].getPosition(),
                        0,
                        1
                    );
                } else if (index == this.markers.length) {
                    a = this.isLineCross1(
                        this.markers[0].getPosition(),
                        this.markers[this.markers.length - 1].getPosition(),
                        0,
                        1
                    );
                    b = this.isLineCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        this.markers.length - 2
                    );
                } else {
                    a = this.isLineCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        index - 2
                    );
                    b = this.isLineCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index].getPosition(),
                        index - 1
                    );
                }
                if (a || b) {
                    this.markers[index - 1].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.changePoint = {
                        index: index - 1,
                        lat: this.startPoints.lat,
                        lng: this.startPoints.lng,
                        type: "edit",
                    };
                    this.drawPolypon();
                    this.drawEditPoint(index);
                    this.$message.warning({
                        message: this.routeLanguage.messageInfo2,
                        duration: 1000,
                    });
                    return false;
                }
            }
            this.routeFormCode = false;
            this.deepCopy();
        },
        //围栏：添加中点，方便直接添加
        drawAddPoint() {
            let center = computeCenter([
                this.markers[this.markers.length - 1].getPosition(),
                this.markers[this.markers.length - 2].getPosition(),
            ]);
            let params = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-o-edit-i",
                params
            );
            addMarker.id = this.markers.length - 1;
            addMarker.setMap(this.map);
            this.addMarkers.push(addMarker);
            addMarker.on("click", this.addClick);
            let labelMarker = this.fenceListPoint([
                this.markers[this.markers.length - 1].getPosition(),
                this.markers[this.markers.length - 2].getPosition(),
            ]);
            labelMarker.setMap(this.map);
            this.distancePoint.push(labelMarker);
            if (this.markers.length > 2) {
                let center1 = computeCenter([
                    this.markers[this.markers.length - 1].getPosition(),
                    this.markers[0].getPosition()
                ]);
                if (this.addMarkers.length == this.markers.length) {
                    this.addMarkers[this.addMarkers.length - 2].setPosition(center1);
                    this.addMarkers[this.addMarkers.length - 2].id =
                        this.markers.length;
                    let temp = this.addMarkers[this.addMarkers.length - 1];
                    this.addMarkers[this.addMarkers.length - 1] =
                        this.addMarkers[this.addMarkers.length - 2];
                    this.addMarkers[this.addMarkers.length - 2] = temp;
                } else {
                    let addMarker1 = initMaps.drawMarker(
                        "+",
                        center1,
                        "marker-o-edit-i",
                        params
                    );
                    addMarker1.id = this.markers.length;
                    addMarker1.setMap(this.map);
                    this.addMarkers.push(addMarker1);
                    addMarker1.on("click", this.addClick);
                }
                if (this.distancePoint.length > 3) {
                    this.map.remove(this.distancePoint[this.distancePoint.length - 2]);
                    this.distancePoint.splice(this.distancePoint.length - 2, 1);
                }
                let labelMarker = this.fenceListPoint([

                    this.markers[this.markers.length - 1].getPosition(),
                    this.markers[0].getPosition(),
                ]);
                labelMarker.setMap(this.map);
                this.distancePoint.push(labelMarker);
            }
        },
        //围栏：修改中点
        drawEditPoint(index) {
            if (this.markers.length < 3) {
                let center = computeCenter([
                    this.markers[0].getPosition(),
                    this.markers[1].getPosition(),
                ]);
                this.addMarkers[0].setPosition(center);
                this.distancePoint[0].setPosition(center);
                let labelMarker = this.fenceListPoint([
                    this.markers[0].getPosition(),
                    this.markers[1].getPosition(),
                ]);
                this.distancePoint[0].setContent(labelMarker._opts.content);
                this.distancePoint[0].setStyle(labelMarker._opts.style);
                this.distancePoint[0].setOffset(labelMarker.getOffset());
            } else {
                if (index == 1) {
                    let center = computeCenter([
                        this.markers[index].getPosition(),
                        this.markers[index - 1].getPosition(),
                    ]);
                    this.addMarkers[index - 1].setPosition(center);
                    this.distancePoint[index - 1].setPosition(center);
                    let labelMarker = this.fenceListPoint([
                        this.markers[index].getPosition(),
                        this.markers[index - 1].getPosition(),
                    ]);
                    this.distancePoint[index - 1].setContent(labelMarker._opts.content);
                    this.distancePoint[index - 1].setStyle(labelMarker._opts.style);
                    this.distancePoint[index - 1].setOffset(labelMarker.getOffset());
                    let center1 = computeCenter([
                        this.markers[0].getPosition(),
                        this.markers[this.markers.length - 1].getPosition(),
                    ]);
                    this.addMarkers[this.addMarkers.length - 1].setPosition(center1);
                    this.distancePoint[this.distancePoint.length - 1].setPosition(
                        center1
                    );
                    let labelMarker1 = this.fenceListPoint([
                        this.markers[0].getPosition(),
                        this.markers[this.markers.length - 1].getPosition(),
                    ]);
                    this.distancePoint[this.distancePoint.length - 1].setContent(
                        labelMarker1._opts.content
                    );
                    this.distancePoint[this.distancePoint.length - 1].setStyle(
                        labelMarker1._opts.style
                    );
                    this.distancePoint[this.distancePoint.length - 1].setOffset(
                        labelMarker1.getOffset()
                    );
                } else if (index > 1) {
                    let center = computeCenter([
                        this.markers[index - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                    ]);
                    this.addMarkers[index - 2].setPosition(center);
                    this.distancePoint[index - 2].setPosition(center);
                    let labelMarker = this.fenceListPoint([
                        this.markers[index - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                    ]);
                    this.distancePoint[index - 2].setContent(labelMarker._opts.content);
                    this.distancePoint[index - 2].setStyle(labelMarker._opts.style);
                    this.distancePoint[index - 2].setOffset(labelMarker.getOffset());
                    if (index == this.markers.length) {
                        let center = computeCenter([
                            this.markers[index - 1].getPosition(),
                            this.markers[0].getPosition(),
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                        this.distancePoint[index - 1].setPosition(center);
                        let labelMarker = this.fenceListPoint([
                            this.markers[index - 1].getPosition(),
                            this.markers[0].getPosition(),
                        ]);
                        this.distancePoint[index - 1].setContent(labelMarker._opts.content);
                        this.distancePoint[index - 1].setStyle(labelMarker._opts.style);
                        this.distancePoint[index - 1].setOffset(labelMarker.getOffset());
                    } else {
                        let center = computeCenter([
                            this.markers[index].getPosition(),
                            this.markers[index - 1].getPosition(),
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                        this.distancePoint[index - 1].setPosition(center);
                        let labelMarker = this.fenceListPoint([
                            this.markers[index].getPosition(),
                            this.markers[index - 1].getPosition(),
                        ]);
                        this.distancePoint[index - 1].setContent(labelMarker._opts.content);
                        this.distancePoint[index - 1].setStyle(labelMarker._opts.style);
                        this.distancePoint[index - 1].setOffset(labelMarker.getOffset());
                    }
                }
            }
        },
        //围栏：点击触发添加事件
        addClick(e) {
            this.routeFormCode = true
            this.num++;
            let index = e.target.id;
            this.map.remove(this.addMarkers[index - 1]);
            this.map.remove(this.distancePoint[index - 1]);
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
            };
            let marker = initMaps.drawMarker(
                index + 1,
                e.lnglat,
                "marker-o-edit",
                params
            );
            marker.id = index + 1;
            marker.setMap(this.map);
            marker.on("dragstart", this.markerDragStart1);
            marker.on("dragging", this.markerDrag1);
            marker.on("dragend", this.markerDragEnd1);
            this.markers.splice(index, 0, marker);
            this.changePoint = {
                index: index,
                lat: marker.getPosition().kT.toFixed(7),
                lng: marker.getPosition().KL.toFixed(7),
                type: "editAdd",
            };
            for (let i = index + 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                    "<div class='marker-o-edit'><span class='text'>" +
                    (i + 1) +
                    "</span></div>";
                this.markers[i].setContent(content);
            }
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            this.drawPolypon();
            for (let n = index; n < this.addMarkers.length; n++) {
                this.addMarkers[n].id = this.addMarkers[n].id + 1;
            }
            let lat =
                (parseFloat(this.markers[index - 1].getPosition().lat) +
                    parseFloat(this.markers[index].getPosition().lat)) /
                2;
            let lng =
                (parseFloat(this.markers[index - 1].getPosition().lng) +
                    parseFloat(this.markers[index].getPosition().lng)) /
                2;
            let center = [lng, lat];
            let params1 = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-o-edit-i",
                params1
            );
            addMarker.setMap(this.map);
            addMarker.id = index;
            this.addMarkers.splice(index - 1, 1, addMarker);
            addMarker.on("click", this.addClick);
            let labelMarker = this.fenceListPoint([
                this.markers[index - 1].getPosition(),
                this.markers[index].getPosition(),
            ]);
            labelMarker.setMap(this.map);
            this.distancePoint.splice(index - 1, 1, labelMarker);
            if (index == this.addMarkers.length) {
                let lat1 =
                    (parseFloat(this.markers[index].getPosition().lat) +
                        parseFloat(this.markers[0].getPosition().lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.markers[index].getPosition().lng) +
                        parseFloat(this.markers[0].getPosition().lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-o-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addClick);
                let labelMarker = this.fenceListPoint([
                    this.markers[index].getPosition(),
                    this.markers[0].getPosition(),
                ]);
                labelMarker.setMap(this.map);
                this.distancePoint.splice(index, 0, labelMarker);
            } else {
                let lat1 =
                    (parseFloat(this.markers[index].getPosition().lat) +
                        parseFloat(this.markers[index + 1].getPosition().lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.markers[index].getPosition().lng) +
                        parseFloat(this.markers[index + 1].getPosition().lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-o-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addClick);
                let labelMarker = this.fenceListPoint([
                    this.markers[index].getPosition(),
                    this.markers[index + 1].getPosition(),
                ]);
                labelMarker.setMap(this.map);
                this.distancePoint.splice(index, 0, labelMarker);
            }
            setTimeout(() => {
                this.routeFormCode = false
                this.deepCopy()
            })
        },
        //围栏：判断是否出现交叉
        isLineCross(marker1, marker2, index) {
            let fencePoints = this.$refs.fenceEdit.fenceForm.point_json
            let arr1 = fencePoints.slice(0, index);
            let arr2 = fencePoints.slice(
                index + 1,
                this.markers.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //围栏：拖拽判断是否出现交叉
        isLineCross1(marker1, marker2, index, num) {
            let fencePoints = this.$refs.fenceEdit.fenceForm.point_json
            let arr1 = fencePoints.slice(0, index);
            let arr2 = fencePoints.slice(
                num ? index + 1 : index + 2,
                num ?
                fencePoints.length - 1 :
                fencePoints.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //正射影像绘制点
        drawOrthoPoint(text, center) {
            if (this.markers.length > 2) {
                let a = this.isLineOrthoCross(
                    this.markers[0].getPosition(),
                    center,
                    0
                );
                let b = this.isLineOrthoCross(
                    this.markers[this.markers.length - 1].getPosition(),
                    center,
                    this.markers.length - 1
                );
                if (a || b) {
                    this.$message.warning({
                        message: this.routeLanguage.messageInfo3,
                        duration: 1000,
                    });
                    this.num--;
                    return false;
                }
            }
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
                zIndex: 55,
            };
            let marker = initMaps.drawMarker(text, center, "marker-edit", params);
            marker.setMap(this.map);
            marker.id = text;
            this.markers.push(marker);
            this.markerClick(marker);
            if (!this.changeCode && !this.importCode && !this.recallClick) {

                this.changePoint = {
                    index: text - 1,
                    lat: parseFloat(marker.getPosition().kT.toFixed(7)),
                    lng: parseFloat(marker.getPosition().KL.toFixed(7)),
                    type: "add",
                };

            }

            marker.on("dragstart", this.markerDragStart2)
            marker.on("dragging", this.markerDrag2);
            marker.on("dragend", this.markerDragEnd2);
            marker.on("click", this.markerClick);
            if (this.markers.length > 1) {
                this.drawOrthoCenter();
            }
            return true
        },
        //正射影像绘制边框图形
        drawOrthoPolypon() {
            let paths = []
            for (let index = 0; index < this.markers.length; index++) {
                paths.push(this.markers[index].getPosition())
            }
            if (this.polypon) {
                this.polypon.setPath(paths)
            } else {
                this.polypon = initMaps.drawPolypon(paths)
                this.polypon.setMap(this.map);
            }
            this.area = computedMethod(6, { fence: paths })
            this.drawOrthoInPolypon()
        },
        //绘制正射影像内图形
        async drawOrthoInPolypon() {
            this.distance = 0
            this.estTime = 0
            this.photoCount = 0
            this.routeSpotCount = 0
            if (this.markers.length > 2) {
                let paths = []
                for (let index = 0; index < this.markers.length; index++) {
                    paths.push(this.markers[index].getPosition())
                }
                let ortho = this.$refs.orthoEdit.orthoForm
                let params = {
                    default_height: ortho.default_height,
                    cameraParamList: this.$refs.orthoEdit.cameraParamList,
                    course: ortho.course,
                    lateral: ortho.lateral,
                    angle: ortho.angle,
                    wheelDist: ortho.wheelDist,
                    paths: paths
                }
                let { points, triggerDist } = orthoPhotoComputer(params, this.map, 1)
                this.orthoAllPoints = points
                this.routeSpotCount = points.length
                if (points.length > 0) {
                    this.messageCodeWarning = false
                    if (this.line) {
                        this.line.setPath(points)

                    } else {
                        this.line = initMaps.drawPolyline(points, { strokeColor: "#07ff0e" })
                        this.line.setMap(this.map)
                    }
                    if (this.startMarker) {
                        this.startMarker.setPosition(points[0])
                        this.endMarker.setPosition(points[points.length - 1])
                    } else {
                        let params = {
                            offset: -9,
                            clickable: false,
                            draggable: false,
                            zIndex: 100
                        }
                        this.startMarker = initMaps.drawMarker("S", points[0], "startend-marker", params)
                        this.startMarker.setMap(this.map)
                        this.endMarker = initMaps.drawMarker("E", points[points.length - 1], "startend-marker", params)
                        this.endMarker.setMap(this.map)
                    }
                } else {
                    if (this.line) {
                        this.map.remove(this.line)
                        this.line = ''
                    }
                    if (!this.messageCodeWarning) {
                        this.$message.warning(this.routeLanguage.errorMessage9)
                        this.messageCodeWarning = true
                    }
                    if (this.startMarker) {
                        this.map.remove(this.startMarker)
                        this.map.remove(this.endMarker)
                        this.startMarker = ''
                        this.endMarker = ''
                    }
                }
                for (let index = 1; index < points.length; index++) {
                    this.distance += computedMethod(1, { point1: points[index - 1], point2: points[index] })
                }
                this.estTime = this.distance / ortho.auto_speed + points.length
                this.photoCount = parseInt(this.distance / triggerDist) + points.length
                if (this.mouseupCode && !this.loading) {
                    this.computedPoints(points)
                }

            } else {
                if (this.line) {
                    this.map.remove(this.line)
                    this.line = ''
                }
                if (this.startMarker) {
                    this.map.remove(this.startMarker)
                    this.map.remove(this.endMarker)
                    this.startMarker = ''
                    this.endMarker = ''
                }
                this.clearElevationMarker()
            }
        },
        // getNewPoint(point1, point2) {
        //     let limitArea = 8000000
        //     let limitWidth = 30
        //     let angle1 = calcAngle([point2, point1], this.map)
        //     let angle2 = calcAngle([point1, point2], this.map)
        //     angle1 = (angle1 + 45) > 180 ? (angle1 + 45 - 360) : (angle1 + 45);
        //     angle2 = (angle2 + 45) > 180 ? (angle2 + 45 - 360) : (angle2 + 45);
        //     // console.log(angle1, angle2)
        //     let a1 = getLonAndLat(point1.lng, point1.lat, angle1, 2 * limitWidth * Math.sqrt(2))
        //     let a2 = getLonAndLat(point2.lng, point2.lat, angle2, 2 * limitWidth * Math.sqrt(2))
        //     let dis = computedMethod(1, { point1, point2 })
        //         // if ((dis + 2 * limitWidth) < limitArea / (2 * limitWidth)) {
        //     if (dis <= 4000) {
        //         return [a1, a2]
        //     } else {
        //         let newPoint = {
        //             lng: (point1.lng + point2.lng) / 2,
        //             lat: (point1.lat + point2.lat) / 2
        //         }
        //         let arr = this.getNewPoint(point1, newPoint)
        //         let arr1 = this.getNewPoint(newPoint, point2)
        //         return [...arr, ...arr1]
        //     }
        // },
        //最初计算高程
        // computedPoints(points) {
        //     this.progress = 0
        //     this.getElevationcode = true
        //     let wgs84Points = points.map(x => {
        //         let a = gcj02_to_wgs84(x.lng, x.lat)
        //         return {
        //             lng: a[0],
        //             lat: a[1]
        //         }
        //     })
        //     computerElevation(wgs84Points, 120, this.progressFun, this.map).then(res => {
        //         this.getElevationcode = false
        //         this.zoomMaxHeight = res
        //     })
        // },
        computedPoints(points) {
            this.clearElevationMarker()
            if (!this.isComputeHight) {
                return false
            }
            this.progress = 0
            this.getElevationcode = true
                // let wgs84Points = points.map(x => {
                //     // let a = gcj02_to_wgs84(x.lng, x.lat)
                //     return {
                //         lng: a[0],
                //         lat: a[1]
                //     }
                // })
                // computerElevationPoints(point1, point2, 120, this.progressFun, distance, this.map, id)
                // this.elevationPoints = {}
                // this.elevationData = {};
                // this.elevationSuccess = {}
            computerElevationArray(points, 120, this.progressFun, this.default_distance, this.map, this.map).then(res => {
                this.getElevationcode = false
                this.zoomMaxHeight = this.getZoomMaxHeight(res)
                this.clearElevationMarker()
                for (let index = 0; index < res.length; index++) {
                    this.elevationData[index + 1] = res[index]
                    this.elevationPoints[index + 1] = []
                    this.elevationSuccess[index + 1] = false
                    if (this.showHeight && this.code == 3 && this.isComputeHight) {
                        this.elevationTime[index + 1] = setTimeout(() => {
                            this.drawElevationPoint(res[index].arr, index + 1)
                        })
                    } else {
                        this.elevationSuccess[index + 1] = true
                    }


                }


            })
        },
        getZoomMaxHeight(res) {
            let maxArray = []
            for (let index = 0; index < res.length; index++) {
                maxArray.push(Math.max.apply(Math, res[index].arr.map(item => { return item.height })))
            }
            return Math.max(...maxArray) / 100

        },
        progressFun(progress) {
            this.progress = progress
        },
        //正射影像拖拽开始
        markerDragStart2(e) {
            this.mouseupCode = false
            this.routeFormCode = true
            let index = e.target.id
            let orthoForm = this.$refs.orthoEdit.orthoForm
            this.startPoints = {
                lng: orthoForm.point_json[index - 1].lng,
                lat: orthoForm.point_json[index - 1].lat
            }
            this.markerClick(e);
            if (this.clearElevationCode < 50) {
                this.clearElevationMarker()
                this.clearElevationCode++;
            }
        },
        //正射影像拖拽中
        markerDrag2(e) {
            let index = e.target.id
            this.markers[index - 1].setPosition(e.lnglat)
            this.changePoint = {
                index: index - 1,
                lat: parseFloat(e.lnglat.kT.toFixed(7)),
                lng: parseFloat(e.lnglat.KL.toFixed(7)),
                type: "edit",
            };
            if (this.markers.length > 1) {
                this.drawOrthoPolypon()
                this.editOrthoCenter(index);
            }
        },
        //正射影像拖拽结束
        markerDragEnd2(e) {
            if (this.clearElevationCode < 50) {
                this.clearElevationMarker()
                this.clearElevationCode++;
            }
            this.clearElevationMarker()
            let index = e.target.id
            let a = computedMethod(2, {
                point1: e.lnglat,
                fence: this.fenceItem.paths,
            });
            if (!a) {
                this.$message.error(this.routeLanguage.placeholder4);
                this.markers[index - 1].setPosition(
                    new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
                );
                this.changePoint = {
                    index: index - 1,
                    lat: this.startPoints.lat,
                    lng: this.startPoints.lng,
                    type: "edit",
                };
                if (this.markers.length > 1) {
                    this.drawOrthoPolypon()
                    this.editOrthoCenter(index);
                }
                return false;
            }
            if (this.markers.length > 1) {
                let d = "";
                if (index == 1) {
                    let ds = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let de = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[this.markers.length - 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    d = ds || de
                } else if (index == this.markers.length) {
                    let ds1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 2].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let de1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[0].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    d = ds1 || de1
                } else {
                    let b1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let b2 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 2].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    d = b1 || b2;
                }
                if (d) {
                    this.$message.error(this.routeLanguage.placeholder5);
                    this.markers[index - 1].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.changePoint = {
                        index: index - 1,
                        lat: this.startPoints.lat,
                        lng: this.startPoints.lng,
                        type: "edit",
                    };
                    this.drawOrthoPolypon();
                    this.editOrthoCenter(index);
                    return false;
                }
            }
            if (this.markers.length > 2) {
                let c, b;
                if (index == 1) {
                    c = this.isLineOrthoCross1(
                        this.markers[0].getPosition(),
                        this.markers[index].getPosition(),
                        0
                    );
                    b = this.isLineOrthoCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[0].getPosition(),
                        0,
                        1
                    );
                } else if (index == this.markers.length) {
                    c = this.isLineOrthoCross1(
                        this.markers[0].getPosition(),
                        this.markers[this.markers.length - 1].getPosition(),
                        0,
                        1
                    );
                    b = this.isLineOrthoCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        this.markers.length - 2
                    );
                } else {
                    c = this.isLineOrthoCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        index - 2
                    );
                    b = this.isLineOrthoCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index].getPosition(),
                        index - 1
                    );
                }
                if (c || b) {
                    this.markers[index - 1].setPosition(
                        new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
                    );
                    this.changePoint = {
                        index: index - 1,
                        lat: this.startPoints.lat,
                        lng: this.startPoints.lng,
                        type: "edit",
                    };

                    this.$message.warning({
                        message: this.routeLanguage.messageInfo3,
                        duration: 1000,
                    });
                    this.drawOrthoPolypon();
                    this.editOrthoCenter(index);
                    return false;
                }
            }
            this.routeFormCode = false;
            this.deepCopy();
            this.mouseupCode = true
            this.drawOrthoPolypon()

        },
        //绘制中心点
        drawOrthoCenter() {
            let center = computeCenter([
                this.markers[this.markers.length - 1].getPosition(),
                this.markers[this.markers.length - 2].getPosition(),
            ]);
            let params = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-edit-i",
                params
            );
            addMarker.id = this.markers.length - 1;
            addMarker.setMap(this.map);
            this.addMarkers.push(addMarker);
            addMarker.on("click", this.addOrthoClick);
            if (this.markers.length > 2) {
                let center1 = computeCenter([
                    this.markers[this.markers.length - 1].getPosition(),
                    this.markers[0].getPosition(),
                ]);
                if (this.addMarkers.length == this.markers.length) {
                    this.addMarkers[this.addMarkers.length - 2].setPosition(center1);
                    this.addMarkers[this.addMarkers.length - 2].id = this.markers.length;
                    let temp = this.addMarkers[this.addMarkers.length - 1];
                    this.addMarkers[this.addMarkers.length - 1] =
                        this.addMarkers[this.addMarkers.length - 2];
                    this.addMarkers[this.addMarkers.length - 2] = temp;
                } else {
                    let addMarker1 = initMaps.drawMarker(
                        "+",
                        center1,
                        "marker-edit-i",
                        params
                    );
                    addMarker1.id = this.markers.length;
                    addMarker1.setMap(this.map);
                    this.addMarkers.push(addMarker1);
                    addMarker1.on("click", this.addOrthoClick);
                }
            }

        },
        //修改中心点坐标
        editOrthoCenter(index) {
            if (this.markers.length < 3) {
                let center = computeCenter([
                    this.markers[0].getPosition(),
                    this.markers[1].getPosition(),
                ]);
                this.addMarkers[0].setPosition(center);
            } else {
                if (index == 1) {
                    let center = computeCenter([
                        this.markers[index].getPosition(),
                        this.markers[index - 1].getPosition(),
                    ]);
                    this.addMarkers[index - 1].setPosition(center);
                    let center1 = computeCenter([
                        this.markers[0].getPosition(),
                        this.markers[this.markers.length - 1].getPosition(),
                    ]);
                    this.addMarkers[this.addMarkers.length - 1].setPosition(center1);
                } else if (index > 1) {
                    let center = computeCenter([
                        this.markers[index - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                    ]);
                    this.addMarkers[index - 2].setPosition(center);
                    if (index == this.markers.length) {
                        let center = computeCenter([
                            this.markers[index - 1].getPosition(),
                            this.markers[0].getPosition(),
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                    } else {
                        let center = computeCenter([
                            this.markers[index].getPosition(),
                            this.markers[index - 1].getPosition(),
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                    }
                }
            }
        },
        //点击添加事件
        addOrthoClick(e) {
            this.routeFormCode = true
            this.num++;
            let index = e.target.id;
            this.map.remove(this.addMarkers[index - 1]);
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
            };
            let marker = initMaps.drawMarker(index + 1, e.lnglat, "marker-edit", params);
            marker.setMap(this.map);
            marker.id = index + 1;
            this.markers.splice(index, 0, marker);
            marker.on("dragstart", this.markerDragStart2)
            marker.on("dragging", this.markerDrag2);
            marker.on("dragend", this.markerDragEnd2);
            let paths = {
                lng: marker.getPosition().KL,
                lat: marker.getPosition().kT,
            }
            this.changePoint = {
                index: index,
                lat: marker.getPosition().kT.toFixed(7),
                lng: marker.getPosition().KL.toFixed(7),
                type: "editAdd",
            };
            for (let i = index + 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                    "<div class='marker-edit'><span class='text'>" +
                    (i + 1) +
                    "</span></div>";
                this.markers[i].setContent(content);
            }
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            this.drawOrthoPolypon();
            for (let n = index; n < this.addMarkers.length; n++) {
                this.addMarkers[n].id = this.addMarkers[n].id + 1;
            }
            let lat =
                (parseFloat(this.markers[index - 1].getPosition().lat) +
                    parseFloat(this.markers[index].getPosition().lat)) /
                2;
            let lng =
                (parseFloat(this.markers[index - 1].getPosition().lng) +
                    parseFloat(this.markers[index].getPosition().lng)) /
                2;
            let center = [lng, lat];
            let params1 = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-edit-i",
                params1
            );
            addMarker.setMap(this.map);
            addMarker.id = index;
            this.addMarkers.splice(index - 1, 1, addMarker);
            addMarker.on("click", this.addOrthoClick);

            if (index == this.addMarkers.length) {
                let lat1 =
                    (parseFloat(this.markers[index].getPosition().lat) +
                        parseFloat(this.markers[0].getPosition().lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.markers[index].getPosition().lng) +
                        parseFloat(this.markers[0].getPosition().lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addOrthoClick);
            } else {
                let lat1 =
                    (parseFloat(this.markers[index].getPosition().lat) +
                        parseFloat(this.markers[index + 1].getPosition().lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.markers[index].getPosition().lng) +
                        parseFloat(this.markers[index + 1].getPosition().lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addOrthoClick);
            }
            setTimeout(() => {
                this.routeFormCode = false
                this.deepCopy()
            })
        },
        //正射影像：判断是否出现交叉
        isLineOrthoCross(marker1, marker2, index) {
            let ortho = this.$refs.orthoEdit.orthoForm.point_json
            let arr1 = ortho.slice(0, index);
            let arr2 = ortho.slice(
                index + 1,
                this.markers.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //正射影像：拖拽判断是否出现交叉
        isLineOrthoCross1(marker1, marker2, index, num) {
            let ortho = this.$refs.orthoEdit.orthoForm.point_json
            let arr1 = ortho.slice(0, index);
            let arr2 = ortho.slice(
                num ? index + 1 : index + 2,
                num ?
                ortho.length - 1 :
                ortho.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //正射影像修改
        changeOrtho() {
            let paths = []
            let ortho = this.$refs.orthoEdit.orthoForm
            for (let index = 0; index < this.markers.length; index++) {
                paths.push(this.markers[index].getPosition())
            }
            if (this.markers.length > 2) {
                let points = renderPolyline({ paths: paths, stepRotate: ortho.rotate, spaceInp: ortho.space, amap: this.map })
                if (this.line) {
                    this.line.setPath(points)
                } else {
                    this.line = initMaps.drawPolyline(points, { strokeColor: "#07ff0e" })
                    this.line.setMap(this.map)
                }
                if (this.startMarker) {
                    this.startMarker.setPosition(points[0])
                    this.endMarker.setPosition(points[points.length - 1])
                }
            }
        },
        //设置数组深拷贝
        deepCopy() {
            if (this.code == 1) {
                if (!this.routeFormCode && !this.recallClick && !this.importCode) {
                    let paths = {
                        routeForm: JSON.parse(JSON.stringify(this.$refs.routeEdit.routeForm)),
                        clickId: this.clickId
                    }
                    this.cacheData.push(paths)
                }
            } else if (this.code == 2) {
                if (!this.routeFormCode && !this.recallClick && !this.importCode) {
                    let paths = {
                        routeForm: JSON.parse(JSON.stringify(this.$refs.fenceEdit.fenceForm)),
                        clickId: this.clickId

                    }
                    this.cacheData.push(paths)

                }
            } else if (this.code == 3) {
                if (!this.routeFormCode && !this.recallClick && !this.importCode) {
                    let paths = {
                        routeForm: JSON.parse(JSON.stringify(this.$refs.orthoEdit.orthoForm)),
                        clickId: this.clickId
                    }
                    this.cacheData.push(paths)
                }
            }
        },
        //点击航线绘制绘制正射影像
        clickOrthoRoute() {
            let { points, triggerDist } = orthoPhotoComputer(this.routeItem, this.map)
            this.routeSpotCount = points.length
            if (this.layer && this.routeItem.point_list[0].type == 20) {
                for (let i = 0; i < points.length; i++) {
                    let a = gcj02_to_wgs84(points[i].KL, points[i].kT)
                    points[i].setLng(a[0])
                    points[i].setLat(a[1])
                }
            } else if (!this.layer && this.routeItem.point_list[0].type == 10) {
                for (let i = 0; i < points.length; i++) {
                    let a = wgs84_to_gcj02(points[i].KL, points[i].kT)
                    points[i].setLng(a[0])
                    points[i].setLat(a[1])
                }
            }
            this.clickRoute = initMaps.drawPolyline(points, { strokeColor: "#07ff0e" })
            if (this.startMarker) {
                this.startMarker.setPosition(points[0])
                this.endMarker.setPosition(points[points.length - 1])
            } else {
                let params = {
                    offset: -9,
                    clickable: false,
                    draggable: false,
                    zIndex: 100
                }
                this.startMarker = initMaps.drawMarker("S", points[0], "startend-marker", params)
                this.startMarker.setMap(this.map)
                this.endMarker = initMaps.drawMarker("E", points[points.length - 1], "startend-marker", params)
                this.endMarker.setMap(this.map)
            }
            this.clickRoute.setMap(this.map)
            for (let index = 1; index < points.length; index++) {
                this.distance += computedMethod(1, { point1: points[index - 1], point2: points[index] })
            }
            this.estTime = this.distance / (this.routeItem.auto_speed / 100) + points.length
            this.$store.commit("setEstTime", this.estTime);
            this.$store.commit("setFirstPoint", points[0])
            this.$store.commit("setLastPoint", points[points.length - 1])
            this.photoCount = parseInt(this.distance / triggerDist) + points.length
            this.computedPoints(points)
        },
    }
}