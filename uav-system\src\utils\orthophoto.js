let map = '' //地图
    //返回正射影像的点
export function renderPolyline(param) {
    cpRPA.setDistanceFn(distance);
    cpRPA.setLatlng2PxFn(latlng2Px);
    cpRPA.setPx2LatlngFn(px2Latlng);
    let {
        paths = undefined,
            stepRotate = 0,
            spaceInp = 5,
            amap = undefined

    } = param
    map = amap
    let points = cpRPA.setOptions({
        polygon: paths,
        rotate: parseFloat(stepRotate),
        space: parseFloat(spaceInp)
    })
    return points
}

function distance(p1, p2) {
    return new AMap.LngLat(p1.lng, p1.lat).distance(new AMap.LngLat(p2.lng, p2.lat))
}

function latlng2Px(latlng) {
    return map.lngLatToContainer(new AMap.LngLat(latlng.lng, latlng.lat))
}

function px2Latlng(px) {
    return map.containerToLngLat(new AMap.Pixel(px[0], px[1]))
}