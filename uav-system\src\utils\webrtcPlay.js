import ZLMRTCClient from "../assets/js/ZLMRTCClient"
export function webrtcPlay(tag, params) {
    let {
        url,
        width,
        height
    } = params
    let player = new ZLMRTCClient.Endpoint({
        element: document.getElementById(tag), // video 标签
        debug: false, // 是否打印日志
        zlmsdpUrl: url,
        simulcast: false,
        useCamera: true,
        audioEnable: true,
        videoEnable: true,
        recvOnly: true,
        resolution: { w: width, h: height },
        usedatachannel: true,
    });

    player.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR, function(e) { // ICE 协商出错
        console.log('ICE 协商出错')
    });

    player.on(ZLMRTCClient.Events.WEBRTC_ON_REMOTE_STREAMS, function(e) { //获取到了远端流，可以播放
        // document.getElementById(tag).srcObject = s;
        document.getElementById(tag).muted = true;
        console.log("获取到远程流，可以播放", e)
            // console.log('播放成功', e.streams)
    });

    player.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED, function(e) { // offer anwser 交换失败
        console.log('offer anwser 交换失败', e)
            // stop();
    });

    player.on(ZLMRTCClient.Events.WEBRTC_ON_LOCAL_STREAM, function(s) { // 获取到了本地流
        document.getElementById(tag).srcObject = s;
        document.getElementById(tag).muted = true;

        console.log('offer anwser 交换失败', s)
    });

    player.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED, function(s) { // 获取本地流失败
        console.log('获取本地流失败')
        this.$message.error('获取本地流失败')
    });

    player.on(ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE, function(state) { // RTC 状态变化 ,详情参考 https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/connectionState
        console.log('当前状态==>', state)
        if (state == 'disconnected') {
            player.close();
        }
    });

    player.on(ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_OPEN, function(event) {
        console.log('rtc datachannel 打开 :', event)
    });

    player.on(ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_MSG, function(event) {
        console.log('rtc datachannel 消息 :', event.data)
            // document.getElementById('msgrecv').value = event.data
    });
    player.on(ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_ERR, function(event) {
        console.log('rtc datachannel 错误 :', event)
        this.$message.error('rtc datachannel 错误 :' + event)
    });
    player.on(ZLMRTCClient.Events.WEBRTC_ON_DATA_CHANNEL_CLOSE, function(event) {
        console.log('rtc datachannel 关闭 :', event)
    });
    player.on(ZLMRTCClient.Events.EBRTC_NOT_SUPPORT, function(event) {
        console.log(event)
    })
    player.on(ZLMRTCClient.Events.WEBRTC_ON_REMOTE_STREAMS, function(event) {
        console.log(event)

    })
    return player;

}