const singleUav = {
    text: 'Control Panel',
    upload: 'Upload',
    uav: 'Uav',
    yuntai: 'PTZ',
    center: 'CTR',
    vertical: 'VERT',
    dialogContent: {
        titleUpload: 'Route upload progress',
        uploadSuccess: 'Route upload completed',
        selectTitle: "Select flight mode",
        placeholder: 'Please select flight mode'
    },
    flightModeList: [{
            label: "Altitude mode",
            value: 2,
        },
        {
            label: "Automatic mode",
            value: 3,
        },
        {
            label: "Follow mode",
            value: 4,
        },
        {
            label: "GPS mode",
            value: 5,
        },
        {
            label: "Return mode",
            value: 6,
        },
        {
            label: "Landing flight mode",
            value: 9,
        },
        {
            label: "Motion mode",
            value: 13,
        },
    ],
    modelChange: 'MODE',
    lock: 'Atresia',
    unlock: 'Unlock',
    takeOff: 'TakeOff',
    message: {
        messageVideo: 'Start recording',
        messageVideo1: 'End recording',
        messagePhoto: 'Photo taken successfully',
        messageReturn: 'Returning',
        continue: 'Continue',
        hover: 'Hover',
        land: "Land",
        suspend: 'Suspend',
        toplane: "Start task execution",
        errorPhoto: 'Photo taking failed (stream address error)',
        errorVideo: 'Video recording failed (stream address error)',
        messageUpload: 'The mission is in progress, please do not upload the route repeatedly.',
        sureUpload: 'The UAV is about to execute the upload route command, please confirm.',
        sureTakeOff: 'The UAV is about to execute takeoff command, please confirm.',
        sureLock: 'The UAV is about to execute the locking command, please confirm.',
        sureunLock: 'The UAV is about to execute the unlocking command, please confirm.',
        tipsLock: "The aircraft is on mission and cannot be locked."
    }

}
export default singleUav