## fileVideo  视频播放
## liveVideo  直播拉流，使用flv

## 引入组件
## import fileVideo from "@/components/video/fileVideo.vue"
## import liveVideo from "@/components/video/liveVideo.vue"

## linkErrorType 视频链接状态：1=>未连接，2=>连接中，3=>网络错误
##  如果需要手动进行控制视频播放/暂停，可调用playVideo方法，接受参数为布尔值

<!-- 直接播放视频 -->
## 使用<fileVideo>组件
##   属性
##      url         视频播放链接，必填
##      title       右上角标题，不传则不显示
##      showFooter  是否显示底部操作，默认显示
##      autoPlay    是否自动播放，默认不播放（注：因浏览器的问题，自动播放必须静音播放）
##      isFill      视频是否充满整个可视区域，默认不充满
##      cover       视频封面图url，选填

##   事件
##      clickVideo  点击视频时触发

<!-- 使用视频拉流 -->
## 使用<liveVideo>组件
##   属性
##      url         视频播放链接，必填
##      videoId     视频id，如果一个页面使用多个，则必填
##      title       右上角标题，不传则不显示
##      showFooter  是否显示底部操作，默认显示
##      autoPlay    是否自动播放，默认不播放（注：因浏览器的问题，自动播放必须静音播放）
##      isFill      视频是否充满整个可视区域，默认不充满
##      cover       视频封面图url，选填

##   事件
##      clickVideo  点击视频时触发

## 注：
##  1、视频暂停时默认截取当前帧视频为图片显示，如需获取图片连接可通过参数imgUrl获取
