<template>
  <div id="login" :style="containerStyle">
    <!-- 粒子 -->
    <vue-particles
      color="#2adbff"
      :particleOpacity="0.7"
      :particlesNumber="50"
      shapeType="circle"
      :particleSize="4"
      linesColor="#FFF"
      :linesWidth="1"
      :lineLinked="true"
      :lineOpacity="0.4"
      :linesDistance="150"
      style="height: 50vh"
      :moveSpeed="2"
      :hoverEffect="true"
      hoverMode="grab"
      :clickEffect="true"
      clickMode="push"
      class="lizi"
    />

    <!-- three波浪 -->
    <pointwave
      style="
        height: 50vh;
        position: fixed;
        height: 100vh;
        width: 100%;
        left: 0;
        bottom: 0;
      "
      color="#048de4"
    />

    <!-- 流光效果 -->
    <div class="login-fluxay">
      <div class="fluxay-main">
        <div class="fluxay-cell" v-for="item in 10" :key="item"></div>
      </div>
    </div>

    <div class="logi-main">
      <!-- logo -->
      <div class="login-logo">
        <img class="logo-img" src="../../assets/img/logo.png" alt="" />
        <img class="logo-bg" src="../../assets/img/home/<USER>" alt="" />
      </div>

      <!-- 表单 -->
      <div class="login-form">
        <div class="form-title">
          <h1 :style="titleStyle">{{ language.title }}</h1>
        </div>
        <div class="form-content">
          <div class="content-main">
            <!-- 四周线条 -->
            <div class="">
              <div
                class="rim-line"
                v-for="(item, index) in rimLineList"
                :key="index"
                :style="item.style"
              ></div>
            </div>

            <!-- 表单标题 -->
            <div class="" style="height: 46px"></div>
            <div class="from-mian-title">
              <div class="title-bg">
                <div class="title-bg-left"></div>
                <div class="title-bg-right"></div>
              </div>
              <div class="text">{{ language.userLogin }}</div>
            </div>

            <!-- 表单内容 -->
            <div class="form-item">
              <el-form
                label-width="0"
                :model="form"
                :rules="rules"
                ref="loginForm"
              >
                <el-form-item prop="account">
                  <el-input
                    v-model="form.account"
                    prefix-icon="el-icon-s-custom"
                    :placeholder="language.form.account"
                  ></el-input>
                </el-form-item>

                <el-form-item prop="password">
                  <el-input
                    v-model="form.password"
                    show-password
                    prefix-icon="el-icon-lock"
                    :placeholder="language.form.password"
                    @keydown.enter.native="loginEvent"
                  ></el-input>
                </el-form-item>

                <el-form-item>
                  <div class="remember-password">
                    <div class="">
                      <el-checkbox v-model="isRemember"
                        ><span style="color: #fff">{{
                          language.rememberPassword
                        }}</span></el-checkbox
                      >
                    </div>
                    <div class="">
                      <!-- <el-link><span style="color: #fff">忘记密码？</span></el-link> -->
                    </div>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button
                    @click="loginEvent"
                    :loading="isLogin"
                    style="
                      width: 100%;
                      background-color: rgba(127, 157, 247, 1);
                      border: none;
                      color: #fff;
                    "
                    >{{ isLogin ? language.upload : language.login }}</el-button
                  >
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <div class="footer-line"></div>
      </div>

      <!-- 底部说明 -->
      <div class="login-footer">
        opyright @ 2021WALKERA 广州市华科尔股份有限公司 版权所有
        粤ICP备05043730号
      </div>
    </div>
  </div>
</template>

<script>
import required from "@/utils/api";

// import pointwave from "@/components/pointwave/index.vue";
import backgroundImg from "@/assets/img/5.jpg";

import {
  setCookie,
  getCookie,
  setMultiCookieObject,
  getMultiCookie,
  removeMultiCookie,
  setLocalStorage,
} from "@/utils/storage";

// import md5 from 'js-md5'
import { getCode } from "@/utils/rsa.js";
export default {
  name: "login",
  components: {
    pointwave: () => import("@/components/pointwave/index.vue"),
  },
  data() {
    return {
      rimLineList: [
        { style: "left: -3px; top: -3px; clip:rect(0px 30px 30px 0px);" }, // 左上角
        {
          style:
            "right: -3px; top: -3px; clip:rect(0px 30px 30px 0px); transform: rotate(90deg);",
        }, // 右上角
        {
          style:
            "left: -3px; bottom: -2px; clip:rect(0px 30px 30px 0px); transform: rotate(-90deg);",
        }, // 左下角
        {
          style:
            "right: -3px; bottom: -2px; clip:rect(0px 30px 30px 0px); transform: rotate(180deg);",
        }, // 右下角
      ],

      form: {
        account: "",
        password: "",
      },
      isRemember: false, // 是否记住密码

      rules: {
        account: [
          { required: true, message: "请输入账户或者手机号", tragger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", tragger: "blur" }],
      },
      isLogin: false,

      containerStyle: {
        height: "100vh",
        backgroundImage: `url(${backgroundImg})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "100% 100%",
        backgroundPosition: "fixed",
        // backgroundPosition: 'center center',
      },
    };
  },
  computed: {
    language() {
      return this.$languagePackage.login;
    },
    titleStyle() {
      return {
        "letter-spacing": this.$language == "chinese" ? "8px" : "0 !important",
      };
    },
  },
  created() {
    this.rules.account[0].message = this.language.form.account;
    this.rules.password[0].message = this.language.form.password;

    let isRemember = getCookie("isRemember");
    if (isRemember) {
      this.isRemember = Boolean(isRemember);
      let data = getMultiCookie(["account", "password"]);
      this.form = Object.assign({}, data);
    }
  },
  methods: {
    loginEvent: function () {
      this.isLogin = true;
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          let params = Object.assign({}, this.form);

          let cookiePass = getCookie("password");
          let isEchoPass = cookiePass && cookiePass === params.password;

          if (isEchoPass) {
            params.password = getCode(params.password);
          } else {
            params.password = getCode(md5(params.password));
          }

          params.pmd = params.account.toString() + params.password.toString();

          required("login", params)
            .then((res) => {
              // 设置是否记住密码
              let cookieParams = {};
              if (this.isRemember) {
                let password = isEchoPass
                  ? this.form.password
                  : md5(this.form.password);
                cookieParams = {
                  isRemember: true,
                  account: params.account,
                  password: password,
                };
                setMultiCookieObject(cookieParams, 72);
              } else {
                removeMultiCookie(["isRemember", "account", "password"]);
              }

              let data = res.data || {};
              this.$store.commit("setUserInfo", data);

              setLocalStorage("userInfo", data);

              setCookie("token", data.token, 72); // 设置token72个小时后过期

              this.$router.push({ name: "home" });
              this.isLogin = false;
            })
            .catch((err) => {
              this.isLogin = false;
            });

          return false;
        }
        this.isLogin = false;
      });
    },
  },
};
</script>
<style lang="less">
#login {
  .el-input__inner {
    background-color: rgba(0, 0, 0, 0);
    border: none;
    border-radius: 0;
    border-bottom: 1px solid #ffffff;
    color: #fff;
  }
}
</style>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  #login {
    // 水平居中
    .logi-main {
      left: @radio * -70px !important;
    }

    .login-logo {
      left: @radio * 15px !important;
      left: @radio * 10px !important;
      height: @radio * 104px !important;
      .logo-img {
        left: @radio * 30px !important;
        top: @radio * 22px !important;
      }
      .logo-bg {
        left: @radio * -190px !important;
        top: @radio * -23px !important;
      }
    }

    .login-form {
      width: @radio * 450px !important;
      margin-top: @radio * -280px !important;
      .form-title {
        h1 {
          letter-spacing: @radio * 8px !important;
        }
      }
      .form-content {
        min-height: @radio * 100px !important;
        border-radius: @radio * 5px !important;
        .content-main {
          @size: @radio * 8px !important;
          width: calc(100% - @size) !important;
          min-height: @radio * 94px !important;
          height: calc(100% - @size) !important;
          .rim-line {
            width: @radio * 40px !important;
            height: @radio * 40px !important;
            border-radius: @radio * 5px !important;
          }
          .from-mian-title {
            width: @radio * 120px !important;
            height: @radio * 46px !important;
            top: @radio * -2px !important;
            margin-left: @radio * -60px !important;
            .title-bg {
              .title-bg-item {
                height: @radio * 30px !important;
              }
              .title-bg-left {
                .title-bg-item;
                left: @radio * 13px !important;
              }
              .title-bg-right {
                .title-bg-item;
                right: @radio * 13px !important;
              }
            }

            .text {
              height: @radio * 30px !important;
            }
          }
          .form-item {
            padding: 0 @radio * 40px !important;
          }
        }
      }
      .footer-line {
        height: @radio * 2px !important;
        margin-top: @radio * -4px !important;
      }
    }
    .login-footer {
      bottom: @radio * 50px !important;
    }

    @fluxayH: @radio * 160px;
    .login-fluxay {
      .fluxay-main {
        .fluxay-cell {
          height: @fluxayH !important;

          &::before {
            width: @radio * 14px !important;
            height: @radio * 160px !important;
          }
        }
      }
    }
  }
}

#login {
  width: 100%;
  height: 100vh;
  // 水平居中
  .logi-main {
    position: fixed;
    top: 0;
    left: -70px;
    bottom: 0;
    right: 0;

    display: flex;
    align-items: center;
    justify-content: center;
  }

  .login-logo {
    position: fixed;
    top: 0;
    left: 15px;
    zoom: 0.7;
    // height: 100%;
    display: flex;
    align-items: center;
    position: absolute;
    left: 10px;
    top: 0;
    height: 104px;
    .logo-img {
      position: fixed;
      z-index: 10;
      left: 30px;
      top: 22px;
      width: 170px;
    }
    .logo-bg {
      position: fixed;
      left: -190px;
      top: -23px;
    }
  }

  .login-form {
    width: 450px;
    height: auto;
    // color: #ffffff;
    margin-top: -280px;
    .form-title {
      width: 100%;
      text-align: center;
      // font-family: '思源黑体';
      h1 {
        letter-spacing: 8px;
        // 2px 1px 0 #000, 1px 1px 0 #000, 3px 3px 0 #000, 3px 3px 0 #000, 3px 3px 0 #000, 3px 3px 0 #000
        // text-shadow: 1px 1px 0 #ccc, 2px 2px 0 #ccc,
        //   /* end of 2 level deep grey shadow */ 3px 3px 0 #444, 4px 4px 0 #444,
        //   5px 5px 0 #444, 6px 6px 0 #444; /* end of 4 level deep dark shadow */
      }
    }
    .form-content {
      width: 100%;
      min-height: 100px;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      // @borderColor: rgba(51, 72, 172, 1);
      .content-main {
        border-radius: 5px;
        width: calc(100% - 8px);
        min-height: 94px;
        height: calc(100% - 8px);
        // border: 2px solid @borderColor;
        // background-color: rgba(6, 59, 127, 0.8);
        // background-image: linear-gradient(
        //   to top,
        //   rgba(19, 64, 159, 0.9),
        //   rgba(17, 57, 144, 0.8),
        //   rgba(13, 44, 112, 0.6),
        //   rgba(11, 38, 100, 0.1)
        // );
        position: relative;
        .rim-line {
          position: absolute;
          width: 40px;
          height: 40px;
          border-radius: 5px;
          // border: 4px solid rgba(116, 137, 249, 1);
        }
        .from-mian-title {
          width: 120px;
          height: 46px;
          position: absolute;
          top: -2px;
          left: 50%;
          margin-left: -60px;
          .title-bg {
            position: relative;
            height: 100%;
            .title-bg-item {
              //   transform: skewX(30deg);
              width: 100%;
              position: absolute;
              //   left: 0;
              height: 30px;
              // border: 2px solid @borderColor;
              border-top: none;
              // background-color: rgba(0, 56, 115, 1);
            }
            .title-bg-left {
              transform: skewX(-30deg);
              .title-bg-item;
              border-left: none;
              left: 13px;
            }
            .title-bg-right {
              transform: skewX(30deg);
              .title-bg-item;
              border-right: none;
              right: 13px;
            }
          }

          .text {
            // color: #ffffff;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .form-item {
          padding: 0 40px;
          .remember-password {
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
    .footer-line {
      width: 80%;
      height: 2px;
      // background-image: linear-gradient(
      //   to right,
      //   rgba(119, 161, 246, 0.1),
      //   rgba(59, 117, 235, 0.4),
      //   rgba(255, 255, 255, 1),
      //   rgba(59, 117, 235, 0.4),
      //   rgba(119, 161, 246, 0.1)
      // );
      border-radius: 10px;
      margin-top: -4px;
      margin-left: 10%;
      z-index: 22;
      position: relative;
      // background-color: #19f6fa;
    }
  }
  .login-footer {
    width: 100%;
    position: fixed;
    bottom: 50px;
    left: 0;
    text-align: center;
    // color: #fff;
  }

  @fluxayH: 160px;
  .login-fluxay {
    position: fixed;
    left: 50%;
    top: 0;
    // top: -@fluxayH;
    .fluxay-main {
      position: relative;
      .fluxay-cell {
        // width: 0px;
        height: @fluxayH;
        position: absolute;
        left: 0;
        top: 0;

        &::before {
          content: "";
          width: 14px;
          height: 160px;
          //   border-radius: 50%;
          opacity: 0.7;
          // background-color: #ffff00;
        }
      }
    }
  }
}
</style>