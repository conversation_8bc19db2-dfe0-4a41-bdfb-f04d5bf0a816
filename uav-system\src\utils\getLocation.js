export function geolocation() {
    return new Promise((resolve, reject) => {
        AMap.plugin('AMap.Geolocation', function() {
            var geolocation = new AMap.Geolocation({
                enableHighAccuracy:  true,
                  //  是否使用高精度定位，默认:true
                        timeout:  1000,
                  //  超过1秒后停止定位，默认：无穷大
                showButton: true, //显示定位按钮，默认：true
            });
            let results = []
            geolocation.getCurrentPosition((status, result) => {
                if (status == "complete") {
                    results[0] = result.position.lng
                    results[1] = result.position.lat
                    window.locationSite = results
                    return resolve(results)
                } else {
                    geoCity().then(res => {
                        results = res
                        window.locationSite = res
                        return resolve(results)
                    }).catch(() => {
                        return reject(false)
                    })
                }

            })
        })

    })
}

function geoCity() {
    return new Promise((resolve, reject) => {
        AMap.plugin('AMap.CitySearch', function() {
            var citySearch = new AMap.CitySearch()
            citySearch.getLocalCity(function(status, result) {
                if (status === 'complete' && result.info === 'OK') {
                    return resolve(result.city)
                } else {
                    reject(false)
                }
            })
        })
    })

}