<template>
  <div class="weatherDiv">
    <div class="weaTitle">
      <span>{{ weatherData.title }}</span>
      <span class="title-time">{{ weatherData.times }}</span>
      <el-button @click="refreshWea"
        ><el-image :src="weatherCode_2 ? refreshImg_1 : refreshImg"></el-image
      ></el-button>
      <el-button @click="closeWea"
        ><el-image :src="weatherCode_1 ? closeImg_1 : closeImg"></el-image
      ></el-button>
    </div>
    <el-divider></el-divider>
    <div class="weaContent">
      <el-row>
        <el-col :span="14" class="temperaClass">
          <span class="item_1">{{ weatherData.temperature }}</span>
          <span class="item-2">℃</span>
        </el-col>
        <el-col :span="10" class="temperaClass_1">
          <div>{{ weatherData.weather }}</div>
          <div>
            {{ routeLanguage.weather.windDirection
            }}{{ weatherData.winddirection }}{{ weatherData.windpower
            }}{{ routeLanguage.weather.level }}
          </div>
          <div>
            {{ routeLanguage.weather.humidity }}{{ weatherData.humidity }}%
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  name: "weatherDiv",
  props: {
    weatherData: {
      type: Object,
      default() {
        return {};
      },
    },
    routeLanguage: {
      type: Object,
      default() {
        return {};
      },
    },
    weatherCode_1: {
      type: Boolean,
      default: false,
    },
    weatherCode_2: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      refreshImg: require("../../../assets/img/routeplan/refresh.png"),
      refreshImg_1: require("../../../assets/img/routeplan/refresh_1.png"),
      closeImg: require("../../../assets/img/routeplan/close.png"),
      closeImg_1: require("../../../assets/img/routeplan/close_1.png"),
    };
  },
  methods: {
    refreshWea() {
      this.$emit("refreshWea", "");
    },
    closeWea() {
      this.$emit("closeWea", "");
    },
  },
};
</script>
<style lang="less" scoped>
.weatherDiv {
  width: auto;
  height: auto;
  min-width: 200px;
  min-height: 100px;
  position: absolute;
  z-index: 20;
  cursor: move;
  bottom: 350px;
  right: 30px;
  padding: 1% 1.5%;
  border-radius: 8px;
  .el-divider {
    height: 3px;
    margin: 0;
  }
  .weaTitle {
    margin-bottom: 2%;
    letter-spacing: 1.5px;
    font-size: 18px;
    .el-button {
      padding: 0;
      margin: 0 5px;
      border: none;
      .el-image {
        width: 18px;
      }
    }
    .title-time {
      margin-left: 80px;
    }
  }
  .weaContent {
    .el-row {
      .el-col {
        &.temperaClass {
          text-align: center;
          .item_1 {
            font-size: 60px;
          }
          .item-2 {
            font-size: 30px;
          }
        }
        &.temperaClass_1 {
          padding-top: 5px;
          font-size: 14px;
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .weatherDiv {
    min-width: @zoomIndex * 200px !important;
    min-height: @zoomIndex * 100px !important;
    bottom: @zoomIndex * 350px;
    right: @zoomIndex * 30px;
    border-radius: @zoomIndex * 8px !important;
    .el-divider {
      height: @zoomIndex * 3px !important;
    }
    .weaTitle {
      letter-spacing: @zoomIndex * 1.5px !important;
      font-size: @zoomIndex * 18px !important;
      .el-button {
        margin: 0 @zoomIndex * 5px !important;
        .el-image {
          width: @zoomIndex * 18px !important;
        }
      }
      .title-time {
        margin-left: @zoomIndex * 80px !important;
      }
    }
    .weaContent {
      .el-row {
        .el-col {
          &.temperaClass {
            .item_1 {
              font-size: @zoomIndex * 60px !important;
            }
            .item-2 {
              font-size: @zoomIndex * 30px !important;
            }
          }
          &.temperaClass_1 {
            padding-top: @zoomIndex * 5px !important;
            font-size: @zoomIndex * 14px !important;
          }
        }
      }
    }
  }
}
</style>