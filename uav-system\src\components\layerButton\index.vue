<template>
  <div class="layerButton" ref="layerButton">
    <div class="scene-mode" v-if="sceneModeButton" @click="sceneModeChange">
      {{ sceneMode3D ? "2D" : "3D" }}
    </div>
    <div class="layer-mode" v-if="layerButton">
      <el-popover
        placement="left-end"
        trigger="hover"
        popper-class="layer-button-popper"
      >
        <el-button slot="reference">
          <el-image :src="layerButtonIcon" class="layer-button-icon"></el-image>
        </el-button>
        <div class="layer-content">
          <div class="layer-list">
            <el-radio-group v-model="layerIndex" @input="layerChange">
              <el-radio
                v-for="item in layerList"
                :key="item.value"
                :label="item.value"
              >
                <el-image class="layer-item-icon" :src="item.imgSrc"></el-image>
                <div class="">{{ item.label }}</div>
              </el-radio>
            </el-radio-group>
          </div>
        </div>
      </el-popover>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    layerIndex: {
      type: Number,
      default: 0,
    },
    layerButton: {
      type: Boolean,
      default: false,
    },
    sceneModeButton: {
      type: Boolean,
      default: false,
    },
    sceneMode3D: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      layerButtonIcon: require("@/assets/img/layerButton.png"),
      layerList: [
        {
          label: "标准图",
          value: 0,
          imgSrc: require("@/assets/img/tileLayer.png"),
          id: "standard",
        },
        {
          label: "卫星图",
          value: 1,
          imgSrc: require("@/assets/img/satellite.png"),
          id: "satellite",
        },
      ],
    };
  },
  created() {
    let layerLabel = this.$languagePackage.routes.layerType;
    for (let index = 0; index < this.layerList.length; index++) {
      this.layerList[index].label = layerLabel[this.layerList[index].id];
    }
  },
};
</script>
<style lang="less" scoped>
.layerButton {
  position: absolute;
  bottom: 30px;
  right: 30px;
  .scene-mode {
    background-color: #ffffff;
    width: 26px;
    height: 26px;
    padding: 4px;
    margin-bottom: 6px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    font-weight: bold;
    color: #707070;
  }
  .layer-mode {
    .el-button {
      padding: 4px;
    }
  }
}
</style>
<style lang="less">
.layerButton {
  .el-button {
    // padding: 4px;
    .layer-button-icon {
      width: 26px;
      height: 26px;
    }
  }
}
.layer-button-popper {
  margin-right: 6px !important;
  min-width: 0 !important;
  padding: 4px !important;
  background-color: #081027;
  border: none;
  width: auto;
  z-index: 9999 !important;
  .popper__arrow {
    display: none;
  }
  .layer-content {
    width: auto;
    .layer-list {
      .el-radio-group {
        .el-radio {
          display: flex;
          align-items: center;
          margin: 8px 4px;
          .el-radio__label {
            display: flex;
            align-items: center;
            color: #ffffff;
          }
          .el-image {
            width: 16px;
            height: 16px;
            margin-right: 4px;
            .el-image__inner {
              vertical-align: bottom;
            }
          }
        }
      }
    }
  }
}
</style>