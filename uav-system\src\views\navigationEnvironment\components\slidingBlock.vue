<!-- 滑块 -->
<template>
  <div
    :class="vertical ? 'sliding-vertical' : 'sliding-block'"
    :style="{ width: width }"
    ref="slidingBlock"
    class="remote-sliding"
  >
    <div class="sliding-block-main">
      <div class="block-mobile" ref="blockMobile" @mousedown="mobileDown">
        <slot name="mobile">
          <div class="block-cell">
            <div class="cell-item" style="margin-top: 0"></div>
            <div class="cell-item"></div>
            <div class="cell-item" style="margin-right: 0"></div>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    width: {
      // 宽度
      type: String,
      default: "100%",
    },
    max: {
      // 最大值
      type: Number,
      default: 100,
    },
    value: [Number, String], // 绑定的值
    // 方向
    vertical: [Boolean, String],
    interval: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      left: 0, // 滑块左边距
      blockMobileWidth: 0, // 滑块长度
      slidingBlockWidth: 0, // 可滑行距离
      isDown: false, // 是否按下
      initLeft: 0, // 滑块初始位置
      mouseStart: 0, // 鼠标按下时的位置
      nextValue: null,

      inputTime: null,
      changeTime: null,

      changeValue: null,
    };
  },
  watch: {
    value(val) {},
  },
  mounted() {
    this.$nextTick(() => {
      let key = this.vertical ? "offsetTop" : "offsetLeft";
      this.initLeft = this.$refs.blockMobile[key];
      // 获取长度
      let length = this.vertical ? "offsetHeight" : "offsetWidth";
      this.blockMobileWidth = this.$refs.blockMobile[length];
      this.slidingBlockWidth = this.$refs.slidingBlock[length];

      this.landscapeEcho();

      console.log("this.interval---------》", this.interval);
    });
  },
  methods: {
    mobileDown: function (e) {
      this.isDown = true;

      if (this.vertical) {
        this.mouseStart = e.pageY; // 开始位置
      } else {
        this.mouseStart = e.pageX; // 开始位置
      }

      // 获取长度
      let length = this.vertical ? "offsetHeight" : "offsetWidth";
      this.blockMobileWidth = this.$refs.blockMobile[length];
      this.slidingBlockWidth = this.$refs.slidingBlock[length];

      // 获取当前滑块位置
      let key = this.vertical ? "offsetTop" : "offsetLeft";
      this.initLeft = this.$refs.blockMobile[key];

      this.$refs.slidingBlock.style.cursop = "move";
      window.addEventListener("mouseup", this.mouseUp);
      window.addEventListener("mousemove", this.mouseMoveSliding);
    },
    mouseMoveSliding: function (e) {
      // 当按下时才去计算移动的距离
      if (this.isDown) {
        let end = this.vertical ? e.pageY : e.pageX;
        let move = end - this.mouseStart + this.initLeft;
        let maxVal = this.slidingBlockWidth - this.blockMobileWidth;
        if (move < 0) {
          move = 0;
        } else if (move > maxVal) {
          move = maxVal;
        }
        let moveKey = this.vertical ? "top" : "left";
        this.$refs.blockMobile.style[moveKey] = move + "px";
        let key = this.vertical ? "offsetTop" : "offsetLeft";
        let left = this.$refs.blockMobile[key];
        let val = this.getSlidingValue(left);

        this.changeValue = val;
        
        if (!this.inputTime) {
          this.inputTime = setInterval(() => {
            this.$emit("onInput", Math.floor(this.changeValue));
          }, this.interval);
        }

        // if (!this.inputTime) {
        //   this.$emit("onInput", Math.floor(val));
        //   this.inputTime = setTimeout(() => {
        //     clearTimeout(this.inputTime);
        //     this.inputTime = null;
        //   }, 20);
        // }
        // console.log("触发频率--------->");
        // this.$emit("onInput", Math.floor(val));
      }
    },
    mouseUp: function () {
      this.isDown = false;
      this.$refs.slidingBlock.style.cursop = "";

      clearInterval(this.inputTime);
      this.inputTime = null;

      let key = this.vertical ? "offsetTop" : "offsetLeft";
      this.initLeft = this.$refs.blockMobile[key];
      // 获取像素与值的百分比, 即1 = 多少px
      let val = this.getSlidingValue();

      val = Math.floor(val);
      this.$emit("onChange", val);
      this.$emit("input", val);
      this.$emit("up", 50);
      window.removeEventListener("mouseup", this.mouseUp);
      window.removeEventListener("mouseup", this.mouseMoveSliding);

      // 松开时回到中间
      this.landscapeEcho(50);
      this.changeValue = 50;
    },
    // 获取滑动的值
    getSlidingValue: function (leftGlissade) {
      let val = 0;
      let left = this.initLeft;
      if (leftGlissade || leftGlissade === 0) {
        left = leftGlissade;
      }
      let width = this.slidingBlockWidth - this.blockMobileWidth;
      if (!this.vertical) {
        val = (left / width) * this.max;
      } else {
        let height = width - left;
        val = (height / width) * this.max;
      }
      return val;
    },

    // 横屏回显值
    landscapeEcho: function (val) {
      if (!this.value && val) {
        return false;
      }
      if (val) {
        this.$emit("input", val);
      }

      let view = this.slidingBlockWidth - this.blockMobileWidth;

      let proportion = this.value / this.max; // 当前值所占比
      let transition = proportion * view; // 转换成像素
      let distance = 0;

      if (this.vertical) {
        distance = view - transition;
      } else {
        distance = transition;
      }

      let move = Math.floor(distance);

      let moveKey = this.vertical ? "top" : "left";
      this.$refs.blockMobile.style[moveKey] = move + "px";

      let key = this.vertical ? "offsetTop" : "offsetLeft";
      this.initLeft = this.$refs.blockMobile[key];
    },
  },
};
</script>

<style lang="less" scoped>
.sliding-block-style(vertical, 0 4px, 100%, 4px, calc(100% - 32px), -100%, 12px, 32px, 80%, 2px);

.sliding-vertical {
  height: 100%;
  position: relative;
  z-index: 10;
  .block-cell {
    flex-direction: column;
    .cell-item {
      margin-right: 0px !important;
      margin-top: 5px;
    }
  }
}

.sliding-block-style(block);

.sliding-block-style(
    @className, // 类名称
    @mainPadding: 4px 0, // 内容内边距
    @slidingBlockHeight: 4px, // 进度条高度
    @slidingBlockWidth: 100%, // 进度条高度
    @mobileTop: -100%, // 滑块上边距
    @mobileLeft: 0, // 滑块左边距
    @blockCellWidth: 32px, // 滑块宽度
    @blockCellHeight: 12px, // 滑块高度
    @blockCellItemWidth: 2px, // 滑块里面分隔线宽度
    @blockCellItemHeight: 80%, // 滑块里面分隔线高度
) {
  // 横屏样式
  .sliding-@{className} {
    padding: @mainPadding;
    .sliding-block-main {
      position: relative;
      height: @slidingBlockHeight;
      width: @slidingBlockWidth;
      // background-color: #fff;
      border-radius: 8px;
      .block-mobile {
        position: absolute;
        left: @mobileLeft;
        top: @mobileTop;
      }

      .block-cell {
        height: @blockCellHeight;
        width: @blockCellWidth;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        cursor: move;
        .cell-item {
          width: @blockCellItemWidth;
          height: @blockCellItemHeight;
          margin-right: 5px;
        }
      }
    }
  }
}
</style>