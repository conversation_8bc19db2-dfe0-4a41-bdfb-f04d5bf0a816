import Vue from "vue"
import {
  getLocalStorage
} from "@/utils/storage.js"
import dictList from '@/assets/json/dictList.json'

// 引入语言包
import Chinese from "./Chinese/index";
import English from "./English/index";

// 引入element-ui英语语言包 
import lang from 'element-ui/lib/locale/lang/en'
import locale from 'element-ui/lib/locale'

// 获取语音设置
let language = getLocalStorage("language");
let type = language || "chinese";
Vue.prototype.$language = type;

// 获取主题
let theme = getLocalStorage("theme") || "default"
Vue.prototype.$theme = theme;
let loadingEnUI = "false";
// 设置语言包
let packages = {};
if (type == "english") {
  packages = English;
  loadingEnUI = true;
  locale.use(lang);

} else {
  packages = Chinese;
  loadingEnUI = false;
  // 加载element-ui英文版

}
let dictData = {}
for (const key in dictList) {
  let items = dictList[key]
  dictData[key] = items.map(item => {
    let label = type == "english" ? item.en : item.label
    item.label = label
    return item
  })
}
Vue.prototype.$dict = dictData;
Vue.prototype.$mapDict = (key) => {
  let list = dictData[key];
  let map = {};
  for (let i = 0; i < list.length; i++) {
    map[list[i].value] = list[i].label;
  }
  return map;
}
Vue.prototype.$languagePackage = packages;
Vue.prototype.$loadingEnUI = loadingEnUI

export default packages;
