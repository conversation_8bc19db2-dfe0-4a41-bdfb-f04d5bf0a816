<!-- 飞行数据 -->
<template>
  <bg-layout :isShowQuadrangle="false" class="flight-data">
    <!-- 头部 -->
    <template v-slot:header>
      <div class="flight-data-top">
        <!-- 标题 -->
        <div class="flight-data-title">{{ language.title }}</div>
        <!-- 圆形图标 -->
        <div class="pie-round" style="">
          <div class="pie-round-top" v-if="filghtEcharts">
            <div class="pie-row" id="canvas1"></div>
            <div class="pie-row" id="canvas2"></div>
            <div class="pie-row" id="canvas3"></div>
          </div>

          <div class="pie-round-text">
            <div class="text-row" :style="pieItemStyle">
              {{ language.pie[1] }}
            </div>
            <div class="text-row" :style="pieItemStyle">
              {{ language.pie[2] }}
            </div>
            <div class="text-row" :style="pieItemStyle">
              {{ language.pie[3] }}
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 表格数据 -->
    <template v-slot:content>
      <div class="table-data">
        <div class="table-thead">
          <table style="width: 100%" border="0" cellpadding="0" cellspacing="0">
            <thead ref="tableThead">
              <tr class="table-thead-tr">
                <th
                  :style="tableItemStyle"
                  style="width: 40%; letter-spacing: 10px"
                  class="thead-cell"
                >
                  {{ language.list.time }}
                </th>
                <th
                  :style="tableItemStyle"
                  style="width: 30%"
                  class="thead-cell"
                >
                  {{ language.list.level }}
                </th>
                <th
                  :style="tableItemStyle"
                  style="width: 30%"
                  class="thead-cell"
                >
                  {{ language.list.stave }}
                </th>
              </tr>
            </thead>
          </table>
        </div>
        <div class="table-tbody">
          <scroll-list :data="data">
            <template v-slot:content="scope">
              <table
                style="width: 100%"
                border="0"
                cellpadding="0"
                cellspacing="0"
              >
                <tbody>
                  <tr
                    class="table-tbody-tr"
                    v-for="(item, index) in scope.data"
                    :key="index"
                    :style="{
                      animationDelay:
                        scope.current == 1 ? 1 + 0.2 * index + 's' : '0s',
                    }"
                  >
                    <td style="width: 40%; color: #2adbff" class="tbody-cell">
                      {{ item.create_time }}
                    </td>
                    <td
                      style="width: 30%"
                      class="tbody-cell"
                      :style="{ color: alarmLevel[item.level] }"
                    >
                      {{ alarmLevelList[item.level] }}
                    </td>
                    <td
                      style="width: 30%"
                      class="tbody-cell"
                      :style="{ color: processState[item.state] }"
                    >
                      {{ processStateList[item.state] }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </template>
          </scroll-list>
        </div>
      </div>
    </template>
  </bg-layout>
</template>

<script>
import bgLayout from "../components/bgLayout.vue";
import scrollList from "@/components/scrollList/index.vue";
export default {
  components: {
    bgLayout,
    scrollList,
  },
  data() {
    return {
      processState: {
        10: "#ff0000",
        20: "#0fc2b1",
      },
      alarmLevel: {
        30: "#ff0000",
        20: "#fff100",
        10: "#0fc2b1",
      },

      data: [],
      isPush: false,
      filghtEcharts: true,
    };
  },
  computed: {
    alarmLevelList: function () {
      return this.$store.getters.getDictDataKeyValue("alarmLevel");
    },
    processStateList: function () {
      return this.$store.getters.getDictDataKeyValue("processState");
    },
    language() {
      return this.$languagePackage.home.flightData;
    },
    tableItemStyle() {
      return {
        "letter-spacing": this.$loadingEnUI ? 0 : "5px",
      };
    },
    pieItemStyle() {
      return {
        "letter-spacing": this.$loadingEnUI ? 0 : "5px",
      };
    },
  },
  created() {
    this.$store.commit("setMultiMessage", {
      key: "flightData",
      message: this.getPushData,
    });

    this.$nextTick(() => {
      if (!this.isPush) {
        let data = {
          pending_warning: 0,
          handled_warning: 0,
        };
        this.getPushData(data);
      }
    });
  },
  mounted() {
    this.echartsInit("canvas1", {
      total: 1,
      value: 0,
      fontColor: "#fc9604",
      totalColor: "rgba(160, 160, 160,0.5)",
      valueColor: "#fc9604",
    });
    this.echartsInit("canvas2", {
      total: 1,
      value: 0,
      fontColor: "#ff0000",
      totalColor: "rgba(160, 160, 160,0.5)",
      valueColor: "#ff0000",
    });
    this.echartsInit("canvas3", {
      total: 1,
      value: 0,
      fontColor: "#01e569",
      totalColor: "rgba(160, 160, 160,0.5)",
      valueColor: "#01e569",
    });
  },
  methods: {
    getPushData: function (msg_id, data) {
      if (msg_id == 105) {
        this.isPush = true;
        this.filghtEcharts = false;
        setTimeout(() => {
          this.filghtEcharts = true;
          this.$nextTick(() => {
            if (this.filghtEcharts) {
              this.echartsInit("canvas1", {
                total: data.total_warnings || 1,
                value: data.total_warnings || 0,
                fontColor: "#fc9604",
                totalColor: "rgba(160, 160, 160,0.5)",
                valueColor: "#fc9604",
              });
              this.echartsInit("canvas2", {
                total: data.total_warnings || 1,
                value: data.pending_warning || 0,
                fontColor: "#ff0000",
                totalColor: "rgba(160, 160, 160,0.5)",
                valueColor: "#ff0000",
              });
              this.echartsInit("canvas3", {
                total: data.total_warnings || 1,
                value: data.handled_warning || 0,
                fontColor: "#01e569",
                totalColor: "rgba(160, 160, 160,0.5)",
                valueColor: "#01e569",
              });
            }
          });
        }, 1);

        this.data = data.warn_list || [];
      }
    },
    /**
     * echarts初始化
     * @param {id} 挂载dom ID
     * @param {config} 相关配置
     */
    echartsInit: function (id, config) {
      let { value, total, fontColor, totalColor, valueColor } = config || {};
      let option = {
        title: {
          show: true,
          text: value,
          x: "center",
          y: "center", // 通过x,y将标题(进度)定位在圆环中心
          textStyle: {
            fontSize: "20",
            color: fontColor || "#ffffff",
            fontWeight: "700",
            fontFamily: "DINPro, DINPro-Regular",
          },
        },
        grid: {
          left: "10px",
          right: "10px",
          top: "10px",
          bottom: "10px",
        },
        tooltip: {
          trigger: "item",
          formatter: "{d}%",
          show: false,
        },
        legend: {
          orient: "vertical",
          x: "left",
          show: false,
        },
        series: {
          emphasis: false,
          name: "",
          type: "pie",
          radius: ["60%", "80%"],
          avoidLabelOverlap: true,
          label: {
            show: false,
            position: "center",
            // emphasis: {
            //   show: false,
            // },
          },
          labelLine: {
            show: false,
          },
          data: [
            {
              value: value,
              name: "",
              itemStyle: {
                color: valueColor || "rgb(22,58,129)",
              },
            },
            {
              value: total - value,
              name: "",
              itemStyle: {
                color: totalColor || "red", //透明色，也可以设置把其他颜色
              },
            },
          ],
        },
      };

      var myChart = this.$echarts.init(document.getElementById(id));
      myChart.setOption(option);
    },
  },
};
</script>

<style lang="less" scoped>
.flight-data {
  //   width: 100%;
  //   border: 2px solid rgb(59, 159, 233);
  //
  .flight-data-top {
    // background-color: rgba(59, 159, 233, 0.1);
  }
  .flight-data-title {
    line-height: 34px;
    padding: 0 10px;
    text-align: center;
    // color: #5dbdff;
    font-weight: 700;
    letter-spacing: 3px;
    // background-image: linear-gradient(
    //   to right,
    //   rgba(47, 83, 174, 0.5),
    //   rgba(26, 50, 112, 0.5),
    //   rgba(19, 40, 85, 0.5)
    // );
  }
  .pie-round {
    height: 130px;
    color: #fff;
    width: 100%;
    // background-image: linear-gradient(
    //   to right,
    //   rgba(26, 50, 112, 0.2),
    //   rgba(19, 40, 85, 0.2)
    // );
    .pie-round-top {
      width: 100%;
      display: flex;
      .pie-row {
        width: 33.3333333%;
        height: 100px;
      }
    }

    .pie-round-text {
      display: flex;
      .text-row {
        flex-grow: 1;
        text-align: center;
        font-size: 12px;
        letter-spacing: 5px;
      }
    }
  }

  .table-data {
    margin-top: 10px;
    height: calc(100% - 174px);
    position: relative;
    .table-tbody {
      height: calc(100% - 35px);
      // overflow: hidden;
      // overflow-y: auto;

      .table-tbody-tr {
        animation-name: tbodyTr;
        animation-duration: 0.4s;
        animation-fill-mode: forwards; // 保留动画最后的状态
        opacity: 0;
      }
      @keyframes tbodyTr {
        0% {
          transform: rotateX(90deg);
        }
        50% {
          opacity: 1;
        }
        100% {
          transform: rotateX(0deg);
          opacity: 1;
        }
      }
    }
    .thead-cell {
      padding: 8px 0;
      font-size: 12px;
      // background-color: #1d2088;
      // color: #fff;
      letter-spacing: 5px;
    }
    .tbody-cell {
      padding: 8px 0;
      font-size: 12px;
      // color: #fff;
      text-align: center;
      // background-color: rgba(8, 17, 49, 1);
      // border-bottom: 1px solid rgb(47, 96, 153);
    }
  }
}
</style>