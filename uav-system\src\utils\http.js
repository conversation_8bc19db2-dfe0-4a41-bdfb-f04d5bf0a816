// import qs from "qs"
import "@/assets/js/qs.min.js"
import axios from "axios"
import {
    Message,
    MessageBox
} from 'element-ui';

import router from "../router/index"
import {
    removeCookie
} from "../utils/storage"
import store from "../store/index"
import Vue from "vue";

//创建axios实例，利用axios.create方法
const axiosService = axios.create({
    // baseURL: http.baseUrl, //发送请求时会在URL前面拼接baseURL
    timeout: 60000, //设置请求超时
})

//设置拦截器
//设置请求拦截器
axiosService.interceptors.request.use(function(config) {
    // 表单类型
    if (config.headers["Content-Type"] == "multipart/form-data;") {
        return config;
    }
    if (config.method == "post") {
        config.data = Qs.stringify(config.data)
    }

    return config;
}, function(error) {
    //错误时处理的事件
    return Promise.reject(error)
})

//设置响应拦截器 <EMAIL>
axiosService.interceptors.response.use(function(response) {
    //响应数据事件
    let successCode = [2000, 2201, 44020];
    let data = response.data; // 请求响应参数
    let code = data.code; // 请求响应状态
    if (['40263'].indexOf(String(code)) !== -1) { // 登录超时
        let item = document.getElementsByClassName("login-40263");

        if (item.length >= 1) { // 保证只弹出一次
            // if (item[0].parentNode.style.display !== "none") {
            return false;
            // }
        }
        MessageBox.confirm(Vue.prototype.$language == "english" ? 'Login has expired, please log in again' : '登录已过期，请重新登录', Vue.prototype.$language == "english" ? 'Tips' : '提示', {
            confirmButtonText: Vue.prototype.$language == "english" ? 'confirm' : '确定',
            cancelButtonText: Vue.prototype.$language == "english" ? 'cancel' : '取消',
            type: 'warning',
            customClass: "login-40263"
        }).then(() => {
            store.commit("setUserInfo", {});
            removeCookie("token");
            router.push({
                name: "login"
            });
        }).catch(() => {}).finally(() => {
            item[0].parentNode.remove()
        })
        return Promise.reject(data);

    } else if (successCode.indexOf(code) === -1) { // 错误提示
        Message({
            type: "error",
            message: data.msg
        })

        return Promise.reject(data);
    }

    return data;
}, function(error) {

    if (error.response) {
        let code = error.response.data.status
        if (code == 404) {
            Message({
                type: "error",
                message: Vue.prototype.$language == "english" ? 'Interface does not exist' : '接口不存在'
            })
        } else if (code == 401) {
            Message({
                type: "error",
                message: Vue.prototype.$language == "english" ? 'No permission to access' : '无权限访问'
            })
        } else if (code == 500) {
            Message({
                type: "error",
                message: Vue.prototype.$language == "english" ? 'Server error, please contact the administrator' : '服务器错误,请联系管理员'
            })
        }
    }


    //响应错误事件
    return Promise.reject(error);
})
export default axiosService