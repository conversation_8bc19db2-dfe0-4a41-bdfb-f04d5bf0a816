//设备管理页面
.equips {
  .equipment {
    background-color: rgb(2, 8, 32);

    .searchDiv {
      .el-button {
        background-color: white;
        color: #0b58de;

        &.active {
          background-color: #0b58de;
          color: white;
        }
      }

      .el-input {
        .el-input__inner {
          background-color: transparent !important;
          color: white !important;
        }
      }
    }

    .content {
      .el-collapse {
        .el-collapse-item {
          background-color: #0c1a2d;

          &.el-collapse-item_1 {
            background-color: #050e19;
          }

          .el-collapse-item__header {
            background-color: transparent !important;

            .num {
              color: white;
            }

            .content-item-1 {
              .el-tag {
                color: white;
              }

              .firstDiv {
                background-color: rgb(51, 233, 51);
              }

              .secondDiv {
                background-color: rgb(53, 53, 204);
              }

              .noneDiv {
                background-color: #35373b;
              }
            }

            .content-item-2 {
              .el-tag {
                color: white;

                .spans {
                  color: #888484;
                }
              }
            }

            .content-item-3 {
              .content-item-3-1 {
                color: white;
                background-color: transparent;

                &.error {
                  color: #35373b;
                }
              }
            }

            .el-tag.error {
              color: #35373b;
            }

            .operation {
              .el-button {
                background-color: transparent;
                color: white;

                &.active {
                  color: #0b58de;
                }
              }
            }

          }

          .el-collapse-item__wrap {
            background-color: #1b1b1c !important;

            .fixedType {
              .content-item-1 {

                // scrollbar-width: thin !important;
                // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
                // -ms-overflow-style: none !important;
                // scrollbar-color: rgba(69, 99, 250, 0.8) #000;

                ::-webkit-scrollbar {
                  width: 3px;
                }

                ::-webkit-scrollbar-track {
                  background-color: #000;
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                ::-webkit-scrollbar-thumb {
                  background-color: rgba(69, 99, 250, 0.8);
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                .content-item-1-state {
                  background-color: #000000;


                  .content-item-1-state-row {
                    .state-col-content {
                      color: white;

                    }

                    .state-col-content {
                      .state-data-title {
                        color: white;

                        .stateDiv {
                          background-color: #fe3500;
                        }

                        .stateDiv-1 {
                          background-color: #1dfd3a;
                        }
                      }

                      .state-data-content-1 {
                        color: #1dfd3a;
                      }

                      .state-data-content-1-1 {
                        color: #1dfd3a;
                      }

                      .state-data-content-2-1 {
                        color: #1dfd3a;
                      }

                      .state-data-content-2 {
                        color: #fe3500;
                      }
                    }
                  }
                }

                .content-item-1-video {


                  .videoOutTip {

                    color: white;
                    background-color: #3d4348;
                  }

                  .inCabinStyle {
                    background-color: rgba(61, 67, 72, 1);

                    .tipinCabin {
                      color: white;

                    }

                    .inCabinTitle {

                      background-color: #010101;
                      color: #fff;
                    }
                  }
                }
              }

              .content-item-2 {
                .videoOutTip {
                  color: white;
                  background-color: #3d4348;

                }
              }

              .content-item-3 {
                background-color: #060606;
                // scrollbar-width: thin !important;
                // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
                // -ms-overflow-style: none !important;
                // scrollbar-color: rgba(69, 99, 250, 0.8) #000;


                &::-webkit-scrollbar {
                  width: 3px;
                }

                &::-webkit-scrollbar-track {
                  background-color: #000;
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                &::-webkit-scrollbar-thumb {
                  background-color: rgba(69, 99, 250, 0.8);
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                .content-item-3-title {
                  color: white;
                }

                .content-item-3-btn {
                  .el-button {
                    background-color: #0b58de;
                    color: white;
                    border: none;

                    &.dangerBtn {
                      background-color: #f56c6c;

                    }

                    &.is-disabled {
                      background-color: #638bd1;
                    }

                    &.active {
                      background-color: white;
                      color: #0b58de;

                      &.is-disabled {
                        background-color: #638bd1;
                      }
                    }

                  }

                  .errorBtn {
                    background-color: #ee3434;
                    color: white;
                  }

                }

                .content-charge {
                  .el-button {
                    background-image: linear-gradient(to bottom, rgb(123, 197, 187), rgb(99, 99, 99));
                    color: white;
                    border: none;

                    &.active {
                      background-image: linear-gradient(to bottom, white, white);
                      // background-color: white;
                      color: #0b58de;
                    }

                    &.is-disabled {
                      background-color: #638bd1;
                    }
                  }
                }

                .content-cell {
                  .content-cell-item {
                    .cell-title {
                      background-color: rgba(38, 17, 155, 0.842);
                      color: #fff;
                    }

                    .cell-value {
                      .el-progress {
                        .el-progress__text {
                          color: #fff !important;
                        }
                      }
                    }
                  }
                }

                // .content-item-3-btn-en{
                //   margin: 2% 5%;
                //   .el-button{
                //     width: 48%;
                //     white-space:normal; 
                //   }

                // }
                .content-item-3-content {
                  .content-index {
                    color: #79818e;

                    &.active {
                      color: #0b58de;
                    }

                    .el-button {
                      background-color: transparent;
                      border: none;

                      &.el-button--text {
                        color: #0b58de !important;

                      }

                      &.is-loading:before {
                        background-color: transparent !important;
                      }
                    }
                  }

                  .el-switch.is-checked .el-switch__core::after {
                    background-color: #0b58de !important;
                  }
                }
              }
            }

            .singleSoldierUav {
              .content-item-1 {
                background-color: rgba(50, 55, 61, 0.3);
                // scrollbar-width: thin !important;
                // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
                // -ms-overflow-style: none !important;
                // scrollbar-color: rgba(69, 99, 250, 0.8) #000;

                &::-webkit-scrollbar {
                  width: 3px;
                }

                &::-webkit-scrollbar-track {
                  background-color: #000;
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                &::-webkit-scrollbar-thumb {
                  background-color: rgba(69, 99, 250, 0.8);
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                .content-item-title {
                  color: rgb(207, 207, 207);
                  border: 1px solid rgb(97, 97, 97);
                  background: rgba(100, 104, 108, 0.3);
                }

                .content-item-1-1 {
                  color: white;

                  .content-item-div {
                    color: rgb(122, 122, 122);

                    .content-item-data {
                      color: white;
                    }
                  }
                }
              }

              .content-item-2 {
                .videoOutTip {
                  color: white;
                  background-color: #3d4348;
                }
              }

              .content-item-3 {
                background-color: rgba(50, 55, 61, 0.3);
                // scrollbar-width: thin !important;
                // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
                // -ms-overflow-style: none !important;
                // scrollbar-color: rgba(69, 99, 250, 0.8) #000;

                &::-webkit-scrollbar {
                  width: 3px;
                }

                &::-webkit-scrollbar-track {
                  background-color: #000;
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                &::-webkit-scrollbar-thumb {
                  background-color: rgba(69, 99, 250, 0.8);
                  -webkit-border-radius: 2em;
                  -moz-border-radius: 2em;
                  border-radius: 2em;
                }

                .content-item-title {
                  color: rgb(207, 207, 207);
                  border: 1px solid rgb(97, 97, 97);
                  background: rgba(100, 104, 108, 0.3);

                }

                .operationBtn {
                  .el-button {
                    background-color: rgb(80, 80, 80);
                    border-color: rgb(96, 96, 96);
                  }

                  .active {
                    background-color: rgb(32, 73, 92);
                    border-color: rgb(105, 177, 229);
                  }
                }

                .zoomlens {
                  color: white;

                  .el-input-number {

                    .el-input-number__decrease,
                    .el-input-number__increase {
                      background-color: #020124 !important;
                      color: white !important;
                      border: none !important;
                    }

                    .el-input__inner {
                      background-color: rgb(6, 11, 23) !important;
                      border: none !important;
                      color: white;
                    }
                  }

                }

                .content-item-1-1 {
                  color: white;

                  .content-item-div {
                    color: rgb(122, 122, 122);

                    .content-item-data {
                      color: white;
                    }

                    .videotape-1 {
                      border: 3px solid white;

                      .videotape-1-1 {
                        background-color: red;
                      }

                      &.videotape-active {
                        border-color: rgb(45, 88, 218);
                      }
                    }
                  }
                }

                .moreSet {
                  color: rgb(55, 99, 217);
                }

                .el-drawer {
                  background-color: rgb(48, 48, 48);

                  .el-drawer__header {
                    color: white;

                  }

                  .el-drawer__body {

                    scrollbar-color: rgba(69, 99, 250, 0.8) #000;



                    &::-webkit-scrollbar-track {
                      background-color: #000;

                    }

                    &::-webkit-scrollbar-thumb {
                      background-color: rgba(69, 99, 250, 0.8);

                    }
                  }
                }
              }


            }
          }
        }
      }

      // scrollbar-width: thin !important;
      // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      // -ms-overflow-style: none !important;
      // scrollbar-color: rgba(69, 99, 250, 0.8) #000;

      &::-webkit-scrollbar-track {
        background-color: #000;
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(69, 99, 250, 0.8);
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }

    }

    .dialogEquip {
      .equipForm {
        .fromButton {
          .saveBut {
            background-color: #0b58de;
            color: white;

            &.checked {
              background-color: transparent;
              color: #0b58de;
            }
          }

          .closeBut {
            background-color: transparent;
            color: #0b58de;

            &.checked {
              background-color: #0b58de;
              color: white;
            }
          }

          .stopBut {
            background-color: #de0b0b;
            color: #ffffff;

            &.checked {
              background-color: transparent;
              color: #0b58de;
            }
          }
        }

      }

      .el-form {
        .el-form-item {
          .el-form-item__label {
            color: #858484 !important;
          }
        }
      }
    }

    .el-dialog__wrapper {
      .el-dialog__body {
        .el-divider {
          background-color: #127ed7 !important;
        }
      }

      input::-webkit-input-placeholder {
        color: #cfcfcf !important;
      }

      input::-moz-input-placeholder {
        color: #cfcfcf !important;
      }

      input::-ms-input-placeholder {
        color: #cfcfcf !important;
      }
    }

    .el-pagination {
      button {
        background-color: transparent;
        border: 1px solid #fff;
        color: #fff;

        &:disabled {
          color: #c0c4cc;
        }
      }

      .el-pager li {
        color: white !important;
      }

      .btn-next:disabled,
      .btn-prev:disabled,
      .el-pager li:disabled {
        color: #c0c4cc !important;
      }

      .el-pager {
        li {
          background-color: transparent !important;
          border: 1px solid white !important;
        }

        li:not(.disabled).active {
          background-color: #124093 !important;
          border: 1px solid #124093 !important;
        }
      }
    }

    input::-webkit-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-moz-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-ms-input-placeholder {
      color: #5e5e5e !important;
    }

    .choose-site {
      .v-model {
        background: url("../../../img/mask.png") no-repeat;
      }

      #mapDiv {
        .title {
          background-color: white;
        }

        .foot-btn {
          background-color: white;

          .el-button {
            background-color: #0b58de;
            color: white;
          }

          .getsitebtn {
            border: none;
            background-color: #fff;
            color: #0b58de;

            &:hover {
              color: #0b58decb;
            }

          }

          .closebtn {
            background-color: #eee;
            color: #0b58de;
          }
        }

        .searchReturn {
          background-color: white;

          border: 1px solid #eee;
        }
      }

    }


  }

}
