<!-- 成果管理 -->
<template>
  <div id="achievement-admin">
    <!-- 表单 -->
    <div class="form el-input-white">
      <el-form label-width="0" inline size="small" :model="form">
        <el-form-item>
          <el-input
            v-model="form.search"
            :placeholder="formPlaceholder.search"
            :style="formInputStyle"
            @keydown.enter.native="search"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">{{
            buttonLabel.search
          }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="form.sn_id"
            :placeholder="formPlaceholder.uav"
            :style="formInputStyle"
            :popper-append-to-body="false"
            @change="search"
            clearable
          >
            <el-option
              v-for="(item, index) in equipmentList"
              :key="index"
              :value="item.sn_id"
              >{{ item.sn_id }}</el-option
            >
          </el-select>
        </el-form-item>

        <el-form-item>
          <twice-date-picker
            :startTime.sync="form.start_tms"
            :endTime.sync="form.end_tms"
            :isTimestamp="true"
            @change="search"
          ></twice-date-picker>
        </el-form-item>

        <el-form-item style="float: right">
          <el-button
            type="primary"
            @click="BatchDeleteChoose"
            v-if="!chooseDel"
            >{{ buttonLabel.bulkDel }}</el-button
          >
          <el-button type="primary" @click="submitDel" v-if="chooseDel">{{
            buttonLabel.submitDel
          }}</el-button>
          <el-button type="primary" @click="cancelDel" v-if="chooseDel">{{
            buttonLabel.cancelDel
          }}</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格 -->
    <custom-table
      :column="column"
      pmd="page"
      urlName="sortiesList"
      :params="form"
      :selectionCode="chooseDel"
      ref="table"
      @selection-change="selectionChange"
    >
      <template v-slot:mission_type="scope">
        {{ planTypeList[scope.row.mission_type] }}
      </template>

      <template v-slot:operation="scope">
        <el-button size="mini" type="text" @click="openDataAnalyse(scope.row)">
          {{ buttonLabel.data }}
        </el-button>
        <el-button size="mini" type="text" @click="openResults(scope.row)">
          {{ buttonLabel.results }}
        </el-button>
        <el-button size="mini" type="text" @click="routePlay(scope.row)">
          {{ buttonLabel.play }}
        </el-button>
        <el-button size="mini" type="text" @click="batchDownload(scope.row)">
          {{ buttonLabel.down }}
        </el-button>
        <el-button
          size="mini"
          v-if="scope.row.alternate_list"
          type="text"
          @click="openRecord(scope.row.alternate_list)"
        >
          {{ buttonLabel.alternateRecord }}
        </el-button>
        <el-button size="mini" type="text" @click="batchDelete(scope.row)">
          {{ buttonLabel.del }}
        </el-button>
        <!-- <el-button size="mini" type="text" @click="sortieDelete(scope.row)">
          {{ buttonLabel.sortieDel }}
        </el-button> -->
        <!-- <el-button size="mini" type="text" @click="autoDelete(0)">
          循环删除成果
        </el-button> -->
      </template>
    </custom-table>
    <component :is="componentId" ref="alternateRecord"></component>
  </div>
</template>

<script>
import requestHttp from "@/utils/api";
import customTable from "@/components/customTable/index.vue";
import { setLocalStorage } from "@/utils/storage.js";
import request from "@/utils/api";
import axios from "axios";

export default {
  name: "achievementAdmin",
  components: {
    customTable,
    twiceDatePicker: () => import("@/components/twiceDatePicker/index.vue"),
    dataAnalyse: () => import("./dataAnalyse.vue"),
  },
  data() {
    return {
      column: [
        { prop: "sort_id", label: "飞行编号" },
        { prop: "mission_name", label: "任务名称" },
        { prop: "sn_id", label: "设备ID" },
        { prop: "uav_id", label: "无人机" },
        { prop: "mission_type", label: "任务类型" },
        { prop: "user_name", label: "飞行用户" },
        { prop: "start_time", label: "起飞时间" },
        { prop: "end_time", label: "着陆时间" },
        { prop: "count", label: "成果总数" },
        { prop: "operation", label: "操作", width: 240 },
      ],
      form: {
        search: "",
        sn_id: "",
        type: 0,
        start_tms: "",
        end_tms: "",
      },

      formInputStyle: {
        width: "300px",
      },
      uavList: [],
      // achievementTypeList: {},
      planTypeList: {},
      chooseDel: false,
      selectionList: [],
      componentId: null,
    };
  },
  computed: {
    // 成果类型
    achievementType() {
      return this.$store.state.dict.achievementType;
    },
    // 设备列表
    equipmentList() {
      return this.$store.state.equipment.equipmentList;
    },
    tableField() {
      return this.$languagePackage.achievement.flightLog.table;
    },
    formPlaceholder() {
      return this.$languagePackage.achievement.form.placeholder;
    },
    buttonLabel() {
      return this.$languagePackage.achievement.flightLog.button;
    },
    tipMessage() {
      return this.$languagePackage.achievement.flightLog.tipMessage;
    },
  },
  created() {
    if (this.equipmentList.length === 0) {
      this.$store.dispatch("getEquipmentList", {});
    }

    this.getPlanTypeList();

    for (let i = 0; i < this.column.length; i++) {
      let item = this.column[i];
      let key = item.prop;
      this.$set(item, "label", this.tableField[key]);
    }
  },
  mounted() {
    this.getComponents();
  },
  methods: {
    getComponents() {
      import("./dialog/alternateRecord.vue").then((module) => {
        this.componentId = module.default;
      });
    },
    // 打开成果列表
    openResults: function (row) {
      this.$router.push({
        name: "totalResultList",
        query: {
          sort_id: row.sort_id,
          count: row.count,
        },
      });
      this.$store.commit("setOpenRouter", "totalResultList");
    },
    search: function () {
      this.$refs.table.refresh();
    },
    routePlay: function (row) {
      const route = this.$router.resolve({
        path: `/routeReplay`,
        query: {
          sn_id: row.sn_id,
          sort_id: row.sort_id,
        },
      });
      row.mission_type_label = this.planTypeList[row.mission_type];
      window.open(route.href, "_blank");
      setLocalStorage(row.sort_id, row);
    },
    // 打开数据分析
    openDataAnalyse: function (row) {
      const route = this.$router.resolve({
        path: `/dataAnalyse`,
        query: {
          sn_id: row.sn_id,
          sort_id: row.sort_id,
        },
      });
      row.mission_type_label = this.planTypeList[row.mission_type];
      window.open(route.href, "_blank");
      setLocalStorage("dataAnalyse" + row.sort_id, row);
    },
    getPlanTypeList: function () {
      requestHttp("missionType").then((res) => {
        res.data.forEach((item) => {
          this.$set(this.planTypeList, item.value, item.name_cn);
        });
      });
    },
    batchDownload: function (item) {
      let params = Object.assign({}, this.form);
      params["page"] = 0;
      params["size"] = item.count;
      params.pmd = "0";
      params.search = item.sort_id;
      request("resultList", params).then((res) => {
        let list = res.data.list ? res.data.list : [];
        // for (let i = 0; i < list.length; i++) {
        //   this.download(list[i]);
        // }
        this.orderDownload(list, 0);
      });
    },
    // 顺序下载
    orderDownload: function (list, index) {
      let item = list[index];
      axios
        .get(item.o_url, { responseType: "blob" })
        .then((response) => {
          const blob = new Blob([response.data]);
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = item.o_name;
          link.click();
          URL.revokeObjectURL(link.href);
          this.batchDelectLoading = false;

          if (list[index + 1]) {
            this.orderDownload(list, index + 1);
          }
        })
        .catch(console.error);
    },
    download: function (item) {
      axios
        .get(item.o_url, { responseType: "blob" })
        .then((response) => {
          const blob = new Blob([response.data]);
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = item.o_name;
          link.click();
          URL.revokeObjectURL(link.href);
        })
        .catch(console.error);
    },
    batchDelete: function (item) {
      this.$confirm(this.tipMessage.achieveDelMsg, this.tipMessage.deleteConf, {
        confirmButtonText: this.tipMessage.confirm,
        cancelButtonText: this.tipMessage.cancel,
        type: "warning",
      }).then(() => {
        let params = {
          sn_id: item.sn_id,
          sort_id: item.sort_id,
          pmd: "sn_id,sort_id",
          jointPmd: true,
        };
        request("resultDeleteSort", params).then((res) => {
          this.$message({
            type: "success",
            message: this.tipMessage.successDel,
          });
          item.count = 0;
        });
      });
    },
    //删除架次数据
    sortieDelete: function (item) {
      this.$confirm(this.tipMessage.sortieDelTip, this.tipMessage.deleteConf, {
        confirmButtonText: this.tipMessage.confirm,
        cancelButtonText: this.tipMessage.cancel,
        type: "warning",
      }).then(() => {
        let params = [
          {
            sort_id: item.sort_id,
            sn_id: item.sn_id,
          },
        ];
        this.delSortieData(params);
      });
    },
    delSortieData: function (ids_json) {
      let param = {
        ids_json: JSON.stringify(ids_json),
        pmd: "ids_json",
        jointPmd: true,
      };
      request("sortiesDelete", param).then((res) => {
        this.$message({
          type: "success",
          message: this.tipMessage.successDel,
        });
        this.$refs.table.refresh();
        this.chooseDel = false;
        this.selectionList = [];
      });
    },
    BatchDeleteChoose: function () {
      this.chooseDel = true;
    },
    cancelDel: function () {
      this.chooseDel = false;
      this.selectionList = [];
    },
    submitDel: function () {
      if (!this.selectionList.length) {
        this.$message.warning(this.tipMessage.noChooseMsg);
        return false;
      }
      this.$confirm(this.tipMessage.sortieDelTips, this.tipMessage.deleteConf, {
        confirmButtonText: this.tipMessage.confirm,
        cancelButtonText: this.tipMessage.cancel,
        type: "warning",
      }).then(() => {
        let params = [];
        for (let index = 0; index < this.selectionList.length; index++) {
          let param = {
            sort_id: this.selectionList[index].sort_id,
            sn_id: this.selectionList[index].sn_id,
          };
          params.push(param);
        }
        this.delSortieData(params);
      });
    },
    selectionChange(rows) {
      this.selectionList = rows;
    },
    // 自动循环删除媒体文件
    autoDelete: function (index = 0) {
      let list = this.$refs.table.tableData;

      let item = list[index];
      if (list[index].count > 0) {
        let params = {
          sn_id: item.sn_id,
          sort_id: item.sort_id,
          pmd: "sn_id,sort_id",
          jointPmd: true,
        };
        request("resultDeleteSort", params)
          .then((res) => {
            item.count = 0;
            if (index++ < 10) {
              this.autoDelete(index);
            } else {
              this.$refs.table.refresh(this.$refs.table.pageInfo.current);
            }
          })
          .catch(() => {
            if (index++ < 10) {
              this.autoDelete(index);
            } else {
              this.$refs.table.refresh(this.$refs.table.pageInfo.current);
            }
          });
      } else {
        index++;
        if (index < 10) {
          this.autoDelete(index);
        } else {
          this.$refs.table.refresh(this.$refs.table.pageInfo.current);
        }
      }
    },
    openRecord(e) {
      this.$refs.alternateRecord.open(e);
    },
  },
};
</script>

<style lang="less" scoped>
#achievement-admin {
  padding: 20px 20px 0 20px;
  height: calc(100% - 20px);
  // background-color: #000;
}
</style>