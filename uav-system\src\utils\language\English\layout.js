const layout = {
    menu: {
        home: "Data View",
        routeplan: "Flight",
        totalResultList: "Achievement",
        solution: "Solution",
        equipment: "Equipment",
        system: "System",
        dictData: "Function",
        userManage: "User",
        coordination: "Synergism",
        resultsShow: "Results Show",
        achievementAdmin: 'Sortie List',
        firmware: 'Firmware',
        systemSet: 'Language Setup',
        taskManage: 'Timed Task',
        deviceType: 'Device Type',
        teamManage: 'Department',
        teachingVideos: 'Videos'
    },

    header: {
        // title: "DRONE MANAGEMENT PLATFORM",
        title: "Drone Management Platform",
    },

    message: {
        esc: {
            title: "Hint",
            content: "This operation will exit the login. Do you want to continue?",
            confirm: "Confirm",
            cancel: "Cancel"
        },
        warning: "This feature is in development, stay tuned for more!"
    },
    teachVideo: {
        column: [{
                label: "Preview",
                prop: "preview_url"
            },
            {
                label: "Title",
                prop: "title"
            },
            {
                label: "Description",
                prop: "describe"
            },
            {
                label: "Language",
                prop: "notes"
            },
            {
                label: "Operation",
                prop: "operation"
            },
        ],
        playBtn: 'play'
    },

    user: {
        // userInfo: "UserInfo",
        accountSettings: 'UserInfo',
        // accountSettings: "UserSet",
        passSet: "Password",
        esc: "LogOut",
        systemSet: "SetUp",
    },
    menuOperateList: [{
        label: 'Open link in new tab',
    }]
}

export default layout;