## 二次封装表格

## 导入
## import customTable from "@/components/customTable/index.vue"

## 使用
<custom-table :column="column">
</custom-table>

## 属性
##  column           表头列表，看下方注解column注解 
##  isShowIndex      是否显示序号，默认显示
##  data             表格数据（注：如果有urlName属性，该项值无效）
##  beforeRequest    请求前（注：必须有urlName属性才会触发）
##  requestResponse  响应完成时触发（注：必须有urlName属性才会触发）
##  isShowPage       是否显示分页
##  params           请求参数
##  urlName          请求url名称
##  pageProps        分页参数字段
##  isSetHeight      是否设置表格为父元素最大可视化高度
##  pmd              请求参数pmd, 以字符串传过来，如：page,time
##  elementLoading   loading遮罩层参数
##  pageInfo         分页参数，类型Object,具体参数如下：

<!-- 
    list: [
        {label: "文本一", value: 1},
        {label: "文本二", value: 2},
        {label: "文本三", value: 3},
    ],
    map: {
        1: "文本值1",
        2: "文本值2"
    }
 -->

## column 表头列表(注：现阶段只有如下属性值) ###############################################
##   prop        显示内容的字段
##   label       标题
##   width       长度
##   transition  Object对象，如上方注释中list参数有：
##     list: 需转换的列表数据， 必填
##     label：显示的文本字段，为空则为“label”
##     value：值的字段，为空则为“value”
##   map         Object对象，格式如上注释map
##   fixed       列是否固定在左侧或者右侧，true 表示固定在左侧
##   align       对齐方式

## elementLoading loading遮罩层参数 ###############################################
##   text        遮罩层显示文本，默认“拼命加载中...”
##   spinner     自定义加载图标类名，默认“el-icon-loading”
##   background  遮罩层背景颜色，默认"rgba(0, 0, 0, 0.7)"

## pageInfo  分页参数 ###############################################
##   current   当前页，默认0
##   pageSize  每页大小，默认为10
##   total     总页数，默认0


## 如果要在column前插入其他的表格项，使用卡槽 front ，如下使用（注：该插槽必须插入ta-table-column组件）
<custom-table :column="column">
    <template v-slot:front>
        <el-table-column></el-table-column>
    </template>
</custom-table>

## 如果要在column后插入其他的表格项，使用卡槽 later ，如下使用（注：该插槽必须插入ta-table-column组件）
<custom-table :column="column">
    <template v-slot:later>
        <el-table-column></el-table-column>
    </template>
</custom-table>

## 如果需要自定义某一项内容为自定义，默认使用表头项中prop属性作为卡槽的名称，如prop为type
<custom-table :column="column">
    <template v-slot:type="scope">
        在这里写上需要自定义的内容；
        参数说明：
            scope.row：行数据
            scope.index：行的下标
    </template>
</custom-table>

## 如果自定义内容，则使用插槽customContent
<custom-table>
    <template v-slot:customContent="scope">
        插入要显示的内容；
        参数说明：
            scope.data：请求的列表数据
    </template>
</custom-table>

## 空数据时自定义展示内容使用卡槽empty
<custom-table>
    <template v-slot:empty>
    </template>
</custom-table>