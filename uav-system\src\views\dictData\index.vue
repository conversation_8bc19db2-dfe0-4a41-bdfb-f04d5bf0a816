﻿<template>
  <div id="dict-data">
    <!-- 表单操作 -->
    <div class="dict-data-form">
      <div></div>
      <div class="operation">
        <el-button
          @click="openAddEdit(null)"
          type="primary"
          class="add-dict-button"
        >
          <i class="el-icon-plus"></i> {{ languagePackage.addTitle }}
        </el-button>
      </div>
    </div>

    <!-- 内容展示区 -->
    <custom-table
      :column="column"
      urlName="funList"
      :isShowPage="false"
      ref="customTable"
    >
      <template v-slot:name="scope">{{
        $language == "english" ? scope.row.name_en : scope.row.name
      }}</template>
      <!-- 操作项 -->
      <template v-slot:operation="scope">
        <el-button type="text" @click="openAddEdit(scope.row)">
          <span class="el-icon-setting"></span>
          {{ languagePackage.tableOperate.edit }}
        </el-button>
        <!-- <el-button type="text" @click="removeDictData(scope.row)">
          <span class="el-icon-delete-solid"></span> {{ languagePackage.tableOperate.delete }}
        </el-button> -->
      </template>
    </custom-table>

    <!-- 添加/编辑 -->
    <add-edit-dialog
      ref="addEditDialog"
      @refresh="refresh"
      :languagePackage="languagePackage"
    ></add-edit-dialog>
  </div>
</template>

<script>
import customTable from "@/components/customTable/index.vue";
import addEditDialog from "./dialog/addEditDialog.vue";

export default {
  name: "dictData",
  components: {
    customTable,
    addEditDialog,
  },
  data() {
    return {
      column: [
        {
          label: "功能名",
          prop: "name",
        },
        {
          label: "功能ID",
          prop: "fun_id",
        },
        {
          label: "功能描述",
          prop: "description",
        },
        {
          label: "状态",
          prop: "state",
          map: {},
        },
        {
          label: "备注",
          prop: "notes",
        },
        {
          // label: "操作",
          prop: "operation",
          width: "180",
        },
      ],
      data: [],
    };
  },
  computed: {
    funState: function () {
      return this.$store.getters.getDictDataKeyValue("funState");
    },
    funMap: function () {
      let list = this.$store.state.dict.funState;
      return this.$store.getters.formatConversionListMap(list);
    },
    languagePackage() {
      return this.$languagePackage.functionManage;
    },

    addTtitle() {
      // return this.
    },
  },
  created() {
    this.column[3].map = this.funMap;
    let tableFile = this.languagePackage.table;
    for (let i = 0; i < this.column.length; i++) {
      let item = this.column[i];
      let key = item.prop;
      item.label = tableFile[key];
    }
  },
  methods: {
    refresh: function () {
      this.$refs.customTable.refresh();
    },
    openAddEdit: function (item) {
      this.$refs.addEditDialog.openDialog(item);
    },
    // 删除数据
    removeDictData: function () {
      let item = this.languagePackage.delete;
      this.$confirm(item.content, item.hint, {
        confirmButtonText: item.confirmText,
        cancelButtonText: item.cancelText,
        type: "warning",
      }).then(() => {
        this.$message({
          type: "success",
          message: item.success,
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
#dict-data {
  padding: 20px;
  // background-color: rgba(0, 0, 0, 1);
  padding-bottom: 0;
  height: calc(100% - 20px);
  .dict-data-form {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .el-button--text {
    font-size: 15px;
    // color: #fff;
    &:hover {
      // color: rgba(12, 89, 223, 1);
    }
  }
  .add-dict-button {
    // background-color: white;
    // color: #0b58de;
    border: none;
    border-radius: 6px;
    font-size: 16px;
  }
}
</style>