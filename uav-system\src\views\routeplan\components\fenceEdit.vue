<template>
  <div class="fenceEdit">
    <div class="title" ref="fenceTitle">
      <el-button
        :class="iconCode ? 'returnIcon' : ''"
        icon="el-icon-arrow-left"
        @click="goBack"
      ></el-button>
      {{ fenceTitle }}
    </div>
    <el-form
      :model="fenceForm"
      :rules="fenceRules"
      ref="fenceForm"
      class="content-item-1"
      :style="{ height: height }"
    >
      <el-form-item :label="routeLanguage.fence.name" prop="name">
        <el-input
          v-model="fenceForm.name"
          :placeholder="routeLanguage.fence.placeholder1"
          class="inputs"
        ></el-input>
      </el-form-item>
      <div class="title">{{ routeLanguage.routeLine.basicSet }}</div>
      <el-form-item :label="routeLanguage.fence.maxHeight">
        <el-slider
          v-model="fenceForm.height_limit"
          :show-tooltip="false"
          show-input
          :show-input-controls="false"
          :max="1000"
          :step="1"
          class="slider-class-1"
        ></el-slider>
      </el-form-item>
      <el-form-item :label="routeLanguage.fence.bindSn" prop="bind_sn_arr">
        <el-select
          v-model="fenceForm.bind_sn_arr"
          :placeholder="routeLanguage.fence.binSnTip"
          class="actionChoose"
          popper-class="selects"
          multiple
        >
          <el-option
            v-for="item in deviceList"
            :key="item.sn_id"
            :label="item.sn_id"
            :value="item.sn_id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="routeLanguage.fence.pointTitle">
        <el-collapse
          v-model="pointCodes"
          v-if="fenceForm.point_json.length > 0 ? true : false"
          class="pointsClass"
        >
          <el-collapse-item
            v-for="(item, index) in fenceForm.point_json"
            :key="index"
            :title="index + 1 + routeLanguage.waypoint.title1"
            :name="index + 1"
          >
            <div>{{ routeLanguage.waypoint.lng }}</div>
            <el-input-number
              v-model="item.lng"
              @blur="changeLatLng(index)"
              @focus="getFocus(index)"
              :controls="false"
              :precision="7"
            ></el-input-number>
            <div>{{ routeLanguage.waypoint.lat }}</div>
            <el-input-number
              v-model="item.lat"
              @blur="changeLatLng(index)"
              @focus="getFocus(index)"
              :controls="false"
              :precision="7"
            ></el-input-number>
          </el-collapse-item>
        </el-collapse>
      </el-form-item>
      <el-button
        class="saveBut"
        :class="saveCode ? 'active' : ''"
        @click="sumbitFence('fenceForm')"
        >{{ routeLanguage.routeLine.save }}</el-button
      >
    </el-form>
  </div>
</template>
<script>
import { computedMethod } from "../../../utils/computedMap";
import requestHttp from "../../../utils/api";
import { computedMapMethods } from "@/utils/cesium/computedMapMethods";
import { typeJudge } from "@/utils/deviceTypeJudge";
import { pointsConvert } from "@/utils/coordinateConvert";

export default {
  name: "fenceEdit",
  props: {
    changePoint: {
      type: Object,
      default() {
        return {};
      },
    },
    mapType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      iconCode: false,
      saveCode: false,
      pointCodes: [],
      fenceCode: false,
      focusPoint: {
        lat: 0,
        lng: 0,
      },
      fenceItem: "",
      fenceTitle: "",
      fenceForm: {
        name: "",
        point_json: [],
        height_limit: 500,
        bind_sn_arr: "",
      },
      fenceRules: {
        name: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
      },
      focusPoint: {},
      height: "",
      deviceList: [],
    };
  },
  created() {
    this.fenceTitle = this.routeLanguage.fence.addFence;
    this.fenceRules.name[0].message = this.routeLanguage.fence.placeholder1;
    this.getDeviceList();
  },
  async mounted() {
    this.fenceItem = this.$store.state.route.operateFItem;
    this.height =
      "calc(100% - " + (this.$refs.fenceTitle.offsetHeight + 10) + "px)";
    if (this.fenceItem) {
      await this.originForm();
      this.fenceCode = false;
    }
  },
  computed: {
    routeLanguage() {
      return this.$languagePackage.routes;
    },
  },
  watch: {
    changePoint(val) {
      switch (val.type) {
        case "add":
          let path = {
            lat: val.lat,
            lng: val.lng,
          };
          this.fenceForm.point_json.push(path);
          break;
        case "edit":
          this.fenceForm.point_json[val.index].lng = val.lng;
          this.fenceForm.point_json[val.index].lat = val.lat;
          break;
        case "editAdd":
          let path1 = {
            lat: val.lat,
            lng: val.lng,
          };
          this.fenceForm.point_json.splice(val.index, 0, path1);
          break;
        case "remove":
          this.fenceForm.point_json.splice(val.index, 1);
          this.pointCodes = this.pointCodes.filter((x) => x !== val.index + 1);
          break;
        case "del":
          this.fenceForm.point_json = [];
          this.pointCodes = [];
          break;
        case "import":
          let path2 = {
            lat: val.lat,
            lng: val.lng,
          };
          this.fenceForm.point_json.push(path2);
          break;
        default:
          break;
      }
    },
    "fenceForm.point_json": {
      handler() {
        //存储数据
        this.$emit("deepCopy", "");
        this.fenceCode = true;
      },
      deep: true,
    },
  },
  methods: {
    //获取设备列表
    getDeviceList() {
      this.deviceList = [];
      let data = {
        page: 0,
        size: 200,
        type: 0,
      };
      data.pmd = data.page.toString() + data.type.toString();
      requestHttp("deviceList", data).then((res) => {
        let deviceList_total = res.data.list ? res.data.list : [];
        for (let index = 0; index < deviceList_total.length; index++) {
          let item = typeJudge(deviceList_total[index]);
          if (item.isStable) {
            let point = pointsConvert({
              point: item,
              type: 10,
              devicePoint: true,
            });
            item.homePoint = point;
            this.deviceList.push(item);
          }
        }
      });
    },
    //初始化表单
    originForm() {
      this.fenceTitle = this.routeLanguage.fence.editTitle;
      this.fenceForm = {
        name: this.fenceItem.title,
        point_json: [],
        height_limit: this.fenceItem.height_limit / 100,
        bind_sn_arr: "",
      };
      for (let index = 0; index < this.fenceItem.point_list.length; index++) {
        let path = {
          lat: this.fenceItem.point_list[index].lat_int / 1e7,
          lng: this.fenceItem.point_list[index].lon_int / 1e7,
        };
        this.fenceForm.point_json.push(path);
      }
      if (this.fenceItem.bind_sn_arr) {
        this.fenceForm.bind_sn_arr = JSON.parse(this.fenceItem.bind_sn_arr);
      }
    },
    //获取焦点
    getFocus(index) {
      let point = this.fenceForm.point_json[index];
      this.focusPoint = Object.assign({}, point);
    },
    //失去焦点触发
    changeLatLng(index) {
      if (
        this.fenceForm.point_json[index].lng == this.focusPoint.lng &&
        this.fenceForm.point_json[index].lat == this.focusPoint.lat
      ) {
        return false;
      }
      if (this.fenceForm.point_json.length > 3) {
        let a = this.mapType ? this.cesiumIsCross(index) : this.isCross(index);
        if (a) {
          this.$message.warning({
            message: this.routeLanguage.messageInfo2,
            duration: 1000,
            customClass: "message-info-tip",
          });
          this.fenceForm.point_json[index].lng = this.focusPoint.lng;
          this.fenceForm.point_json[index].lat = this.focusPoint.lat;
          return false;
        }
      }
      let changePoint = {
        index: index,
        lat: this.fenceForm.point_json[index].lat,
        lng: this.fenceForm.point_json[index].lng,
      };
      this.$emit("changeMarker", changePoint);
    },
    //判断填入之后是否交叉
    isCross(index) {
      let a, b;
      if (index == 0) {
        a = this.isLineCross1(
          this.fenceForm.point_json[0],
          this.fenceForm.point_json[index + 1],
          0
        );
        b = this.isLineCross1(
          this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
          this.fenceForm.point_json[0],
          0,
          1
        );
      } else if (index == this.fenceForm.point_json.length - 1) {
        a = this.isLineCross1(
          this.fenceForm.point_json[0],
          this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
          0,
          1
        );
        b = this.isLineCross1(
          this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
          this.fenceForm.point_json[index - 1],
          this.fenceForm.point_json.length - 2
        );
      } else {
        a = this.isLineCross1(
          this.fenceForm.point_json[index],
          this.fenceForm.point_json[index - 1],
          index - 1
        );
        b = this.isLineCross1(
          this.fenceForm.point_json[index + 1],
          this.fenceForm.point_json[index],
          index
        );
      }
      return a || b;
    },
    //围栏：拖拽判断是否出现交叉
    isLineCross1(marker1, marker2, index, num) {
      let arr1 = this.fenceForm.point_json.slice(0, index);
      let arr2 = this.fenceForm.point_json.slice(
        num ? index + 1 : index + 2,
        num
          ? this.fenceForm.point_json.length - 1
          : this.fenceForm.point_json.length
      );
      let arr = arr2.concat(arr1);
      return computedMethod(5, {
        point1: new AMap.LngLat(marker1.lng, marker1.lat),
        point2: new AMap.LngLat(marker2.lng, marker2.lat),
        fence: arr,
      });
    },
    cesiumIsCross(index) {
      let arr = [];
      let point = this.fenceForm.point_json[index];
      let point1 =
        this.fenceForm.point_json[this.fenceForm.point_json.length - 1];
      if (index > 0) {
        arr = this.fenceForm.point_json.slice(0, index - 1);
        point1 = this.fenceForm.point_json[index - 1];
      }
      let arr1 = this.fenceForm.point_json.slice(
        index + 1,
        this.fenceForm.point_json.length
      );
      let arrconcat = [...arr1, ...arr];
      let isCross = false;
      if (arrconcat.length > 1) {
        isCross = computedMapMethods("lineCross", {
          point,
          point1,
          fence: arrconcat,
        });
        if (isCross) {
          return true;
        }
      }
      //操作点与后一个点形成的线判断
      let arr2 = [];
      let point2 = "";
      if (index == this.fenceForm.point_json.length - 1) {
        arr2 = this.fenceForm.point_json.slice(1, index);
        point2 = this.fenceForm.point_json[0];
      } else {
        let arr3 = this.fenceForm.point_json.slice(0, index);
        point2 = this.fenceForm.point_json[index + 1];
        let arr4 = [];
        if (index + 1 < this.fenceForm.point_json.length - 1) {
          arr4 = this.fenceForm.point_json.slice(
            index + 2,
            this.fenceForm.point_json.length
          );
        }
        arr2 = [...arr4, ...arr3];
      }
      let isCross1 = false;
      if (arrconcat.length > 1) {
        isCross1 = computedMapMethods("lineCross", {
          point,
          point1: point2,
          fence: arr2,
        });
        if (isCross1) {
          return true;
        }
      }
      return false;
    },
    //返回
    goBack() {
      this.iconCode = true;
      if (this.fenceCode) {
        this.$confirm(this.routeLanguage.placeholder2, this.routeLanguage.tip, {
          confirmButtonText: this.routeLanguage.saveBtn,
          cancelButtonText: this.routeLanguage.cancelBtn,
          type: "warning",
          customClass: "messageTip",
        })
          .then(() => {
            this.originalData();
          })
          .catch(() => {})
          .finally(() => {
            this.iconCode = false;
          });
      } else {
        this.originalData();
      }
    },
    //返回数据初始化
    originalData() {
      this.pointCodes = [];
      this.$emit("goBack", "");
      this.fenceForm = {
        name: "",
        point_json: [],
        height_limit: 500,
        bind_sn_arr: "",
      };
      this.fenceTitle = this.routeLanguage.fence.addFence;
    },
    //提交数据
    async sumbitFence(e) {
      if (!this.saveCode) {
        this.saveCode = true;
        let center = {
          lat: 0,
          lng: 0,
        };
        await this.$refs[e].validate((valid) => {
          if (valid && this.saveCode) {
            if (this.fenceForm.point_json.length > 2) {
              if (this.judgeDevice()) {
                return false;
              }
              var point_jsons = [];
              for (
                let index = 0;
                index < this.fenceForm.point_json.length;
                index++
              ) {
                let point_json = {
                  seq: index + 1,
                  lat_int: parseInt(this.fenceForm.point_json[index].lat * 1e7),
                  lon_int: parseInt(this.fenceForm.point_json[index].lng * 1e7),
                  type: 20,
                };
                point_jsons.push(point_json);
                center.lat += parseInt(
                  this.fenceForm.point_json[index].lat * 1e7
                );
                center.lng += parseInt(
                  this.fenceForm.point_json[index].lng * 1e7
                );
              }
              center.lat = parseInt(
                center.lat / this.fenceForm.point_json.length
              );
              center.lng = parseInt(
                center.lng / this.fenceForm.point_json.length
              );
              this.fenceForm.point_jsons = point_jsons;
              let data = {
                title: this.fenceForm.name,
                type: 10,
                height_limit: parseInt(this.fenceForm.height_limit * 100),
                lat_int: center.lat,
                lon_int: center.lng,
                solid_color: "#801357B1",
                stroke_color: "#FF1352FF",
                stroke_width: 5,
                bind_sn_arr: "",
              };
              if (
                this.fenceForm.bind_sn_arr &&
                this.fenceForm.bind_sn_arr.length
              ) {
                data.bind_sn_arr = JSON.stringify(this.fenceForm.bind_sn_arr);
              }
              if (this.fenceItem) {
                data.f_id = this.fenceItem.f_id;
                data.state = this.fenceItem.state;
                for (let index = 0; index < point_jsons.length; index++) {
                  for (let i = 0; i < this.fenceItem.point_list.length; i++) {
                    if (
                      point_jsons[index].seq == this.fenceItem.point_list[i].seq
                    ) {
                      point_jsons[index].id = this.fenceItem.point_list[i].id;
                    }
                  }
                  point_jsons[index].state = 10;
                }
                if (point_jsons.length < this.fenceItem.point_list.length) {
                  let point_jsons1 = [];
                  for (
                    let index = 0;
                    index < this.fenceItem.point_list.length;
                    index++
                  ) {
                    if (
                      this.fenceItem.point_list[index].seq > point_jsons.length
                    ) {
                      let a = {
                        seq: this.fenceItem.point_list[index].seq,
                        id: this.fenceItem.point_list[index].id,
                        state: 30,
                        lon_int: this.fenceItem.point_list[index].lon_int,
                        lat_int: this.fenceItem.point_list[index].lat_int,
                        // type: 20
                        type: this.$coordinateType === "wgs84" ? 10 : 20,
                      };
                      point_jsons1.push(a);
                    }
                  }
                  point_jsons = point_jsons.concat(point_jsons1);
                }
                data.point_json = JSON.stringify(point_jsons);
                data.pmd =
                  data.title +
                  data.point_json +
                  data.type.toString() +
                  data.lat_int.toString() +
                  data.lon_int.toString() +
                  data.f_id.toString() +
                  data.state.toString();
              } else {
                data.point_json = JSON.stringify(point_jsons);
                data.pmd =
                  data.title +
                  data.point_json +
                  data.type.toString() +
                  data.lat_int.toString() +
                  data.lon_int.toString();
              }
              requestHttp(this.fenceItem ? "fenceEdit" : "fenceAdd", data).then(
                (res) => {
                  this.$message.success({
                    message: this.fenceItem
                      ? this.routeLanguage.successMessage
                      : this.routeLanguage.successMessage1,
                    customClass: "message-info-tip",
                  });
                  this.originalData();
                }
              );
            } else {
              this.saveCode = false;
              this.$message.info({
                message: this.routeLanguage.messageInfo4,
                customClass: "message-info-tip",
              });
            }
          } else {
            this.saveCode = false;
          }
        });
      }
      this.timeOut = setTimeout(() => {
        this.saveCode = false;
      }, 200);
    },
    judgeDevice() {
      if (this.fenceForm.bind_sn_arr && this.fenceForm.bind_sn_arr.length) {
        let noInRang = [];
        for (
          let index = 0;
          index < this.fenceForm.bind_sn_arr.length;
          index++
        ) {
          const sn = this.fenceForm.bind_sn_arr[index];
          let i = this.deviceList.findIndex((item) => {
            return item.sn_id == sn;
          });
          if (i !== -1) {
            let isArea = computedMapMethods("pointInPolygon", {
              point: this.deviceList[i].homePoint,
              polygon: this.fenceForm.point_json,
            });
            if (!isArea) {
              noInRang.push(sn);
            }
          }
        }
        if (noInRang.length) {
          let str = this.routeLanguage.fence.deviceErrorTip.replace(
            "name",
            JSON.stringify(noInRang)
          );
          this.$message.error(str);
          return true;
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .fenceEdit {
    .title {
      font-size: @zoomIndex * 18px !important;
      .el-button {
        font-size: @zoomIndex * 22px !important;
      }
    }
    .content-item-1 {
      margin-top: @zoomIndex * 10px !important;
      .el-form-item {
        .el-button {
          font-size: @zoomIndex * 16px !important;
          letter-spacing: @zoomIndex * 2px !important;
        }
        .el-collapse {
          .el-collapse-item {
            .routeActionDiv {
              .el-button {
                font-size: @zoomIndex * 24px !important;
                padding-top: @zoomIndex * 6px !important;
              }
            }
          }
        }
      }
      .title {
        letter-spacing: @zoomIndex * 2px !important;
      }
      .saveBut {
        font-size: @zoomIndex * 18px !important;
        padding: @zoomIndex * 12px @zoomIndex * 20px !important;
        border-radius: @zoomIndex * 4px !important;
      }
    }
  }
}
.fenceEdit {
  .title {
    // height: 4%;
    font-size: 18px;
    .el-button {
      font-size: 22px;

      padding: 0;
    }
  }
  .content-item-1 {
    width: 100%;
    // height: 94%;
    overflow-x: hidden;
    overflow-y: auto;
    margin-top: 10px;
    .el-form-item {
      width: 98%;
      .el-input {
        margin-top: 1%;
        width: 100%;
      }
      .el-button {
        width: 100%;
        padding: 10px;
        font-size: 16px;
        letter-spacing: 2px;
        font-weight: 550;
      }

      .slider-class-1 {
        width: 100%;
        display: inline-block;
        margin-left: 2%;
      }
      .el-select {
        width: 100%;
      }
      .el-collapse {
        width: 100%;
        .el-collapse-item {
          width: 100%;
          margin-top: 2%;
          div {
            margin-left: 1%;
          }
          .el-input-number {
            margin-left: 1%;
            width: 98%;
          }
          .addActionBtn {
            margin-top: 2%;
            margin-left: 1%;
            width: 98%;
          }
          .routeActionDiv {
            width: 100%;
            margin-left: 0 !important;
            margin-top: 1%;
            .el-select {
              width: 85%;
            }
            .el-button {
              margin-left: 2%;
              width: auto;
              font-size: 24px;
              padding: 0;
              padding-top: 6px;
            }
            .actionValueDiv {
              margin: 1%;
              margin-left: 2%;
              margin-right: 0;
              width: 98%;
              .el-input-number {
                width: 18%;
              }
            }
          }
        }
      }
    }
    .title {
      text-align: center;
      font-weight: 520;
      letter-spacing: 2px;
    }
    .saveBut {
      margin: 0.5%;
      width: 98%;
      font-weight: 600;
      font-size: 18px;
      padding: 12px 20px;
      border-radius: 4px;
    }
  }
}
</style>
<style lang="less">
.fenceEdit {
  .content-item-1 {
    .el-form-item {
      .el-form-item__label {
        padding: 0 !important;
        line-height: 20px !important;
        font-size: 14px !important;
      }
      .el-form-item__content {
        line-height: 10px !important;
        .inputs {
          .el-input__inner {
            border-radius: 4px !important;
            height: 40px !important;
            line-height: 40px !important;
            padding: 0 15px !important;
          }
        }
        .actionChoose {
          .el-input__inner {
            border-radius: 4px !important;
            min-height: 40px !important;
            line-height: 40px !important;
            padding: 0 15px !important;
            // height: auto !important;
          }
          .el-tag {
            background-color: #1646a8 !important;
            color: #fff !important;
            border: none !important;
          }
        }
      }
      .el-input {
        font-size: 14px !important;
      }
      .actionChoose {
        .el-input__suffix {
          right: 5px !important;
          .el-select__caret {
            font-size: 14px !important;
            width: 25px !important;
            line-height: 40px !important;
          }
        }
      }
      .slider-class-1 {
        .el-slider__runway {
          height: 2px !important;
          margin: 0 !important;
          width: 79%;
          margin-top: 15px !important;
          .el-slider__bar {
            height: 2px !important;
            background-color: #0555ff !important;
          }
        }
        .el-slider__button-wrapper {
          width: 10px !important;
          height: 10px !important;
          top: -10px;
          .el-slider__button {
            width: 8px !important;
            height: 8px !important;
            border: 1px solid #0555ff !important;
          }
        }
        .el-slider__input,
        .el-input-number--small {
          width: 17% !important;
          line-height: 0 !important;
          margin-right: 5px !important;
          .el-input__inner {
            padding: 0 5px !important;
            height: 32px !important;
            line-height: 32px !important;
          }
        }
      }
      .silderInput {
        .sliderValue {
          .el-slider__runway {
            width: 100% !important;
          }
        }
      }
      .el-collapse {
        .el-collapse-item {
          .el-input {
            .el-input__inner {
              text-align: left !important;
              height: 40px !important;
            }
          }
          .el-collapse-item__header {
            padding-left: 5% !important;
            border-radius: 6px;
            width: 95% !important;
            letter-spacing: 1px !important;
            height: 48px !important;
            line-height: 48px !important;
            font-size: 13px !important;
            .el-collapse-item__arrow {
              // display: none !important;
              transform: rotate(90deg) !important;
            }
            .el-collapse-item__arrow.is-active {
              transform: rotate(0deg) !important;
            }
          }
          .el-collapse-item__wrap {
            width: 100% !important;

            .el-collapse-item__content {
              width: 100% !important;
              padding-bottom: 25px !important;
              font-size: 13px !important;
            }
          }
          .routeActionDiv {
            .actionValueDiv {
              width: 98%;
              .el-input-number {
                .el-input__inner {
                  padding: 0 5px !important;
                  text-align: center !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .el-loading-mask {
    .el-loading-spinner {
      top: 50% !important;
      font-size: 16px !important;
      .el-loading-text {
        font-size: 16px !important;
      }
      i {
        font-size: 30px !important;
      }
    }
  }
}
.selects {
  .el-select-dropdown__item {
    font-size: 14px !important;
    height: 34px !important;
    line-height: 34px !important;
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .fenceEdit {
    .content-item-1 {
      .el-form-item {
        .el-form-item__label {
          line-height: @zoomIndex * 20px !important;
          font-size: @zoomIndex * 14px !important;
        }
        .el-form-item__content {
          line-height: @zoomIndex * 10px !important;
          .inputs {
            .el-input__inner {
              border-radius: @zoomIndex * 4px !important;
              height: @zoomIndex * 40px !important;
              line-height: @zoomIndex * 40px !important;
              padding: 0 @zoomIndex * 15px !important;
            }
          }
        }
        .actionChoose {
          .el-input__suffix {
            right: @zoomIndex * 5px !important;
            .el-select__caret {
              font-size: @zoomIndex * 14px !important;
              width: @zoomIndex * 25px !important;
              line-height: @zoomIndex * 40px !important;
            }
          }
          .el-input__inner {
            border-radius: @zoomIndex * 4px !important;
            min-height: @zoomIndex * 40px !important;
            line-height: @zoomIndex * 40px !important;
            padding: 0 @zoomIndex * 15px !important;
          }
        }
        .el-input {
          font-size: @zoomIndex * 14px !important;
        }
        .slider-class-1 {
          .el-slider__runway {
            height: @zoomIndex * 2px !important;
            margin-top: @zoomIndex * 15px !important;
            .el-slider__bar {
              height: @zoomIndex * 2px !important;
            }
          }
          .el-slider__button-wrapper {
            width: @zoomIndex * 10px !important;
            height: @zoomIndex * 10px !important;
            // top: @zoomIndex * -10px !important;
            .el-slider__button {
              width: @zoomIndex * 8px !important;
              height: @zoomIndex * 8px !important;
              border: @zoomIndex * 1px solid #0555ff !important;
            }
          }
          .el-slider__input,
          .el-input-number--small {
            font-size: @zoomIndex * 14px !important;
            margin-right: @zoomIndex * 5px !important;
            .el-input__inner {
              padding: 0 @zoomIndex * 5px !important;
              height: @zoomIndex * 32px !important;
              line-height: @zoomIndex * 32px !important;
            }
          }
        }
        .el-collapse {
          .el-collapse-item {
            .el-input {
              .el-input__inner {
                height: @zoomIndex * 40px !important;
              }
            }
            .el-collapse-item__header {
              border-radius: @zoomIndex * 6px !important;
              letter-spacing: @zoomIndex * 1px !important;
              height: @zoomIndex * 48px !important;
              line-height: @zoomIndex * 48px !important;
              font-size: @zoomIndex * 13px !important;
            }
            .el-collapse-item__wrap {
              .el-collapse-item__content {
                padding-bottom: @zoomIndex * 25px !important;
                font-size: @zoomIndex * 13px !important;
              }
            }
            .routeActionDiv {
              .actionValueDiv {
                .el-input-number {
                  .el-input__inner {
                    padding: 0 @zoomIndex * 5px !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    .el-loading-mask {
      .el-loading-spinner {
        font-size: @zoomIndex * 16px !important;
        .el-loading-text {
          font-size: @zoomIndex * 16px !important;
        }
        i {
          font-size: @zoomIndex * 30px !important;
        }
      }
    }
  }
  .selects {
    .el-select-dropdown__item {
      font-size: @zoomIndex * 14px !important;
      height: @zoomIndex * 34px !important;
      line-height: @zoomIndex * 34px !important;
    }
  }
}
</style>
