const routes = {
    language: 'zh-CN',
    tool: {
        placeholder: "请输入搜索地址",
        limitFly: '限飞区',
        noflyzone: '禁飞区',
        limitHeight: '限高区',
        airportBuffer: '机场缓冲区',
        temporaryNofly: '临时禁飞区',
        weatherLayer: '天气图层',
        RealTimeWeather: "实时天气"
    },
    toolMenu: ['位置', '距离', "面积", "圆形", "方位", "清除"],
    fence: {
        title: '飞行作业列表',
        addFence: "新建作业围栏",
        edit: "编\xa0\xa0辑",
        del: "删\xa0\xa0除",
        editTitle: "编辑作业围栏",
        name: "作业围栏名称",
        basicSet: "基础设置",
        maxHeight: "围栏最大高度（m）",
        pointTitle: '围栏边界点',
        placeholder: '请输入关键字搜索作业围栏',
        placeholder1: "请输入围栏名称",
        common: '共',
        strip: '条',
        do: '作',
        noSeachMessage: '未搜索到与之匹配的作业围栏！',
        entryReturn: '输入为空，返回原始列表',
        delMessage: '此操作无法恢复，您确定删除该作业围栏信息？',
        delTip: '提示',
        delSuccess: "该围栏已删除成功！",
        cancelDel: "已取消删除！",
        save: "保存",
        bindSn: '绑定设备SN',
        binSnTip: '请选择绑定设备SN',
        deviceErrorTip: '设备 name 不在围栏范围内',
        equipTitle: '设备：'
    },
    routeLine: {
        addtask: '添加任务',
        performTask: "执行任务",
        issued: "下发任务",
        set: '设置',
        noData: "暂无数据",
        rename: "重\xa0命\xa0名",
        del: "删\xa0\xa0\xa0\xa0除",
        export: "导\xa0\xa0\xa0出",
        ET: "航线预计时间",
        planeET: '预计飞行时间',
        distance: "距离",
        waypointCount: "航点数",
        area: '面积',
        photoNumber: "预计拍照数量",
        addTitle: "新增任务",
        editTitle: "编辑任务",
        taskName: '任务名称',
        fenceName: "所属作业围栏",
        taskType: '任务类型',
        basicSet: "基础设置",
        heightType: '高度类型',
        autoSpeed: "自动巡航速度（m/s）",
        maxSpeed: "最大飞行速度（m/s）",
        defaultHeight: "所有航点默认高度（m）",
        returnHeight: "返航高度（m）",
        taskAction: "任务完成后动作",
        waypoint: "航点",
        save: '保\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0存',
        cancel: '取\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0消',
        routeLoading: "航点加载中",
        placeholder: '请输入任务名称',
        placeholder1: '请选择任务完成后动作',
        placeholder2: '请输入关键字搜索任务',
        placeholder5: '选择高度类型',
        placeholder4: '选择航线执行类型',
        taskRename: "任务重命名",
        cancelRename: '已取消重命名！',
        renameSuccess: "重命名成功！",
        uploading: "正在上传航线，请完成后再执行操作！",
        delMessage: "此操作无法恢复，您确定删除该任务信息？",
        delTip: '提示',
        delSuccess: "删除成功！",
        cancelDel: '已取消删除！',
        setNewTask: '新建任务',
        cameraType: "相机类型",
        placeholder3: "请选择相机类型",
        customCamera: '自定义相机',
        cameraType1: "6k相机",
        cameraType2: '双光相机',
        sensor: '传感器',
        image: '图像',
        focalLength: '焦距',
        course: '航向重叠',
        lateral: '旁向重叠',
        wheelDist: '转弯距离',
        angle: '角度',
        toPlaneType: '航线执行类型',
        timeNode: "执行时间节点",
        intervalHeight: "间隔计算高度(m)",
        isPlaneHight: "是否计算高程数据",
        photoType: '拍照类型',
        placeholder6: '选择拍照类型'
    },
    waypoint: {
        title: '号航点',
        title1: '号边界点',
        lat: '纬度',
        lng: '经度',
        height: "高度（m）",
        waypointAction: "航点动作",
        actionOptions: [{
                label: "无人机偏航角",
                value: "uav_yaw",
            },
            {
                label: "悬停",
                value: "hover",
            },
            {
                label: "拍照",
                value: "takephoto",
            },
            {
                label: "云台角度控制",
                value: "gimbal_ctrl",
            },
            {
                label: "设置无人机巡航速度",
                value: "speed",
            },
            // {
            //     label: "全景拍照",
            //     value: "panorama_takephoto",
            // },
            // {
            //     label: "相机触发距离",
            //     value: "cam_trig_dist",
            // },
        ],
        uav_yaw: '无人机偏航角（范围[0,360]，单位：°）：',
        gimbal_yaw: '云台偏航角（范围[-180,180]，单位：°）：',
        gimbal_pitch: '云台俯仰角（范围[-180,180]，单位：°）：',
        cam_trig_dist: '拍照距离（单位：m）：',
        speed: '无人机巡航速度（单位：m/s）：',
        hoverTime: '悬停时间（范围[0,255]，单位S）：',
        placeholder: '请选择航点动作'


    },
    operationBtn: {
        drawPoint: "标点",
        delete: "删除",
        remove: "清除",
        recall: "撤回",
        import: "导入",
        export: "导出"
    },
    dialogType: {
        title: "选择任务类型",
        undeveloped: "该任务类型暂未开发，敬请期待。"
    },
    dialogEquip: {
        title: "选择执行的任务的设备",
        noInEquip: '暂无在线设备',
        previous: '上一页',
        next: '下一页',
        save: "确\xa0\xa0定",
        cancel: '取\xa0\xa0消',
        cancelMessage: "已取消执行任务！",
        cancelMessage1: "已取消下发任务！",
        noSelectMessage: '未选择执行任务的设备',
        noSelectMessage1: '未选择下发任务的设备',
        sendSuccessMsg: '航线定时任务已下发成功！',
        flightTime: '预计飞行时间：',
    },
    weather: {
        windDirection: '风向：',
        level: '级',
        humidity: '相对湿度'

    },
    cameraParams: {
        width: '宽度',
        height: '高度'

    },
    options: [{
            label: "悬停",
            value: 10,
        },
        {
            label: "返航",
            value: 20,
        },
        {
            label: "自动降落",
            value: 30,
        },
        {
            label: "回到第一个航点",
            value: 40,
        },
    ],
    options1: [{
            label: "相对高度",
            value: 0,
        },
        {
            label: "海拔高度",
            value: 1,
        },
    ],
    options2: [{
        label: '单次执行',
        value: 0
    }, {
        label: '定时执行',
        value: 1
    }],
    photoTypeList: [
        { label: '红外', value: 1 },
        { label: '可见光', value: 2 },
        { label: '红外和可见光', value: 3 },
    ],
    placeholder: '无法获取当前具体位置！',
    placeholder1: '该航线信息未保存，确定返回？',
    placeholder2: '该围栏信息未保存，确定返回？',
    placeholder3: '正在加载航点信息，暂无法操作！',
    placeholder4: '航点不能超出围栏范围！',
    placeholder5: '航线超出围栏范围！',
    placeholder6: '相邻航点有效距离不能超过5千米！',
    placeholder7: '边界线超出围栏范围！',
    placeholder8: '您确定清除该航线的所有信息？',
    placeholder9: '您确定清除该围栏的所有信息？',
    placeholder10: '已撤回至最初状态',
    placeholder11: '此操作将会清空已绘制的航线, 是否继续?',
    placeholder12: '此操作将会清空已绘制的围栏, 是否继续?',
    placeholder13: '是否将航线数据导出为kml文件？',
    placeholder14: '是否将围栏数据导出为kml文件？',
    messageInfo: '清除成功!',
    messageInfo1: '已取消清除',
    messageInfo2: '温馨提示：围栏禁止交叉',
    messageInfo3: '温馨提示：禁止交叉',
    messageInfo4: '围栏不完整！',
    messageInfo5: '任务航线不完整！',
    successMessage: '编辑围栏成功！',
    successMessage1: '新增围栏成功！',
    successMessage2: '编辑任务成功！',
    successMessage3: '新建任务成功！',
    errorMessage: '不能为0',
    errorMessage1: '选择的文件格式不正确，请选择.kml/.json/.plan文件',
    errorMessage2: '导入的kml格式不正确！',
    errorMessage3: '选择的kml文件任务的类型与选择的任务类型不匹配！',
    errorMessage4: "选择的kml文件不是完整的围栏！",
    errorMessage5: '号航点与',
    errorMessage6: '号航点连线超出围栏范围',
    errorMessage7: '航线不在围栏范围内！',
    errorMessage8: '相邻航点有效距离不能超过5千米！',
    errorMessage9: '相邻航线间距太大',
    errorMessage10: "定时任务的时间间隔不能小于30分钟",
    errorMessage11: "高程数据计算中，请等计算完成后再保存",
    elevation: '高程：',
    cancelexport: '已取消导出！',
    tip: '提示',
    saveBtn: '确定',
    cancelBtn: '取消',
    inLine: '在线',
    outLine: '离线',
    toolBar: {
        azimuth: '方位角：',
        circular: '圆心',
        area: '面积：',
        meter: '平方米',
    },
    getHeightLoading: '正在获取高程。。。',
    heightLabel: '区域峰值海拔：',
    layerType: {
        standard: "标准图",
        satellite: "卫星图"
    },
    mapModeTip: "当前地图为3D模式，点击绘制点可能有偏差，建议切换到2D模式。",
    photoActionError: "航点[name]已选拍照/全景拍照，不能再选全景拍照",
    photoActionError1: "航点[name]已选全景拍照，不能再选拍照。"
}
export default routes