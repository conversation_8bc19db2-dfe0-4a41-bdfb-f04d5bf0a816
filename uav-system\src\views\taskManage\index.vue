<template>
  <div class="taskManage">
    <div class="searchDiv">
      <el-input
        v-model="searchValue"
        :placeholder="language.searchTip"
        clearable
        @keyup.enter.native="search"
      ></el-input>
      <el-button class="searchBut" @click="search">{{
        language.searchBtn
      }}</el-button>
      <div class="stateChoose">
        <div class="stateItem">
          <span>{{ language.taskStateLabel }}:</span>
          <el-tag
            v-for="item in taskState"
            :key="item.value"
            :class="item.value == param.task_state ? 'active' : ''"
            @click="refreshList(item.value, 'task_state')"
            >{{ item.label }}</el-tag
          >
        </div>
        <div class="stateItem">
          <span>{{ language.dataStateLabel }}:</span>
          <el-tag
            v-for="item in dataState"
            :key="item.value"
            :class="item.value === param.data_state ? 'active' : ''"
            @click="refreshList(item.value, 'data_state')"
            >{{ item.label }}</el-tag
          >
        </div>
      </div>
    </div>
    <custom-table
      :column="column"
      urlName="taskList"
      ref="taskList"
      :isShowPage="true"
      :params="param"
      pmd="page"
    >
      <template #task_tms="scope">
        {{ returnTime(scope.row.task_tms) }}
      </template>
      <template #task_state="scope">
        {{ returnState(scope.row.task_state, taskState) }}
      </template>
      <template #operation="scope">
        <el-button
          type="text"
          @click="edit(scope.row)"
          v-if="scope.row.task_state == 1 || scope.row.task_state == 3"
          >{{ language.edit }}</el-button
        >
        <el-button
          type="text"
          @click="del(scope.row)"
          v-if="scope.row.data_state === 0"
          >{{ language.delete }}</el-button
        >
        <el-button type="text" v-else disabled>{{
          language.deleted
        }}</el-button>
      </template>
    </custom-table>
    <edit-dialog ref="editDialog" @refresh="refresh"></edit-dialog>
  </div>
</template>
<script>
import customTable from "@/components/customTable/index";
import requestHttp from "@/utils/api";
import editDialog from "./components/editDialog.vue";
export default {
  data() {
    return {
      param: {
        task_state: "",
        data_state: "",
      },
      searchValue: "",
    };
  },
  computed: {
    language() {
      return this.$languagePackage.taskManage;
    },
    column() {
      return this.language.column;
    },
    dataState() {
      return this.language.dataState;
    },
    taskState() {
      return this.language.taskState;
    },
  },
  components: {
    customTable,
    editDialog,
  },
  methods: {
    returnState(state, list) {
      let a = list.findIndex((item) => {
        return item.value === state;
      });
      if (a !== -1) {
        return list[a].label;
      }
    },
    returnTime(tms) {
      let date = new Date(tms);
      let y = date.getFullYear();
      let m = (date.getMonth() + 1).toString().padStart(2, "0");
      let d = date.getDate().toString().padStart(2, "0");
      let h = date.getHours().toString().padStart(2, "0");
      let mm = date.getMinutes().toString().padStart(2, "0");
      let s = date.getSeconds().toString().padStart(2, "0");
      return y + "-" + m + "-" + d + " " + h + ":" + mm + ":" + s;
    },
    edit(row) {
      this.$refs.editDialog.open(row);
    },
    del(row) {
      if (row.data_state == 10) {
        this.$message.info(this.language.delTip);
        return false;
      }
      this.$confirm(this.language.delSumitTip, this.language.tip, {
        confirmButtonText: this.language.submit,
        cancelButtonText: this.language.cancel,
        type: "warning",
      }).then(() => {
        let data = {
          id: row.id,
          data_state: 10,
          os_timestampCode: true,
        };
        data.pmd = data.id.toString() + data.data_state.toString();
        requestHttp("taskEdit", data).then((res) => {
          this.$message.success(this.language.delSuccess);
          this.refresh();
        });
      });
    },
    refresh() {
      this.$refs.taskList.refresh();
    },
    search() {
      this.param.search = this.searchValue;
      if (!this.searchValue) {
        delete this.param.search;
      }
      this.$refs.taskList.refresh();
    },
    refreshList(value, key) {
      this.param[key] = value;
      this.$refs.taskList.refresh();
    },
  },
};
</script>
<style lang="less" scoped>
.taskManage {
  background-color: rgba(0, 0, 0, 1);
  padding: 0 20px;
  height: 100%;
  .searchDiv {
    padding: 1% 2%;
    text-align: left;
    width: 96%;
    display: flex;
    align-items: center;
    .el-input {
      width: 20%;
      margin-right: 1%;
    }
    .el-button {
      border-radius: 6px;
      font-size: 14px;
      font-weight: 550;
      padding: 13px 20px;
      background-color: white;
      color: #397ff8;
    }
    .stateChoose {
      margin-left: 30px;
      .stateItem {
        color: #fff;
        font-size: 14px;
        display: flex;
        align-items: center;
        &:first-child {
          margin-bottom: 6px;
        }
        .el-tag {
          background-color: transparent;
          color: rgb(201, 201, 201);
          margin-left: 6px;
          font-size: 13px;
          border: none;
          line-height: 22px;
          height: 24px;
          cursor: pointer;
          &.active {
            border: 1px solid #0f35dd;
            color: #0f35dd;
          }
        }
      }
    }
  }
  .firmware-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .operate-btn {
      .el-button {
        font-size: 16px;
        background-color: white;
        color: #397ff8;
        border: none;
      }
    }
    .type-choose {
      display: flex;
      align-items: center;
      font-size: 16px;
      .title {
        color: rgb(190, 190, 190);
      }
      .el-tag {
        cursor: pointer;
        background-color: transparent;
        margin: 0 10px;
        border: none;
        color: #fff;
        font-size: 14px;
        height: 24px;
        line-height: 24px;
        min-width: 60px;
        text-align: center;
        &.active {
          background: transparent;
          border: 1px solid #3455e9;
          color: #3455e9;
        }
      }
    }
  }
  .custom-table {
    margin: 0.5% 2%;
    .desc-div {
      white-space: pre-line;
    }
  }
}
</style>
<style lang="less">
.taskManage {
  .searchDiv {
    .el-input {
      .el-input__inner {
        background-color: transparent !important;
        color: white !important;
      }
    }
  }
}
</style>