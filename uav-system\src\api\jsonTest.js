/**
 * 注：此api文件只存放本地json文件路径
 */
let prefix = process.env.NODE_ENV == 'production' ? '.' : '';
let jsonPath = '/static/json/data/'

let jsonTest = {
    jsonInspectionLeaderboard: `${prefix}${jsonPath}inspectionLeaderboard.json`, 
    checkingType: `${prefix}${jsonPath}checkingType.json`, // 遥感巡检成果
    realtimeMessage: `${prefix}${jsonPath}realtimeMessage.json`, // 遥感实时信息
    equipment: `${prefix}${jsonPath}equipment.json`, // 设备列表
    inspectionRecord: `${prefix}${jsonPath}inspectionRecord.json`, // 数据大屏巡检
    menuJson: prefix + "/static/json/menus.json", // 菜单
}

export default jsonTest;

// /**
//  * 注：此api文件只存放本地json文件路径
//  */
//  let jsonTest = {
//     jsonInspectionLeaderboard: '/static/json/data/inspectionLeaderboard.json', 
//     checkingType: '/static/json/data/checkingType.json', // 遥感巡检成果
//     realtimeMessage: '/static/json/data/realtimeMessage.json', // 遥感实时信息
//     equipment: '/static/json/data/equipment.json', // 设备列表
//     inspectionRecord: '/static/json/data/inspectionRecord.json', // 数据大屏巡检
// }

// export default jsonTest;