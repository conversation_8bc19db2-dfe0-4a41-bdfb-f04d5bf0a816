import Vue from "vue"
const isEnglish = Vue.prototype.$language == 'english'
    //公司名称
const company = (value, list) => {
        for (let index = 0; index < list.length; index++) {
            if (value == list[index].value) {
                return list[index].label
            }
        }
    }
    //功能列表
const funfilter = (value, list) => {
        let funStr = ''
        for (let index = 0; index < list.length; index++) {
            for (let i = 0; i < value.length; i++) {
                if (value[i] == list[index].fun_id) {
                    funStr = (funStr == '') ? (list[index].name) : (funStr + '、' + list[index].name)
                }
            }
        }

        return funStr
    }
    //任务类型
const getTypeTitle = (value, list) => {
        for (let index = 0; index < list.length; index++) {
            if (list[index].value == value) {
                return isEnglish ? list[index].name_en : list[index].name_cn
            }

        }
    }
    //用户状态
const userState = (value) => {
        if (value == 10) {
            return "正常"
        } else {
            return "已删除"
        }
    }
    //用户时间转换
const timeFormat = (value) => {
    if (value >= 4765104000) {
        return "永久有效"
    } else {
        let time = new Date(parseInt(value * 1000))
        return time.getFullYear() + "-" + dataFormat(time.getMonth() + 1) + "-" + dataFormat(time.getDate()) + "  " + dataFormat(time.getHours()) + ":" + dataFormat(time.getMinutes()) + ":" + dataFormat(time.getSeconds())
    }


}
const timeFormat1 = (value) => {
        let time = new Date(value)
        return time.getFullYear() + "-" + dataFormat(time.getMonth() + 1) + "-" + dataFormat(time.getDate()) + "  " + dataFormat(time.getHours()) + ":" + dataFormat(time.getMinutes()) + ":" + dataFormat(time.getSeconds())
    }
    //数字转换
const dataFormat = (value) => {
        if (value > 9) {
            return value
        } else {
            return "0" + value
        }

    }
    //距离转换
const distanceFormat = (value) => {
    if (value >= 1000) {
        return parseFloat(value / 1000).toFixed(2)
    } else {
        return parseInt(value)
    }


}
const areaFormat = value => {
        if (value >= 10000) {
            return parseFloat(value / 1000000).toFixed(2)
        } else {
            return value.toFixed(2)
        }

    }
    //时间转换
const estTimeFormat = (value) => {
        let s = Math.ceil(value)
        let min = parseInt(s / 60)
        let ss = s - (min * 60)
        return (min > 9 ? min : "0" + min) + ":" + (ss > 9 ? ss : "0" + ss)
    }
    //设备类型转换(10：固定式。100：移动式。200：单兵)
const deviceTypeFormat = (value) => {
        if (value == 10) {
            return "固定式机巢"
        } else if (value == 100) {
            return "移动式机巢"
        } else if (value == 200) {
            return "单兵无人机"
        }

    }
    //风向转换
const windFormat = (value) => {
        let windDirection = ''
        if (typeof(value) == "string") {
            return value
        } else {
            switch (value) {
                case 0:
                    windDirection = "北"
                    break;
                case 90:
                    windDirection = "东"
                    break;
                case 180:
                    windDirection = "南"
                    break;
                case 270:
                    windDirection = "西"
                    break;
                case 360:
                    windDirection = "北"
                    break;
                default:
                    if (value > 0 && value < 90) {
                        windDirection = "东北"
                    }
                    if (value > 90 && value < 180) {
                        windDirection = "东南"
                    }
                    if (value > 180 && value < 270) {
                        windDirection = "西南"
                    }
                    if (value > 270 && value < 360) {
                        windDirection = "西北"
                    }
                    break;
            }
            return windDirection

        }

    }
    //机巢操作提示文字返回
const getTipTitle = (value, item) => {
        let result = ''
        let result_en = ''
        if (value == 3) {
            switch (item.id) {
                case "Hatch":
                    result = "舱门正在打开"
                    result_en = "Nest Opening"
                    break;
                case "Lift":
                    result = "升降台正在上升"
                    result_en = "Platform rising"
                    break;
                case "center":
                    result = "脚架正在张开"
                    result_en = "Drone release"
                    break;
                case "charger":
                    result = "充电器正在打开"
                    result_en = "Charger turning on"
                    break;
                case "uav":
                    result = "无人机正在开启"
                    result_en = "Drone start"
                    break;
                default:
                    break;
            }
        } else if (value == 2) {
            switch (item.id) {
                case "Hatch":
                    result = "舱门正在关闭"
                    result_en = "Nest close"
                    break;
                case "Lift":
                    result = "升降台正在下降"
                    result_en = "Platform decline"
                    break;
                case "center":
                    result = "脚架正在收缩"
                    result_en = "Drone contraction"
                    break;
                case "charger":
                    result = "充电器正在关闭"
                    result_en = "Charger turning off"
                    break;
                case "uav":
                    result = "无人机正在关闭"
                    result_en = "Drone end"
                    break;
                default:
                    break;
            }
        }
        return isEnglish ? result_en : result
    }
    //组网设备状态
const planStateFormat = value => {
        if (value == 0) {
            return "设备离线"
        } else if (value == 1) {
            return "执行任务中"
        } else if (value == 2) {
            return "正常待机"
        }
    }
    //组网在线设备数
const onLineNum = value => {
        let num = 0
        for (let index = 0; index < value.length; index++) {
            if (value[index].is_push_on) {
                num++
            }
        }
        return num
    }
    //组网判断是否选中
const ischeck = (value, list) => {
        for (let index = 0; index < list.length; index++) {
            if (list[index].sn_id == value) {
                return true;
            }
        }
        return false;
    }
    //判断图传还是4G
const lineType = value => {
    if (value) {
        let val = value.substr(1, 1)
        return val == 0 ? "图传" : "4G"
    }


}
export {
    company,
    funfilter,
    userState,
    timeFormat,
    getTypeTitle,
    distanceFormat,
    areaFormat,
    estTimeFormat,
    deviceTypeFormat,
    windFormat,
    getTipTitle,
    planStateFormat,
    timeFormat1,
    onLineNum,
    ischeck,
    lineType
}