<!-- 成果项 -->
<template>
  <div class="achievement-item" :style="{ animationDelay: item.delayed + 's' }">
    <div class="content-li" :class="className" :style="styles">
      <slot name="main">
        <!-- 图片 -->
        <div class="content-image" @click.stop="openDetails(item)">
          <el-image style="width: 100%; height: 100%" :src="item.p_url">
            <template #placeholder>
              <div class="content-loading">
                <i class="el-icon-loading"></i>
                {{ buttonGroup.imgLoading }}
              </div>
            </template>
          </el-image>

          <!-- 右上角类型图标 -->
          <div class="right-top-icon">
            <img-icon
              :name="typeIcon[item.file_type]"
              :width="rightIconWidth"
            />
          </div>

          <!-- 鼠标进入显示 -->
          <div class="mouse-hover-show" v-if="item.file_type == 20">
            <div class="show-mian" @click.stop="openDetails(item)">
              <img-icon name="video-play" width="40" />
            </div>
          </div>

          <div class="mouse-hover-show" v-if="item.file_type == 15">
            <div class="show-mian" @click.stop="openDetails(item)">
              <img-icon :name="typeIcon[item.file_type]" width="40" />
            </div>
          </div>
        </div>

        <div class="content-title" v-if="item.o_name.length <= 10">
          {{ item.o_name }}
        </div>

        <el-tooltip effect="dark" :content="item.o_name" v-else>
          <div class="content-title">
            {{ item.o_name }}
          </div>
        </el-tooltip>

        <div
          class="content-text"
          :class="$loadingEnUI ? 'content-text-en' : ''"
          v-for="(row, lens) in contentTextList"
          :key="lens"
        >
          <div class="text-label" v-if="row.label">{{ row.label }}：</div>
          <div class="text-value" :title="item[row.key]">
            {{ item[row.key] }}
          </div>
        </div>

        <!-- 图片下方省略图标 -->
        <el-popover
          placement="right"
          width="120"
          trigger="click"
          popper-class="achievementl-popover"
        >
          <ul>
            <li @click="download">{{ buttonGroup.down }}</li>
            <li @click="deleteImg">{{ buttonGroup.del }}</li>
          </ul>

          <div slot="reference" class="img-bottom-icon">
            <div class="circle"></div>
            <div class="circle"></div>
          </div>
        </el-popover>
      </slot>
    </div>

    <!-- 图片预览插件 -->
    <el-image-viewer
      :z-index="2000"
      :initial-index="0"
      v-if="showViewer"
      :on-close="closeViewer"
      :url-list="previewSrcList"
    ></el-image-viewer>

    <!-- 视频弹窗 -->
    <video-dialog ref="videoDialog"></video-dialog>

    <!-- 可选择删除 -->
    <div
      class="edit-delete"
      :style="{ borderColor: editColor, color: editColor }"
      v-if="edit"
      :class="item.isSelect ? 'edit-delete-select' : ''"
      @click="selectDelete"
    >
      <i class="el-icon-check" v-if="item.isSelect"></i>
    </div>
  </div>
</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import imgIcon from "@/components/imgIcon/index.vue";
import videoDialog from "../dialog/videoDialog.vue";
import axios from "axios";

export default {
  components: {
    ElImageViewer,
    videoDialog,
    imgIcon,
  },
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    className: String,
    styles: [String, Object],
    itemWidth: String,
    edit: Boolean,
    editColor: {
      type: String,
      default: "#409EFF",
    },
  },
  data() {
    return {
      showViewer: false,
      previewSrcList: [],

      contentTextList: [
        { label: "设备ID", key: "sn_id" },
        { label: "飞行编号", key: "sort_id" },
        { label: "任务名称", key: "sort_name" },
        { label: "创建时间", key: "create_time" },
      ],

      typeIcon: {
        10: "image",
        20: "video",
        15: "panoramagram",
      },
    };
  },
  computed: {
    listLabel() {
      return this.$languagePackage.achievement.form.listLabel;
    },

    labelStyle() {
      return {
        "min-width": "90px",
        width: "90px",
      };
    },
    buttonGroup() {
      return this.$languagePackage.achievement.form.button;
    },
    rightIconWidth() {
      return (16 * window.innerWidth) / 1920;
    },
  },
  created() {
    for (let i = 0; i < this.contentTextList.length; i++) {
      let item = this.contentTextList[i];
      let key = item.key;
      item.label = this.listLabel[key];
    }
    this.imgTransition(this.item.p_url, "p_url");
  },
  methods: {
    openDetails: function (item) {
      let type = item.file_type;
      if (type == 10) {
        if (
          item.o_url.indexOf(".tiff") !== -1 ||
          item.o_url.indexOf(".tif") !== -1
        ) {
          this.tiffImageLoad(item.o_url)
            .then((res) => {
              this.previewSrcList = [res];
              this.showViewer = true;
            })
            .catch(() => {
              this.$message({
                type: "error",
                message: "转换失败，请重试",
              });
            });
        } else {
          this.previewSrcList = [item.o_url];
          this.showViewer = true;
        }
      } else if (type == 20) {
        this.$refs.videoDialog.openDialog(item);
      } else if (type == 15) {
        const route = this.$router.resolve({
          path: `/panorama`,
          query: {
            imgSrc: item.o_url,
          },
        });
        window.open(route.href, "_blank");
      }
    },
    openVideoDetails: function (item) {
      this.$refs.videoDialog.openDialog(item);
    },

    closeViewer() {
      //   document.body.style.overflow = prevOverflow
      this.showViewer = false;
    },

    download: function () {
      axios
        .get(this.item.o_url, { responseType: "blob" })
        .then((response) => {
          const blob = new Blob([response.data]);
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = this.item.o_name;
          link.click();
          URL.revokeObjectURL(link.href);
        })
        .catch(console.error);
    },
    deleteImg: function () {
      this.$emit("onDelete");
    },
    selectDelete: function () {
      this.$set(this.item, "isSelect", !this.item.isSelect);

      this.$emit("onSelect", this.item, this.item.isSelect);
    },
    tiffImageLoad: function (url) {
      return new Promise((resolve, reject) => {
        try {
          var xhr = new XMLHttpRequest();
          xhr.open("GET", url, true); //filename为tif文件地址
          xhr.responseType = "arraybuffer";
          xhr.onload = function (e) {
            var buffer = xhr.response;
            var tiff = new Tiff({ buffer: buffer });
            const imgData = tiff.toDataURL(); // 使用base64调此方法
            resolve(imgData);
          };
          xhr.send();
        } catch (error) {
          reject(error);
        }
      });
    },
    // 图片转换
    imgTransition: function (url, key) {
      if (!url) {
        return url;
      }
      if (url.indexOf(".tiff") !== -1 || url.indexOf(".tif") !== -1) {
        this.tiffImageLoad(url).then((res) => {
          this.item[key] = res;
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
@keyframes showItem {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@media screen and(min-width: 1921px) {
  @radit: 100vw / 1920px;
  .achievement-item {
    width: @radit * 280px !important;
    height: @radit * 320px !important;
    border-bottom-left-radius: @radit * 10px !important;
    border-bottom-right-radius: @radit * 10px !important;

    .edit-delete {
      font-size: @radit * 48px !important;
    }

    .content-image {
      height: @radit * 150px !important;
      margin-bottom: @radit * 10px !important;

      .mouse-hover-show {
        .show-mian {
          height: @radit * 150px !important;
          border-radius: @radit * 5px !important;
        }
      }
    }

    .right-top-icon {
      width: @radit * 46px;
      height: @radit * 26px;
      border-bottom-left-radius: @radit * 6px;
    }

    .img-bottom-icon {
      top: @radit * 160px !important;
      right: @radit * 10px !important;
      width: @radit * 10px !important;
      height: @radit * 20px !important;
      border-radius: @radit * 10px !important;
      .circle {
        width: @radit * 4px !important;
        height: @radit * 4px !important;
        margin: 2px 0;
      }
    }

    .content-title {
      padding: 0 0 @radit * 14px @radit * 20px !important;
      width: calc(100% - (@radit * 50px)) !important;
      font-size: @radit * 18px !important;
    }
    .content-text {
      padding: 0 @radit * 20px 0px @radit * 20px !important;
      height: @radit * 26px !important;
      .text-label {
        font-size: @radit * 14px !important;
        width: @radit * 70px !important;
        min-width: @radit * 70px !important;
      }
      .text-value {
        font-size: @radit * 18px !important;
      }
      &.content-text-en {
        padding: 0 @radit * 20px 0px @radit * 5px !important;
        .text-label {
          font-size: @radit * 12px !important;
          width: @radit * 110px !important;
        }
        .text-value {
          font-size: @radit * 16px !important;
        }
      }
    }
  }
}

.achievement-item {
  width: 280px;
  height: 320px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  margin: 0 40px 80px 0;
  animation-name: showItem;
  animation-fill-mode: forwards;
  animation-duration: 0.1s;
  opacity: 0;
  position: relative;

  .edit-delete {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    color: #409eff;
    font-size: 48px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .edit-delete-select {
    border: 3px solid #409eff;
    width: calc(100% - 6px) !important;
    height: calc(100% - 6px) !important;
  }

  .content-li {
    width: 100%;
    height: 100%;
    position: relative;

    .content-image {
      width: 100%;
      height: 150px;
      overflow: hidden;
      margin-bottom: 10px;
      position: relative;
      &:hover .mouse-hover-show {
        transform: scale(1);
      }
      .content-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #13213d;
        z-index: 100;
      }

      .mouse-hover-show {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        align-items: center;
        justify-content: center;
        // display: none;
        transform: scale(0);
        transition: 0.1s;
        .show-mian {
          width: 100%;
          height: 150px;
          // background-color: rgba(20, 69, 145, 0.5);
          border-radius: 5px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    .content-title {
      // color: #000000;
      padding: 0 0 14px 20px;
      display: inline-block;
      white-space: nowrap;
      width: calc(100% - 50px);
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 700;
      font-size: 18px;
    }
    .content-text {
      display: flex;
      // color: #958f8f;
      padding: 0 20px 0px 20px;
      align-items: center;
      height: 26px;
      .text-label {
        text-align: right;
        font-weight: 700;
        font-size: 14px;
        width: 70px;
        min-width: 70px;
        line-height: 1;
      }
      .text-value {
        flex: 1;
        font-size: 18px;
        flex-grow: 1;
        // color: #000000;
        display: inline-block;
        white-space: nowrap;
        // width: calc(100% - 70px);
        overflow: hidden;
        text-overflow: ellipsis;
        // letter-spacing: 1px;
      }
      &.content-text-en {
        padding: 0 20px 0px 5px;
        .text-label {
          font-size: 12px;
          width: 110px;
        }
        .text-value {
          font-size: 16px;
        }
      }
    }
    .right-top-icon {
      position: absolute;
      right: 0;
      top: 0;
      width: 46px;
      height: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-bottom-left-radius: 6px;
    }
    .img-bottom-icon {
      position: absolute;
      top: 160px;
      right: 10px;
      width: 10px;
      height: 20px;
      border: 1px solid #ccc;
      border-radius: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .circle {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        margin: 2px 0;
      }
      &:hover {
      }
      &:hover .circle {
      }
    }
  }
}
</style>

<style lang="less">
.achievement-item {
  .content-image {
    .el-image img {
      transition: 0.5s;
    }

    &:hover .el-image img {
      transform: scale(1.5);
    }
  }
}

.achievementl-popover {
  padding: 0 !important;
  min-width: 80px;
  background-color: #13213d;
  border: none;
  overflow: hidden;
  ul {
    padding: 0;
    margin: 0;
    li {
      padding: 0 16px;
      height: 32px;
      display: flex;
      align-items: center;
      cursor: pointer;
      &:hover {
        color: #60d8fc;
        background-color: #0d1930;
      }
    }
  }
}
</style>