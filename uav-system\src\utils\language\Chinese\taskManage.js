const taskManage = {
    language: 'zh-CN',
    taskState: [
        { label: '全部', value: '' },
        { label: "等待执行", value: 1 },
        { label: "暂停任务", value: 3 },
        { label: "下发成功", value: 10 },
        { label: "下发失败:超时", value: 40 },
        { label: "下发失败:不在线 ", value: 41 },

    ],
    dataState: [
        { label: '全部', value: '' },
        { label: "正常", value: 0 },
        { label: "已删除", value: 10 },
    ],
    dataStateList: [
        { label: "正常", value: 0 },
        { label: "删除", value: 10 },
    ],
    submit: '确定',
    cancel: '取消',
    column: [
        { label: "围栏名称", prop: "f_title" },
        { label: "航线名称", prop: "m_title" },
        { label: "设备ID", prop: "sn_id" },
        { label: "执行时间", prop: "task_tms" },
        { label: "执行状态", prop: "task_state" },
        { label: "创建时间", prop: "create_time" },
        { label: "操作", prop: "operation" },
    ],
    edit: '编辑',
    NotEditable: '不可编辑',
    delete: '删除',
    deleted: '已删除',
    delTip: '该定时任务已删除！',
    delSumitTip: '是否确定删除该定时任务？',
    tip: '确认删除',
    delSuccess: '定时任务删除成功！',
    editTask: '编辑任务',
    taskTime: '执行时间',
    taskStateLabel: '执行状态',
    dataStateLabel: '任务状态',
    placeholderTime: '选择日期时间',
    editSuccess: '任务编辑成功！',
    searchTip: '模糊匹配搜索',
    searchBtn: '搜索'

}
export default taskManage