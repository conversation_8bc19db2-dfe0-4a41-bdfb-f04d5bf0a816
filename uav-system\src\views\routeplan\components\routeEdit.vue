<template>
  <div class="routeEdit">
    <div class="title" ref="routeEditTitle">
      <el-button
        :class="iconCode ? 'returnIcon' : ''"
        icon="el-icon-arrow-left"
        @click="goBack"
      ></el-button>
      {{ routeTitle }}
    </div>
    <el-form
      :model="routeForm"
      :rules="routeRules"
      ref="routeForm"
      class="content-item-1"
      :style="{ height: height }"
    >
      <el-form-item :label="routeLanguage.routeLine.taskName" prop="name">
        <el-input
          v-model="routeForm.name"
          :placeholder="routeLanguage.routeLine.placeholder"
        ></el-input>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.fenceName">
        <el-button>{{ fenceItem.title }}</el-button>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.taskType">
        <el-button>{{
          $language == "chinese" ? checkType.name_cn : checkType.name_en
        }}</el-button>
      </el-form-item>
      <div class="title">{{ routeLanguage.routeLine.basicSet }}</div>
      <el-form-item
        :label="routeLanguage.routeLine.toPlaneType"
        prop="plane_type"
      >
        <el-select
          v-model="routeForm.plan_type"
          :placeholder="routeLanguage.routeLine.placeholder4"
          class="actionChoose"
          popper-class="selects"
          @change="changePlanType"
        >
          <el-option
            v-for="item in options2"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.timeNode"
        v-if="routeForm.plan_type == 1"
      >
        <select-date
          :plan_time.sync="routeForm.plan_time"
          ref="selectDate"
        ></select-date>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.autoSpeed"
        prop="auto_speed"
      >
        <el-slider
          v-model="routeForm.auto_speed"
          :show-tooltip="false"
          show-input
          :show-input-controls="false"
          :max="routeForm.max_speed"
          :step="0.1"
          class="slider-class-1"
        ></el-slider>
      </el-form-item>
      <!-- <el-form-item label="最大飞行速度（m / s）" prop="max_speed">
            <el-slider
              v-model="routeForm.max_speed"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :min="5"
              :max="1000"
              class="slider-class-1"
            ></el-slider>
          </el-form-item> -->
      <el-form-item
        :label="routeLanguage.routeLine.heightType"
        prop="height_type"
      >
        <el-select
          v-model="routeForm.height_type"
          :placeholder="routeLanguage.routeLine.placeholder5"
          class="actionChoose"
          popper-class="selects"
        >
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.defaultHeight"
        prop="default_height"
      >
        <el-slider
          v-model="routeForm.default_height"
          :show-tooltip="false"
          show-input
          :show-input-controls="false"
          :step="1"
          :max="fenceItem.height"
          :min="2"
          @change="setDefaultHeight"
          class="slider-class-1"
        ></el-slider>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.returnHeight"
        prop="return_height"
      >
        <el-slider
          v-model="routeForm.return_height"
          :show-tooltip="false"
          show-input
          :show-input-controls="false"
          :step="1"
          :max="fenceItem.height"
          :min="2"
          class="slider-class-1"
        ></el-slider>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.taskAction"
        prop="action_completed"
      >
        <el-select
          v-model="routeForm.action_completed"
          :placeholder="routeLanguage.routeLine.placeholder1"
          class="actionChoose"
          popper-class="selects"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.photoType"
        prop="action_completed"
      >
        <el-select
          v-model="routeForm.capture_mode"
          :placeholder="routeLanguage.routeLine.placeholder6"
          class="actionChoose"
          popper-class="selects"
        >
          <el-option
            v-for="item in photoTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.isPlaneHight"
        prop="interval_height"
      >
        <el-switch
          v-model="routeForm.isComputeHight"
          active-color="#0555ff"
          inactive-color="rgb(92, 92, 92)"
          style="margin-left: 20px"
        ></el-switch>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.intervalHeight"
        prop="interval_height"
      >
        <div class="interval_height">
          <el-input
            v-model="routeForm.interval_height"
            placeholder=""
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @blur="changeIntervalHeight"
          ></el-input>
          <el-switch
            v-model="routeForm.showHeight"
            active-color="#0555ff"
            inactive-color="rgb(92, 92, 92)"
          ></el-switch>
        </div>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.waypoint">
        <el-collapse
          v-model="pointCodes"
          v-if="routeForm.point_json.length > 0 ? true : false"
          class="pointsClass"
        >
          <el-collapse-item
            v-for="(item, index) in routeForm.point_json"
            :key="index"
            :title="index + 1 + routeLanguage.waypoint.title"
            :name="index + 1"
          >
            <div>{{ routeLanguage.waypoint.lng }}</div>
            <el-input-number
              v-model="item.lng"
              @blur="changeLngLat(index)"
              @focus="getFocus(index)"
              :controls="false"
              :precision="7"
              ref="lngList"
            ></el-input-number>
            <div>{{ routeLanguage.waypoint.lat }}</div>
            <el-input-number
              v-model="item.lat"
              @blur="changeLngLat(index)"
              @focus="getFocus(index)"
              :controls="false"
              :precision="7"
            ></el-input-number>
            <div>{{ routeLanguage.waypoint.height }}</div>
            <el-input-number
              :controls="false"
              :precision="0"
              v-model="item.height"
              @blur="getPointHeight(index)"
              :min="routeForm.height_type ? -100 : 2"
              :max="fenceItem.height"
            ></el-input-number>
            <el-button
              icon="el-icon-plus"
              class="addActionBtn"
              :class="addActionCode == index + 1 ? 'active' : ''"
              @click="addAction(index)"
              v-if="checkType.value == 20"
              >{{ routeLanguage.waypoint.waypointAction }}</el-button
            >
            <div
              class="routeActionDiv"
              v-for="(item_1, i) in item.actionList"
              :key="'action' + i"
            >
              <div style="display: flex; align-items: center">
                <el-select
                  v-model="item_1.action_id"
                  :placeholder="routeLanguage.waypoint.placeholder"
                  class="actionChoose"
                  popper-class="selects"
                  clearable
                  @change="chooseAction(item_1.action_id, index, i)"
                >
                  <el-option
                    v-for="(item_2, index) in actionOptions"
                    :key="'action_1' + index"
                    :label="item_2.label"
                    :value="item_2.value"
                  >
                  </el-option>
                </el-select>
                <el-button
                  type="text"
                  icon="el-icon-minus"
                  @click="delAction(index, i)"
                ></el-button>
              </div>

              <div
                v-if="item_1.action_id == 'uav_yaw' ? true : false"
                class="actionValueDiv"
              >
                {{ routeLanguage.waypoint.uav_yaw }}
                <el-input-number
                  v-model="item_1.param_list[0].value"
                  :min="0"
                  :max="360"
                  :controls="false"
                  :precision="0"
                ></el-input-number>
              </div>
              <div
                v-if="item_1.action_id == 'hover' ? true : false"
                class="actionValueDiv"
              >
                {{ routeLanguage.waypoint.hoverTime }}
                <el-input-number
                  v-model="item_1.param_list[0].value"
                  :min="0"
                  :max="255"
                  :controls="false"
                  :precision="0"
                ></el-input-number>
              </div>
              <div
                v-if="item_1.action_id == 'gimbal_ctrl' ? true : false"
                class="actionValueDiv"
              >
                {{ routeLanguage.waypoint.gimbal_yaw }}
                <el-input-number
                  v-model="item_1.param_list[0].value"
                  :min="-180"
                  :max="255"
                  :controls="false"
                  :precision="0"
                ></el-input-number>
              </div>
              <div
                v-if="item_1.action_id == 'gimbal_ctrl' ? true : false"
                class="actionValueDiv"
              >
                {{ routeLanguage.waypoint.gimbal_pitch }}
                <el-input-number
                  v-model="item_1.param_list[1].value"
                  :min="-180"
                  :max="255"
                  :controls="false"
                  :precision="0"
                ></el-input-number>
              </div>
              <!-- <div
                    v-if="item_1.action_id == 'cam_trig_dist' ? true : false"
                    class="actionValueDiv"
                  >
                    拍照距离（单位：m）：
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="0"
                      :precision="2"
                      :controls="false"
                    ></el-input-number>
                  </div> -->
              <!-- <div
                    v-if="item_1.action_id == 'cam_zoom' ? true : false"
                    class="actionValueDiv"
                  >
                    相机变焦倍数（范围[1-200]）：
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="0"
                      :max="200"
                      :precision="0"
                      :controls="false"
                    ></el-input-number>
                  </div> -->
              <div
                v-if="item_1.action_id == 'speed' ? true : false"
                class="actionValueDiv"
              >
                {{ routeLanguage.waypoint.speed }}
                <el-input-number
                  v-model="item_1.param_list[0].value"
                  :min="0"
                  :max="routeForm.max_speed"
                  :controls="false"
                  :precision="0"
                ></el-input-number>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form-item>
      <el-button
        class="saveBut"
        @click="sumbitRoute('routeForm')"
        :class="saveCode ? 'active' : ''"
        >{{ routeLanguage.routeLine.save }}</el-button
      >
    </el-form>
  </div>
</template>
<script>
import { computedMethod } from "../../../utils/computedMap";
import requestHttp from "../../../utils/api";
import selectDate from "./selectDate.vue";
import { gcj02_to_wgs84, wgs84_to_gcj02 } from "../../../utils/wgs84_to_gcj02";
import { computedMapMethods } from "@/utils/cesium/computedMapMethods";
import { pointsConvert } from "@/utils/coordinateConvert";
export default {
  name: "routeEdit",
  props: {
    fenceItem: {
      type: Object,
      default() {
        return {};
      },
    },
    checkType: {
      type: Object,
      default() {
        return {};
      },
    },
    changePoint: {
      type: Object,
      default() {
        return {};
      },
    },
    extra: {
      type: Boolean,
      default: false,
    },
    importPoints: {
      type: Array,
      default: [],
    },
    elevationSuccess: {
      type: Object,
      default() {
        return {};
      },
    },
    elevationData: {
      type: Object,
      default() {
        return {};
      },
    },
    mapType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      iconCode: false,
      routeTitle: "",
      options: "",
      options1: "",
      options2: "",
      pointCodes: [],
      routeItem: "",
      actionOptions: "",
      addActionCode: 0,
      routeForm: {
        name: "",
        auto_speed: 10,
        max_speed: 20,
        default_height: 100,
        return_height: 100,
        action_completed: 20,
        point_json: [],
        height_type: 0,
        // dateTime:[],
        plan_type: 0,
        plan_time: [],
        interval_height: 100,
        showHeight: false,
        isComputeHight: false,
        capture_mode: 3,
      },
      routeRules: {
        name: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        auto_speed: [{ validator: this.numFormat, trigger: "change" }],
        max_speed: [{ validator: this.numFormat, trigger: "change" }],
        default_height: [{ validator: this.numFormat, trigger: "change" }],
        return_height: [{ validator: this.numFormat, trigger: "change" }],
        action_completed: [
          {
            required: true,
            message: "",
            trigger: "change",
          },
        ],
      },
      saveCode: false,
      routeCode: false,
      height: "",
      photoTypeList: [],
      pointListImport: [],
    };
  },
  components: {
    selectDate,
  },
  computed: {
    routeLanguage() {
      return this.$languagePackage.routes;
    },
  },
  created() {
    this.options = this.routeLanguage.options;
    this.options1 = this.routeLanguage.options1;
    this.options2 = this.routeLanguage.options2;
    this.routeTitle = this.routeLanguage.routeLine.setNewTask;
    this.actionOptions = this.routeLanguage.waypoint.actionOptions;
    this.routeRules.name[0].message = this.routeLanguage.routeLine.placeholder;
    this.routeRules.action_completed[0].message =
      this.routeLanguage.routeLine.placeholder1;
    this.photoTypeList = this.routeLanguage.photoTypeList;
  },
  async mounted() {
    this.routeItem = this.$store.state.route.routeItem;
    this.height =
      "calc(100% - " + (this.$refs.routeEditTitle.offsetHeight + 10) + "px)";
    if (this.routeItem) {
      await this.originForm();
      this.routeCode = false;
    }
  },
  methods: {
    //初始化表单
    originForm() {
      let photo_type_json = this.routeItem.photo_type_json
        ? JSON.parse(this.routeItem.photo_type_json)
        : {};
      this.routeForm = {
        name: this.routeItem.title,
        auto_speed: this.routeItem.auto_speed / 100,
        max_speed: this.routeItem.max_speed / 100,
        default_height: this.routeItem.default_height / 100,
        return_height: this.routeItem.return_height / 100,
        action_completed: this.routeItem.action_completed,
        point_json: [],
        height_type: this.routeItem.height_type,
        plan_time: this.routeItem.task_json
          ? JSON.parse(this.routeItem.task_json)
          : [],
        plan_type: this.routeItem.is_timed_task ? 1 : 0,
        interval_height: 100,
        showHeight: false,
        isComputeHight: false,
        capture_mode: photo_type_json.capture_mode
          ? photo_type_json.capture_mode
          : 3,
      };
      let cal_alt_json = this.routeItem.cal_alt_json
        ? JSON.parse(this.routeItem.cal_alt_json)
        : "";
      if (cal_alt_json) {
        this.routeForm.interval_height = cal_alt_json.interval_height;
        this.routeForm.isComputeHight = cal_alt_json.isComputeHight
          ? cal_alt_json.isComputeHight
          : false;
      }
      this.routeTitle = this.routeLanguage.routeLine.editTitle;
      for (let index = 0; index < this.routeItem.point_list.length; index++) {
        let apoint = [
          this.routeItem.point_list[index].lon_int / 1e7,
          this.routeItem.point_list[index].lat_int / 1e7,
        ];
        if (this.routeItem.point_list[index].type == 20) {
          let a1 = gcj02_to_wgs84(apoint[0], apoint[1]);
          apoint = a1;
        }
        let lat = apoint[1],
          lng = apoint[0];
        let path = {
          lat: lat,
          lng: lng,

          height: this.routeItem.point_list[index].height / 100,
        };
        if (this.checkType.value == 20) {
          path.actionList = this.routeItem.point_list[index].action_json
            ? JSON.parse(this.routeItem.point_list[index].action_json)
            : [];
          if (path.actionList.length > 0) {
            for (let j = 0; j < path.actionList.length; j++) {
              path.actionList[j].param_list = path.actionList[j].param_list
                ? JSON.parse(path.actionList[j].param_list)
                : path.actionList[j].param_list;
            }
          }
        }

        this.routeForm.point_json.push(path);
      }
    },
    //校验是否为0
    numFormat(rule, value, callback) {
      if (value == 0) {
        callback(new Error(this.routeLanguage.errorMessage));
      } else {
        callback();
      }
    },
    //获取焦点
    getFocus(index) {
      let point = this.routeForm.point_json[index];
      this.focusPoint = Object.assign({}, point);
    },
    //失去焦点修改坐标
    changeLngLat(index) {
      let a = true;
      let points =
        this.$coordinateType == "gcj02"
          ? wgs84_to_gcj02(
              this.routeForm.point_json[index].lng,
              this.routeForm.point_json[index].lat
            )
          : [
              this.routeForm.point_json[index].lng,
              this.routeForm.point_json[index].lat,
            ];
      if (this.mapType) {
        let point = {
          lat: points[1],
          lng: points[0],
          height: this.routeForm.point_json[index].height,
        };
        a = this.judgeInArea(point, index);
      } else {
        a = computedMethod(2, {
          point1: new AMap.LngLat(
            this.routeForm.point_json[index].lng,
            this.routeForm.point_json[index].lat
          ),
          fence: this.fenceItem.paths,
        });
      }

      if (a) {
        let changePoint = {
          index: index,
          lat: points[1],
          lng: points[0],
          height: this.routeForm.point_json[index].height,
        };
        this.$emit("changeMarker", changePoint);
      } else {
        !this.mapType &&
          this.$message.error({
            message: this.routeLanguage.placeholder4,
            customClass: "message-info-tip",
          });
        this.routeForm.point_json[index].lng = this.focusPoint.lng;
        this.routeForm.point_json[index].lat = this.focusPoint.lat;
      }
    },
    //判断航点航线是否在围栏内
    judgeInArea(point, index, lastPoint) {
      let isArea = computedMapMethods("pointInPolygon", {
        point: [point.lng, point.lat],
        polygon: this.fenceItem.paths,
      });
      if (!isArea) {
        this.$message.error({
          message: this.routeLanguage.placeholder4,
          customClass: "message-info-tip",
        });
        return false;
      }
      let len = lastPoint ? 0 : 1;
      if (this.routeForm.point_json.length > len) {
        let isCross = false;
        if (lastPoint) {
          let pointBefore =
            this.$coordinateType == "gcj02"
              ? wgs84_to_gcj02(
                  this.routeForm.point_json[index - 1].lng,
                  this.routeForm.point_json[index - 1].lat
                )
              : [
                  this.routeForm.point_json[index - 1].lng,
                  this.routeForm.point_json[index - 1].lat,
                ];
          isCross = computedMapMethods("lineCross", {
            point,
            point1: {
              lat: pointBefore[1],
              lng: pointBefore[0],
            },
            fence: this.fenceItem.paths,
            type: true,
          });
        } else {
          let beforePoint = "";
          let afterPoint = "";
          let a = false;
          let b = false;
          if (index == 0) {
            if (this.routeForm.point_json[index + 1]) {
              let a =
                this.$coordinateType == "gcj02"
                  ? wgs84_to_gcj02(
                      this.routeForm.point_json[index + 1].lng,
                      this.routeForm.point_json[index + 1].lat
                    )
                  : [
                      this.routeForm.point_json[index + 1].lng,
                      this.routeForm.point_json[index + 1].lat,
                    ];
              afterPoint = {
                lat: a[1],
                lng: a[0],
              };
            }
          } else {
            if (this.routeForm.point_json[index - 1]) {
              let a =
                this.$coordinateType == "gcj02"
                  ? wgs84_to_gcj02(
                      this.routeForm.point_json[index - 1].lng,
                      this.routeForm.point_json[index - 1].lat
                    )
                  : [
                      this.routeForm.point_json[index - 1].lng,
                      this.routeForm.point_json[index - 1].lat,
                    ];
              beforePoint = { lat: a[1], lng: a[0] };
            }

            if (index < this.routeForm.point_json.length - 1) {
              if (this.routeForm.point_json[index + 1]) {
                let b =
                  this.$coordinateType == "gcj02"
                    ? wgs84_to_gcj02(
                        this.routeForm.point_json[index + 1].lng,
                        this.routeForm.point_json[index + 1].lat
                      )
                    : [
                        this.routeForm.point_json[index + 1].lng,
                        this.routeForm.point_json[index + 1].lat,
                      ];
                afterPoint = { lat: b[1], lng: b[0] };
              }
            }
          }
          if (beforePoint) {
            a = computedMapMethods("lineCross", {
              point,
              point1: beforePoint,
              fence: this.fenceItem.paths,
              type: true,
            });
          }
          if (afterPoint) {
            b = computedMapMethods("lineCross", {
              point,
              point1: afterPoint,
              fence: this.fenceItem.paths,
              type: true,
            });
          }
          isCross = a || b;
        }
        if (isCross) {
          this.$message.error({
            message: this.routeLanguage.placeholder5,
            customClass: "message-info-tip",
          });
          return false;
        }
      }
      return true;
    },
    //添加航点动作
    addAction(index) {
      let action_json = {
        action_id: "",
        param_list: "",
      };
      this.routeForm.point_json[index].actionList.push(action_json);
    },
    //删除航点动作
    delAction(index, i) {
      this.routeForm.point_json[index].actionList.splice(i, 1);
    },
    //选中航点动作时触发
    chooseAction(e, index, i) {
      if (e == "takephoto" || e == "panorama_takephoto") {
        this.routeForm.point_json[index].actionList[i].param_list = "";
        let a = this.judgePhotoType(e, index, i);
        if (a) {
          return false;
        }
      } else {
        this.routeForm.point_json[index].actionList[i].param_list = [];
        let a = {
          param_id: "",
          value: 0,
        };
        let b = {
          param_id: "",
          value: 0,
        };
        switch (e) {
          case "uav_yaw":
            a.param_id = "yaw";
            a.value = 0;
            break;
          case "hover":
            a.param_id = "hovertime";
            a.value = 3;
            break;
          case "gimbal_ctrl":
            a.param_id = "gimbal_yaw";
            a.value = 0;
            b.param_id = "gimbal_pitch";
            b.value = 0;
            break;
          case "cam_trig_dist":
            a.param_id = "dist";
            a.value = 1;
            break;
          case "speed":
            a.param_id = "speed";
            a.value = this.routeForm.auto_speed;
            break;

          default:
            break;
        }
        this.routeForm.point_json[index].actionList[i].param_list.push(a);
        if (b.param_id) {
          this.routeForm.point_json[index].actionList[i].param_list.push(b);
        }
      }
    },
    judgePhotoType(e, index, i) {
      if (e == "panorama_takephoto") {
        let arr = {};
        for (let n = 0; n < this.routeForm.point_json.length; n++) {
          const element = this.routeForm.point_json[n].actionList;
          for (let j = 0; j < element.length; j++) {
            const item = element[j];
            if (!(n === index && i === j)) {
              if (
                item.action_id == "takephoto" ||
                item.action_id == "panorama_takephoto"
              ) {
                arr[n + 1] = item.action_id;
              }
            }
          }
        }
        let str = Object.keys(arr).join(",");
        if (str) {
          this.routeForm.point_json[index].actionList[i].action_id = "";
          this.$message.error(
            this.routeLanguage.photoActionError.replace("[name]", str)
          );
        }
      }
      if (e == "takephoto") {
        let arr = {};
        for (let n = 0; n < this.routeForm.point_json.length; n++) {
          const element = this.routeForm.point_json[n].actionList;
          for (let j = 0; j < element.length; j++) {
            const item = element[j];
            if (!(n === index && i === j)) {
              if (item.action_id == "panorama_takephoto") {
                arr[n + 1] = item.action_id;
              }
            }
          }
        }
        let str = Object.keys(arr).join(",");
        if (str) {
          this.routeForm.point_json[index].actionList[i].action_id = "";
          this.$message.error(
            this.routeLanguage.photoActionError1.replace("[name]", str)
          );
        }
      }
      return false;
    },
    //所有航点默认高度
    setDefaultHeight() {
      for (let index = 0; index < this.routeForm.point_json.length; index++) {
        if (this.mapType) {
          if (
            this.routeForm.point_json[index].height !=
            this.routeForm.default_height
          ) {
            this.routeForm.point_json[index].height =
              this.routeForm.default_height;
            this.getPointHeight(index);
          }
        }
        this.routeForm.point_json[index].height = this.routeForm.default_height;
      }
    },
    //返回事件
    goBack() {
      this.iconCode = true;
      if (this.routeCode) {
        this.$confirm(this.routeLanguage.placeholder1, this.routeLanguage.tip, {
          confirmButtonText: this.routeLanguage.saveBtn,
          cancelButtonText: this.routeLanguage.cancelBtn,
          type: "warning",
          customClass: "messageTip",
        })
          .then(() => {
            this.$emit("update:extra", false);
            this.originalData();
          })
          .catch(() => {})
          .finally(() => {
            this.iconCode = false;
          });
      } else {
        this.originalData();
        this.$emit("update:extra", false);
      }
    },
    //返回数据初始化
    originalData() {
      this.pointCodes = [];
      this.$emit("goBack", "");
      this.routeForm = {
        name: "",
        auto_speed: 10,
        max_speed: 20,
        default_height: 100,
        return_height: 100,
        action_completed: 20,
        point_json: [],
        height_type: 0,
        plan_time: [],
        plan_type: 0,
        isComputeHight: false,
        capture_mode: 3,
      };
      this.routeTitle = this.routeLanguage.routeLine.setNewTask;
      this.$emit("update:importPoints", []);
    },
    sumbitRoute(e) {
      if (!this.saveCode) {
        this.saveCode = true;
        if (this.extra) {
          this.$message.error({
            message: this.routeLanguage.errorMessage7,
            customClass: "message-info-tip",
          });
          this.saveCode = false;
          return false;
        }
        let i = 0;
        for (const key in this.elevationSuccess) {
          if (!this.elevationSuccess[key]) {
            i++;
          }
        }
        if (this.routeForm.isComputeHight && i) {
          this.$message.error({
            message: this.routeLanguage.errorMessage11,
            customClass: "message-info-tip",
          });
          this.saveCode = false;
          return false;
        }
        if (e == "routeForm") {
          this.$refs[e].validate((valid, validator) => {
            if (valid) {
              if (this.$refs.selectDate) {
                for (
                  let index = 0;
                  index < this.$refs.selectDate.error.length;
                  index++
                ) {
                  if (this.$refs.selectDate.error[index]) {
                    this.$message.error({
                      message: this.routeLanguage.errorMessage10,
                      customClass: "message-info-tip",
                    });
                    this.saveCode = false;
                    return false;
                  }
                }
              }
              // let importKmlCode = false;
              // if (this.importPoints && this.importPoints.length) {
              //   importKmlCode = true;
              // }

              if (this.routeForm.point_json.length > 1) {
                var point_jsons = [];
                for (
                  let index = 0;
                  index < this.routeForm.point_json.length;
                  index++
                ) {
                  let point_json = {};
                  point_json = {
                    seq: index + 1,
                    lat_int: parseInt(
                      this.routeForm.point_json[index].lat * 1e7
                    ),
                    lon_int: parseInt(
                      this.routeForm.point_json[index].lng * 1e7
                    ),
                    height: parseInt(
                      this.routeForm.point_json[index].height * 100
                    ),
                    type: 10,
                  };
                  // if (importKmlCode) {
                  //   let p = this.getLngLatData(
                  //     {
                  //       point: [
                  //         this.importPoints[index][0],
                  //         this.importPoints[index][1],
                  //       ],
                  //       type: 10,
                  //     },
                  //     this.routeForm.point_json[index],
                  //     true
                  //   );
                  //   point_json = Object.assign({}, p);
                  //   point_json.type = point_json.type
                  //     ? point_json.type
                  //     : this.$coordinateType === "wgs84"
                  //     ? 10
                  //     : 20;
                  //   point_json.seq = index + 1;
                  //   point_json.height = parseInt(
                  //     this.routeForm.point_json[index].height * 100
                  //   );
                  // } else {
                  //   if (this.routeItem && this.routeItem.point_list[index]) {
                  //     let p = this.getLngLatData(
                  //       {
                  //         point: [
                  //           this.routeItem.point_list[index].lon_int / 1e7,
                  //           this.routeItem.point_list[index].lat_int / 1e7,
                  //         ],
                  //         type: this.routeItem.point_list[index].type,
                  //       },
                  //       this.routeForm.point_json[index],
                  //       true
                  //     );
                  //     point_json = Object.assign({}, p);
                  //     point_json.type = point_json.type
                  //       ? point_json.type
                  //       : this.$coordinateType === "wgs84"
                  //       ? 10
                  //       : 20;
                  //     point_json.seq = index + 1;
                  //     point_json.height = parseInt(
                  //       this.routeForm.point_json[index].height * 100
                  //     );
                  //   } else {
                  //     point_json = {
                  //       seq: index + 1,
                  //       lat_int: parseInt(
                  //         this.routeForm.point_json[index].lat * 1e7
                  //       ),
                  //       lon_int: parseInt(
                  //         this.routeForm.point_json[index].lng * 1e7
                  //       ),
                  //       height: parseInt(
                  //         this.routeForm.point_json[index].height * 100
                  //       ),
                  //       type: this.$coordinateType === "wgs84" ? 10 : 20,
                  //     };
                  //   }
                  // }
                  if (this.checkType.value == 20) {
                    let action_jsons = [];
                    for (
                      let i = 0;
                      i < this.routeForm.point_json[index].actionList.length;
                      i++
                    ) {
                      if (
                        this.routeForm.point_json[index].actionList[i].action_id
                      ) {
                        let action = {
                          action_id:
                            this.routeForm.point_json[index].actionList[i]
                              .action_id,
                          param_list: JSON.stringify(
                            this.routeForm.point_json[index].actionList[i]
                              .param_list
                          ),
                        };
                        action_jsons.push(action);
                      }
                    }
                    if (action_jsons.length > 0) {
                      point_json.action_json = JSON.stringify(action_jsons);
                    } else {
                      point_json.action_json = "";
                    }
                  }

                  point_jsons.push(point_json);
                }
                let cal_alt_json = {
                  interval_height: this.routeForm.interval_height,
                  elevationData: this.elevationData,
                  isComputeHight: this.routeForm.isComputeHight,
                };
                let data = {
                  title: this.routeForm.name,
                  f_id: this.fenceItem.id,
                  type: this.checkType.value,
                  auto_speed: parseInt(this.routeForm.auto_speed * 100),
                  max_speed: parseInt(this.routeForm.max_speed * 100),
                  default_height: parseInt(this.routeForm.default_height * 100),
                  return_height: parseInt(this.routeForm.return_height * 100),
                  action_completed: this.routeForm.action_completed,
                  height_type: this.routeForm.height_type,
                  is_timed_task: this.routeForm.plan_type == 0 ? false : true,
                  task_json:
                    this.routeForm.plan_time && this.routeForm.plan_time.length
                      ? JSON.stringify(this.routeForm.plan_time)
                      : "",
                  cal_alt_json: cal_alt_json
                    ? JSON.stringify(cal_alt_json)
                    : "",
                  photo_type_json: JSON.stringify({
                    capture_mode: this.routeForm.capture_mode,
                  }),
                };
                if (this.routeItem) {
                  data.m_id = this.routeItem.m_id;
                  data.state = this.routeItem.state;
                  data.break_json = "";
                  for (let index = 0; index < point_jsons.length; index++) {
                    for (let i = 0; i < this.routeItem.point_list.length; i++) {
                      if (
                        point_jsons[index].seq ==
                        this.routeItem.point_list[i].seq
                      ) {
                        point_jsons[index].id = this.routeItem.point_list[i].id;
                      }
                    }
                    point_jsons[index].state = 10;
                  }
                  if (point_jsons.length < this.routeItem.point_list.length) {
                    let point_jsons1 = [];
                    for (
                      let index = 0;
                      index < this.routeItem.point_list.length;
                      index++
                    ) {
                      if (
                        this.routeItem.point_list[index].seq >
                        point_jsons.length
                      ) {
                        let a = {
                          seq: this.routeItem.point_list[index].seq,
                          id: this.routeItem.point_list[index].id,
                          state: 30,
                          lon_int: this.routeItem.point_list[index].lon_int,
                          lat_int: this.routeItem.point_list[index].lat_int,
                          height: this.routeItem.point_list[index].height,
                          type: this.routeItem.point_list[index].type,
                        };
                        point_jsons1.push(a);
                      }
                    }
                    point_jsons = point_jsons.concat(point_jsons1);
                  }
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString() +
                    data.is_timed_task.toString() +
                    data.m_id.toString() +
                    data.state.toString();
                } else {
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title.toString() +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString() +
                    data.is_timed_task.toString();
                }
                requestHttp(this.routeItem ? "missionEdit" : "missionAdd", data)
                  .then(() => {
                    this.$message.success({
                      message: this.routeItem
                        ? this.routeLanguage.successMessage2
                        : this.routeLanguage.successMessage3,
                      customClass: "message-info-tip",
                    });
                    this.originalData();
                  })
                  .catch(() => {
                    this.saveCode = false;
                  });
              } else {
                this.saveCode = false;
                this.$message.warning({
                  message: this.routeLanguage.messageInfo5,
                  customClass: "message-info-tip",
                });
              }
            } else {
              if (validator) {
                for (const key in validator) {
                  this.$message.error({
                    message: validator[key][0].message,
                    customClass: "message-info-tip",
                  });
                  break;
                }
              }
              this.saveCode = false;
            }
          });
        }
      }
    },
    getLngLatData(lnglat, lnglat1, code) {
      let a = pointsConvert({ point: lnglat.point, type: lnglat.type });
      // let a = wgs84_to_gcj02(lnglat[0], lnglat[1]);
      if (a[0].toFixed(7) == lnglat1.lng && a[1].toFixed(7) == lnglat1.lat) {
        let data = {
          lat_int: parseInt(lnglat.point[1] * 1e7),
          lon_int: parseInt(lnglat.point[0] * 1e7),
        };
        if (code) {
          data.type = lnglat.type;
        }
        return data;
      } else {
        return {
          lat_int: parseInt(lnglat1.lat * 1e7),
          lon_int: parseInt(lnglat1.lng * 1e7),
        };
      }
    },
    //改变状态
    changePlanType(e) {
      if (e) {
        this.routeForm.plan_time = [];
      } else {
        this.routeForm.plan_time = "";
      }
    },
    //改变高程
    changeIntervalHeight() {
      this.$emit("changeIntervalHeight", this.routeForm.interval_height);
    },
    //获取航点的高度
    getPointHeight(index) {
      if (!this.mapType) {
        return false;
      }
      let points =
        this.$coordinateType == "gcj02"
          ? wgs84_to_gcj02(
              this.routeForm.point_json[index].lng,
              this.routeForm.point_json[index].lat
            )
          : [
              this.routeForm.point_json[index].lng,
              this.routeForm.point_json[index].lat,
            ];
      let changePoint = {
        index: index,
        lat: points[1],
        lng: points[0],
        height: this.routeForm.point_json[index].height,
      };
      this.$emit("changeMarker", changePoint);
    },
  },
  watch: {
    changePoint(val) {
      switch (val.type) {
        case "add":
          let path = {
            lat: val.lat,
            lng: val.lng,
            height: this.routeForm.default_height,
          };
          if (this.$coordinateType == "gcj02") {
            let point = gcj02_to_wgs84(path.lng, path.lat);
            path.lng = point[0].toFixed(7);
            path.lat = point[1].toFixed(7);
          }
          if (this.checkType.value == 20) {
            path.actionList = [];
          }
          this.routeForm.point_json.push(path);
          break;
        case "edit":
          let editPoint = {
            lng: val.lng,
            lat: val.lat,
          };
          if (this.$coordinateType == "gcj02") {
            let editPoint1 = gcj02_to_wgs84(editPoint.lng, editPoint.lat);
            editPoint.lng = editPoint1[0].toFixed(7);
            editPoint.lat = editPoint1[1].toFixed(7);
          }
          this.routeForm.point_json[val.index].lng = editPoint.lng;
          this.routeForm.point_json[val.index].lat = editPoint.lat;
          break;
        case "editAdd":
          let path1 = {
            lat: Number(val.lat),
            lng: Number(val.lng),
            height: this.routeForm.default_height,
          };
          if (this.$coordinateType == "gcj02") {
            let point1 = gcj02_to_wgs84(path1.lng, path1.lat);
            path1.lng = point1[0].toFixed(7);
            path1.lat = point1[1].toFixed(7);
          }
          if (this.checkType.value == 20) {
            path1.actionList = [];
          }
          this.routeForm.point_json.splice(val.index, 0, path1);
          break;
        case "remove":
          this.routeForm.point_json.splice(val.index, 1);
          this.pointCodes = this.pointCodes.filter((x) => x !== val.index + 1);
          if (this.routeForm.point_json.length == 0) {
            this.$emit("update:extra", false);
          }
          break;
        case "del":
          this.routeForm.point_json = [];
          this.pointCodes = [];
          this.$emit("update:extra", false);
          break;
        case "import":
          let index = this.routeForm.point_json.length;
          let path2 = {
            lat: this.pointListImport[index][1],
            lng: this.pointListImport[index][0],
            height:
              val.height || val.height == 0
                ? val.height
                : this.routeForm.default_height,
          };
          if (this.checkType.value == 20) {
            if (val.action && val.action.length > 0) {
              path2.actionList = val.action;
            } else {
              path2.actionList = [];
            }
          }

          this.routeForm.point_json.push(path2);
          break;
        default:
          break;
      }
    },
    "routeForm.point_json": {
      handler() {
        //存储数据
        this.$emit("deepCopy", "");
        this.routeCode = true;
      },
      deep: true,
    },
    "routeForm.auto_speed"(val) {
      this.$emit("changeSpeed", val);
    },
    "routeForm.showHeight"(val) {
      this.$emit("showHeightEvent", val);
    },
    "routeForm.isComputeHight"(val) {
      this.$emit("isComputeHightEvent", val);
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .routeEdit {
    .title {
      font-size: @zoomIndex * 18px !important;
      .el-button {
        font-size: @zoomIndex * 22px !important;
      }
    }
    .content-item-1 {
      margin-top: @zoomIndex * 10px !important;
      .el-form-item {
        .el-button {
          padding: @zoomIndex * 10px !important;
          font-size: @zoomIndex * 16px !important;
          letter-spacing: @zoomIndex * 2px !important;
        }
        .el-collapse {
          .el-collapse-item {
            .routeActionDiv {
              .el-button {
                font-size: @zoomIndex * 24px !important;
                padding-top: @zoomIndex * 6px !important;
              }
            }
          }
        }
      }
      .title {
        letter-spacing: @zoomIndex * 2px !important;
      }
      .saveBut {
        font-size: @zoomIndex * 18px !important;
        padding: @zoomIndex * 12px @zoomIndex * 20px !important;
        border-radius: @zoomIndex * 4px !important;
      }
    }
  }
}
.routeEdit {
  .title {
    // height: 4%;
    font-size: 18px;
    .el-button {
      font-size: 22px;

      padding: 0;
    }
  }
  .content-item-1 {
    width: 100%;
    // height: 94%;
    overflow-x: hidden;
    overflow-y: auto;
    margin-top: 10px;
    .el-form-item {
      width: 98%;
      .el-input {
        margin-top: 1%;
        width: 100%;
      }
      .el-button {
        width: 100%;
        padding: 10px;
        font-size: 16px;
        letter-spacing: 2px;
        font-weight: 550;
      }

      .slider-class-1 {
        width: 100%;
        display: inline-block;
        margin-left: 2%;
      }
      .el-select {
        width: 100%;
      }
      .select-date {
        width: 100%;
      }
      .el-collapse {
        width: 100%;
        .el-collapse-item {
          width: 100%;
          margin-top: 2%;
          div {
            margin-left: 1%;
          }
          .el-input-number {
            margin-left: 1%;
            width: 98%;
          }
          .addActionBtn {
            margin-top: 2%;
            margin-left: 1%;
            width: 98%;
          }
          .routeActionDiv {
            width: 100%;
            margin-left: 0 !important;
            margin-top: 1%;
            .el-select {
              width: 85%;
            }
            .el-button {
              margin-left: 2%;
              width: auto;
              font-size: 24px;
              padding: 0;
              padding-top: 6px;
            }
            .actionValueDiv {
              margin: 1%;
              margin-left: 2%;
              margin-right: 0;
              width: 98%;
              .el-input-number {
                width: 18%;
              }
            }
          }
        }
      }
    }
    .title {
      text-align: center;
      font-weight: 520;
      letter-spacing: 2px;
    }
    .saveBut {
      margin: 0.5%;
      width: 98%;
      font-weight: 600;
      font-size: 18px;
      padding: 12px 20px;
      border-radius: 4px;
    }
  }
}
</style>
<style lang="less">
.routeEdit {
  .content-item-1 {
    .el-form-item {
      .el-form-item__label {
        padding: 0 !important;
        line-height: 20px !important;
        font-size: 14px !important;
      }
      .el-form-item__content {
        line-height: 10px !important;
      }
      .el-input {
        font-size: 14px !important;
        .el-input__inner {
          border-radius: 4px !important;
          height: 40px !important;
          line-height: 40px !important;
          padding: 0 15px !important;
        }
      }
      .interval_height {
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        .el-input {
          width: 72%;
        }
      }
      .slider-class-1 {
        .el-slider__runway {
          height: 2px !important;
          margin: 0 !important;
          width: 79%;
          margin-top: 15px !important;
          .el-slider__bar {
            height: 2px !important;
            background-color: #0555ff !important;
          }
        }
        .el-slider__button-wrapper {
          width: 10px !important;
          height: 10px !important;
          top: -10px;
          .el-slider__button {
            width: 8px !important;
            height: 8px !important;
            border: 1px solid #0555ff !important;
          }
        }
        .el-slider__input,
        .el-input-number--small {
          width: 17% !important;
          line-height: 0 !important;
          margin-right: 5px !important;
          .el-input__inner {
            padding: 0 5px !important;
            height: 32px !important;
            line-height: 32px !important;
          }
        }
      }
      .silderInput {
        .sliderValue {
          .el-slider__runway {
            width: 100% !important;
          }
        }
      }
      .el-collapse {
        .el-collapse-item {
          .el-input {
            .el-input__inner {
              text-align: left !important;
              height: 40px !important;
            }
          }
          .el-collapse-item__header {
            padding-left: 5% !important;
            border-radius: 6px;
            width: 95% !important;
            letter-spacing: 1px !important;
            height: 48px !important;
            line-height: 48px !important;
            font-size: 13px !important;
            .el-collapse-item__arrow {
              // display: none !important;
              transform: rotate(90deg) !important;
            }
            .el-collapse-item__arrow.is-active {
              transform: rotate(0deg) !important;
            }
          }
          .el-collapse-item__wrap {
            width: 100% !important;

            .el-collapse-item__content {
              width: 100% !important;
              padding-bottom: 25px !important;
              font-size: 13px !important;
            }
          }
          .routeActionDiv {
            .actionValueDiv {
              width: 98%;
              .el-input-number {
                .el-input__inner {
                  padding: 0 5px !important;
                  text-align: center !important;
                }
              }
            }
          }
        }
      }
      .actionChoose {
        .el-input__suffix {
          right: 5px !important;
          .el-select__caret {
            font-size: 14px !important;
            width: 25px !important;
            line-height: 40px !important;
          }
        }
      }
      .select-date {
        .el-input__inner {
          font-size: 14px !important;
          padding: 0 30px !important;
        }
        .el-input__prefix {
          left: 5px !important;
        }
        .el-input__suffix {
          right: 5px !important;
        }
        .el-input__prefix,
        .el-input__suffix {
          .el-input__icon {
            width: 25px !important;
            line-height: 40px !important;
          }
        }
      }
    }
  }

  .el-loading-mask {
    .el-loading-spinner {
      top: 50% !important;
      font-size: 16px !important;
      .el-loading-text {
        font-size: 16px !important;
      }
      i {
        font-size: 30px !important;
      }
    }
  }
}
.selects {
  .el-select-dropdown__item {
    font-size: 14px !important;
    height: 34px !important;
    line-height: 34px !important;
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .routeEdit {
    .content-item-1 {
      .el-form-item {
        .el-form-item__label {
          line-height: @zoomIndex * 20px !important;
          font-size: @zoomIndex * 14px !important;
        }
        .el-form-item__content {
          line-height: @zoomIndex * 10px !important;
        }
        .el-input {
          font-size: @zoomIndex * 14px !important;
          .el-input__inner {
            border-radius: @zoomIndex * 4px !important;
            height: @zoomIndex * 40px !important;
            line-height: @zoomIndex * 40px !important;
            padding: 0 @zoomIndex * 15px !important;
          }
        }

        .slider-class-1 {
          .el-slider__runway {
            height: @zoomIndex * 2px !important;
            margin-top: @zoomIndex * 15px !important;
            .el-slider__bar {
              height: @zoomIndex * 2px !important;
            }
          }
          .el-slider__button-wrapper {
            width: @zoomIndex * 10px !important;
            height: @zoomIndex * 10px !important;
            // top: @zoomIndex * -10px !important;
            .el-slider__button {
              width: @zoomIndex * 8px !important;
              height: @zoomIndex * 8px !important;
              border: @zoomIndex * 1px solid #0555ff !important;
            }
          }
          .el-slider__input,
          .el-input-number--small {
            font-size: @zoomIndex * 14px !important;
            margin-right: @zoomIndex * 5px !important;
            .el-input__inner {
              padding: 0 @zoomIndex * 5px !important;
              height: @zoomIndex * 32px !important;
              line-height: @zoomIndex * 32px !important;
            }
          }
        }
        .el-collapse {
          .el-collapse-item {
            .el-collapse-item__header {
              border-radius: @zoomIndex * 6px !important;
              letter-spacing: @zoomIndex * 1px !important;
              height: @zoomIndex * 48px !important;
              line-height: @zoomIndex * 48px !important;
              font-size: @zoomIndex * 13px !important;
            }
            .el-collapse-item__wrap {
              .el-collapse-item__content {
                padding-bottom: @zoomIndex * 25px !important;
                font-size: @zoomIndex * 13px !important;
              }
            }
            .routeActionDiv {
              .actionValueDiv {
                .el-input-number {
                  .el-input__inner {
                    padding: 0 @zoomIndex * 5px !important;
                  }
                }
              }
            }
          }
        }
        .actionChoose {
          .el-input__suffix {
            right: @zoomIndex * 5px !important;
            .el-select__caret {
              font-size: @zoomIndex * 14px !important;
              width: @zoomIndex * 25px !important;
              line-height: @zoomIndex * 40px !important;
            }
          }
        }
        .select-date {
          .el-input__inner {
            font-size: @zoomIndex * 14px !important;
            padding: 0 @zoomIndex * 30px !important;
          }
          .el-input__prefix {
            left: @zoomIndex * 5px !important;
          }
          .el-input__suffix {
            right: @zoomIndex * 5px !important;
          }
          .el-input__prefix,
          .el-input__suffix {
            .el-input__icon {
              width: @zoomIndex * 25px !important;
              line-height: @zoomIndex * 40px !important;
            }
          }
        }
      }
    }

    .el-loading-mask {
      .el-loading-spinner {
        font-size: @zoomIndex * 16px !important;
        .el-loading-text {
          font-size: @zoomIndex * 16px !important;
        }
        i {
          font-size: @zoomIndex * 30px !important;
        }
      }
    }
  }
  .selects {
    .el-select-dropdown__item {
      font-size: @zoomIndex * 14px !important;
      height: @zoomIndex * 34px !important;
      line-height: @zoomIndex * 34px !important;
    }
  }
}
</style>
