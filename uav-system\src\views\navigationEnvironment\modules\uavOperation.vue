<!-- 无人机操作 -->
<template>
  <div class="">
    <put-away
      class="uav-operation"
      v-model="isOpen"
      :styles="{ bottom: 0, right: 0 }"
      :buttonStyle="buttonStyle"
      :tooltipText="language.title"
      ref="putAway"
    >
      <template v-slot:main>
        <div class="uav-operation-main">
          <!-- 头部 -->
          <div class="main-header">
            <div class="header-left mr15" @click="startTask('')">
              {{ language.executeTask }}
            </div>
            <!-- 拍照 -->
            <div class="header-cell mr15" @click="uavPhotograph">
              <img-icon
                name="video-stop-fff"
                hover="video-stop-hover"
                width="36"
              />
            </div>

            <!-- 录像 -->
            <div class="header-video mr15" @click="pictureRecording(null)">
              <div
                class="video-cell"
                :class="isVideo ? 'open-video-style' : ''"
              ></div>
            </div>
            <!--  -->
            <div class="" @click="cameraSet">
              <img-icon name="video-stop-fff3" width="28" />
            </div>
          </div>

          <!-- 中间 -->
          <div class="main-center">
            <!-- :class="item.key == uavOperateType ? 'select-center-style' : ''" -->
            <div
              class="center-cell"
              :style="item.style"
              v-for="(item, index) in uavOperateList"
              :key="index"
            >
              <el-button
                type="text"
                style="padding: 0; font-size: 12px; width: 100%; height: 100%"
                @click="cutUavEvent(item)"
              >
                {{ item.label }}
              </el-button>
            </div>

            <!-- :class="item.key == uavOperateType ? 'select-center-style' : ''" -->
            <div
              class="center-cell"
              :style="item.style"
              @click="cutUavEvent(item)"
              v-for="(item, index) in flyMode"
              :key="index + item.key"
            >
              <el-button
                type="text"
                style="padding: 0; font-size: 12px; width: 100%; height: 100%"
              >
                {{ item.label }}
              </el-button>
            </div>
          </div>

          <!-- 底部 -->
          <div class="main-footer">
            <!-- 左边 -->
            <virtual-joystick
              width="70"
              leftProgressHiehg="100px"
              @operation="uavOperation"
              @slidingInput="leftSlidingInput"
              @slidingUp="leftSlidingUp"
              @undo="leftUndo"
            >
              <template v-slot:circle-center>
                <div class="circle-center"></div>
              </template>
              <template v-slot:title>
                <span class="uav-left-title"> {{ language.uav }} </span>
              </template>
            </virtual-joystick>

            <!-- 右边圆 -->
            <virtual-joystick
              width="70"
              :leftSliding="false"
              leftProgressHiehg="100px"
              @operation="panTiltOperation"
              @slidingInput="rightSlidingInput"
              @undo="rightUndo"
              @slidingUp="rightSlidingUp"
              :interval="80"
              style="margin-right: 0px"
            >
              <template v-slot:circle-center>
                <div class="right-circle-center" @click="straighten">
                  {{ language.payTilt }}
                </div>
              </template>

              <template v-slot:bottom>
                <div class="cradle-head">
                  <el-button @click="straighten" size="mini">{{
                    language.center
                  }}</el-button>
                  <el-button
                    @click="keydownEvent({ keyCode: 221 }, true)"
                    size="mini"
                  >
                    {{ language.vertical }}
                  </el-button>
                </div>
              </template>
            </virtual-joystick>
          </div>
        </div>
      </template>

      <template v-slot:showContent>
        <div class="" style="color: #fff">{{ language.title }}</div>
      </template>
    </put-away>
    <!-- 相机配置 -->
    <camera-config ref="customDialog"></camera-config>
  </div>
</template>

<script>
import slidingBlock from "../components/slidingBlock.vue";

import imgIcon from "@/components/imgIcon/index";
import putAway from "../components/putAway.vue";
import virtualJoystick from "../components/virtualJoystick.vue";

import { createTooltip } from "@/components/infoTip/index.js";

import cameraConfig from "../components/cameraConfig/index.vue";
import { getRouteParam } from "@/utils/getRouteParam.js";
import requestHttp from "@/utils/api";
import { gcj02_to_wgs84, wgs84_to_gcj02 } from "../../../utils/wgs84_to_gcj02";
export default {
  components: {
    slidingBlock,
    imgIcon,
    putAway,
    virtualJoystick,
    cameraConfig,
  },
  props: {
    websocket: [Function, Object],
  },
  data() {
    return {
      isOpen: true,
      equipmentInfo: {
        sn_id: "",
        type: "",
      },

      buttonStyle: {
        left: "-24px",
        "border-top-left-radius": "5px",
        "border-bottom-left-radius": "5px",
      },
      isVideo: false, // 是否正在录制视频

      // 当前正在执行的操作
      isExecuteTask: false,
      uavOperateType: "suspend",
      singlePointCourseReversal: false,
      uavOperateList: [
        {
          label: "暂停",
          key: "suspend",
          clickEvent: this.uavPauseContinue,
          state: 1, // 1>暂停， 2>继续
        },
        {
          label: "恢复飞行",
          key: "keepOn",
          clickEvent: this.uavKeepOn,
        },
        {
          label: "返航",
          key: "courseReversal",
          clickEvent: this.uavCourseReversal,
          state: false,
        },
        {
          label: "降落",
          key: "descent",
          clickEvent: this.uavDescent,
        },
      ],
      flyMode: [
        // {
        //   label: "自动飞行",
        //   key: "autoFlight",
        //   clickEvent: this.uavAutoFlight,
        // },
        // {
        //   label: "指定飞行",
        //   key: "adviceFlight",
        //   clickEvent: this.uavAdviceFlight,
        // },
        // {
        //   label: "手动飞行",
        //   key: "manualFlight",
        //   clickEvent: this.uavManualFlight,
        // },
        {
          label: "起飞",
          key: "uavTakeOff",
          clickEvent: this.uavTakeOff,
        },
        {
          label: "解锁",
          key: "uavUnlocking",
          clickEvent: this.uavUnlocking,
        },
        {
          label: "闭锁",
          key: "atresia",
          clickEvent: this.uavShutting,
        },
        {
          label: "中断",
          key: "interrupt",
          clickEvent: this.interruptCourse,
        },
        {
          label: "开启二维码降落",
          key: "QRcodelanding",
          clickEvent: this.QRLanding,
        },
        {
          label: "关闭二维码降落",
          key: "closeQRcodelanding",
          clickEvent: this.closeQRLanding,
        },
        // {
        //   label:"下载",
        //   key:"downRoute",
        //   clickEvent:this.downRoute
        // }
      ],
      downData: [],
      heartbeatTime: null,
      cameraZoom: null,
      yuntaiValue: 1500,
      uavValue: 1500,

      frontBack: 1500, // 虚拟摇杆前后
      leftRight: 1500, // 虚拟摇杆左右
      riseFall: 1500, // 上升下降
      turnLeftRight: 1500, // 机身左转右转
      zoomTime: null,
      keydownTime: null,

      downState: false,
      num: 3,
      QRCode: "",
      timeLoop: "",
      from_cno: "",
      keydownTip: "",
      routeItemInfo: "",
      equipType: "",
      rain_fall: 0,
      wind_speed: 0,
    };
  },
  computed: {
    video_record_state() {
      return this.$store.state.equipment.staveTwoData.video_record_state;
    },
    flightCourse() {
      return this.$store.state.equipment.flightCourseInfo;
    },
    railInfo() {
      return this.$store.state.equipment.railInfo;
    },
    //飞机解锁闭锁状态
    flightArm() {
      return this.$store.state.equipment.staveThreeData.flight_arm;
    },
    // 飞行模式
    flightMode() {
      return this.$store.state.equipment.staveThreeData.flight_mode;
    },
    // 飞行状态
    flightStatus() {
      return this.$store.state.equipment.staveThreeData.flight_status;
    },
    sortId() {
      return this.$store.state.equipment.staveThreeData.sort_id;
    },
    // 起飞/返航
    laterTook() {
      return this.$store.state.equipment.laterTook;
    },
    // 飞行任务
    flightTask() {
      return this.$store.state.equipment.flightTask;
    },
    cameraZoomvalue() {
      return this.$store.state.equipment.staveTwoData.camera_zoom_value || 0;
    },
    maps() {
      return this.$store.state.equipment.maps;
    },
    leafletMaps() {
      return this.$store.state.equipment.leafletMaps;
    },
    language() {
      return this.$languagePackage.navigation.uavOperation;
    },
    uavOperate() {
      return this.language.uavOperateList;
    },
    keyboardOperate() {
      return this.$store.state.equipment.keyboardOperate;
    },
  },
  watch: {
    flightStatus: function (val) {
      if (val == 8) {
        this.$emit("openFlightStep", 1);
      }
    },
    video_record_state: function (val) {
      let message = "";

      if (val == 1) {
        this.isVideo = true;
        message = this.uavOperate.startVideo;
      } else if (val === 0 && this.isVideo) {
        this.isVideo = false;
        message = this.uavOperate.endVideo;
      }

      if (message) {
        this.$message({
          type: "success",
          message: message,
        });
      }
    },
    keyboardOperate(val) {
      if (val) {
        window.addEventListener("keydown", this.keydownEvent);
        window.addEventListener("keyup", this.keyupEvent);
      } else {
        this.downState = false;
        window.removeEventListener("keydown", this.keydownEvent);
        window.removeEventListener("keyup", this.keyupEvent);
        this.keydownTip = "";
      }
    },
  },

  beforeDestroy() {
    window.removeEventListener("keydown", this.keydownEvent);
    window.removeEventListener("keyup", this.keyupEvent);
  },
  created() {
    // console.log("时区：",new Date().getTimezoneOffset()/60)
    let query = this.$route.query || {};
    this.equipType = query.type;
    this.cameraZoom =
      this.$store.state.equipment.staveTwoData.camera_zoom_value || 1;
    if (this.equipType == 12) {
      this.flyMode.push({
        label: "急停",
        key: "crashStop",
        clickEvent: this.crashStop,
      });
    }

    for (let i = 0; i < this.uavOperateList.length; i++) {
      let item = this.uavOperateList[i];
      item.label = this.uavOperate[item.key];
    }
    for (let i = 0; i < this.flyMode.length; i++) {
      let item = this.flyMode[i];
      item.label = this.uavOperate[item.key];
    }
  },

  methods: {
    // 键盘松开
    keyupEvent: function (item) {
      console.log(item);
      let code = item.keyCode;
      // 相机云台
      let cameraList = [38, 40];

      // 虚拟遥感
      let virtualList = [65, 68, 87, 83, 67, 90, 81, 69];
      if (cameraList.indexOf(code) !== -1) {
        this.rightUndo();
      } else if (virtualList.indexOf(code) !== -1) {
        // 虚拟遥感定时发送
        // clearInterval(this.websocket.virtualTime);

        setTimeout(() => {
          this.virtualJoystickUp();
        }, 50);
      }

      clearInterval(this.keydownTime);
      this.keydownTime = null;
    },
    // 键盘按下事件
    keydownEvent: function (item, state) {
      let keyCode = item.keyCode;
      // 可按键数值
      let list = [
        65, 68, 87, 83, 67, 90, 81, 69, 48, 221, 38, 37, 39, 40, 189, 187,
      ];
      if (list.indexOf(keyCode) === -1) {
        clearInterval(this.keydownTime);
        return false;
      }
      //
      if (!this.keyboardOperate && !state) {
        this.keydownTip = "";
        this.downState = false;
        return;
      }
      if (!this.downState && !state) {
        if (!this.keydownTip) {
          this.keydownTip = 1;
          this.$confirm(this.uavOperate.keydownTip, this.uavOperate.tips, {
            confirmButtonText: this.uavOperate.sure,
            cancelButtonText: this.uavOperate.cancel,
            type: "warning",
          })
            .then(() => {
              this.downState = true;
            })
            .catch(() => {
              this.keydownTip = "";
            });
        }

        return;
      }

      // 虚拟遥感停止定时发送
      let virtualList = [65, 68, 87, 83, 67, 90, 81, 69, 38, 40];
      if (virtualList.indexOf(keyCode) != -1) {
        clearInterval(this.websocket.virtualTime);
      }

      if (this.keydownTime) {
        return false;
      }

      // 按下即触发
      this.keyboardOrder(keyCode);

      if (state) {
        return false;
      }

      // 按下每200毫秒
      this.keydownTime = setInterval(() => {
        this.keyboardOrder(keyCode);
      }, 200);
    },

    keyboardOrder: function (keyCode) {
      let value = 40;

      // 如果未赋值
      if (!this.cameraZoom) {
        this.cameraZoom = this.cameraZoomvalue;
      }
      switch (keyCode) {
        case 87: // 无人机向前 i
          this.frontBack -= 15;
          if (this.frontBack <= 1000) {
            this.frontBack = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 3,
              value: this.frontBack,
            },
            401
          );
          break;
        case 65: // 无人机左飞 j
          this.leftRight -= 15;
          if (this.leftRight <= 1000) {
            this.leftRight = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 7,
              value: this.leftRight,
            },
            401
          );
          break;
        case 83: // 无人机后退 k
          this.frontBack += 15;
          if (this.frontBack >= 2000) {
            this.frontBack = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 4,
              value: this.frontBack,
            },
            401
          );
          break;
        case 68: // 无人机右飞 d
          this.leftRight += 15;
          if (this.leftRight >= 2000) {
            this.leftRight = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 8,
              value: this.leftRight,
            },
            401
          );
          break;

        case 90: // 无人机机身左转 a
          this.turnLeftRight -= 15;
          if (this.turnLeftRight <= 1000) {
            this.turnLeftRight = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 5,
              value: this.turnLeftRight,
            },
            401
          );
          break;
        case 67: // 无人机机身右转 d
          this.turnLeftRight += 15;
          if (this.turnLeftRight >= 2000) {
            this.turnLeftRight = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 6,
              value: this.turnLeftRight,
            },
            401
          );
          break;
        case 81: // 无人机上升 w
          this.riseFall += 15;
          if (this.riseFall >= 2000) {
            this.riseFall = 2000;
          }

          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 1,
              value: this.riseFall,
            },
            401
          );
          break;
        case 69: // 无人机下降 s
          this.riseFall -= 15;
          if (this.riseFall <= 1000) {
            this.riseFall = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 2,
              value: this.riseFall,
            },
            401
          );
          break;

        case 48: // 镜头回正 0
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 1,
              value: 0,
            },
            401
          );
          break;
        case 221: // 镜头垂直 ]}
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 2,
              value: -90,
            },
            401
          );
          break;
        case 38: // 向前看 ↑
          this.yuntaiValue += value;
          if (this.yuntaiValue >= 2000) {
            this.yuntaiValue = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 3,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 37: // 向左看 ←
          this.yuntaiValue -= value;
          if (this.yuntaiValue <= 1000) {
            this.yuntaiValue = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 6,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 39: // 向右看 →
          this.yuntaiValue += value;
          if (this.yuntaiValue <= 2000) {
            this.yuntaiValue = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 7,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 40: // 向下看 ↓
          this.yuntaiValue -= value;
          if (this.yuntaiValue <= 1000) {
            this.yuntaiValue = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 4,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 189: // 镜头缩小 -
          let minZoom = this.cameraZoom <= 1 ? 1 : this.cameraZoom--;
          this.websocket.manualSend(
            {
              cmd_type: 3,
              zoom_multiple: minZoom,
            },
            402
          );
          break;
        case 187: // 镜头放大 +=
          let maxZoom = this.cameraZoom >= 30 ? 30 : this.cameraZoom++;
          this.websocket.manualSend(
            {
              cmd_type: 3,
              zoom_multiple: maxZoom,
            },
            402
          );
          break;
        default:
          console.log("不需要执行操作----------->");
          break;
      }
    },

    // 相机设置
    cameraSet: function () {
      this.$refs.customDialog.show();
    },

    close: function (state) {
      this.isOpen = state;
    },

    // 处理websocket获得数据时
    disposeData: function (msg_id, data) {
      // console.log(msg_id, data)
      switch (msg_id) {
        case 200: // 设备认证
          this.equipmentInfo = data;
          this.from_cno = data.cno;
          this.$store.commit("setFormCno", this.from_cno);
          break;
        case 207:
          this.from_cno = data.cno;
          this.$store.commit("setFormCno", this.from_cno);
          break;
        case 401:
          let type = data.cmd_type;
          // 飞行模式
          if (type == 4) {
            let item = this.uavOperateList[0];
            let actionValue = data.action_value;
            // 自动巡航
            if (actionValue == 3) {
              item.state = 1;
              item.label = this.uavOperate.suspend;
            } // 悬停
            else if (actionValue == 5) {
            } // 返航
            else if (actionValue == 6) {
              this.messageSuccess(this.uavOperate.beingShadowed);
              this.singlePointCourseReversal = true;

              let item = this.uavOperateList[0];
              if (item.state == 2) {
                item.state = 1;
                item.label = this.uavOperate.suspend;
              }
            }
          } // 航线上传
          else if (type == 8) {
            if (this.flightStatus == 2) {
              this.$emit("openRouteStep", "");
            } else {
              this.$emit("openFlightStep", 0);
              let item = this.uavOperateList[0];
              item.state = 1;
              item.label = this.uavOperate.suspend;
            }
            this.$store.commit("setFlightTask", true);
          } else if (type == 10) {
            if (this.num > 0) {
              if (this.timeLoop) {
                clearTimeout(this.timeLoop);
                this.timeLoop = "";
              }
              this.num = 3;
              this.$message.success(
                this.QRCode == 1
                  ? this.uavOperate.openQRSuccess
                  : this.uavOperate.closeQRSuccess
              );
            }
          }
          break;
        case 402: // 相机设置
          if (data.cmd_type == 1) {
            this.$message({
              type: "success",
              message: this.uavOperate.photographSuccss,
            });
          }
          break;
        case 427: // 悬停
          let item = this.uavOperateList[0];

          if (item.state == 1) {
            item.state = 2;
            item.label = this.uavOperate.continue;
          } else if (item.state == 2) {
            item.state = 1;
            item.label = this.uavOperate.suspend;
          }
          this.messageSuccess(item.label);
          break;
        case 423:
          this.messageSuccess(this.uavOperate.descent);
          break;
        case 424: // 执行任务
          // this.uavOperateList[0].state = 1;
          // this.uavOperateList[0].title = "暂停";
          // this.isExecuteTask = true;
          // this.messageSuccess("开始执行任务");
          break;
        case 401: // 云台控制
          console.log("云台控制----->", data);
          break;
        case 440:
          this.$emit("openFlightStep", 0);
          let item1 = this.uavOperateList[0];
          item1.state = 1;
          item1.label = this.uavOperate.suspend;
          break;
        case 415:
          let uavOperateItem = this.uavOperateList[0];
          uavOperateItem.state = 2;
          uavOperateItem.label = this.uavOperate.continue;
          this.$store.commit("setFlightTask", false);
          break;
        case 434:
          this.rain_fall = data.rain_fall;
          this.wind_speed = data.wind_speed;
          break;
        default:
          break;
      }
    },

    messageSuccess: function (msg) {
      this.$message({
        type: "success",
        message: msg,
      });
    },

    getDateTime: function () {
      let date = new Date();
      return date.getTime();
    },

    cutPutAwayState: function () {
      this.isOpen = !this.isOpen;
    },

    // 拍照
    uavPhotograph: function () {
      let uav = this.equipmentInfo.stream_uav_list;
      if (!uav) {
        return this.$message({
          message: this.uavOperate.photographError,
          type: "error",
        });
      }

      let data = {
        cmd_type: 1,
        cap_sort_id: this.sortId || "wk_003",
        cap_rtp_stream: uav[0],
        cap_timeout_sec: 10,
      };

      this.websocket.manualSend(data, 402);
      // this.websocket.manualSend(data, 220);
    },

    // 录像
    pictureRecording: function (ask_type) {
      let uav = this.equipmentInfo.stream_uav_list;
      if (!uav) {
        return this.$message({
          message: this.uavOperate.videoError,
          type: "error",
        });
      }
      if (this.isVideo) {
        let data = {
          cmd_type: 2,
          rec_rtp_stream: this.equipmentInfo.stream_uav_list[0], // 拉流地址
          rec_ask_type: ask_type || (this.isVideo ? 30 : 20), //
          sort_id: this.sortId,
          rec_sort_name: "",
        };
        this.websocket.manualSend(data, 402);
        return false;
      }
      createTooltip({
        message: this.uavOperate.pictureMsg,
        confirm: () => {
          let data = {
            cmd_type: 2,
            rec_rtp_stream: this.equipmentInfo.stream_uav_list[0], // 拉流地址
            rec_ask_type: ask_type || (this.isVideo ? 30 : 20), //
            sort_id: this.sortId,
            rec_sort_name: "",
          };
          this.websocket.manualSend(data, 402);
        },
      });
      // this.websocket.manualSend(data, 230);
    },

    // 开始任务
    startTask: async function (message, breakCode) {
      if (this.flightTask) {
        this.$message({
          type: "error",
          message: this.uavOperate.executeTaskHint,
        });
        return false;
      }
      if (this.rain_fall > 0) {
        this.$message({
          type: "error",
          message: this.uavOperate.executeTaskHint,
        });
        return false;
      }
      if (this.wind_speed > 3) {
        this.$message({
          type: "error",
          message: this.uavOperate.executeTaskHint,
        });
        return false;
      }
      if (this.$store.state.equipment.elevationcode) {
        this.$message({
          type: "error",
          message: this.uavOperate.elevationLoading,
        });
        return false;
      }
      let query = this.$route.query || {};
      let flightParam = sessionStorage.getItem("flightParam");
      let m_id = "";
      try {
        if (!flightParam) {
          return false;
        }
        let data = JSON.parse(flightParam)[query.sn_id];
        m_id = data.route.m_id;
      } catch (error) {
        console.error(error);
        return false;
      }
      if (!m_id) {
        this.$message({
          type: "error",
          message: this.uavOperate.notChooseRoute,
        });
        return false;
      }
      let params = {
        m_id: m_id,
      };
      params.pmd = params.m_id.toString();
      await requestHttp("missionInfo", params).then((res) => {
        this.routeItemInfo = res.data;
        if (res.data.break_json) {
          if (breakCode) {
            this.toPlaneTack(
              message,
              res.data.break_json,
              res.data.uploaded_pid
            );
          } else {
            this.$confirm(
              this.uavOperate.breakPointText,
              this.uavOperate.executeTip,
              {
                confirmButtonText: this.uavOperate.executeSubmit1,
                cancelButtonText: this.uavOperate.executeCancel,
                closeOnClickModal: false,
                closeOnPressEscape: false,
                type: "warning",
                showClose: true,
                distinguishCancelAndClose: true,
                // showCancelButton: false,
              }
            )
              .then(() => {
                this.executeTask(
                  message,
                  res.data.break_json,
                  res.data.uploaded_pid
                );
              })
              .catch((error) => {
                if (error === "cancel") {
                  this.executeTask(message);
                }
              });
          }
        } else {
          this.executeTask(message);
        }
      });
    },
    executeTask(message, breakPoint, uploaded_pid) {
      if (!breakPoint) {
        if (this.from_cno && this.from_cno.substr(1, 1) != 0) {
          this.$confirm(
            this.uavOperate.notExecute,
            this.uavOperate.executeTip,
            {
              confirmButtonText: this.uavOperate.executeSubmit,
              type: "warning",
              showCancelButton: false,
            }
          ).then(() => {});
          return false;
        }
      }
      let str =
        "<div style='color:#e6a23c'><i class='el-icon-warning'></i>" +
        this.uavOperate.performTip +
        "</div>";
      createTooltip({
        message: message || this.uavOperate.startTaskHint + str,
        confirm: () => {
          // 上传航线
          // let data = getRouteParam(this.flightCourse, this.railInfo, this.maps,this.downData);
          this.toPlaneTack(message, breakPoint, uploaded_pid);
        },
      });
    },
    toPlaneTack(message, breakPoint, uploaded_pid) {
      let data = getRouteParam(
        this.flightCourse,
        this.railInfo,
        this.maps,
        this.leafletMaps
      );
      let manySeq = 0;
      if (breakPoint) {
        if (uploaded_pid) {
          let i = data.waypoints.findIndex((point) => {
            return point.id == uploaded_pid;
          });
          if (i !== -1 && i > 0) {
            data.waypoints.splice(0, i);
            manySeq = 2;
          }
        }
        let breakData = JSON.parse(breakPoint);
        let waypoints_seq = data.waypoints.length + 3;
        for (let index = 0; index < data.waypoints.length; index++) {
          if (
            data.waypoints[index].action_list &&
            data.waypoints[index].action_list.length
          ) {
            waypoints_seq =
              waypoints_seq + data.waypoints[index].action_list.length;
          }
        }
        if (breakData.data.break_cmd_seq < waypoints_seq + manySeq) {
          let point = {
            altitude: breakData.data.altitude_relative,
            latitude: breakData.data.latitude,
            longitude: breakData.data.longitude,
            id: -1,
            action_list: [
              {
                action_id: "hover",
                param_list: [
                  {
                    param_id: "hovertime",
                    value: 1,
                  },
                ],
              },
            ],
          };
          if (breakData.data.break_cmd_seq - 1 < 4 + manySeq) {
            data.waypoints.unshift(point);
          } else {
            let n = 0;
            let lastNum = 0;
            for (let index = 0; index < data.waypoints.length; index++) {
              if (
                data.waypoints[index].action_list &&
                data.waypoints[index].action_list.length
              ) {
                lastNum =
                  1 + data.waypoints[index].action_list.length + lastNum;
              }
              if (breakData.data.break_cmd_seq - 4 - manySeq < lastNum) {
                n = index + 1;
                break;
              }
            }
            data.waypoints.splice(0, n, point);
          }
        }
      }
      let flightParam = sessionStorage.getItem("flightParam") || {};
      let query = this.$route.query || {};
      let params = JSON.parse(flightParam)[query.sn_id];
      data.uav_id = params.uav_id;
      data.standing_direction =
        !params.direction_angle && params.direction_angle !== 0
          ? -1
          : params.direction_angle;
      let userInfo = this.$store.state.user.userInfo;
      data.user_id = userInfo.u_id;
      data.user_name = userInfo.nick;
      data.cmd_type = 8;
      data.alternate_rtl_latitude = params.uav_point
        ? params.uav_point[1]
          ? params.uav_point[1]
          : 0
        : 0;
      data.alternate_rtl_longitude = params.uav_point
        ? params.uav_point[0]
          ? params.uav_point[0]
          : 0
        : 0;
      data.place_rtl_latitude = params.point[1] * 1e7;
      data.place_rtl_longitude = params.point[0] * 1e7;
      data.elevation_array = this.sendElevation();
      console.log("航线", data);
      this.websocket.manualSend(data, 401);
      let param1 = {
        m_id: params.route.m_id,
      };
      param1.pmd = param1.m_id.toString();
      requestHttp("clearBreak", param1).then((res) => {
        this.clearSaveBreak();
        this.$store.commit("setDelBreakMarkerCode", true);
        console.log("清除航线断点成功");
      });
      let closeFlightTrack = this.$store.state.equipment.closeFlightTrack;
      closeFlightTrack && closeFlightTrack();
    },
    sendElevation() {
      let routeItem = this.routeItemInfo;
      if (routeItem.cal_alt_json) {
        let cal_alt_json = JSON.parse(routeItem.cal_alt_json);
        if (!cal_alt_json.isComputeHight) {
          return [];
        }
        if (routeItem.break_json) {
          if (routeItem.uploaded_pid) {
            let point_list = routeItem.point_list;
            let i = point_list.findIndex((point) => {
              return point.id == routeItem.uploaded_pid;
            });
            if (i !== -1 && i > 0) {
              point_list.splice(0, i);
            }
            point_list.unshift(JSON.parse(breakPoint));
            let success = {};
            let elevationData = {};
            for (let index = 0; index < point_list.length - 1; index++) {
              success[index + 1] = false;
              let point1 = {};
              if (index == 0) {
                let point = wgs84_to_gcj02(
                  point_list[index].longitude / 1e7,
                  point_list[index].latitude / 1e7
                );
                point1 = { lat: point[1], lng: point[0] };
              } else {
                point1 = {
                  lat: point_list[index].lat_int / 1e7,
                  lng: point_list[index].lon_int / 1e7,
                };
              }
              let point2 = {
                lat: point_list[index + 1].lat_int / 1e7,
                lng: point_list[index + 1].lon_int / 1e7,
              };
              computerElevationPoints(
                point1,
                point2,
                120,
                this.progressFun,
                cal_alt_json.interval_height,
                this.maps,
                index + 1
              ).then((res) => {
                elevationData[index + 1] = res;
                success[index + 1] = true;
                let i = 0;
                for (const key in success) {
                  if (!success[index + 1]) {
                    i++;
                  }
                }
                if (!i) {
                  let arr = this.returnElevationArr(elevationData, true);
                  return arr;
                }
              });
            }
          }
        } else {
          let arr = this.returnElevationArr(cal_alt_json.elevationData);
          return arr;
        }
      } else {
        return [];
      }
    },
    returnElevationArr(elevationData, breakCode) {
      let arr = [];
      for (const key in elevationData) {
        let s_Point = breakCode && Number(key) == 1 ? -1 : Number(key);
        let obj = {
          s_point: s_Point,
          e_point: Number(key) + 1,
          height_arr: [],
          distance: elevationData[key].distance,
        };
        for (let index = 0; index < elevationData[key].arr.length; index++) {
          obj.height_arr.push(elevationData[key].arr[index].height);
        }
        arr.push(obj);
      }
      return arr;
    },
    clearSaveBreak() {
      let query = this.$route.query || {};
      let flightParam = sessionStorage.getItem("flightParam");
      try {
        if (!flightParam) {
          return false;
        }
        let data = JSON.parse(flightParam);
        let data1 = data[query.sn_id];
        // 航线
        // let j = data.route.findIndex((item) => item.m_id == query.m_id);
        // if (j !== -1) {
        //   data.route[j].break_json = "";
        //   sessionStorage.setItem("flightParam", JSON.stringify(data));
        // }
        data1.route.break_json = "";
        data[query.sn_id] = data1;
        sessionStorage.setItem("flightParam", JSON.stringify(data));
      } catch (error) {
        console.error("数据解析失败------>", error);
      }
    },
    // 无人机操作事件
    cutUavEvent: function (item) {
      // if (this.laterTook) {
      //   this.$message({
      //     type: "error",
      //     message: `${this.laterTook.hintText}`,
      //   });
      //   return false;
      // }
      this.uavOperateType = item.key;
      item.clickEvent(item);
    },

    // 暂停/继续
    uavPauseContinue: function (item) {
      if (!this.sortId) {
        return this.$message({
          type: "error",
          message: this.uavOperate.taskError,
        });
      }
      if (item.state == 1) {
        this.websocket.manualSend(
          {
            cmd_type: 4,
            flight_mode: 5,
          },
          401
        );
        // createTooltip({
        //   message: this.uavOperate.suspendHint,
        //   confirm: () => {
        //     this.websocket.manualSend(
        //       {
        //         cmd_type: 4,
        //         flight_mode: 5,
        //       },
        //       401
        //     );
        //   },
        // });
      } else if (item.state == 2) {
        createTooltip({
          message: this.uavOperate.continueHint,
          confirm: () => {
            this.startTask("", true);
            setTimeout(() => {
              let item = this.uavOperateList[0];
              item.state = 1;
              item.label = this.uavOperate.suspend;
              this.$store.commit("setFlightTask", true);
            }, 1000);
            // this.websocket.manualSend(
            //   {
            //     cmd_type: 4,
            //     flight_mode: 3,
            //   },
            //   401
            // );
          },
        });
      }
    },
    //恢复飞行
    uavKeepOn: function (item) {
      if (this.equipType == 12) {
        if (!(this.flightMode == 5 && this.formatState())) {
          return this.$message({
            type: "error",
            message: this.uavOperate.errorHint,
          });
        }
      } else {
        if (!(this.uavOperateList[0].state == 2 && this.formatState())) {
          return this.$message({
            type: "error",
            message: this.uavOperate.errorHint,
          });
        }
      }

      this.uavOperateList[0].state = 1;
      this.uavOperateList[0].label = this.uavOperate.suspend;
      createTooltip({
        message: this.uavOperate.continueHint1,
        confirm: () => {
          this.websocket.manualSend(
            {
              cmd_type: 4,
              flight_mode: 3,
            },
            401
          );
        },
      });
    },
    formatState() {
      if (this.flightStatus !== 4 && this.flightStatus !== 1) {
        if (this.flightArm) {
          return true;
        }
      }
      return false;
    },
    // 返航
    uavCourseReversal: function (item) {
      createTooltip({
        message: this.uavOperate.courseReversalHint,
        confirm: () => {
          this.websocket.manualSend(
            {
              cmd_type: 4,
              flight_mode: 6,
            },
            401
          );
        },
      });
    },

    // 降落
    uavDescent: function (item) {
      createTooltip({
        message: this.uavOperate.descentHint,
        confirm: () => {
          this.websocket.manualSend(
            {
              cmd_type: 4,
              flight_mode: 9,
            },
            401
          );
        },
      });
    },

    // 自动飞行
    uavAutoFlight: function (item) {
      createTooltip({
        message: this.uavOperate.uavAutoFlightHint,
        confirm: () => {
          this.websocket.manualSend({ cmd_type: 7 }, 401);
        },
      });
    },
    // 起飞
    uavTakeOff: function () {
      createTooltip({
        message: this.uavOperate.uavTakeOffHint,
        confirm: () => {
          this.websocket.manualSend(
            {
              cmd_type: 3,
              takeoff_height: 3.5,
            },
            401
          );
        },
      });
    },

    // 指点飞行
    uavAdviceFlight: function (item) {
      createTooltip({
        message: this.uavOperate.uavAdviceFlightHint,
        confirm: () => {
          this.websocket.manualSend({ flight_mode: 2 }, 462);
        },
      });
    },
    // 闭锁
    uavShutting: function () {
      createTooltip({
        message: this.uavOperate.uavShuttingHint,
        confirm: () => {
          this.websocket.manualSend({ cmd_type: 2, arm: false }, 401);
        },
      });
    },

    // 解锁
    uavUnlocking: function () {
      createTooltip({
        message: this.uavOperate.uavUnlockingHint,
        confirm: () => {
          this.websocket.manualSend({ cmd_type: 2, arm: true }, 401);
        },
      });
    },
    interruptCourse: function () {
      createTooltip({
        message: this.uavOperate.uavInterruptHint,
        confirm: () => {
          this.websocket.manualSend({ control_cmd: 3 }, 473);
        },
      });
    },
    //开启二维码识别降落
    QRLanding: function () {
      createTooltip({
        message: this.uavOperate.uavQRcodeLandHint,
        confirm: () => {
          this.websocket.manualSend(
            { cmd_type: 10, vision_landing: true },
            401
          );
          this.QRCode = 1;
          this.num = 3;
          this.subNum();

          // this.websocket.manualSend({ control_cmd: 3 }, 473);
        },
      });
    },
    //关闭二维码识别降落
    closeQRLanding: function () {
      createTooltip({
        message: this.uavOperate.uavcloseQRcodeLandHint,
        confirm: () => {
          this.websocket.manualSend(
            { cmd_type: 10, vision_landing: false },
            401
          );
          this.QRCode = 0;
          this.num = 3;
          this.subNum();
          // this.websocket.manualSend({ control_cmd: 3 }, 473);
        },
      });
    },
    //倒计时
    subNum() {
      if (this.num > 0) {
        this.timeLoop = setTimeout(() => {
          this.num = this.num - 1;
          this.subNum();
        }, 1000);
      } else {
        this.$message.error(
          this.QRCode == 1
            ? this.uavOperate.openQRError
            : this.uavOperate.closeQRError
        );
        if (this.timeLoop) {
          clearTimeout(this.timeLoop);
          this.timeLoop = "";
          // this.num = 3;
        }
      }
    },

    // 手动飞行
    uavManualFlight: function (item) {
      createTooltip({
        message: this.uavOperate.uavManualFlightHint,
        confirm: () => {
          this.websocket.manualSend({ flight_mode: 3 }, 462);
        },
      });
    },
    // 无人机
    uavOperation: function (item) {
      // 按下触发的键
      let down = {
        left: 65, // 向左
        right: 68, // 向右
        top: 87, // 向前
        bottom: 83, // 向下
      };
      this.keydownEvent(
        {
          keyCode: down[item],
        },
        true
      );
    },

    // 云台
    panTiltOperation: function (item) {
      // 按下触发的键
      let down = {
        left: 37, // 向左
        right: 39, // 向右
        top: 38, // 向前
        bottom: 40, // 向下
      };

      this.keydownEvent(
        {
          keyCode: down[item],
        },
        true
      );
    },

    // 左边园滚动条滑动时触发
    leftSlidingInput: function (key, val) {
      let keyCode = null;
      if (key == "bottom") {
        keyCode = val >= 50 ? 67 : 90;
      } else {
        keyCode = val >= 50 ? 81 : 69;
      }

      this.keydownEvent(
        {
          keyCode: keyCode,
        },
        true
      );
    },
    // 右边园滚动条滑动时触发
    rightSlidingInput: function (key, val) {
      let keyCode = null;
      // 变焦
      if (key == "right") {
        keyCode = val > 50 ? 187 : 189;
        this.keydownEvent(
          {
            keyCode: keyCode,
          },
          true
        );
      }
    },

    // 云台回正
    straighten: function () {
      this.websocket.manualSend(
        {
          cmd_type: 5,
          action_cmd: 1,
          value: 0,
        },
        401
      );
    },

    // 右侧松开
    rightUndo: function () {
      this.yuntaiValue = 1500;

      for (let i = 0; i < 5; i++) {
        this.websocket.manualSend(
          {
            cmd_type: 5,
            action_cmd: 5,
            value: 1500,
          },
          401
        );
      }
      this.websocket.virtualUp();
      // console.log("松开---------->");
    },
    // 右侧变焦
    rightSlidingUp: function () {
      this.zoomTime = null;
    },
    // 左侧控制
    leftSlidingUp: function (type) {
      // if (type == "left") {
      //   this.riseFall = 1500;
      // } else if (type == "bottom") {
      //   this.turnLeftRight = 1500;
      // }

      this.virtualJoystickUp();
    },
    // 虚拟摇杆左侧松开
    leftUndo: function (type) {
      // if (type == "top" || type == "bottom") {
      //   this.frontBack = 1500; // 虚拟摇杆前后
      // } else {
      //   this.leftRight = 1500; // 虚拟摇杆左右
      // }
      this.virtualJoystickUp();
    },

    // 虚拟摇杆松开,停止
    virtualJoystickUp: function () {
      this.riseFall = 1500;
      this.turnLeftRight = 1500;
      this.frontBack = 1500; // 虚拟摇杆前后
      this.leftRight = 1500; // 虚拟摇杆左右

      for (let i = 0; i < 5; i++) {
        this.websocket.manualSend(
          {
            cmd_type: 1,
            action_cmd: 9,
            value: 1500,
          },
          401
        );
      }

      this.websocket.virtualUp();
    },
    //下载航线
    // downRoute:function(){
    //   this.websocket.manualSend({cmd_type:7}, 401);

    // }
    //急停
    crashStop: function () {
      createTooltip({
        message: this.uavOperate.crashStopTip,
        confirm: () => {
          this.websocket.manualSend({ nest_action_cmd: 109 }, 403);
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .uav-operation {
    .uav-operation-main {
      border-radius: @radio * 5px !important;

      .main-header {
        height: @radio * 55px;
        .header-left {
          width: @radio * 84px !important;
          height: @radio * 28px !important;
          font-size: @radio * 10px !important;
          border-radius: @radio * 5px !important;
        }

        .header-video {
          width: @radio * 25px !important;
          height: @radio * 25px !important;
          .video-cell {
            width: @radio * 10px !important;
            height: @radio * 10px !important;
          }
        }
      }
      .main-center {
        padding: @radio * 12px @radio * 20.5px 0 @radio * 28.5px !important;

        .center-cell {
          width: @radio * 67px !important;
          height: @radio * 28px !important;
          border-radius: @radio * 5px !important;
          font-size: @radio * 12px !important;
          margin-right: @radio * 10px !important;
          margin-bottom: @radio * 12px !important;
        }
      }
      .main-footer {
        border-top-right-radius: @radio * 10px !important;
        border-top-left-radius: @radio * 10px !important;
        padding-top: @radio * 10px !important;
        .circle-center {
          width: @radio * 16px !important;
          height: @radio * 16px !important;
          margin: @radio * -8px 0 0 @radio * -8px !important;
        }
        .right-circle-center {
          width: @radio * 30px !important;
          height: @radio * 30px !important;
          margin: @radio * -15px 0 0 @radio * -15px !important;
          font-size: @radio * 12px !important;
        }
        .uav-left-title {
          font-size: @radio * 12px !important;
        }
        .cradle-head {
          margin-top: @radio * 16px !important;
          .el-button {
            padding: @radio * 5px @radio * 5px !important;
          }

          .head-img {
            width: @radio * 40px !important;
            height: @radio * 40px !important;
          }
        }
      }
    }
  }
}

.uav-operation {
  position: relative;
  height: 100%;
  .uav-operation-main {
    // background-color: rgba(0, 0, 0, 0.5);
    border-radius: 5px;
    height: 100%;
    overflow: hidden;
    .main-header {
      height: 55px;
      display: flex;
      align-items: center;
      justify-content: center;
      // background-image: linear-gradient(
      //   to bottom,
      //   rgb(86, 128, 156),
      //   rgba(86, 128, 156, 0.1)
      // );
      .header-left {
        width: 84px;
        height: 28px;
        // background-color: rgba(11, 88, 222, 1);
        // color: #fff;
        font-size: 10px;
        border-radius: 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .header-video {
        width: 25px;
        height: 25px;
        // border: 3px solid #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          border-color: #409eff;
        }
        .video-cell {
          width: 10px;
          height: 10px;
          // background-color: red;
          border-radius: 50%;
        }
        @keyframes identifier {
          0% {
            transform: scale(1);
            // background-color: aquamarine;
          }
          100% {
            transform: scale(2);
            // background-color: rgb(0, 238, 255);
          }
        }
        .open-video-style {
          // background-color: #1df50e;
          position: relative;
          &::after {
            content: "";
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0%;
            top: 0%;
            border-radius: 50%;
            // background-color: #1df50e;
            opacity: 0.5;
            animation-name: identifier;
            animation-duration: 2s;
            animation-iteration-count: infinite;
            animation-timing-function: linear;
          }
        }
      }
    }

    .main-center {
      // width: 100%;
      display: flex;
      flex-wrap: wrap;
      // padding-top: 12px;
      // justify-content: center;

      padding: 12px 28.5px 0 28.5px;

      .center-cell {
        width: 67px;
        height: 28px;
        border-radius: 5px;
        // background-color: rgba(78, 84, 82, 1);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        // color: #fff;
        margin-right: 12px;
        margin-bottom: 12px;
        cursor: pointer;
        .el-button {
          width: 100%;
          word-wrap: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          height: auto;
          overflow: hidden;
        }

        &:nth-child(3n) {
          margin-right: 0;
        }
      }
      .select-center-style {
        // color: rgba(83, 169, 243, 1);
        // color: #0b58de;
        // background-color: rgba(0, 0, 0, 0.7);
        // text-align: center;
      }
    }
    .main-footer {
      width: 100%;
      display: flex;
      justify-content: space-between;
      overflow: hidden;
      // background-image: linear-gradient(
      //   to bottom,
      //   rgba(4, 11, 43, 0.8),
      //   rgba(13, 134, 255, 0.5)

      // );
      border-top-right-radius: 10px;
      border-top-left-radius: 10px;
      padding-top: 10px;
      .circle-center {
        width: 16px;
        height: 16px;
        // background-color: rgba(27, 57, 86, 1);
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -8px 0 0 -8px;
      }
      .right-circle-center {
        width: 30px;
        height: 30px;
        // border: 1px solid rgba(27, 57, 86, 1);
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -15px 0 0 -15px;
        transform: rotate(-45deg);
        font-size: 12px;
        // background-color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        // color: rgba(76, 166, 255, 1);
        font-weight: 700;
      }
      .uav-left-title {
        font-size: 12px;
        font-weight: 700;
        // letter-spacing: 6px;
      }
      .cradle-head {
        display: flex;
        justify-content: center;
        margin-top: 16px;
        width: 100%;
        .el-button {
          padding: 5px 5px !important;
        }
        .head-link {
          &:hover {
            // color: #0b58de;
          }
        }
        .head-img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          // background-color: rgba(0, 0, 0, 1);
        }
      }
    }
  }
}
</style>
