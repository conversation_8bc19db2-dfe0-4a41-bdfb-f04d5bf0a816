<!-- 相机配置 -->
<template>
  <transition name="el-fade-in-linear">
    <div class="camera-config dialog" v-show="isShow">
      <div class="dialog-body" @click.stop>
        <div class="body-header">
          <!-- 一级导航 -->
          <div class="header-main" v-show="showHierarchy == 1">
            <div
              class="main-item"
              v-for="(item, index) in headerNav"
              :key="index"
              @click="cutShowNav(item, index)"
            >
              <!-- 图标 -->
              <img-icon
                :width="item.width"
                :name="navIndex == item.key ? item.selectIcon : item.icon"
                :style="item.iconStyle"
              />
              <!-- 底部线条 -->
              <div class="footer-line" v-show="navIndex == item.key"></div>
            </div>
          </div>

          <!-- 二级导航 -->
          <div class="secondary-navigation" v-show="showHierarchy == 2">
            <div class="nav-main" @click="returnSuperior">{{language.return}}</div>
          </div>

          <!-- 关闭按钮 -->
          <div class="shut-icon" @click="shut">
            <i class="el-icon-close"></i>
          </div>
        </div>

        <div class="body-content">
          <components :is="isShowComponents" :isShow="isShow"></components>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import imgIcon from "@/components/imgIcon/index.vue";
import more from "./more.vue";
import moreInside from "./moreInside.vue";
import patterns from "./pattern.vue";
import videos from "./video.vue";
import camera from "./camera.vue";
import rtkset from './rtkset'
export default {
  components: {
    imgIcon,
    more,
    moreInside,
    patterns,
    videos,
    camera,
    rtkset
  },
  name: "Dialog",
  data() {
    return {
      // isShowComponents: "patterns",

      isShow: false,
      navIndex: "patterns",
      headerNav: [
        {
          label: "基本设置",
          selectIcon: "camera-nav-1-s",
          icon: "camera-nav-1",
          width: 24,
          key: "patterns",
        },
        // {
        //   label: "拍照",
        //   selectIcon: "camera-nav-2-s",
        //   icon: "camera-nav-2",
        //   width: 24,
        //   key: "camera",
        // },
        {
          label: "录像",
          selectIcon: "camera-nav-3-s",
          icon: "camera-nav-3",
          width: 24,
          key: "videos",
        },
        {
          label: "更多",
          selectIcon: "camera-nav-4-s",
          icon: "camera-nav-4",
          width: 24,
          key: "more",
        },
        {
          label: "RTK设置",
          selectIcon: "camera-nav-5-s",
          icon: "camera-nav-5",
          width: 24,
          key: "rtkset",
        },
      ],
    };
  },
  computed: {
    isShowComponents() {
      return this.$store.state.camera.showComponents;
    },
    showHierarchy() {
      return this.$store.state.camera.hierarchy;
    },
    cameraType(){
      return this.$store.state.equipment.staveTwoData.camera_type;
    },
    language(){
      return this.$languagePackage.navigation.cameraConfig.more
    }
  },
  methods: {
    show: function () {
      this.isShow = true;
      this.$store.commit("setOpenCameraState", true);
      if (!this.isShowComponents) {
        this.$store.commit("setShowComponents", "patterns");
      }
    },
    shut: function () {
      this.isShow = false;
      this.$store.commit("setOpenCameraState", false);
    },
    cutShowNav: function (item, index) {
      this.navIndex = item.key;
      // this.isShowComponents = item.key;
      this.$store.commit("setShowComponents", item.key);
    },
    // 返回上级
    returnSuperior: function () {
      this.$store.commit("setShowComponents", "more");
      this.$store.commit("setHierarchy", 1);
    },
  },
};
</script>

<style lang="less" scoped>
.dialog {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 200;
  //   background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  .dialog-body {
    width: 700px;
    // height: 350px;
    border-radius: 5px;
    overflow: hidden;
    // background-color: rgba(0, 0, 0, 0.8);
    .body-header {
      padding: 0 50px;
      display: flex;
      position: relative;
      // background-color: #000000;
      // color: #fff;
      height: 60px;
      border-radius: 5px;
      .header-main {
        display: flex;
        height: calc(100% - 1px);
        width: 100%;
        align-items: center;
        // border-bottom: 1px solid #ccc;
        .main-item {
          width: 25%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          position: relative;
          .footer-line {
            width: 38px;
            height: 3px;
            border-radius: 2px;
            position: absolute;
            left: 50%;
            bottom: -2px;
            margin-left: -19px;
            // background-color: #398bcc;
          }
        }
      }

      .secondary-navigation {
        position: absolute;
        top: 50%;
        margin-top: -10px;
        right: 50px;
        height: 100%;
        .nav-main {
          // border: 1px solid #ccc;
          width: 70px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          // color: #fff;
          font-size: 12px;
          cursor: pointer;
          border-radius: 8px;
          &:hover {
            // color: #398bcc;
            // border-color: #398bcc;
          }
        }
      }

      .shut-icon {
        position: absolute;
        right: 20px;
        top: 20px;
        // border: 1px solid #ccc;
        width: 20px;
        height: 20px;
        cursor: pointer;
        border-radius: 2px;
        // color: #fff;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        &:hover {
          // color: #398bcc;
          // border-color: #398bcc;
        }
      }
    }
    .body-content {
      width: 100%;
      overflow: hidden;
      // overflow-y: auto;
      // height: 290px;
    }
  }
}
</style>