import 'element-ui/lib/theme-chalk/index.css'
import {
    Pagination,
    Dialog,
    Autocomplete,
    //   Dropdown,
    //   DropdownMenu,
    //   DropdownItem,
    //   Menu,
    //   Submenu,
    //   MenuItem,
    //   MenuItemGroup,
    Input,
    InputNumber,
    Radio,
    RadioGroup,
    RadioButton,
    Checkbox,
    CheckboxButton,
    CheckboxGroup,
    Switch,
    Select,
    Option,
    OptionGroup,
    Button,
    //   ButtonGroup,
    Table,
    TableColumn,
    DatePicker,
    //   TimeSelect,
    //   TimePicker,
    Popover,
    Tooltip,
    Drawer,
    //   Breadcrumb,
    //   BreadcrumbItem,
    Form,
    FormItem,
    //   Tabs,
    //   TabPane,
    Tag,
    //   Tree,
    //   Alert,
    Slider,
    Icon,
    Row,
    Col,
    Upload,
    Progress,
    //   Spinner,
    //   Badge,
    //   Card,
    //   Rate,
    //   Steps,
    //   Step,
    //   Carousel,
    //   CarouselItem,
    Collapse,
    CollapseItem,
    Cascader,
    //   ColorPicker,
    //   Transfer,
    Container,
    Header,
    Aside,
    Main,
    //   Footer,
    //   Timeline,
    TimelineItem,
    Link,
    Divider,
    Image,
    //   Calendar,
    //   Backtop,
    //   PageHeader,
    //   CascaderPanel,
    Loading,
    MessageBox,
    Message,
    Notification
} from 'element-ui';

const maps = {
    Dialog,
    Pagination,
    Input,
    InputNumber,
    Autocomplete,
    //   Dropdown,
    //   DropdownMenu,
    //   DropdownItem,
    //   Menu,
    //   Submenu,
    //   MenuItem,
    //   MenuItemGroup,
    //   InputNumber,
    Radio,
    RadioGroup,
    RadioButton,
    Checkbox,
    CheckboxButton,
    CheckboxGroup,
    Switch,
    Select,
    Option,
    OptionGroup,
    Button,
    //   ButtonGroup,
    Table,
    TableColumn,
    DatePicker,
    //   TimeSelect,
    //   TimePicker,
    Popover,
    Tooltip,
    Drawer,
    //   Breadcrumb,
    //   BreadcrumbItem,
    Form,
    FormItem,
    //   Tabs,
    //   TabPane,
    Tag,
    //   Tree,
    //   Alert,
    Slider,
    Icon,
    Row,
    Col,
    Upload,
    Progress,
    //   Spinner,
    //   Badge,
    //   Card,
    //   Rate,
    //   Steps,
    //   Step,
    //   Carousel,
    //   CarouselItem,
    Collapse,
    CollapseItem,
    Cascader,
    //   ColorPicker,
    //   Transfer,
    Container,
    Header,
    Aside,
    Main,
    //   Footer,
    //   Timeline,
    TimelineItem,
    Link,
    Divider,
    Image,
    //   Calendar,
    //   Backtop,
    //   PageHeader,
    //   CascaderPanel,
    // Loading,
    //   MessageBox,
    //   Message,
    //   Notification
}

export function initElement(vue) {
    Object.keys(maps).forEach(item => {
        vue.component(maps[item].name, maps[item])
    })

    vue.use(Loading.directive);
    // 全局使用
    vue.prototype.$loading = Loading.service;
    vue.prototype.$msgbox = MessageBox;
    vue.prototype.$alert = MessageBox.alert;
    vue.prototype.$confirm = MessageBox.confirm;
    vue.prototype.$prompt = MessageBox.prompt;
    vue.prototype.$notify = Notification;
    vue.prototype.$message = Message;
}