<template>
  <div class="collapseItemHeader">
    <div class="num">{{ index + 1 }}</div>
    <div class="image-box">
      <el-image :src="item.imgSrc" fit="contain"></el-image>
      <el-image
        class="disabledImg"
        v-if="item.state == 20"
        :src="disabledImg"
        fit="contain"
      ></el-image>
    </div>
    <div class="content-item-1">
      <el-tag :class="item.is_push_on ? 'firstDiv' : 'noneDiv noneDiv1'">{{
        item.is_push_on
          ? equipLanguage.equipInfo.equipStateIn
          : equipLanguage.equipInfo.equipStateOut
      }}</el-tag>
      <!-- <el-tag :class="item.is_pull_on ? 'secondDiv' : 'noneDiv'">{{
                item.is_pull_on
                  ? equipLanguage.equipInfo.personalStatus
                  : equipLanguage.equipInfo.personalStatusNo
              }}</el-tag> -->
      <button-popup :deviceItem="item" ref="buttonPopup"></button-popup>
    </div>
    <div class="content-item-2">
      <el-tag :class="item.state == 20 ? 'error' : ''">
        <span v-if="item.bind_com_info" style="margin-right: 16px">
          <span class="spans">{{ equipLanguage.equipInfo.belonging }}</span>
          {{ item.bind_com_info.name }}
        </span>
        <span>
          <span class="spans">{{ equipLanguage.equipInfo.equipName }}</span>
          {{ item.name }}
        </span>
      </el-tag>
      <el-tag :class="item.state == 20 ? 'error' : ''"
        ><span class="spans">{{ equipLanguage.equipInfo.equipNum }}</span
        >{{ item.sn_id }}</el-tag
      >
    </div>
    <div class="content-item-3">
      <div
        class="content-item-3-1"
        :class="item.state == 20 ? 'error' : ''"
        v-if="item.address ? true : false"
      >
        {{ equipLanguage.equipInfo.address }}：{{ item.address }}
      </div>
      <div
        class="task-data"
        v-if="
          item.task_list && item.task_list.list && item.task_list.list.length
        "
      >
        <div class="circle-point"></div>
        <div class="task-data-content">
          {{ equipLanguage.equipInfo.taskExecuted
          }}<span style="color: #33e933; padding: 0 5px">{{
            item.task_list.list.length
          }}</span
          >{{ equipLanguage.equipInfo.unit }}
        </div>
      </div>
    </div>
    <div class="operation">
      <el-button
        @click.stop="sendGetLog(item)"
        v-if="userInfo.phone == '18824860188' && !item.isAlone && checkOpen"
        :loading="getLogCode"
      >
        {{ getLogCode ? "正在上传" : equipLanguage.button.nestLog }}
      </el-button>
      <el-button @click.stop="judgeToVideo(item)" v-if="item.judgeVideo">
        {{ equipLanguage.button.outCabinVCR }}
      </el-button>

      <el-button
        @click.stop="setEquip(item)"
        :class="item.sn_id == sn_id ? 'active' : ''"
        v-show="!checkOpen"
      >
        <el-image
          v-show="item.sn_id === sn_id"
          :src="setImg1"
          fit="contain"
        ></el-image>
        <el-image
          v-show="item.sn_id !== sn_id"
          :src="setImg"
          fit="contain"
        ></el-image>
        {{ equipLanguage.button.setEquip }}
      </el-button>
      <el-button
        @click.stop="delEquip(item)"
        :class="delCode == item.sn_id ? 'active' : ''"
        v-show="!checkOpen"
      >
        <el-image
          :src="delCode == item.sn_id ? delImg1 : delImg"
          fit="contain"
        ></el-image>
        {{ equipLanguage.button.delEquip }}
      </el-button>
      <el-button
        v-if="!item.isAlone && checkOpen"
        @click.stop="openCalibration"
        class="firmware"
        >{{ equipLanguage.button.calibration }}</el-button
      >
      <el-popover
        placement="left-start"
        trigger="click"
        v-if="!item.isAlone && checkOpen && isShowData"
        popper-class="version-list-box"
      >
        <el-button slot="reference" class="firmware" @click.stop>{{
          equipLanguage.button.firmwareVersion
        }}</el-button>
        <div class="version-list">
          <div class="version-item" v-for="item in versionList" :key="item.id">
            <div class="label">{{ item.label }}</div>
            <div class="">
              {{ item.value ? formatVersion(item.value) : "" }}
            </div>
          </div>
        </div>
      </el-popover>
      <el-button
        v-if="!item.isAlone && checkOpen && !isShowData"
        class="firmware"
        @click.stop="clickTip"
        >{{ equipLanguage.button.firmwareVersion }}</el-button
      >
      <el-button
        :class="upgradeCode == item.sn_id ? 'active' : ''"
        v-show="item.type !== 200 && checkOpen"
        @click.stop="upgradeFirmware(item)"
        >{{ equipLanguage.button.Upgrade }}</el-button
      >
    </div>
  </div>
</template>
<script>
import buttonPopup from "./buttonPopup.vue";
import requestHttp from "@/utils/api";
export default {
  props: {
    index: {
      type: Number,
      default: 0,
    },
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    checkOpen: {
      type: String,
      default: "",
    },
    isShowData: {
      type: Boolean,
      default: false,
    },
    versionList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    buttonPopup,
  },
  data() {
    return {
      sn_id: "",
      delCode: "",
      upgradeCode: "",
      setImg: require("@/assets/img/setting.png"),
      setImg1: require("@/assets/img/seted.png"),
      delImg: require("@/assets/img/del.png"),
      delImg1: require("@/assets/img/deled.png"),
      disabledImg: require("@/assets/img/equipment/disabled.png"),
      getLogCode: false,
    };
  },

  computed: {
    equipLanguage() {
      return this.$languagePackage.equipment;
    },
    userInfo() {
      return this.$store.state.user.userInfo;
    },
  },
  created() {},
  methods: {
    //跳转到录像列表
    judgeToVideo(item) {
      this.$router.push({
        path: "/videoList",
        query: {
          sn_id: item.sn_id,
        },
      });
    },
    setEquip(item) {
      this.sn_id = item.sn_id;
      if (!item.is_pull_on) {
        this.$emit("operateEvent", "setEquip", item);
      } else {
        this.$message.warning({
          message: this.equipLanguage.noset,
          customClass: "message-info",
        });
      }
      setTimeout(() => {
        this.sn_id = "";
      }, 200);
    },
    //点击删除设备
    delEquip(item) {
      this.delCode = item.sn_id;
      if (!item.is_pull_on) {
        this.$confirm(this.equipLanguage.delTip, this.equipLanguage.tips, {
          confirmButtonText: this.equipLanguage.airportMap.sure,
          cancelButtonText: this.equipLanguage.airportMap.cancel,
          type: "warning",
          customClass: "messageTip",
        })
          .then(() => {
            let data = {
              sn_id: item.sn_id,
              type: item.type,
              state: 30,
              name: item.name,
              uav_type: item.uav_type,
              uav_sn: item.uav_sn,
              from_cor: item.from_cor,
            };
            if (item.description) {
              data.description = item.description;
            }
            if (item.bind_com_info) {
              data.bind_com_id = item.bind_com_info.id;
            }
            data.os_timestampCode = true;
            if (item.isStable) {
              data.address = item.address;
              data.lat_int = item.lat_int;
              data.lon_int = item.lon_int;
              data.direction_angle = item.direction_angle;
              data.pmd =
                data.sn_id.toString() +
                data.type.toString() +
                data.name +
                data.address +
                data.lat_int.toString() +
                data.lon_int.toString() +
                data.uav_type.toString() +
                data.uav_sn.toString() +
                data.state.toString() +
                data.from_cor.toString();
            } else {
              data.pmd =
                data.sn_id.toString() +
                data.type.toString() +
                data.name +
                data.uav_type.toString() +
                data.uav_sn.toString() +
                data.state.toString() +
                data.from_cor.toString();
            }
            if (data.bind_com_id) {
              data.pmd = data.pmd + data.bind_com_id.toString();
            }

            requestHttp("deviceEdit", data).then((res) => {
              this.$message({
                type: "success",
                message: this.equipLanguage.delSuccess,
                customClass: "message-info",
              });
              this.$emit("operateEvent", "getMentData");
            });
          })
          .catch(() => {
            this.$message.info({
              // type: "info",
              message: this.equipLanguage.cancelDel,
              customClass: "message-info",
            });
          })
          .finally(() => {
            this.delCode = "";
          });
      } else {
        this.$message.warning({
          message: this.equipLanguage.noDel,
          customClass: "message-info",
        });
        setTimeout(() => {
          this.delCode = "";
        }, 200);
      }
    },
    //打开传感器校准
    openCalibration() {
      this.$emit("operateEvent", "openCalibration");
    },
    //格式化版本信息
    formatVersion(num) {
      if (!num) {
        return "";
      }
      let version1 = parseInt(num / 1000);
      let version2 = parseInt((num % 1000) / 100);
      let version3 = parseInt(num % 100);
      return `V${version1}.${version2}.${version3}`;
    },
    //点击版本信息提示
    clickTip() {
      this.$message.info(this.equipLanguage.noVersionData);
    },
    //升级固件
    upgradeFirmware(item) {
      if (!item.is_push_on) {
        this.$message.error(this.equipLanguage.upgradeMsg.noUpgrade);
        return false;
      }
      this.upgradeCode = item.sn_id;
      this.$emit("operateEvent", "upgradeFirmware");
    },
    sendGetLog(item) {
      this.getLogCode = true;
      this.$emit("sendGetLog", item);
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .num {
    font-size: @zoomIndex * 16px !important;
  }
  .image-box {
    border-radius: @zoomIndex * 4px !important;
  }
  .content-item-1 {
    .el-tag {
      padding: @zoomIndex * 6px @zoomIndex * 10px !important;
      font-size: @zoomIndex * 12px !important;
      line-height: @zoomIndex * 16px !important;
    }
    .firstDiv {
      margin-bottom: @zoomIndex * 8px !important;
    }
    .noneDiv1 {
      margin-bottom: @zoomIndex * 8px !important;
    }
  }
  .content-item-2 {
    .el-tag {
      padding: @zoomIndex * 6px !important;
      padding-top: @zoomIndex * 12px !important;
      font-size: @zoomIndex * 14px !important;
      line-height: @zoomIndex * 16px !important;
    }
  }
  .content-item-3 {
    // margin-top: @zoomIndex * -30px !important;
    .content-item-3-1 {
      padding: 0 @zoomIndex * 6px !important;
      font-size: @zoomIndex * 16px !important;
      line-height: @zoomIndex * 30px !important;
    }
    .task-data {
      height: @zoomIndex * 30px !important;
      margin-left: @zoomIndex * 20px !important;
      .circle-point {
        width: @zoomIndex * 16px !important;
        height: @zoomIndex * 16px !important;
      }
      .task-data-content {
        padding: 0 @zoomIndex * 2px !important;
        font-size: @zoomIndex * 16px !important;
        // height: @zoomIndex * 26px !important;
        margin-left: @zoomIndex * 6px !important;
        // margin-bottom: @zoomIndex * -10px !important;
        // line-height: @zoomIndex * 24px !important;
      }
    }
  }
  .operation {
    .el-button {
      font-size: @zoomIndex * 16px !important;
      // letter-spacing: 12px;
      .el-image {
        width: @zoomIndex * 18px !important;
      }
    }
  }
}
.collapseItemHeader {
  width: 100%;
  display: flex;
  align-items: center;

  .num {
    margin-right: 2%;
    font-size: 16px;
    width: 20px;
  }
  .image-box {
    width: 8%;
    flex-shrink: 0; /*防止被压缩*/
    line-height: 0px;
    position: relative;
    .el-image {
      width: 100%;
      border: none;
      border-radius: 4px;
    }
    .disabledImg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      border: none;
    }
  }
  .content-item-1 {
    margin-left: 1.5%;
    .el-tag {
      padding: 6px 10px;
      font-size: 12px;
      display: block;
      border: none;
      height: auto;
      line-height: 16px;
    }
    .firstDiv {
      margin-bottom: 8px;
    }
    .noneDiv1 {
      margin-bottom: 8px;
    }
  }
  .content-item-2 {
    margin-left: 1%;
    width: 20%;
    text-align: left;
    .el-tag {
      padding: 6px;
      padding-top: 12px;
      font-size: 14px;
      display: block;
      border: none;
      height: auto;
      line-height: 16px;
      background-color: transparent;
      .spans {
        white-space: normal;
      }
    }
  }
  .content-item-3 {
    // width: 47%;
    margin-left: 2%;
    text-align: left;
    margin-right: auto;
    .content-item-3-1 {
      padding: 0 6px;
      border: none;
      font-size: 16px;
      line-height: 30px;
    }
    .task-data {
      display: flex;
      align-items: center;
      height: 30px;
      margin-left: 20px;
      .circle-point {
        width: 16px;
        height: 16px;
        background-color: #33e933;
        border-radius: 50%;
      }
      .task-data-content {
        padding: 0 3px;
        font-size: 16px;
        // height: 26px;
        margin-left: 6px;
        // margin-bottom: -10px;
        // line-height: 24px;
        color: #fff;
        // background-color: #3535cc;
        border: none;
      }
    }
  }
  .operation {
    margin-left: 1%;
    width: 19%;
    text-align: right;
    display: flex;
    justify-content: flex-end;

    .el-button {
      font-size: 16px;
      border: none;
      // margin-right: 4%;
      // letter-spacing: 12px;
      .el-image {
        width: 18px;
        border: none;
        margin-right: 20%;
        vertical-align: text-bottom;
      }
    }
    .firmware {
      text-align: left;
      padding-left: 0;
      padding-right: 24px;
      &:first-child {
        margin-right: 24px;
      }
    }
  }
}
</style>
<style lang="less">
.version-list-box {
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff;
  .version-list {
    .version-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px;
      .label {
        padding-right: 10px;
      }
    }
  }
  &.el-popper[x-placement^="left"] .popper__arrow {
    border-left-color: rgba(77, 75, 75, 0.788);
  }
  &.el-popper[x-placement^="left"] .popper__arrow::after {
    border-left-color: rgba(77, 75, 75, 0.788);
  }
}
.collapseItemHeader {
  .el-button.is-loading,
  .el-button.is-loading::before {
    background-color: transparent !important;
    color: #fff !important;
  }
}
</style>