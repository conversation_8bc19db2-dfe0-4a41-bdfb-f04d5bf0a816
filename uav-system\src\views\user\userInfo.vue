<!-- 用户信息 -->
<template>
  <custom-dialog
    :title="userLanguage.title"
    :isShow.sync="isShow"
    ref="userLayout"
    @submit="saveUserInfo"
    :submitLoading="isSave"
  >
    <template v-slot:main>
      <el-form
        label-width="120px"
        :model="form"
        :rules="rules"
        ref="userInfoForm"
      >
        <el-form-item :label="formLabel.nick">
          <el-input
            :placeholder="formPlaceholder.nick"
            readonly
            v-model="form.nick"
          />
        </el-form-item>
        <el-form-item :label="formLabel.account">
          <el-input
            :placeholder="formPlaceholder.account"
            readonly
            v-model="form.account"
          />
        </el-form-item>
        <el-form-item :label="formLabel.startTime">
          <el-input
            :placeholder="formPlaceholder.startTime"
            readonly
            v-model="form.start_tms"
          />
        </el-form-item>
        <el-form-item :label="formLabel.endTime">
          <el-input
            :placeholder="formPlaceholder.endTime"
            readonly
            v-model="form.end_tms"
          />
        </el-form-item>
      </el-form>
    </template>
  </custom-dialog>
</template>

<script>
import customDialog from "@/components/customDialog/index.vue";
import { getLocalStorage } from "@/utils/storage";
import { nowDate } from "@/utils/date";

export default {
  name: "userInfo",
  components: {
    customDialog,
  },
  data() {
    return {
      form: {
        nick: "",
        account: "",
        start_tms: "",
        end_tms: "",
      },
      rules: {},
      isSave: false,
      isShow: false,
    };
  },
  created() {
    console.log("this.$languagePackage--------->", this.$languagePackage);
  },
  computed: {
    userLanguage() {
      return this.$languagePackage.user.userInfo;
    },
    formLabel() {
      return this.userLanguage.label;
    },
    formPlaceholder() {
      return this.userLanguage.placeholder;
    },
  },
  methods: {
    saveUserInfo: function () {
      this.isSave = true;
      this.$refs.userInfoForm.validate((volid) => {
        if (volid) {
          return false;
        }
        this.isSave = false;
      });
    },
    open: function () {
      this.isShow = true;
      let user = getLocalStorage("userInfo");
      this.form.nick = user.nick;
      this.form.account = user.phone || user.email;
      this.userInfo = user;
      this.form.start_tms = nowDate(user.start_time).dateTime;

      this.form.end_tms =
        user.end_time == **********
          ? this.userLanguage.validity
          : nowDate(user.end_time).dateTime;

      this.$refs.userLayout.show();
    },
    shut: function () {
      this.isShow = false;
      this.$refs.userLayout.shut();
    },
  },
};
</script>