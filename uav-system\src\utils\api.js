import axiosService from '../utils/http'
import baseUrl from '../utils/global'
import apiInfo from '@/api/index'
// import md5 from '../../node_modules/js-md5'
import {
    getCookie
} from '@/utils/storage'
import {
    getCode
} from './rsa'
import Vue from 'vue'
//请求公参
export function publicParameter() {
    var time = new Date().getTime()
    var app_ids = 'com.nest.wk0Bate'
    var datas = {
        "app_type": "3",
        "app_ids": app_ids,
        "app_code": "0",
        "app_flavor": "cBinL",
        "os_timestamp": time,
        "os_language": Vue.prototype.$language == "english" ? "en" : 'zh',
        "token": getCookie("token") ? getCode(getCookie("token")) : ''
    }
    return datas
}
//post请求函数接口
const requestHttp = (name, data, datas1, fileUpload) => {
    let datas = ''
    let header = undefined
    datas = publicParameter()
    datas = Object.assign(datas, data)
    if (data && data.requestUrl) {
        if (!datas.pmd) {
            datas.pmd = md5(datas.os_timestamp + datas.token)
        } else {
            datas.pmd = md5(datas.os_timestamp + datas.token + datas.pmd)
        }
        delete datas.requestUrl
    } else {
        if (!datas.pmd) {
            datas.pmd = ''
        }

        // 自动拼接pmd，参数以逗号隔开
        if (data && data.jointPmd) {
            let pmds = datas.pmd.split(',');
            let pmd = "";
            for (let i = 0; i < pmds.length; i++) {
                pmd += String(datas[pmds[i]]) || "";
            }
            datas.pmd = pmd;
            delete datas.jointPmd; // 删除该参数
        }
        if (datas.os_timestampCode) {
            datas.pmd = md5(datas.os_timestamp + datas.token + datas.pmd.toString())

            delete datas.os_timestampCode
        } else {
            datas.pmd = md5(datas.token + datas.pmd)
        }
    }
    if (fileUpload) {
        let formData = new FormData();
        for (let k in datas) {
            formData.append(k, datas[k]);
        }
        datas = formData;
        header = {
            "Content-Type": "multipart/form-data;",
        }
    }
    let url = apiInfo[name]
    let base = data && data.requestUrl ? baseUrl.BASE_URL_FLY : baseUrl.BASE_URL
    return axiosService({
        url: base + url,
        method: 'post',
        data: datas,
        headers: header,
        cancelToken: datas1
    })
}
export default requestHttp