<!-- tab，可滚动 -->
<template>
  <div class="tab-scroll custom-tab-scroll">
    <div class="table-icon left-icon" @click="subtract" v-if="upDown">
      <el-button
        type="text"
        icon="el-icon-arrow-left"
        :disabled="selectIndex1 === 0"
      >
      </el-button>
    </div>

    <div
      class="tab-scroll-main"
      @mousedown="mouseDown"
      ref="scrollMain"
      :style="scrollMainStyle"
    >
      <ul class="tab-scroll-ul" ref="scrollUl">
        <li
          v-for="(item, index) in list"
          :key="index"
          @click="cutTabCell(item, index)"
          :class="selectIndex == index ? 'select-li' : ''"
          ref="scrollLi"
          :style="liStyle"
        >
          <slot name="content" :row="item" :index="index">
            {{ item[field.label] }}
          </slot>
        </li>
      </ul>
    </div>

    <div class="table-icon right-icon" @click="gagarin" v-if="upDown">
      <el-button
        type="text"
        icon="el-icon-arrow-right"
        :disabled="selectIndex1 === list.length - 1"
      >
      </el-button>
    </div>

    <div class="forbid" v-if="readonly"></div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    value: [String, Number],
    bisect: Boolean, // 平分，只有在列表数据不可以滚动时才会生效
    field: {
      // 字段对象
      type: Object,
      default: () => {
        return {
          label: "label", // 显示的值
          value: "value", // 绑定的值
        };
      },
    },
    // 是否显示选中上一个或者下一个
    upDown: {
      type: Boolean,
      default: true,
    },
    clickCut: {
      // 是否允许点击切换
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
        scollCode:{
          type:Boolean,default:false
        }
    
  },
  data() {
    return {
      selectIndex: 0,
      selectIndex1: 0,
      selectValue: this.value,
      isDown: false,
      mouserStartX: 0,
      scrollMainW: 0,
      ulLeft: 0,

      liStyle: {},
      isChangeEvent: false,
      subCode: false,
      addCode: false,
    };
  },
  watch: {
    selectIndex1: function (val) {
      if (val < 0) {
        return false;
      }
      // 判断是否被遮挡或者有下一个
      let mainW = this.$refs.scrollMain.offsetWidth;
      let ulW = this.$refs.scrollUl.offsetWidth;
      let ulL = this.$refs.scrollUl.offsetLeft;
      let liL = this.$refs.scrollLi[val].offsetLeft;
      let liW = this.$refs.scrollLi[val].offsetWidth;

      // 判断左侧是否被挡住
      if (ulL * -1 >= liL) {
        this.$refs.scrollUl.style.left = liL == 0 ? 0 : -(liL - 8) + "px";
      } else if (ulL * -1 + mainW <= liL + liW) {
        let left = ulL - liW;
        if (val == this.list.length - 1) {
          left = (ulW - mainW) * -1;
        }
        this.$refs.scrollUl.style.left = left + "px";
      }

      // let item = this.list[val];
      // this.selectValue = item[this.field.value];

      // this.$emit("input", this.selectValue);

      // // console.log("this.isChangeEvent------>", this.isChangeEvent);
      // if (!this.readonly && !this.isChangeEvent) {
      //   this.$emit("onChange", this.selectValue, item);
      // }

      setTimeout(() => {
        this.isChangeEvent = false;
      }, 10);
    },
    selectIndex: function (val) {
      if (val < 0) {
        return false;
      }
      if(!this.scollCode){
        return false
      }
      // 判断是否被遮挡或者有下一个
      let mainW = this.$refs.scrollMain.offsetWidth;
      let ulW = this.$refs.scrollUl.offsetWidth;
      let ulL = this.$refs.scrollUl.offsetLeft;
      let liL = this.$refs.scrollLi[val].offsetLeft;
      let liW = this.$refs.scrollLi[val].offsetWidth;

      // 判断左侧是否被挡住
      if (ulL * -1 >= liL) {
        this.$refs.scrollUl.style.left = liL == 0 ? 0 : -(liL - 8) + "px";
      } else if (ulL * -1 + mainW <= liL + liW) {
        let left = ulL - liW;
        if (val == this.list.length - 1) {
          left = (ulW - mainW) * -1;
        }
        this.$refs.scrollUl.style.left = left + "px";
      }

      // let item = this.list[val];
      // this.selectValue = item[this.field.value];

      // this.$emit("input", this.selectValue);

      // // console.log("this.isChangeEvent------>", this.isChangeEvent);
      // if (!this.readonly && !this.isChangeEvent) {
      //   this.$emit("onChange", this.selectValue, item);
      // }

      setTimeout(() => {
        this.isChangeEvent = false;
      }, 10);
    },
    value: function () {
      this.isChangeEvent = true;
      this.updateSelectIndex();
    },
  },
  computed: {
    scrollMainStyle() {
      return {
        width: this.upDown ? "calc(100% - 40px)" : "100%",
      };
    },
  },
  created() {
    this.updateSelectIndex(1);
  },
  mounted() {
    this.$nextTick(() => {
      let mainW = this.$refs.scrollMain.offsetWidth; // 最外成宽度
      let uiW = this.$refs.scrollUl.offsetWidth; // ui的宽度
      // 如果是平分并且不可以滚动
      if (this.bisect && mainW > uiW) {
        this.$refs.scrollUl.style.width = "calc(100% - 12px)";
        this.$set(this.liStyle, "flex-grow", 1);
      }
    });
  },
  methods: {
    updateSelectIndex: function (num) {
      if ((this.value || this.value === 0) && this.list.length > 0) {
        this.selectIndex = -1;
        if (num == 1) {
          this.selectIndex1 = -1;
        }

        for (let i = 0; i < this.list.length; i++) {
          if (this.value == this.list[i][this.field.value]) {
            this.selectIndex = i;
            if (num == 1) {
              this.selectIndex1 = i;
            }

            break;
          }
        }
      }
    },
    // 切换tba
    cutTabCell: function (item, index) {
      if (this.clickCut) {
        this.selectIndex = index;
        let item = this.list[index];
        this.selectValue = item[this.field.value];

        // this.$emit("input", this.selectValue);
        this.$emit("onChange", this.selectValue, item);
      }
    },
    // 减 1
    subtract: function () {
      if (this.selectIndex1 > 0) {
        let mainW = this.$refs.scrollMain.offsetWidth;
        let liW = this.$refs.scrollLi[0].offsetWidth;
        let a = parseInt(mainW / liW);
        if (this.selectIndex1 > this.list.length - a) {
          this.selectIndex1 = this.list.length - a - 1;
        } else {
          if (this.addCode) {
            this.selectIndex1 = this.selectIndex1 - a;
          } else {
            this.selectIndex1--;
          }
        }
        this.addCode = false;
        this.subCode = true;
      }
    },
    // 加 1
    gagarin: function () {
      if (this.selectIndex1 < this.list.length - 1) {
        let mainW = this.$refs.scrollMain.offsetWidth;
        let liW = this.$refs.scrollLi[0].offsetWidth;
        let a = parseInt(mainW / liW);
        if (this.selectIndex1 < a) {
          this.selectIndex1 = a;
        } else {
          if (this.subCode) {
            this.selectIndex1 = this.selectIndex1 + a;
          } else {
            this.selectIndex1++;
          }
        }
        this.addCode = true;
        this.subCode = false;
      }
    },
    // 鼠标按下
    mouseDown: function (event) {
      this.scrollMainW = this.$refs.scrollMain.offsetWidth; // 最外成宽度
      let uiW = this.$refs.scrollUl.offsetWidth; // ui的宽度
      if (this.scrollMainW >= uiW) {
        console.log("可移动的距离为0");
        return false;
      }

      this.isDown = true;
      this.mouserStartX = event.pageX; // 鼠标移动的位置
      this.ulLeft = this.$refs.scrollUl.offsetLeft; // 本移动的初始位置

      window.addEventListener("mousemove", this.mouseMove);
      window.addEventListener("mouseup", this.mouseUp);
    },
    // 鼠标移动
    mouseMove: function (event) {
      if (this.isDown) {
        let x = event.pageX - this.mouserStartX;

        let uiW = this.$refs.scrollUl.offsetWidth; // ui的宽度
        let maxMoveL = uiW - this.scrollMainW; // 可往左移动的最大值

        if (this.ulLeft + x >= 0) {
          this.$refs.scrollUl.style.left = "0px";
        } else if ((this.ulLeft + x) * -1 >= maxMoveL) {
          this.$refs.scrollUl.style.left = -maxMoveL + "px";
        } else {
          this.$refs.scrollUl.style.left = this.ulLeft + x + "px";
        }
      }
    },
    // 鼠标松开
    mouseUp: function () {
      if (this.isDown) {
        this.isDown = false;
        window.removeEventListener("mousemove", this.mouseMove);
        window.removeEventListener("mouseup", this.mouseUp);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.tab-scroll {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
  .forbid {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 30;
  }

  .tab-scroll-main {
    width: calc(100% - 40px);
    overflow: hidden;
    position: relative;
    .tab-scroll-ul {
      padding: 0 6px;
      margin: 0;
      height: 100%;
      display: flex;
      position: absolute;
      left: 0;
      top: 0;
      white-space: nowrap;
      li {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        list-style: none;
        padding: 0 6px;
        margin: 0;
        font-size: 12px;
        // color: #ffffff;
        cursor: pointer;
        position: relative;
      }
      .select-li {
        position: relative;
        // color: #398bcc;
        &::after {
          content: "";
          width: 0;
          height: 0;
          position: absolute;
          // border-left: 5px solid transparent;
          // border-right: 5px solid transparent;
          // border-bottom: 5px solid #398bcc;
          bottom: -5px;
          left: 50%;
          margin-left: -5px;
        }
      }
    }
  }

  .table-icon {
    width: 20px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    list-style: none;
    cursor: pointer;
    // color: #ffffff !important;
  }
  .left-icon {
    // border-right: 1px solid #ccc;
  }
  .right-icon {
    // border-left: 1px solid #ccc;
  }
}
</style>