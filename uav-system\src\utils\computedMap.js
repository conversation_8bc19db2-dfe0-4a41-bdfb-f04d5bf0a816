import { getLonAndLat } from './getCoordinate'
//高德地图的数学计算方法
export function computedMethod(value, param) {
    let { point1, point2, fence } = param;
    let result = "";
    switch (value) {
        case 1: //计算两点距离
            result = AMap.GeometryUtil.distance(point1, point2);
            break;
        case 2: //计算点是否在环内
            result = AMap.GeometryUtil.isPointInRing(point1, fence);
            break;
        case 3: //计算经纬度围成的环的面积
            result = AMap.GeometryUtil.ringArea(fence);
            break;
        case 4: //计算线段是否与环相交
            result = AMap.GeometryUtil.doesSegmentRingIntersect(
                point1,
                point2,
                fence
            );
            break;
        case 5: //计算线段是否与路径相交
            result = AMap.GeometryUtil.doesSegmentLineIntersect(
                point1,
                point2,
                fence
            );
            break;
        case 6: //计算区域面积
            result = AMap.GeometryUtil.ringArea(fence);
            break;
        default:
            break;
    }
    return result;
}
//经纬度计算中心点
export function computeCenter(lnglatArr) {
    var total = lnglatArr.length;
    var X = 0,
        Y = 0,
        Z = 0;
    lnglatArr.map((item) => {
        var lng = (item.lng * Math.PI) / 180;
        var lat = (item.lat * Math.PI) / 180;
        var x, y, z;
        x = Math.cos(lat) * Math.cos(lng);
        y = Math.cos(lat) * Math.sin(lng);
        z = Math.sin(lat);
        X += x;
        Y += y;
        Z += z;
    });
    X = X / total;
    Y = Y / total;
    Z = Z / total;
    var Lng = Math.atan2(Y, X);
    var Hyp = Math.sqrt(X * X + Y * Y);
    var Lat = Math.atan2(Z, Hyp);
    return new AMap.LngLat((Lng * 180) / Math.PI, (Lat * 180) / Math.PI);
}
//计算方位角
export function calcAngle(points, map) {
    let start = [points[0].lng, points[0].lat];
    let end = [points[1].lng, points[1].lat];
    var p_start = map.lngLatToContainer(start),
        p_end = map.lngLatToContainer(end);
    var diff_x = p_end.x - p_start.x,
        diff_y = p_end.y - p_start.y;
    let a = (360 * Math.atan2(diff_y, diff_x)) / (2 * Math.PI) + 90;
    if (a > 180) {
        a = -(360 - a);
    }
    return a;
}

//计算两点间的距离是否大于5千米
export function computedDistance(point_1, point_2) {
    let distance = computedMethod(1, {
        point1: point_1,
        point2: point_2,
    });
    if (distance > 5000) {
        return false;
    } else return true;
}