import Vue from 'vue'
// import Router from 'vue-router'
import "@/assets/js/vue-router.min.js"

Vue.use(VueRouter)

import layout from "@/views/layout/index"
const router = new VueRouter({
  mode: "history",
  // mode: "hash",
  base: process.env.NODE_ENV == "production" ? 'nest' : '',
  routes: [{
      path: '/',
      name: 'login',
      component: () =>
        import('@/views/login/index'),
    },
    {
      path: '/dataAnalyse',
      name: 'dataAnalyse',
      component: () =>
        import('@/views/achievementAdmin/dataAnalyse')
    },
    {
      path: "/layout",
      name: "layout",
      component: layout,
      children: [{
          path: "/home",
          name: "home",
          component: () =>
            import("@/views/home/<USER>"),
        },
        {
          path: '/routeReplay',
          name: 'routeReplay',
          component: () =>
            import('@/views/routeReplay/index.vue'),
          meta: {
            title: '成果回放'
          }
        },
        {
          path: '/videoList',
          name: 'videoList',
          meta: {
            title: '舱外录像列表'
          },
          component: () =>
            import('@/views/videoList/index')
        },
      ]
    },
    {
      path: '/panorama',
      name: 'panorama',
      component: () =>
        import('@/components/panorama/index')
    },

  ]
})

export default router
