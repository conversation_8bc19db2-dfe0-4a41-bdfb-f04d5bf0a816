const home = {

    index: {
        state: {
            1: "Online",
            2: "Offline",
            3: "Connected"
        }
    },
    // 无人机设备列表
    uavList: {
        title: "Drone\xa0\xa0List",
        tab: {
            AutomaticAirport: "Auto Airport",
            solos: "Solos"
        },
        state: {
            1: "Online",
            2: "Offline",
            3: "Connected"
        }
    },

    // 巡检记录
    inspectionRecord: {
        title: "Inspection\xa0\xa0Record",
        type: {
            total: "Total Degree",
            day: "Today's Date"
        },
        prpo: {
            flightLog: "Flight log playback",
            achievement: "achievements"
        },
        time: {
            start: "StartTime",
            end: "EndTime"
        }
    },

    // 数据总览
    dataOverview: {
        time: {
            title: "Total Inspection Times"
        },
        mileage: {
            title: "Total Inspection Mileage(KM)"
        },
        achievement: {
            title: "Inspection Results"
        },
        lastMonthTotal: "This Month"
    },

    // 巡检排行榜
    rankingList: {
        title: "Inspection Ranking List",
        tab: {
            10: "Attendance",
            20: "Mileage",
            30: "Achievement"
        }
    },
    //定时任务
    taskList: {
        title: 'Timed Task List',
        taskType: [
            { label: 'Task to be executed', value: 1 },
            { label: 'Successfully executed task', value: 10 },

        ],
        column: [
            { label: "Route Name", prop: "m_title" },
            { label: "SN_ID", prop: "sn_id" },
            { label: "Execution Time", prop: "task_tms", width: "100" }
        ],
    },

    // 飞行数据
    flightData: {
        title: "Flight\xa0\xa0Data",
        pie: {
            1: "Common alarm",
            2: "Alarms to be handled",
            3: "Handled alarms"
        },
        list: {
            time: "Time",
            level: "Rank",
            stave: "Handled state"
        }
    }
}

export default home;