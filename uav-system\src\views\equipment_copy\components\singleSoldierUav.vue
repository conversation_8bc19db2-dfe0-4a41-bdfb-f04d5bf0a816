<template>
  <div class="singleSoldierUav">
    <div class="content-item-1">
      <div class="content-item-title">{{ equipLanguage.uavStatus.title }}</div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.lng }}
          <div class="content-item-data">
            {{ uavItemList ? uavItemList.longitude / 1e7 : 113.1235682 }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.lat }}
          <div class="content-item-data">
            {{ uavItemList ? uavItemList.latitude / 1e7 : 22.1235682 }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.rtkStatus }}
          <div class="content-item-data">{{ rtkStatus }}</div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.satellitesNum }}
          <div class="content-item-data">
            {{ uavItemList ? uavItemList.numsv : 0 }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.electricity }}
          <div class="content-item-data">
            {{ uavItemList ? uavItemList.battery_remaining + "%" : 0 }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.voltage }}
          <div class="content-item-data">
            {{ uavItemList ? uavItemList.voltage.toFixed(2) : 0 }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.uavState }}
          <div class="content-item-data">
            {{
              uavItemList
                ? flightStateList[uavItemList.flight_status]
                : flightStateList[0]
            }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.flightModel }}
          <div class="content-item-data">
            {{
              uavItemList
                ? flightModeList[uavItemList.flight_mode]
                : flightModeList[2]
            }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.height }}
          <div class="content-item-data">
            {{ uavItemList ? (uavItemList.altitude / 1000).toFixed(2) : 0 }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.yaw }}
          <div class="content-item-data">
            {{ uavItemList ? (uavItemList.attitude_yaw / 100).toFixed(2) : 0 }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.roll }}
          <div class="content-item-data">
            {{ uavItemList ? (uavItemList.attitude_roll / 100).toFixed(2) : 0 }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.pitch }}
          <div class="content-item-data">
            {{
              uavItemList ? (uavItemList.attitude_pitch / 100).toFixed(2) : 0
            }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.hSpeed }}
          <div class="content-item-data">
            {{ uavItemList ? uavItemList.hSpeed.toFixed(2) : 0 }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.uavStatus.vSpeed }}
          <div class="content-item-data">
            {{ uavItemList ? uavItemList.vSpeed.toFixed(2) : 0 }}
          </div>
        </div>
      </div>
    </div>
    <div class="content-item-2">
      <live-video
        v-if="deviceItem && equipState"
        :title="equipLanguage.uav"
        :videoId="'uavVideo'"
        :url="deviceItem.stream_uav_list ? deviceItem.stream_uav_list[0] : ''"
        :autoPlay="true"
        :isFill="true"
      ></live-video>
      <div v-if="deviceItem && !equipState" class="videoOutTip">
        <div class="titleTip">{{ equipLanguage.equipInfo.equipStateOut1 }}</div>
      </div>
    </div>
    <div class="content-item-3">
      <div class="content-item-title">
        {{ equipLanguage.holderOperation.title }}
      </div>
      <div class="ballHead1">
        <div class="ballHead">
          <disk-div
            @getParams="contolsDisk"
            :equipLanguage="equipLanguage"
          ></disk-div>
        </div>
      </div>
      <div class="operationBtn">
        <!-- <el-button @click="operationClick('up')">垂直向上</el-button> -->
        <el-button
          @click="operationClick('center')"
          :class="clickCodeOperation == 'center' ? 'active' : ''"
          >{{ equipLanguage.holderOperation.center }}</el-button
        >
        <el-button
          @click="operationClick('down')"
          :class="clickCodeOperation == 'down' ? 'active' : ''"
          >{{ equipLanguage.holderOperation.down }}</el-button
        >
      </div>
      <div class="zoomlens">
        {{ equipLanguage.holderOperation.zoom }}
        <el-input-number
          v-model="zoomValue"
          size="medium"
          :min="1"
          :max="30"
          :precision="0"
          @change="changeZoomLens"
        ></el-input-number>
      </div>
      <div class="content-item-title">
        {{ equipLanguage.cameraParams.title }}
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.cameraParams.ISO }}
          <div class="content-item-data">
            {{ cameraList ? cameraList.camera_iso : 100 }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.cameraParams.shutter }}
          <div class="content-item-data">
            {{
              cameraList
                ? camera_shutter[cameraList.camera_shutter]
                  ? camera_shutter[cameraList.camera_shutter]
                  : cameraList.camera_shutter
                : camera_shutter[99]
            }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.cameraParams.expModel }}
          <div class="content-item-data">
            {{
              cameraList
                ? cameraExpModeList[cameraList.camera_exp_mode]
                : cameraExpModeList[0]
            }}
          </div>
        </div>
        <div class="content-item-div">
          {{ equipLanguage.cameraParams.expValue }}
          <div class="content-item-data">
            {{ camera_exp_value }}
          </div>
        </div>
      </div>
      <div class="content-item-1-1">
        <div class="content-item-div">
          {{ equipLanguage.cameraParams.awb }}
          <div class="content-item-data">
            {{ cameraList ? awb[cameraList.camera_awb] : awb[0] }}
          </div>
        </div>
      </div>
      <!-- <div class="content-item-1-1">
        <div class="content-item-div">
          <el-image :src="photoImg" style="width:25%" v-show="!chooseImgCode" @mouseout="chooseImgCode=false" @mouseover="chooseImgCode=true"></el-image>
          <el-image :src="photoImg1" style="width:25%" v-show="chooseImgCode"  @mouseout="chooseImgCode=false" @mouseover="chooseImgCode=true" @click="photoClick"></el-image>
        </div>
        <div class="content-item-div">
          <div class="videotape-1" :class="chooseVideotapeCode?'videotape-active':''" @mouseout="chooseVideotapeCode=false" @mouseover="chooseVideotapeCode=true" >
            <div class="videotape-1-1"></div>
          </div>
        </div>
      </div> -->
      <!-- <div class="moreSet" @click="setClick">
        {{ equipLanguage.cameraParams.moreSet }}
      </div>
      <el-drawer
        :visible.sync="drawerCode"
        :append-to-body="false"
        :modal="false"
        size="100%"
        :show-close="false"
      >
        <template slot="title">
          <div>
            <i
              class="el-icon-arrow-left iconBack"
              @click="drawerCode = false"
            ></i>
            {{ equipLanguage.cameraParams.title }}
          </div>
        </template>
        <camera-set
          :cameraList="cameraList"
          :websocket1="websocket1"
          :equipLanguage="equipLanguage"
        ></camera-set>
      </el-drawer> -->
    </div>
  </div>
</template>
<script>
import liveVideo from "@/components/video/webrtcVideoHttps.vue";
import diskDiv from "./disk.vue";
import cameraSet from "../components/cameraSet/cameraSet.vue";
export default {
  name: "singleSoldierUav",
  props: {
    deviceItem: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    cameraList: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    uavItemList: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    websocket1: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    equipState: {
      type: Boolean,
      default: false,
    },
    equipLanguage: {
      type: [Object, String],
      default() {
        return {};
      },
    },
  },
  components: {
    liveVideo,
    diskDiv,
    cameraSet,
  },
  data() {
    return {
      flightModeList: {},
      flightStateList: {},
      rtkStatusList: [],
      rtkStatus: "",
      zoomValue: 1,
      cameraExpModeList: {
        0: "AUTO",
        1: "M",
      },
      expValueList: {
        0: "-5.0",
        1: "-4.5",
        2: "-4.0",
        3: "-3.5",
        4: "-3.0",
        5: "-2.5",
        6: "-2.0",
        7: "-1.5",
        8: "-1.0",
        9: "-0.5",
        10: "0.0",
        11: "0.5",
        12: "1.0",
        13: "1.5",
        14: "2.0",
        15: "2.5",
        16: "3.0",
        17: "3.5",
        18: "4.0",
        19: "4.5",
        20: "5.0",
      },
      camera_shutter: {
        99: "1/10000",
        199: "1/5000",
        333: "1/3000",
        399: "1/2500",
        500: "1/2000",
        555: "1/1800",
        666: "1/1500",
        833: "1/1200",
        1000: "1/1000",
        2000: "1/500",
        4000: "1/250",
        5000: "1/200",
        8000: "1/125",
        10000: "1/100",
        20000: "1/500",
        33333: "1/300",
        40000: "1/25",
        50000: "1/20",
        66666: "1/15",
        76923: "1/13",
        100000: "1/10",
        200000: "1/5",
        500000: "1/2",
        1000000: "1.0",
        2000000: "2.0",
      },
      awb: [],
      clickCodeOperation: "",
      drawerCode: false,
      chooseImgCode: false,
      sortId: "",
      chooseVideotapeCode: false,
      photoImg: require("../../../assets/icon/video-stop-fff.png"),
      photoImg1: require("../../../assets/icon/video-stop-hover.png"),
    };
  },
  computed: {
    camera_exp_value() {
      if (this.cameraList) {
        let value = (86 - this.cameraList.camera_exp_value) / 10;
        return value;
      } else {
        return 0;
      }
    },
  },
  created() {
    this.flightModeList = this.equipLanguage.flightModeList;
    this.flightStateList = this.equipLanguage.flightStateList;
    this.rtkStatus = this.equipLanguage.rtkStatusList[0];
    this.awb = this.equipLanguage.awb;
  },
  // mounted(){
  //   setTimeout(()=>{
  //     console.log(this.deviceItem)

  //   },200)

  // },
  methods: {
    changeZoomLens(val) {
      this.websocket1.manualSend({ cmd_type: 3, zoom_multiple: val }, 402);
    },
    contolsDisk(val) {
      this.websocket1.manualSend(val, 401);
    },
    setClick() {
      this.drawerCode = true;
    },
    operationClick(id) {
      if (!this.clickCodeOperation) {
        this.clickCodeOperation = id;
        let params = {
          cmd_type: 5,
          action_cmd: 1,
          value: 0,
        };
        if (id == "center") {
          params = {
            cmd_type: 5,
            action_cmd: 1,
            value: 0,
          };
        } else if (id == "down") {
          params = {
            cmd_type: 5,
            action_cmd: 2,
            value: -90,
          };
        }
        this.websocket1.manualSend(params, 401);
        setTimeout(() => {
          this.clickCodeOperation = "";
        }, 200);
      }
    },
    //   photoClick(){
    //     let stream_uav=this.deviceItem.stream_uav_list
    //     // console.log(this.deviceItem)
    //     if (!stream_uav) {
    //       return this.$message({
    //         message: "拍照失败（流地址错误）",
    //         type: "error",
    //       });
    //     }
    //     let data = {
    //       sort_id: this.deviceItem.sn_id,
    //       sort_name: "",
    //       rtp_stream: stream_uav[0],
    //       timeout_sec: 10,
    //     };
    //     this.websocket1.manualSend(data, 404);
    //     // this.websocket1.manualSend(data, 220);
    //   }
  },
  watch: {
    "uavItemList.rtk_status"(val) {
      if (val < 0) {
        this.rtkStatus = this.equipLanguage.rtkStatusList[0];
      } else if (val < 3) {
        this.rtkStatus = this.equipLanguage.rtkStatusList[1];
      } else if (val === 3) {
        this.rtkStatus = this.equipLanguage.rtkStatusList[2];
      } else if (val === 4 || val === 5) {
        this.rtkStatus = this.equipLanguage.rtkStatusList[3];
      } else if (val === 6) {
        this.rtkStatus = this.equipLanguage.rtkStatusList[4];
      }
    },
    "cameraList.camera_zoom_value"(val) {
      this.zoomValue = val;
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .singleSoldierUav {
    .content-item-1 {
      .content-item-title {
        font-size: @zoomIndex * 16px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
      .content-item-1-1 {
        .content-item-div {
          font-size: @zoomIndex * 14px !important;
          .content-item-data {
            font-size: @zoomIndex * 18px !important;
          }
        }
      }
    }
    .content-item-2 {
      .videoOutTip {
        .titleTip {
          font-size: @zoomIndex * 18px !important;
        }
      }
    }
    .content-item-3 {
      // border-radius:@zoomIndex * 6px !important;
      .content-item-title {
        font-size: @zoomIndex * 16px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
      .ballHead1 {
        padding: @zoomIndex * 5px 0 !important;
        .ballHead {
          height: @zoomIndex * 150px !important;
          width: @zoomIndex * 150px !important;
        }
      }
      .operationBtn {
        .el-button {
          padding: @zoomIndex * 10px !important;
          font-size: @zoomIndex * 14px !important;
          border-radius: @zoomIndex * 4px !important;
          border-width: @zoomIndex * 1px !important;
        }
      }
      .zoomlens {
        font-size: @zoomIndex * 16px !important;
        .el-input-number {
          margin-left: @zoomIndex * 5px !important;
        }
      }
      .content-item-1-1 {
        .content-item-div {
          font-size: @zoomIndex * 14px !important;
          .content-item-data {
            font-size: @zoomIndex * 18px !important;
          }
          .el-image {
            cursor: pointer;
          }
          // .videotape-1 {
          //   width: @zoomIndex * 25px !important;
          //   height: @zoomIndex * 25px !important;
          //   transform: translate(
          //     @zoomIndex * -12.5px,
          //     @zoomIndex * -18px
          //   ) !important;
          //   .videotape-1-1 {
          //     width: @zoomIndex * 10px !important;
          //     height: @zoomIndex * 10px !important;
          //     margin: @zoomIndex * 7.5px !important;
          //   }
          // }
        }
      }
      .moreSet {
        text-align: right;
        margin-top: @zoomIndex * 20px !important;
        margin-right: @zoomIndex * 5px !important;
        font-size: @zoomIndex * 16px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
      .el-drawer {
        .iconBack {
          font-size: @zoomIndex * 24px !important;
        }
      }
    }
  }
}
.singleSoldierUav {
  width: 100%;
  height: 100%;
  display: flex;
  .content-item-1 {
    width: 20%;
    margin: 2% 0 2% 1%;
    overflow-y: auto;
    overflow-x: hidden;
    .content-item-title {
      text-align: center;
      width: 100%;
      padding: 1%;
      font-size: 16px;
      letter-spacing: 2px;
      box-sizing: border-box;
    }
    .content-item-1-1 {
      width: 100%;
      height: auto;
      display: flex;
      text-align: center;
      .content-item-div {
        font-size: 14px;
        width: 48%;
        margin: 1%;
        .content-item-data {
          font-size: 18px;
        }
      }
    }
  }
  .content-item-2 {
    margin: 2% 1%;
    width: 56%;
    .video-module {
      height: 100%;
      width: 100%;
    }
    .videoOutTip {
      width: 100%;
      height: 100%;
      position: relative;
      .titleTip {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
      }
    }
  }
  .content-item-3 {
    width: 20%;
    margin: 2% 1% 2% 0;
    // border-radius: 6px;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    .content-item-title {
      text-align: center;
      width: 100%;
      padding: 1%;
      font-size: 16px;
      letter-spacing: 2px;
      box-sizing: border-box;
    }
    .ballHead1 {
      width: 100%;
      height: auto;
      padding: 5px 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      .ballHead {
        height: 150px;
        width: 150px;
      }
    }

    .operationBtn {
      text-align: center;
      .el-button {
        color: white;
        padding: 10px;
        font-size: 14px;
        border-radius: 4px;
        border-width: 1px;
      }
    }
    .zoomlens {
      width: 98%;
      padding: 1%;
      font-size: 16px;
      text-align: center;
      .el-input-number {
        margin-left: 5px;
      }
    }
    .content-item-1-1 {
      width: 100%;
      height: auto;
      display: flex;
      text-align: center;
      .content-item-div {
        font-size: 14px;

        width: 48%;
        margin: 1%;
        position: relative;
        .content-item-data {
          font-size: 18px;
        }
        .el-image {
          cursor: pointer;
        }
        // .videotape-1 {
        //   width: 25px;
        //   height: 25px;
        //   position: absolute;
        //   top: 50%;
        //   left: 50%;
        //   transform: translate(-12.5px, -18px);

        //   border-radius: 50%;
        //   .videotape-1-1 {
        //     width: 10px;
        //     height: 10px;
        //     margin: 7.5px;
        //   }
        // }
      }
    }
    .moreSet {
      text-align: right;
      margin-top: 20px;
      margin-right: 5px;
      cursor: pointer;
      font-size: 16px;
      letter-spacing: 2px;
      text-decoration: underline;
    }
    .el-drawer {
      .iconBack {
        cursor: pointer;
        font-size: 24px;
      }
    }
  }
}
</style>
<style lang="less">
.singleSoldierUav {
  .content-item-1 {
    .content-item-1-1 {
      .el-image {
        .el-image__inner {
          vertical-align: bottom;
        }
      }
    }
  }
  .content-item-3 {
    .el-drawer__wrapper {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      height: 91%;
      width: 101%;
    }
    .el-drawer {
      .el-drawer__header {
        font-size: 20px;
        padding: 10px;
        margin-bottom: 0;
        padding-bottom: 5px;
      }
      .el-drawer__body {
        // scrollbar-width: thin;

        &::-webkit-scrollbar {
          width: 3px;
        }

        &::-webkit-scrollbar-track {
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }

        &::-webkit-scrollbar-thumb {
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
      }
    }
    .zoomlens {
      .el-input-number {
        width: 200px !important;
        line-height: 34px !important;

        .el-input-number__decrease,
        .el-input-number__increase {
          width: 36px !important;
          font-size: 14px !important;
        }
        .el-input--medium {
          font-size: 14px !important;
          .el-input__inner {
            height: 36px !important;
            line-height: 36px !important;
          }
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .singleSoldierUav {
    .content-item-3 {
      .el-drawer {
        .el-drawer__header {
          font-size: @zoomIndex * 20px !important;
          padding: @zoomIndex * 10px !important;
          padding-bottom: @zoomIndex * 5px !important;
        }
        .el-drawer__body {
          &::-webkit-scrollbar {
            width: @zoomIndex * 3px !important;
          }

          &::-webkit-scrollbar-track {
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }

          &::-webkit-scrollbar-thumb {
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }
        }
      }
      .zoomlens {
        .el-input-number {
          width: @zoomIndex * 200px !important;
          line-height: @zoomIndex * 34px !important;

          .el-input-number__decrease,
          .el-input-number__increase {
            width: @zoomIndex * 36px !important;
            font-size: @zoomIndex * 14px !important;
          }
          .el-input--medium {
            font-size: @zoomIndex * 14px !important;
            .el-input__inner {
              height: @zoomIndex * 36px !important;
              line-height: @zoomIndex * 36px !important;
            }
          }
        }
      }
    }
  }
}
</style>