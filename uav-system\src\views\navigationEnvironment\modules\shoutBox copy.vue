<template>
  <put-away
    class="shout-box"
    v-model="isOpen"
    :styles="{ top: 0, left: 0 }"
    :buttonStyle="buttonStyle"
    :tooltipText="language.title"
  >
    <template v-slot:main>
      <div class="shout-box-main">
        <div class="shout-btn">
          <el-button type="primary" @click="startShout">{{
            language.startShout
          }}</el-button>
          <el-button type="primary" @click="stopShout">{{
            language.stopShout
          }}</el-button>
        </div>
        <div class="shout-input">
          <el-input
            v-model="shoutValue"
            type="textarea"
            autosize
            placeholder="请喊话内容"
          ></el-input>
          <el-button type="primary" @click="sendText">{{
            language.shout
          }}</el-button>
        </div>
      </div>
    </template>
    <template v-slot:showContent>
      <div class="" style="color: #fff">{{ language.title }}</div>
    </template>
  </put-away>
</template>
<script>
import PutAway from "../components/putAway.vue";
export default {
  props: {
    websocket: [Function, Object],
  },
  components: {
    PutAway,
  },
  data() {
    return {
      isOpen: true,
      shoutValue: "",
      buttonStyle: {
        right: "-24px",
        "border-top-right-radius": "5px",
        "border-bottom-right-radius": "5px",
      },
      mediaRecorder: null,
      stream: null,
      sendCode: false,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.navigation.shout;
    },
  },
  methods: {
    checkSupport() {
      // 1. 检查getUserMedia
      const hasGetUserMedia = !!(
        navigator.mediaDevices && navigator.mediaDevices.getUserMedia
      );
      // 2. 检查MediaRecorder
      const hasMediaRecorder = typeof window.MediaRecorder !== "undefined";
      // 3. 检查MediaRecorder是否支持opus
      let supportsOpus = false;
      if (hasMediaRecorder) {
        supportsOpus = MediaRecorder.isTypeSupported("audio/webm;codecs=opus");
      }
      if (hasGetUserMedia && hasMediaRecorder && supportsOpus) {
        return true;
      }
    },
    //开始录音
    startShout: async function () {
      console.log(this.websocket);
      if (!this.checkSupport()) {
        this.$message.error(this.language.checkError);
        return false;
      }
      if (!this.mediaRecorder) {
        // 1. 获取麦克风权限
        this.stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000,
          },
        });
        // 2. 创建MediaRecorder，指定opus编码
        this.mediaRecorder = new MediaRecorder(this.stream, {
          mimeType: "audio/webm;codecs=opus",
        });
        this.mediaRecorder.ondataavailable = (e) => {
          if (e.data.size > 0) {
            console.log(e.data);
            this.changeBase64(e.data);
            // e.data.arrayBuffer().then((buffer) => {
            //   this.websocket.ws.binaryType = "arraybuffer";
            //   this.websocket.ws.send(buffer);
            //   console.log(buffer);
            //   // this.ws.send(buffer);
            // });
          }
        };
        this.mediaRecorder.start(40);
      }
    },
    changeBase64: function (blob) {
      const reader = new FileReader();
      reader.onloadend = () => {
        // 结果是 data:audio/webm;codecs=opus;base64,xxxx
        const base64data = reader.result.split(",")[1]; // 只取base64部分
        this.websocket.manualSend({ text: base64data }, 460);
      };
      reader.readAsDataURL(blob);
    },
    //停止录音
    stopShout: function () {
      this.websocket.ws.binaryType = "blob";
      if (this.mediaRecorder && this.mediaRecorder.state !== "inactive") {
        this.mediaRecorder.stop();
        this.mediaRecorder = null;
      }
      if (this.stream) {
        this.stream.getTracks().forEach((track) => track.stop());
        this.stream = null;
      }
    },
    //发送喊话
    sendText: function () {
      if (!this.shoutValue) {
        return false;
      }
      if (this.sendCode) {
        return false;
      }
      this.sendCode = true;
      this.websocket.manualSend({ text: this.shoutValue }, 460);
      setTimeout(() => {
        this.sendCode = false;
      }, 800);
    },
  },
};
</script>
<style lang="less" scoped>
.shout-box {
  .shout-box-main {
    background-color: rgba(71, 71, 71, 0.5);
    padding: 10px 5px;
    border-radius: 5px;
    .shout-btn {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .el-button {
        flex: 1;
        background-color: #4e5452;
        border: none;
        color: #409eff;
      }
    }
    .shout-input {
      display: flex;
      align-items: flex-start;
      .el-textarea {
        flex-grow: 1;
        .el-textarea__inner {
          background-color: #c4c4c4;
        }
      }
      .el-button {
        flex-shrink: 0;
        padding: 8.5px 10px;
      }
    }
  }
}
</style>
<style lang="less">
.shout-box {
  .shout-box-main {
    .shout-input {
      .el-textarea {
        .el-textarea__inner {
          // background-color: #c4c4c4;
          // color: #000;
        }
      }
    }
  }
}
</style>