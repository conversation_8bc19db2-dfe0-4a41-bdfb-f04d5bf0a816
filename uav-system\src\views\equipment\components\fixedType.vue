<template>
  <div class="fixedType">
    <div class="content-item-1">
      <div class="content-item-1-state">
        <div class="state-all-content">
          <div class="content-item-1-state-row">
            <div class="state-col-content">
              <div class="img-div">
                <el-image
                  :src="weatherImg"
                  style="width: 50%"
                  fit="contain"
                ></el-image>
              </div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{ equipLanguage.equipStatus.weather }}
              </div>
              <div
                :class="
                  weatherState.weather && weatherState.weather.length <= 1
                    ? 'state-data-content-1'
                    : 'state-data-content-1-1'
                "
              >
                {{ weatherState.weather }}
              </div>
            </div>
            <div class="state-col-content">
              <div class="img-div">
                <el-image
                  :src="temperatureImg"
                  style="width: 24%"
                  fit="contain"
                ></el-image>
              </div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{ equipLanguage.equipStatus.humidity }}
              </div>
              <div class="state-data-content-1">
                {{
                  deviceItemState.humidity
                    ? parseInt(deviceItemState.humidity) + "%"
                    : weatherState.humidity
                }}
              </div>
            </div>
            <div class="state-col-content">
              <div class="img-div">
                <el-image
                  :src="windImg"
                  style="width: 45%"
                  fit="contain"
                ></el-image>
              </div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{ equipLanguage.equipStatus.wind }}
              </div>
              <div
                :class="
                  deviceItemState.wind_level
                    ? parseInt(deviceItemState.wind_level) < 7
                      ? 'state-data-content-2-1'
                      : 'state-data-content-2'
                    : parseInt(weatherState.windpower.replace(/[^\d]/g, ' ')) <
                      7
                    ? 'state-data-content-2-1'
                    : 'state-data-content-2'
                "
              >
                {{
                  deviceItemState.wind_level
                    ? parseInt(deviceItemState.wind_level)
                    : weatherState.windpower
                }}
              </div>
            </div>
          </div>
          <div class="content-item-1-state-row">
            <div class="state-col-content">
              <div>{{ equipLanguage.equipStatus.temperature }}</div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{
                  deviceItemState.temp_outside
                    ? parseInt(deviceItemState.temp_outside) + "℃"
                    : weatherState.temperature
                }}
              </div>
            </div>
            <div class="state-col-content">
              <div>{{ equipLanguage.equipStatus.rain }}</div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{
                  deviceItemState.rain_fall == 1
                    ? deviceItemState.rain_fall.toFixed(1) + "mm"
                    : NaN
                }}
              </div>
            </div>
            <div class="state-col-content">
              <div>{{ equipLanguage.equipStatus.windDirection }}</div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{
                  deviceItemState.wind_speed
                    ? deviceItemState.wind_speed.toFixed(1) + "m/s"
                    : NaN
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-item-1-video" ref="contentItemVideo">
        <div
          :style="video.inCabin.style"
          class="inCabinStyle"
          v-if="is_push_on && deviceItem.inCabin"
        >
          <live-video
            v-if="deviceItem.isExists"
            :title="equipLanguage.inCabin"
            :videoId="'inVideo'"
            :url="equipItem.stream_in_list ? equipItem.stream_in_list[0] : ''"
            :autoPlay="true"
            @clickVideo="changeVideo('inCabin')"
          ></live-video>
          <div class="tipinCabin" v-if="!deviceItem.isExists">
            {{ equipLanguage.novideoTip }}
          </div>
          <div class="inCabinTitle" v-if="!deviceItem.isExists">
            {{ equipLanguage.inCabin }}
          </div>
        </div>
        <div v-if="!is_push_on && deviceItem.inCabin" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>
        <div
          class="batteryBox"
          v-if="!deviceItem.inCabin"
          :style="video.inCabin.style"
        >
          <div class="battery-content">
            <div class="battery-header">{{ language.title }}</div>
            <div class="battery-main">
              <div
                class="battery-item"
                v-for="item in batteryInfo"
                :key="item.id"
              >
                <div class="item-label">{{ item.label }}</div>
                <div class="item-value">{{ format(item) }}{{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="content-item-1-video" ref="contentItemVideo1">
        <div :style="video.uav.style">
          <live-video
            v-if="is_push_on"
            :title="equipLanguage.uav"
            @clickVideo="changeVideo('uav')"
            :videoId="'uavVideo'"
            :url="equipItem.stream_uav_list ? equipItem.stream_uav_list[0] : ''"
            :autoPlay="true"
          ></live-video>
          <div v-if="!is_push_on" class="videoOutTip">
            <div class="titleTip">
              {{ equipLanguage.equipInfo.equipStateOut1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-item-2" ref="contentVideo">
      <div :style="video.outCabin.style">
        <live-video
          v-if="is_push_on"
          :title="equipLanguage.outCabin"
          :videoId="'outVideo'"
          :url="equipItem.stream_out_list ? equipItem.stream_out_list[0] : ''"
          :autoPlay="true"
          :isFill="true"
          @clickVideo="changeVideo('outCabin')"
        ></live-video>
        <div v-if="!is_push_on" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>

        <!-- :url="deviceItem.stream_out_list ? deviceItem.stream_out_list[0] : ''" -->
      </div>
      <div class="link-group">
        <el-radio-group v-model="form_cor" @change="formCorChange">
          <el-radio :label="0">{{ link.auto }}</el-radio>
          <el-radio :label="10">{{ link.fpv }}</el-radio>
          <el-radio :label="20">{{ link.uav }}</el-radio>
        </el-radio-group>
        <div class="rtk-status">
          {{ equipLanguage.rtkStateTitle
          }}<span
            :style="{
              color: uavItemList.rtk_status
                ? rtkStatus[uavItemList.rtk_status].color
                : rtkStatus[0].color,
            }"
            >{{
              uavItemList.rtk_status
                ? rtkStatus[uavItemList.rtk_status].text
                : rtkStatus[0].text
            }}</span
          >
        </div>
      </div>
    </div>
    <div class="content-item-3">
      <stable-operation
        ref="stableOperation"
        :websocket="websocket"
        :deviceItem="deviceItem"
      ></stable-operation>
    </div>
  </div>
</template>
<script>
import { Websockets } from "@/utils/websocketUntil";
import baseUrl from "@/utils/global";
import liveVideo from "@/components/video/webrtcVideoHttps.vue";
import stableOperation from "./stableOperation.vue";
export default {
  props: {
    deviceItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    liveVideo,
    stableOperation,
  },
  computed: {
    equipLanguage() {
      return this.$languagePackage.equipment;
    },
    is_push_on() {
      return this.deviceItem.is_push_on;
    },
    link() {
      return this.$languagePackage.navigation.link;
    },
    language() {
      return this.$languagePackage.navigation.batteryBox;
    },
  },
  data() {
    return {
      websocket: "",
      uavItemList: {},
      equipItem: {},
      deviceItemState: {},
      weatherState: {
        humidity: NaN,
        temperature: NaN,
        weather: "无",
        winddirection: "无",
        windpower: "NaN",
      },
      video: {
        uav: {
          style: {},
        },
        outCabin: {
          style: {},
        },
        inCabin: {
          style: {},
        },
      },
      batteryInfo: [
        { id: "voltage", label: "电压", unit: "V", accuracy: 2 },
        { id: "current", label: "电流", unit: "A", accuracy: 2 },
        { id: "battery_remaining", label: "电量", unit: "%", accuracy: 0 },
        { id: "battery_temperature", label: "温度", unit: "℃", accuracy: 0 },
      ],
      form_cor: "",
      from_cno: "",
      rtkStatus: {},
      temperatureImg: require("@/assets/img/equipment/temperature.png"),
      weatherImg: require("@/assets/img/equipment/weather.png"),
      windImg: require("@/assets/img/equipment/wind.png"),
    };
  },
  created() {
    this.weatherState = this.equipLanguage.weatherState;
    let battery = this.language.batteryInfo;
    for (let index = 0; index < this.batteryInfo.length; index++) {
      let item = this.batteryInfo[index];
      this.batteryInfo[index].label = battery[item.id];
    }
    this.getRtkStatus();
  },
  mounted() {
    this.initWebsocket();
    this.$nextTick(() => {
      this.originVideoLayout();
    });
  },
  methods: {
    //获取rtk状态
    getRtkStatus() {
      let info = {};
      let obj = this.$languagePackage.dict.rtkStatus;
      for (const key in obj) {
        if (key == 0 || key == 1) {
          info[key] = {
            text: obj[key],
            color: "red",
          };
        } else if (key == 2) {
          info[3] = {
            text: obj[key],
            color: "red",
          };
        } else if (key == 3) {
          info[4] = {
            text: obj[key],
            color: "yellow",
          };
        } else if (key == 4) {
          info[6] = {
            text: obj[key],
            color: "rgb(19, 224, 19)",
          };
        }
      }
      this.rtkStatus = info;
    },
    //建立设备websocket链接
    initWebsocket() {
      this.websocket = new Websockets(baseUrl.WS_URL, {
        equipmentVerify: {
          sn_id: this.deviceItem.sn_id,
          type: this.deviceItem.type,
          vst: 40,
        },
        heartbeat: 20000,
        message: this.returnMessage,
      });
    },
    //接受websokcet返回的数据
    returnMessage(e) {
      let msg_id = e.msg_id;
      let data = e.data;
      switch (msg_id) {
        case 200:
          this.equipItem = data;
          this.form_cor = data.cor;
          this.from_cno = data.cno;
          break;
        case 206:
          let cnoList = data.list;
          let index = cnoList.findIndex((item) => {
            return item.cor == this.form_cor;
          });
          let params = {};
          if (this.form_cor !== 0) {
            params = {
              cor: this.form_cor,
              cno: cnoList[index].cno,
            };
          } else {
            params = {
              cor: this.form_cor,
            };
          }
          this.websocket.manualSend(params, 207);
          break;
        case 207:
          this.form_cor = data.cor;
          this.from_cno = data.cno;
          let list = this.equipItem.stream_uav_list[0].split("/");
          if (list[list.length - 1] !== data.cno) {
            list[list.length - 1] = data.cno;
          }
          let url = list.join("/") + "&type=play";
          this.$set(this.equipItem.stream_uav_list, 0, url);
          break;
        case 432:
          this.uavItemList = data || {};
          this.$emit("returnMessage", msg_id, data);
          break;
        case 434:
          this.deviceItemState = data;
          this.$emit("returnMessage", msg_id, data);
          break;
        case 435:
          this.cameraList = data;
          this.zoomValue = data.camera_zoom_value;
          break;
        case 99:
          this.$emit("changeLogCode", item);
          this.$confirm("机巢日志上传成功", "上传提示", {
            confirmButtonText: this.tipMessage.confirm,
            cancelButtonText: this.tipMessage.cancel,
            type: "success",
          }).then(() => {});
          break;
        default:
          break;
      }
      let stableOperation = this.$refs.stableOperation;
      stableOperation && stableOperation.getMessage(msg_id, data);
    },
    //初始化视频盒子样式
    originVideoLayout() {
      this.video.inCabin.style = {
        top: this.$refs.contentItemVideo.offsetTop + "px",
        left: this.$refs.contentItemVideo.offsetLeft + "px",
        width: this.$refs.contentItemVideo.offsetWidth + "px",
        height: this.$refs.contentItemVideo.offsetHeight * 0.95 + "px",
        marginTop: this.$refs.contentItemVideo.offsetHeight * 0.05 + "px",
        position: "absolute",
      };
      this.video.inCabin.code = 0;
      this.video.uav.style = {
        top: this.$refs.contentItemVideo1.offsetTop + "px",
        left: this.$refs.contentItemVideo1.offsetLeft + "px",
        width: this.$refs.contentItemVideo1.offsetWidth + "px",
        height: this.$refs.contentItemVideo1.offsetHeight * 0.95 + "px",
        marginTop: this.$refs.contentItemVideo1.offsetHeight * 0.05 + "px",
        position: "absolute",
      };
      this.video.uav.code = 1;
      this.video.outCabin.style = {
        top: this.$refs.contentVideo.offsetTop + "px",
        left: this.$refs.contentVideo.offsetLeft + "px",
        width: this.$refs.contentVideo.offsetWidth + "px",
        height: this.$refs.contentVideo.offsetHeight + "px",
        position: "absolute",
      };
      this.video.outCabin.code = 2;
    },
    //修改视频盒子样式
    changeVideoLayout() {
      for (let key in this.video) {
        switch (this.video[key].code) {
          case 0:
            this.video[key].style = {
              top: this.$refs.contentItemVideo.offsetTop + "px",
              left: this.$refs.contentItemVideo.offsetLeft + "px",
              width: this.$refs.contentItemVideo.offsetWidth + "px",
              height: this.$refs.contentItemVideo.offsetHeight * 0.95 + "px",
              marginTop: this.$refs.contentItemVideo.offsetHeight * 0.05 + "px",
              position: "absolute",
            };

            break;
          case 1:
            this.video[key].style = {
              top: this.$refs.contentItemVideo1.offsetTop + "px",
              left: this.$refs.contentItemVideo1.offsetLeft + "px",
              width: this.$refs.contentItemVideo1.offsetWidth + "px",
              height: this.$refs.contentItemVideo1.offsetHeight * 0.95 + "px",
              marginTop:
                this.$refs.contentItemVideo1.offsetHeight * 0.05 + "px",
              position: "absolute",
            };
            break;
          case 2:
            this.video[key].style = {
              top: this.$refs.contentVideo.offsetTop + "px",
              left: this.$refs.contentVideo.offsetLeft + "px",
              width: this.$refs.contentVideo.offsetWidth + "px",
              height: this.$refs.contentVideo.offsetHeight + "px",
              position: "absolute",
            };
            break;
        }
      }
    },
    //切换视频位置
    changeVideo(item) {
      if (this.video[item].code !== 2) {
        let keys;
        for (let key in this.video) {
          if (this.video[key].code == 2) {
            keys = key;
          }
        }
        this.video[keys].code = this.video[item].code;
        this.video[item].code = 2;
        let style = this.video[item].style;
        this.video[item].style = this.video[keys].style;
        this.video[keys].style = style;
      }
    },
    //机巢电池电压等状态转换
    format(item) {
      return this.uavItemList[item.id]
        ? this.uavItemList[item.id].toFixed(item.accuracy)
        : this.uavItemList[item.id];
    },
    //视频流切换指令
    formCorChange() {
      this.websocket.manualSend({}, 206);
    },
    sendGetLog(item) {
      this.websocket && this.websocket.manualSend({}, 99);
    },
  },
  beforeDestroy() {
    if (this.websocket) {
      this.websocket.manualClone();
      this.websocket = "";
    }
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .fixedType {
    .content-item-1 {
      .content-item-1-state {
        border-radius: @zoomIndex * 6px !important;
        .state-all-content {
          padding: @zoomIndex * 10px 0 !important;
          .content-item-1-state-row {
            .state-col-content {
              font-size: @zoomIndex * 14px !important;
              .state-data-title {
                font-size: @zoomIndex * 14px !important;
                line-height: @zoomIndex * 15px !important;
              }
              .state-data-content-1 {
                font-size: @zoomIndex * 30px !important;
                line-height: @zoomIndex * 31px !important;
              }
              .state-data-content-1-1 {
                font-size: @zoomIndex * 24px !important;
                line-height: @zoomIndex * 31px !important;
              }
              .state-data-content-2-1 {
                font-size: @zoomIndex * 28px !important;
                line-height: @zoomIndex * 31px !important;
              }
              .state-data-content-2 {
                font-size: @zoomIndex * 28px !important;
                line-height: @zoomIndex * 31px !important;
              }
            }
          }
        }
      }
      .content-item-1-video {
        .videoOutTip {
          .titleTip {
            font-size: @zoomIndex * 18px !important;
          }
        }
        .inCabinStyle {
          .tipinCabin {
            font-size: @zoomIndex * 16px !important;
          }
          .inCabinTitle {
            padding: @zoomIndex * 3px @zoomIndex * 10px !important;
          }
        }
        .batteryBox {
          border-radius: @zoomIndex * 6px !important;
          .battery-content {
            border-radius: @zoomIndex * 6px !important;
            .battery-header {
              padding: @zoomIndex * 10px !important;
              font-size: @zoomIndex * 18px !important;
            }
            .battery-main {
              padding: @zoomIndex * 10px !important;
              font-size: @zoomIndex * 14px !important;
              .battery-item {
                padding: @zoomIndex * 5px @zoomIndex * 10px !important;
              }
            }
          }
        }
      }
    }
    .content-item-2 {
      .videoOutTip {
        .titleTip {
          font-size: @zoomIndex * 18px !important;
        }
      }
    }
    .content-item-3 {
      border-radius: @zoomIndex * 6px !important;
      .content-item-3-title {
        font-size: @zoomIndex * 14px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
      .content-item-3-btn {
        .el-button {
          min-width: @zoomIndex * 120px !important;
          font-size: @zoomIndex * 14px !important;
          letter-spacing: @zoomIndex * 2px !important;
          padding: @zoomIndex * 12px !important;
        }
      }
      .content-item-3-content {
        .content-index {
          font-size: @zoomIndex * 18px !important;
          letter-spacing: @zoomIndex * 2px !important;
          .el-button {
            padding: @zoomIndex * 6px 0 !important;
            &.el-button--text {
              font-size: @zoomIndex * 16px !important;
            }
          }
        }
      }
      .content-charge {
        .el-button {
          min-width: @zoomIndex * 120px !important;
          font-size: @zoomIndex * 14px !important;
          letter-spacing: @zoomIndex * 2px !important;
          padding: @zoomIndex * 12px !important;
        }
      }
      .content-cell {
        margin: 0 @zoomIndex * 10px !important;
        .content-cell-item {
          font-size: @zoomIndex * 14px !important;
          margin: @zoomIndex * 5px !important;
          .cell-title {
            border-radius: @zoomIndex * 2px !important;
          }
          .cell-value {
            .el-progress {
              margin-top: @zoomIndex * 5px !important;
            }
          }
        }
      }
    }
  }
}
.fixedType {
  width: 100%;
  height: 100%;
  display: flex;
  .content-item-1 {
    width: 20%;
    margin: 2% 0 2% 1%;
    .content-item-1-state {
      width: 100%;
      height: 17%;
      margin-bottom: 1%;
      // display: flex;
      border-radius: 6px;
      overflow-y: auto;
      .state-all-content {
        width: 100%;
        height: auto;
        padding: 10px 0;
        .content-item-1-state-row {
          width: 96%;
          height: auto;
          padding: 0 2%;
          display: flex;
          align-items: center;
          .state-col-content {
            font-size: 14px;
            width: 16.6%;
            text-align: center;
            .state-data-title {
              font-size: 14px;
              line-height: 15px;
            }
            .state-data-content-1 {
              font-size: 30px;
              line-height: 31px;
            }
            .state-data-content-1-1 {
              font-size: 24px;
              line-height: 31px;
            }
            .state-data-content-2-1 {
              font-size: 28px;
              line-height: 31px;
            }
            .state-data-content-2 {
              font-size: 28px;
              line-height: 31px;
            }
          }
        }
      }
    }
    .content-item-1-video {
      width: 100%;
      height: 41%;
      .video-module {
        height: 100%;
        width: 100%;
      }
      .videoOutTip {
        width: 100%;
        height: 100%;
        color: white;
        position: relative;
        .titleTip {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 18px;
        }
      }
      .inCabinStyle {
        position: relative;
        .tipinCabin {
          width: 100%;
          text-align: center;
          vertical-align: middle;
          padding-top: 25%;
          font-size: 16px;
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-80%);
        }
        .inCabinTitle {
          position: absolute;
          top: 0;
          left: 0;
          border: none;
          padding: 3px 10px;
        }
      }
      .batteryBox {
        border-radius: 6px;
        overflow-y: auto;
        background-color: rgba(0, 0, 0, 1);
        .battery-content {
          width: 100%;
          height: 100%;
          border-radius: 6px;

          .battery-header {
            padding: 10px;
            font-size: 18px;
            color: aqua;
          }
          .battery-main {
            padding: 10px;
            font-size: 14px;
            color: #fff;
            .battery-item {
              padding: 5px 10px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            }
          }
        }
      }
    }
  }
  .content-item-2 {
    margin: 2% 1%;
    width: 56%;
    .video-module {
      height: 100%;
      width: 100%;
    }
    .videoOutTip {
      width: 100%;
      height: 100%;
      position: relative;
      .titleTip {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
      }
    }
    .link-group {
      position: absolute;
      right: 22%;

      // padding: 0 10px;
      z-index: 20;
      .el-radio-group {
        padding: 5px 10px;
        background-color: rgba(0, 0, 0, 0.3);
        .el-radio {
          color: #fff;
        }
      }

      .rtk-status {
        margin: 3px 0 0 auto;
        padding: 2px 5px;
        color: #fff;
        width: fit-content;
        background-color: rgba(0, 0, 0, 0.3);
      }
    }
  }
  .content-item-3 {
    width: 20%;
    margin: 2% 1% 2% 0;
    border-radius: 6px;
    overflow: auto;
    .content-item-3-title {
      margin: 2% 8%;
      font-weight: 550;
      font-size: 14px;
      letter-spacing: 2px;
    }
    .content-item-3-btn {
      margin: 2% 8%;
      display: flex;
      flex-flow: row wrap;
      .el-button {
        white-space: normal;
        margin-left: 0;
        width: 45%;
        min-width: 120px;
        border: none;
        font-size: 14px;
        font-weight: 550;
        letter-spacing: 2px;
        margin-bottom: 1%;
        padding: 12px;
        &:nth-child(odd) {
          margin-right: 5%;
        }
        // &:nth-child(even){
        //   margin-left: 5%;
        // }
      }
    }
    // .content-item-3-btn-en{
    //   margin: 2% 5%;
    //   .el-button{
    //     width: 48%;
    //     white-space:normal;
    //   }

    // }
    .content-item-3-content {
      margin: 2% 8%;
      .content-index {
        margin: 10% 0;
        font-size: 18px;
        text-align: left;
        margin-left: 7%;
        font-weight: 500;
        letter-spacing: 2px;
        &.content-index-1 {
          margin: 5% 0;
        }
        .content-switch,
        .el-button {
          float: right;
        }
        .el-switch {
          height: 50%;
        }
        .el-button {
          border: none;
          padding: 6px 0;
          &.el-button--text {
            font-size: 16px;
          }
        }
      }
    }
    .content-charge {
      margin: 2% 8%;
      .el-button {
        white-space: normal;
        margin-left: 0;
        width: 45%;
        min-width: 120px;
        border: none;
        font-size: 14px;
        font-weight: 550;
        letter-spacing: 2px;
        margin-bottom: 1%;
        padding: 12px;
        &:nth-child(odd) {
          margin-right: 4%;
        }
      }
    }
    .content-cell {
      margin: 0 10px;
      display: flex;
      align-items: center;
      text-align: center;
      .content-cell-item {
        flex: 1;
        font-size: 14px;
        text-align: center;
        margin: 5px;
        .cell-title {
          border-radius: 2px;
        }
        .cell-value {
          .el-progress {
            margin-top: 5px;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.fixedType {
  .content-item-3 {
    .content-item-3-content {
      .el-switch {
        .el-switch__core {
          height: 36px !important;
          width: 100px !important;
          border-radius: 20px !important;
          &::after {
            height: 32px !important;
            width: 32px !important;
          }
        }
      }
      .el-switch.is-checked .el-switch__core::after {
        margin-left: -30px !important;
      }
    }
    .content-cell {
      .content-cell-item {
        .cell-value {
          .el-progress {
            .el-progress__text {
              font-size: 14px !important;
            }
          }
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .fixedType {
    .content-item-3 {
      .content-item-3-content {
        .el-switch {
          .el-switch__core {
            height: @zoomIndex * 36px !important;
            width: @zoomIndex * 100px !important;
            border-radius: @zoomIndex * 20px !important;
            &::after {
              height: @zoomIndex * 32px !important;
              width: @zoomIndex * 32px !important;
            }
          }
        }
        .el-switch.is-checked .el-switch__core::after {
          margin-left: @zoomIndex * -30px !important;
        }
      }
      .content-cell {
        .content-cell-item {
          .cell-value {
            .el-progress {
              .el-progress__text {
                font-size: @zoomIndex * 14px !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>