<template>
  <div class="googleMap">
    <div id="maps"></div>
    <input
      type="file"
      style="display: none"
      ref="files"
      accept=".kml"
      @change="chooseFileAfter"
    />
    <div class="listShow" v-show="!mapLoading">
      <plan-list
        ref="planList"
        v-show="code == 0 || code == 4 ? true : false"
        :routeLanguage="routeLanguage"
      ></plan-list>
      <div
        class="addContent"
        v-if="code == 1 ? true : false"
        v-loading="loading"
        :element-loading-text="routeLanguage.routeLine.routeLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(10, 10, 10, 0.8)"
      >
        <div class="title">
          <el-button
            :class="iconCode ? 'returnIcon' : ''"
            icon="el-icon-arrow-left"
            @click="goBack"
          ></el-button>
          {{ routeTitle }}
        </div>
        <el-form
          :model="routeForm"
          :rules="routeRules"
          ref="routeForm"
          class="content-item-1"
        >
          <el-form-item :label="routeLanguage.routeLine.taskName" prop="name">
            <el-input
              v-model="routeForm.name"
              :placeholder="routeLanguage.routeLine.placeholder"
            ></el-input>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.fenceName">
            <el-button>{{ fenceItem.title }}</el-button>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.taskType">
            <el-button>{{ checkType.name_cn }}</el-button>
          </el-form-item>
          <div class="title">{{ routeLanguage.routeLine.basicSet }}</div>
          <el-form-item
            :label="routeLanguage.routeLine.autoSpeed"
            prop="auto_speed"
          >
            <el-slider
              v-model="routeForm.auto_speed"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :max="routeForm.max_speed"
              :step="0.1"
              class="slider-class-1"
            ></el-slider>
          </el-form-item>
          <!-- <el-form-item label="最大飞行速度（m / s）" prop="max_speed">
            <el-slider
              v-model="routeForm.max_speed"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :min="5"
              :max="1000"
              class="slider-class-1"
            ></el-slider>
          </el-form-item> -->
          <el-form-item
            :label="routeLanguage.routeLine.defaultHeight"
            prop="default_height"
          >
            <el-slider
              v-model="routeForm.default_height"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :max="fenceItem.height"
              :min="2"
              @change="setDefaultHeight"
              class="slider-class-1"
            ></el-slider>
          </el-form-item>
          <el-form-item
            :label="routeLanguage.routeLine.returnHeight"
            prop="return_height"
          >
            <el-slider
              v-model="routeForm.return_height"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :max="fenceItem.height"
              :min="2"
              class="slider-class-1"
            ></el-slider>
          </el-form-item>
          <el-form-item
            :label="routeLanguage.routeLine.taskAction"
            prop="action_completed"
          >
            <el-select
              v-model="routeForm.action_completed"
              :placeholder="routeLanguage.routeLine.placeholder1"
              class="actionChoose"
              popper-class="selects"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.waypoint">
            <el-collapse
              v-model="pointCodes"
              v-if="routeForm.point_json.length > 0 ? true : false"
              class="pointsClass"
            >
              <el-collapse-item
                v-for="(item, index) in routeForm.point_json"
                :key="index"
                :title="index + 1 + routeLanguage.waypoint.title"
                :name="index + 1"
              >
                <div>{{ routeLanguage.waypoint.lng }}</div>
                <el-input-number
                  v-model="item.lng"
                  @blur="changeLngLat(index, 1)"
                  @focus="code == 1 ? getFocus(index) : (lng = item.lng)"
                  :controls="false"
                  :precision="7"
                  ref="lngList"
                ></el-input-number>
                <div>{{ routeLanguage.waypoint.lat }}</div>
                <el-input-number
                  v-model="item.lat"
                  @blur="changeLngLat(index, 2)"
                  @focus="code == 1 ? getFocus(index) : (lat = item.lat)"
                  :controls="false"
                  :precision="7"
                ></el-input-number>
                <div>{{ routeLanguage.waypoint.height }}</div>
                <el-input-number
                  :controls="false"
                  :precision="2"
                  v-model="heights[index]"
                  :min="2"
                  :max="fenceItem.height"
                ></el-input-number>
                <el-button
                  icon="el-icon-plus"
                  class="addActionBtn"
                  :class="addActionCode == index + 1 ? 'active' : ''"
                  @click="addAction(index + 1)"
                  >{{ routeLanguage.waypoint.waypointAction }}</el-button
                >
                <div
                  class="routeActionDiv"
                  v-for="(item_1, i) in allActionList[index].actionList"
                  :key="'action' + i"
                >
                  <el-select
                    v-model="item_1.action_id"
                    :placeholder="routeLanguage.waypoint.placeholder"
                    class="actionChoose"
                    popper-class="selects"
                    clearable
                    @change="chooseAction(item_1.action_id, index, i)"
                  >
                    <el-option
                      v-for="(item_2, index) in actionOptions"
                      :key="'action_1' + index"
                      :label="item_2.label"
                      :value="item_2.value"
                    >
                    </el-option>
                  </el-select>
                  <el-button
                    type="text"
                    icon="el-icon-minus"
                    @click="delAction(item_1.action_id, index, i)"
                  ></el-button>
                  <div
                    v-if="item_1.action_id == 'uav_yaw' ? true : false"
                    class="actionValueDiv"
                  >
                    {{ routeLanguage.waypoint.uav_yaw }}
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="0"
                      :max="360"
                      :controls="false"
                      :precision="0"
                    ></el-input-number>
                  </div>
                  <div
                    v-if="item_1.action_id == 'hover' ? true : false"
                    class="actionValueDiv"
                  >
                    {{ routeLanguage.waypoint.hoverTime }}
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="0"
                      :max="255"
                      :controls="false"
                      :precision="0"
                    ></el-input-number>
                  </div>
                  <div
                    v-if="item_1.action_id == 'gimbal_ctrl' ? true : false"
                    class="actionValueDiv"
                  >
                    {{ routeLanguage.waypoint.gimbal_yaw }}
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="-180"
                      :max="255"
                      :controls="false"
                      :precision="0"
                    ></el-input-number>
                  </div>
                  <div
                    v-if="item_1.action_id == 'gimbal_ctrl' ? true : false"
                    class="actionValueDiv"
                  >
                    {{ routeLanguage.waypoint.gimbal_pitch }}
                    <el-input-number
                      v-model="item_1.param_list[1].value"
                      :min="-180"
                      :max="255"
                      :controls="false"
                      :precision="0"
                    ></el-input-number>
                  </div>
                  <!-- <div
                    v-if="item_1.action_id == 'cam_trig_dist' ? true : false"
                    class="actionValueDiv"
                  >
                    拍照距离（单位：m）：
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="0"
                      :precision="2"
                      :controls="false"
                    ></el-input-number>
                  </div> -->
                  <!-- <div
                    v-if="item_1.action_id == 'cam_zoom' ? true : false"
                    class="actionValueDiv"
                  >
                    相机变焦倍数（范围[1-200]）：
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="0"
                      :max="200"
                      :precision="0"
                      :controls="false"
                    ></el-input-number>
                  </div> -->
                  <div
                    v-if="item_1.action_id == 'speed' ? true : false"
                    class="actionValueDiv"
                  >
                    {{ routeLanguage.waypoint.speed }}
                    <el-input-number
                      v-model="item_1.param_list[0].value"
                      :min="0"
                      :max="routeForm.max_speed"
                      :controls="false"
                      :precision="0"
                    ></el-input-number>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
          <el-button
            class="saveBut"
            @click="sumbitRoute('routeForm')"
            :class="saveCode ? 'active' : ''"
            >{{ routeLanguage.routeLine.save }}</el-button
          >
        </el-form>
      </div>
      <div class="addContent" v-if="code == 2 ? true : false">
        <div class="title">
          <el-button
            :class="iconCode ? 'returnIcon' : ''"
            icon="el-icon-arrow-left"
            @click="goBack"
          ></el-button>
          {{ fenceTitle }}
        </div>
        <el-form
          :model="fenceForm"
          :rules="fenceRules"
          ref="fenceForm"
          class="content-item-1"
        >
          <el-form-item :label="routeLanguage.fence.name" prop="name">
            <el-input
              v-model="fenceForm.name"
              :placeholder="routeLanguage.fence.placeholder1"
            ></el-input>
          </el-form-item>
          <div class="title">{{ routeLanguage.routeLine.basicSet }}</div>
          <el-form-item :label="routeLanguage.fence.maxHeight">
            <el-slider
              v-model="fenceForm.height_limit"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :max="1000"
              :step="0.1"
              class="slider-class-1"
            ></el-slider>
          </el-form-item>
          <el-form-item :label="routeLanguage.fence.pointTitle">
            <el-collapse
              v-model="pointCodes"
              v-if="fenceForm.point_json.length > 0 ? true : false"
              class="pointsClass"
            >
              <el-collapse-item
                v-for="(item, index) in fenceForm.point_json"
                :key="index"
                :title="index + 1 + routeLanguage.waypoint.title1"
                :name="index + 1"
              >
                <div>{{ routeLanguage.waypoint.lng }}</div>
                <el-input-number
                  v-model="item.lng"
                  @blur="changeLatLng(index, 0)"
                  @focus="lng = item.lng"
                  :controls="false"
                  :precision="7"
                ></el-input-number>
                <div>{{ routeLanguage.waypoint.lat }}</div>
                <el-input-number
                  v-model="item.lat"
                  @blur="changeLatLng(index, 1)"
                  @focus="lat = item.lat"
                  :controls="false"
                  :precision="7"
                ></el-input-number>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
          <el-button
            class="saveBut"
            :class="saveCode ? 'active' : ''"
            @click="sumbitFence('fenceForm')"
            >{{ routeLanguage.routeLine.save }}</el-button
          >
        </el-form>
      </div>
      <div
        class="addContent"
        v-if="code == 3 ? true : false"
        v-loading="loading"
        :element-loading-text="routeLanguage.routeLine.routeLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(10, 10, 10, 0.8)"
      >
        <div class="title">
          <el-button
            :class="iconCode ? 'returnIcon' : ''"
            icon="el-icon-arrow-left"
            @click="goBack"
          ></el-button>
          {{ routeTitle }}
        </div>
        <el-form
          :model="orthoForm"
          :rules="orthoRules"
          ref="orthoForm"
          class="content-item-1"
        >
          <el-form-item :label="routeLanguage.routeLine.taskName" prop="name">
            <el-input
              v-model="orthoForm.name"
              :placeholder="routeLanguage.routeLine.placeholder"
            ></el-input>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.fenceName">
            <el-button>{{ fenceItem.title }}</el-button>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.taskType">
            <el-button>{{ checkType.name_cn }}</el-button>
          </el-form-item>
          <div class="title">{{ routeLanguage.routeLine.basicSet }}</div>
          <el-form-item
            :label="routeLanguage.routeLine.autoSpeed"
            prop="auto_speed"
          >
            <el-slider
              v-model="orthoForm.auto_speed"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :max="orthoForm.max_speed"
              :step="0.1"
              class="slider-class-1"
            ></el-slider>
          </el-form-item>
          <!-- <el-form-item label="最大飞行速度（m / s）" prop="max_speed">
            <el-slider
              v-model="orthoForm.max_speed"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :min="5"
              :max="1000"
              class="slider-class-1"
            ></el-slider>
          </el-form-item> -->
          <el-form-item
            :label="routeLanguage.routeLine.defaultHeight"
            prop="default_height"
          >
            <el-slider
              v-model="orthoForm.default_height"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :max="fenceItem.height"
              :min="2"
              @change="setDefaultHeight"
              class="slider-class-1"
            ></el-slider>
          </el-form-item>
          <el-form-item
            :label="routeLanguage.routeLine.returnHeight"
            prop="return_height"
          >
            <el-slider
              v-model="orthoForm.return_height"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :max="fenceItem.height"
              :min="2"
              class="slider-class-1"
            ></el-slider>
          </el-form-item>
          <el-form-item
            :label="routeLanguage.routeLine.taskAction"
            prop="action_completed"
          >
            <el-select
              v-model="orthoForm.action_completed"
              :placeholder="routeLanguage.routeLine.placeholder1"
              class="actionChoose"
              popper-class="selects"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            :label="routeLanguage.routeLine.cameraType"
            prop="cameraType"
          >
            <el-select
              v-model="orthoForm.cameraType"
              :placeholder="routeLanguage.routeLine.placeholder3"
              class="actionChoose"
              popper-class="selects"
              @change="changeCamera"
            >
              <el-option
                v-for="item in cameraOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <camera-param
            v-if="orthoForm.cameraType == 1"
            @cameraParams="getCameraParams"
            :cameraParamList="cameraParamList"
            :cameraParams="routeLanguage.cameraParams"
          ></camera-param>
          <el-form-item :label="routeLanguage.routeLine.course">
            <silder-input
              :numValue.sync="orthoForm.course"
              :max="99"
              :suffixCode="'%'"
            ></silder-input>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.lateral">
            <silder-input
              :numValue.sync="orthoForm.lateral"
              :max="99"
              :suffixCode="'%'"
            ></silder-input>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.wheelDist">
            <silder-input
              :numValue.sync="orthoForm.wheelDist"
              :max="100"
              :suffixCode="'m'"
            ></silder-input>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.angle" prop="rotate">
            <silder-input
              :numValue.sync="orthoForm.angle"
              :max="360"
              :suffixCode="'°'"
            ></silder-input>
          </el-form-item>
          <el-form-item :label="routeLanguage.routeLine.waypoint">
            <el-collapse
              v-model="pointCodes"
              v-if="orthoForm.point_json.length > 0 ? true : false"
              class="pointsClass"
            >
              <el-collapse-item
                v-for="(item, index) in orthoForm.point_json"
                :key="index"
                :title="index + 1 + routeLanguage.waypoint.title"
                :name="index + 1"
              >
                <div>{{ routeLanguage.waypoint.lng }}</div>
                <el-input-number
                  v-model="item.lng"
                  @blur="changeLngLat(index, 1)"
                  @focus="code == 3 ? getFocus(index) : (lng = item.lng)"
                  :controls="false"
                  :precision="7"
                  ref="lngList"
                ></el-input-number>
                <div>{{ routeLanguage.waypoint.lat }}</div>
                <el-input-number
                  v-model="item.lat"
                  @blur="changeLngLat(index, 2)"
                  @focus="code == 3 ? getFocus(index) : (lat = item.lat)"
                  :controls="false"
                  :precision="7"
                ></el-input-number>
                <!-- <div>高度（m）</div>
                <el-input-number
                  :controls="false"
                  :precision="2"
                  v-model="heights[index]"
                  :min="2"
                  :max="fenceItem.height"
                ></el-input-number> -->
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
          <el-button
            class="saveBut"
            @click="sumbitRoute('orthoForm')"
            :class="saveCode ? 'active' : ''"
            >{{ routeLanguage.routeLine.save }}</el-button
          >
        </el-form>
      </div>
    </div>
    <div
      class="operateBar_1"
      v-if="code == 1 || code == 2 || code == 3 ? true : false"
    >
      <el-button @click="drawMark" :class="drawend ? 'chooseAcitve' : ''">
        <el-image
          :src="drawend ? pointImg : pointImg_1"
          fit="contain"
        ></el-image>
        <span style="vertical-align: middle">{{
          routeLanguage.operationBtn.drawPoint
        }}</span>
      </el-button>
      <el-button
        @click="removeMarker"
        :class="removeClick ? 'chooseAcitve' : ''"
      >
        <el-image
          :src="removeClick ? removeImg : removeImg_1"
          fit="contain"
          style="width: 23%"
        ></el-image>
        <span style="vertical-align: middle">{{
          routeLanguage.operationBtn.delete
        }}</span>
      </el-button>
      <el-button @click="delMarker" :class="delClick ? 'chooseAcitve' : ''">
        <el-image :src="delClick ? delImg : delImg_1" fit="contain"></el-image>
        <span style="vertical-align: middle">{{
          routeLanguage.operationBtn.remove
        }}</span>
      </el-button>
      <el-button
        @click="recallMarker"
        :class="recallClick ? 'chooseAcitve' : ''"
      >
        <el-image
          :src="recallClick ? recallImg : recallImg_1"
          fit="contain"
        ></el-image>
        <span style="vertical-align: middle">{{
          routeLanguage.operationBtn.recall
        }}</span>
      </el-button>
      <el-button
        @click="importEvent"
        :class="importClick ? 'chooseAcitve' : ''"
      >
        <el-image
          :src="importClick ? importImg : importImg_1"
          fit="contain"
        ></el-image>
        <span style="vertical-align: middle">{{
          routeLanguage.operationBtn.import
        }}</span>
      </el-button>
      <el-button
        @click="exportEvent"
        :class="exportClick ? 'chooseAcitve' : ''"
      >
        <el-image
          :src="exportClick ? exportImg : exportImg_1"
          fit="contain"
        ></el-image>
        <span style="vertical-align: middle">{{
          routeLanguage.operationBtn.export
        }}</span>
      </el-button>
    </div>
    <div
      class="timer"
      v-if="code == 1 || code == 3 || (code == 4 && routeItem) ? true : false"
    >
      <div class="timer-item">
        <div>{{ routeLanguage.routeLine.ET }}</div>
        <div class="timer-item-1">{{ estTime | estTimeFormat }}</div>
      </div>
      <div class="timer-item">
        <div>
          {{ routeLanguage.routeLine.distance }}（{{
            distance >= 1000 ? "KM" : "M"
          }}）
        </div>
        <div class="timer-item-1">{{ distance | distanceFormat }}</div>
      </div>
      <div class="timer-item" v-if="code == 3 || routeItem.type == 50">
        <div>{{ routeLanguage.routeLine.waypointCount }}</div>
        <div class="timer-item-1">{{ routeSpotCount }}</div>
      </div>
      <div class="timer-item" v-if="code == 3 || routeItem.type == 50">
        <div>{{ routeLanguage.routeLine.area }}（m²）</div>
        <div class="timer-item-1">{{ area }}</div>
      </div>
      <div class="timer-item" v-if="code == 3 || routeItem.type == 50">
        <div>{{ routeLanguage.routeLine.photoNumber }}</div>
        <div class="timer-item-1">{{ photoCount }}</div>
      </div>
    </div>
    <div class="layerBut">
      <el-popover
        placement="left"
        trigger="manual"
        popper-class="popover-item popover-item-1"
        v-model="layerCode1"
      >
        <div>
          <el-autocomplete
            popper-class="autoInput"
            v-model="searchSite"
            :fetch-suggestions="searchSiteTip"
            :placeholder="routeLanguage.tool.placeholder"
            @keyup.enter.native="chooseSite()"
            clearable
            @select="handleSelect"
          >
            <el-button slot="append" @click="chooseSite()"
              ><el-image :src="searchImg1"></el-image
            ></el-button>
            <template slot-scope="{ item }">
              <div class="name">{{ item.name }}</div>
            </template>
          </el-autocomplete>
        </div>
        <el-button slot="reference" @click="layerCode1 = !layerCode1">
          <el-image
            :src="layerCode1 ? searchImg1_1 : searchImg1"
            fit="fill"
          ></el-image>
        </el-button>
      </el-popover>
      <el-popover
        placement="left"
        trigger="manual"
        popper-class="popover-item popover-item-2"
        v-model="layerCode2"
      >
        <div class="content">
          <el-button
            v-for="(item, index) in toolMenu"
            :key="item.value"
            :class="choosed == item.value ? 'actived' : ''"
            @click="toolEvent(item.value)"
          >
            <el-image
              :src="choosed == item.value ? item.imgSrc_1 : item.imgSrc"
              fit="fill"
              :class="
                item.value == 5 || item.value == 4
                  ? 'el-image_1'
                  : item.value == 10
                  ? 'el-image_2'
                  : ''
              "
            ></el-image>
            {{ routeLanguage.toolMenu[index] }}
          </el-button>
        </div>
        <el-button slot="reference" @click="layerCode2 = !layerCode2">
          <el-image
            :src="layerCode2 ? toolImg_1 : toolImg"
            fit="fill"
          ></el-image>
        </el-button>
      </el-popover>
      <el-popover
        placement="left-start"
        trigger="manual"
        v-model="layerCode"
        popper-class="popover-item popover-item-3"
      >
        <layer-div
          :weatherCode="weatherCode"
          @weacode="weacode"
          @zoneState="zoneState"
          ref="layerDiv"
          :toolLanguage="routeLanguage.tool"
        ></layer-div>
        <el-button slot="reference" @click="layerCode = !layerCode">
          <el-image
            :src="layerCode ? layerImg_1 : layerImg"
            fit="fill"
          ></el-image>
        </el-button>
      </el-popover>
    </div>
    <div class="weatherDiv" id="weatherDiv" ref="weather1" v-show="weatherCode">
      <div class="weaTitle">
        <span>{{ weatherData.title }}</span>
        <span style="margin-left: 80px">{{ weatherData.times }}</span>
        <el-button @click="refreshWea"
          ><el-image :src="weatherCode_2 ? refreshImg_1 : refreshImg"></el-image
        ></el-button>
        <el-button @click="closeWea"
          ><el-image :src="weatherCode_1 ? closeImg_1 : closeImg"></el-image
        ></el-button>
      </div>
      <el-divider></el-divider>
      <div class="weaContent">
        <el-row>
          <el-col :span="14" class="temperaClass">
            <span class="item_1">{{ weatherData.temperature }}</span>
            <span style="font-size: 30px">℃</span>
          </el-col>
          <el-col :span="10" class="temperaClass_1">
            <div>{{ weatherData.weather }}</div>
            <div>
              风向：{{ weatherData.winddirection }}{{ weatherData.windpower }}级
            </div>
            <div>相对湿度{{ weatherData.humidity }}%</div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import initMaps from "../../utils/maps";
import layerDiv from "./components/layer";
import togeojson from "../../../node_modules/@mapbox/togeojson";
import planList from "./components/planList";
import minxins from "../../utils/draw";
import {
  searchTip,
  searchPoi,
  searchLnglat,
  searchSky,
} from "../../utils/mapApi";
import requestHttp from "../../utils/api";
import cameraParam from "./components/cameraParam.vue";
import silderInput from "./components/silderInput.vue";
import { gcj02_to_wgs84 } from "../../utils/wgs84_to_gcj02";
export default {
  name: "googleMap",
  components: {
    planList,
    layerDiv,
    cameraParam,
    silderInput,
  },
  mixins: [minxins],
  data() {
    return {
      map: "",
      mapLoading: true,
      distancePoint: [],
      distanceCode: 1,
      awaitMap: false,
      fenceItem: {},
      mouseTool: "",
      searchValue: "",
      searchSite: "",
      addplanCode: false,
      addwordCode: false,
      loading: false,
      pointCodes: [],
      estTime: 0,
      distance: 0,
      timeOut: "",
      focusPoint: "",
      zone_type: "",
      fm: 50000,
      zoomShow: true,
      lngIndex: "",
      importCode: false,
      polyponZone: [],
      zoneList: [],
      routeTitle: "",
      fenceTitle: "",
      fenceForm: {
        name: "",
        point_json: [],
        height_limit: 200,
      },
      heights: [],
      routeForm: {
        name: "",
        auto_speed: 10,
        max_speed: 20,
        default_height: 100,
        return_height: 100,
        action_completed: 20,
        point_json: [],
      },
      orthoForm: {
        name: "",
        auto_speed: 10,
        max_speed: 20,
        default_height: 100,
        return_height: 100,
        action_completed: 20,
        point_json: [],
        cameraType: "",
        lateral: 80,
        course: 70,
        wheelDist: 10,
        angle: 0,
      },
      fenceRules: {
        name: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
      },
      routeRules: {
        name: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        auto_speed: [{ validator: this.numFormat, trigger: "change" }],
        max_speed: [{ validator: this.numFormat, trigger: "change" }],
        default_height: [{ validator: this.numFormat, trigger: "change" }],
        return_height: [{ validator: this.numFormat, trigger: "change" }],
        action_completed: [
          {
            required: true,
            message: "",
            trigger: "change",
          },
        ],
      },
      orthoRules: {
        name: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        auto_speed: [{ validator: this.numFormat, trigger: "change" }],
        max_speed: [{ validator: this.numFormat, trigger: "change" }],
        default_height: [{ validator: this.numFormat, trigger: "change" }],
        return_height: [{ validator: this.numFormat, trigger: "change" }],
        action_completed: [
          {
            required: true,
            message: "",
            trigger: "change",
          },
        ],
        cameraType: {
          required: true,
          message: "",
          trigger: "change",
        },
      },
      options: "",
      cameraOptions: [],
      cameraParamList: [],
      actionOptions: "",
      allActionList: [],
      noChange: false,
      routeSpotCount: 0,
      area: 0,
      photoCount: 0,
      lat: "",
      lng: "",
      deviceList: "",
      drawend: false,
      layerCode: false,
      layerCode1: false,
      layerCode2: false,
      clickSetCode: false,
      searchCode: false,
      removeClick: false,
      importClick: false,
      exportClick: false,
      delClick: false,
      recallClick: false,
      iconCode: false,
      saveCode: false,
      addActionCode: 0,
      fenceFromCode: false,
      routeFormCode: false,
      orthoFormCode: false,
      messageCodeWarning: false,
      num: 0,
      markers: [],
      addMarkers: [],
      line: "",
      polypon: "",
      startPoints: "",
      typeCode: "",
      searchTips: "",
      choosed: 0,
      toolMenu: [
        {
          imgSrc: require("../../assets/img/routeplan/site.png"),
          imgSrc_1: require("../../assets/img/routeplan/site_1.png"),
          value: 1,
        },
        {
          imgSrc: require("../../assets/img/routeplan/distance.png"),
          imgSrc_1: require("../../assets/img/routeplan/distance_1.png"),
          value: 2,
        },
        {
          imgSrc: require("../../assets/img/routeplan/area.png"),
          imgSrc_1: require("../../assets/img/routeplan/area_1.png"),
          value: 3,
        },
        {
          imgSrc: require("../../assets/img/routeplan/circle.png"),
          imgSrc_1: require("../../assets/img/routeplan/circle_1.png"),
          value: 4,
        },
        {
          imgSrc: require("../../assets/img/routeplan/direction.png"),
          imgSrc_1: require("../../assets/img/routeplan/direction_1.png"),
          value: 5,
        },
        {
          imgSrc: require("../../assets/img/routeplan/delall.png"),
          imgSrc_1: require("../../assets/img/routeplan/delall_1.png"),
          value: 10,
        },
      ],
      siteMark: "",
      pointLength: 0,
      choosePoint: 0,
      centerMarker: [],
      polyponList: [],
      weatherData: "",
      rangTool: "",
      weatherCode: false,
      weatherCode_1: false,
      weatherCode_2: false,
      lnglat: "",
      originalX: 0,
      originalY: 0,
      clickId: 0,
      clickRoute: "",
      clickRoutePolypon: "",
      routeMarkers: [],
      deviceMarker: [],
      textLabel: [],
      changeCode: false,
      cacheData: [],
      routePoints: [],
      line1: "",
      line2: "",
      line3: "",
      startMarker: "",
      endMarker: "",
      pointImg: require("../../assets/img/routeplan/point.png"),
      removeImg: require("../../assets/img/routeplan/remove.png"),
      delImg: require("../../assets/img/routeplan/del.png"),
      recallImg: require("../../assets/img/routeplan/recall.png"),
      importImg: require("../../assets/img/routeplan/import.png"),
      exportImg: require("../../assets/img/routeplan/export.png"),
      pointImg_1: require("../../assets/img/routeplan/point_1.png"),
      removeImg_1: require("../../assets/img/routeplan/remove_1.png"),
      delImg_1: require("../../assets/img/routeplan/del_1.png"),
      recallImg_1: require("../../assets/img/routeplan/recall_1.png"),
      importImg_1: require("../../assets/img/routeplan/import_1.png"),
      exportImg_1: require("../../assets/img/routeplan/export_1.png"),
      searchImg1: require("../../assets/img/routeplan/toolSearch.png"),
      toolImg: require("../../assets/img/routeplan/tool.png"),
      layerImg: require("../../assets/img/routeplan/toolLayer.png"),
      searchImg1_1: require("../../assets/img/routeplan/toolSearch_1.png"),
      toolImg_1: require("../../assets/img/routeplan/tool_1.png"),
      layerImg_1: require("../../assets/img/routeplan/toolLayer_1.png"),
      setImg: require("../../assets/img/routeplan/set.png"),
      setImg_1: require("../../assets/img/routeplan/set_1.png"),
      iconImg: require("../../assets/img/routeplan/pldot.png"),
      refreshImg: require("../../assets/img/routeplan/refresh.png"),
      refreshImg_1: require("../../assets/img/routeplan/refresh_1.png"),
      closeImg: require("../../assets/img/routeplan/close.png"),
      closeImg_1: require("../../assets/img/routeplan/close_1.png"),
    };
  },
  created() {
    this.routeTitle = this.routeLanguage.routeLine.setNewTask;
    this.fenceTitle = this.routeLanguage.fence.addFence;
    this.fenceRules.name[0].message = this.routeLanguage.fence.placeholder1;
    this.routeRules.name[0].message = this.routeLanguage.routeLine.placeholder;
    this.routeRules.action_completed[0].message =
      this.routeLanguage.routeLine.placeholder1;
    this.orthoRules.name[0].message = this.routeLanguage.routeLine.placeholder;
    this.orthoRules.action_completed[0].message =
      this.routeLanguage.routeLine.placeholder1;
    this.orthoRules.cameraType.message =
      this.routeLanguage.routeLine.placeholder3;
    this.options = this.routeLanguage.options;
    this.cameraOptions = [
      {
        label: this.routeLanguage.routeLine.customCamera,
        value: 1,
        params: [
          {
            name: this.routeLanguage.routeLine.sensor,
            width: "13.20",
            height: "8.80",
            id: "sensor",
          },
          {
            name: this.routeLanguage.routeLine.image,
            width: "5480",
            height: "3648",
            id: "image",
          },
          {
            name: this.routeLanguage.routeLine.focalLength,
            width: "",
            height: "25.0",
            id: "focalLength",
          },
        ],
      },
      {
        label: this.routeLanguage.routeLine.cameraType1,
        value: 2,
        params: [
          {
            name: this.routeLanguage.routeLine.sensor,
            width: "13.20",
            height: "8.80",
            id: "sensor",
          },
          {
            name: this.routeLanguage.routeLine.image,
            width: "5480",
            height: "3648",
            id: "image",
          },
          {
            name: this.routeLanguage.routeLine.focalLength,
            width: "",
            height: "9.0",
            id: "focalLength",
          },
        ],
      },
    ];
    this.actionOptions = this.routeLanguage.waypoint.actionOptions;
  },

  mounted() {
    this.mountedFun();
    // console.log(navigator.onLine)检测是否联网
  },
  methods: {
    async mountedFun() {
      this.obj = this.$refs.fileRef;
      await this.init();
      await this.getDeviceData();
      // this.map.setLayers([new AMap.TileLayer.Satellite()])
      this.map.on("click", this.getClickPoint);
      this.map.on("zoomend", this.changeZoom);
      this.map.on("dragend", this.dragMap);
      await this.$refs.planList.getFenceList();

      this.mapLoading = false;
      this.$store.commit("setMultiMessage", {
        key: "equipment",
        message: this.getMessage,
      });
      this.dragDivOpen();
      this.getZone();
    },
    //获取设备列表数据
    async getDeviceData() {
      let data = {
        page: 0,
        size: 100,
        type: 10,
      };
      data.pmd = data.page.toString() + data.type.toString();
      await requestHttp("deviceList", data).then((res) => {
        this.deviceList = res.data.list?res.data.list:[];
      });
      //console.log(this.deviceList)
      this.drawDevicePoint();
    },
    //绘画设备坐标
    drawDevicePoint() {
      let points = [];
      for (let index = 0; index < this.deviceList.length; index++) {
        let path = gcj02_to_wgs84(
          this.deviceList[index].lon_int / 1e7,
          this.deviceList[index].lat_int / 1e7
        );
        points.push({
          lnglat: path,
          id: this.deviceList[index].sn_id,
          name: this.deviceList[index].name,
          state: this.deviceList[index].is_push_on,
        });
      }
      //点聚合
      let cluster = initMaps.markerMerge(this.map, points);
      let self = this;
      cluster.on("click", function (e) {
        if (e.clusterData.length > 1) {
          self.info = initMaps.setInfoWindow(e.clusterData);
          let point = e.marker.getPosition();
          self.info.open(self.map, point);
          self.info.on("mouseover", self.mouseoverEvent);
          self.info.on("mouseout", self.mouseoutEvent);
        }
      });
    },
    //鼠标移入窗口
    mouseoverEvent() {
      this.map.setStatus({ scrollWheel: false });
    },
    //鼠标移除窗口
    mouseoutEvent() {
      this.map.setStatus({ scrollWheel: true });
    },
    //地图缩放事件
    changeZoom(e) {
      let zoom = e.target.getZoom();
      if (this.distancePoint) {
        if (zoom < 11.5) {
          this.distanceCode = 0;
          this.map.remove(this.distancePoint);
        } else {
          if (this.distanceCode == 0) {
            for (let index = 0; index < this.distancePoint.length; index++) {
              this.distancePoint[index].setMap(this.map);
            }
          }
        }
      }
      if (this.code != 1 && this.code != 2 && this.code != 3) {
        if (zoom >= 9) {
          for (let index = 0; index < this.polyponZone.length; index++) {
            for (let i = 0; i < this.zone_type.length; i++) {
              if (
                this.polyponZone[index].getExtData().type == this.zone_type[i]
              ) {
                this.polyponZone[index].setMap(this.map);
              }
            }
          }
          this.zoomShow = false;
          this.getZone();
        } else {
          if (!this.zoomShow) {
            for (let index = 0; index < this.polyponZone.length; index++) {
              this.map.remove(this.polyponZone[index]);
            }
            this.zoomShow = true;
          }
          this.fm = 100000;
          this.getZone();
        }
      }
    },
    //地图拖拽事件
    dragMap(e) {
      this.getZone();
    },
    // 初始化地图
    async init() {
      await initMaps
        .initMap("maps", {
          //   mapStyle: "amap://styles/92907bd07b27bf2b8ca66585015fdc7a",
          //   viewMode: "3D",
          layer: true,
        })
        .then((res) => {
          this.map = res;
          // this.map.setMapStyle("amap://styles/c4431fd33bde81876cb984544ac4d8d7")
        });
      let self = this;
      this.map.plugin("AMap.Geolocation", function () {
        var geolocation = new AMap.Geolocation({
          enableHighAccuracy: true, //是否使用高精度定位，默认:true
          timeout: 1000, //超过10秒后停止定位，默认：无穷大
          maximumAge: 0, //定位结果缓存0毫秒，默认：0
          convert: true, //自动偏移坐标，偏移后的坐标为高德坐标，默认：true
          showButton: true, //显示定位按钮，默认：true
          buttonPosition: "LB", //定位按钮停靠位置，默认：'LB'，左下角
          buttonOffset: new AMap.Pixel(10, 20), //定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
          showMarker: false, //定位成功后在定位到的位置显示点标记，默认：true
          showCircle: false, //定位成功后用圆圈表示定位精度范围，默认：true
          panToLocation: true, //定位成功后将定位到的位置作为地图中心点，默认：true
          zoomToAccuracy: false, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        });
        self.map.addControl(geolocation);
        geolocation.getCurrentPosition();
        // geolocation.getCurrentPosition((status, result) => {
        //   if (status == "complete") {
        //   } else if (status == "error") {
        //
        //   }
        // });
        geolocation.on("error", function () {
          self.$message.info(self.routeLanguage.placeholder);
        });
      });
      AMapUI.loadUI(["control/BasicControl"], function (BasicControl) {
        //添加一个缩放控件
        // map.addControl(new BasicControl.Zoom({
        //     position: 'lt'
        // }));

        //缩放控件，显示Zoom值
        self.map.addControl(
          new BasicControl.Zoom({
            position: {
              right: "30px",
              bottom: "70px",
            },
            showZoomNum: true,
          })
        );
      });
      let controlBar = new AMap.ControlBar({
        position: {
          bottom: "230px",
          right: "2px",
        },
      });
      this.map.addControl(controlBar);
      this.map.setZoom(15);
      setTimeout(() => {
        this.getWeather(this.map.getCenter());
        this.weatherCode = true;
      }, 500);
      // let layers=this.map.getLayers()
      // for (let index = 0; index < layers.length; index++) {
      //   this.map.removeLayer(layers[index])
      // }
      // console.log(this.map.getLayers())
    },
    //获取天气信息
    async getWeather(points) {
      this.lnglat = points;
      let point = points.lng + "," + points.lat;
      let adcode = 0;
      let city = "";
      await searchLnglat(point).then((res) => {
        adcode = res.data.regeocode.addressComponent.adcode;
        city = res.data.regeocode.addressComponent.city;
      });
      await searchSky(adcode).then((e) => {
        this.weatherData = e.data.lives[0];
        if (this.weatherData.province) {
          this.weatherData.title =
            this.weatherData.province +
            "省" +
            (city == this.weatherData.city
              ? this.weatherData.city
              : city + this.weatherData.city);
          let time = new Date();
          let hours =
            time.getHours() > 9 ? time.getHours() : "0" + time.getHours();
          let minutes =
            time.getMinutes() > 9 ? time.getMinutes() : "0" + time.getMinutes();
          this.weatherData.times = hours + ":" + minutes;
        }
      });
      setTimeout(() => {
        this.weatherCode_2 = false;
      }, 200);
    },
    //获取点击地图的坐标
    getClickPoint(e) {
      this.getWeather(e.lnglat);
    },
    //地址提示
    async searchSiteTip(queryString, callback) {
      if (queryString) {
        await searchTip(this.searchSite).then((res) => {
          if (res.data.status == 1) {
            this.searchTips = res.data.tips;
          } else this.searchTips = [];
        });
      } else this.searchTips = [];
      callback(this.searchTips);
    },
    //选中后的值
    handleSelect(e) {
      this.searchSite = e.name;
      this.chooseSite(e);
    },
    //点击搜索地址
    chooseSite(item) {
      this.searchSite = item ? item.name : this.searchSite;
      searchPoi(this.searchSite).then((res) => {
        if (res.data.status == 1 && res.data.pois.length > 0) {
          let pointData = res.data.pois[0];
          let a = pointData.location.split(",");
          this.map.setCenter([a[0], a[1]]);
          let points = {
            lng: a[0],
            lat: a[1],
          };
          this.getWeather(points);
        }
      });
    },
    //接收天气图层返回的参数
    weacode(e) {
      this.weatherCode = e;
    },
    //点击关闭天气图层
    closeWea() {
      this.weatherCode_1 = true;
      this.timeOut = setTimeout(() => {
        this.weatherCode = false;
        this.weatherCode_1 = false;
      }, 200);
    },
    //刷新天气图层
    refreshWea() {
      this.weatherData = "";
      this.weatherCode_2 = true;
      this.getWeather(this.lnglat);
    },
    //设置拖拽
    dragDivOpen() {
      let divId = document.getElementById("weatherDiv");
      if (divId) {
        divId.onmousedown = this.downDiv;
      }
    },
    //鼠标按下
    downDiv(e) {
      e = e || window.event;
      // get the mouse cursor position at startup:
      this.originalX = e.clientX;
      this.originalY = e.clientY;
      document.onmouseup = this.closeDragDiv;
      // call a function whenever the cursor moves:
      document.onmousemove = this.dragDiv;
    },
    //拖拽div
    dragDiv(e) {
      let divId = document.getElementById("weatherDiv");
      e = e || window.event;
      // calculate the new cursor position:
      let x = this.originalX - e.clientX;
      let y = this.originalY - e.clientY;
      this.originalX = e.clientX;
      this.originalY = e.clientY;
      // set the element's new position:
      let bottom = parseInt(window.getComputedStyle(divId).bottom) + y;
      let right = parseInt(window.getComputedStyle(divId).right) + x;
      let top = parseInt(window.getComputedStyle(divId).top) - y;
      let left = parseInt(window.getComputedStyle(divId).left) - x;
      if (bottom > 0 && top > 0) {
        divId.style.bottom = bottom + "px";
      }
      if (right > 0 && left > 365) {
        divId.style.right = right + "px";
      }
    },
    //关闭拖拽
    closeDragDiv() {
      document.onmouseup = null;
      document.onmousemove = null;
    },
    //点击触发工具事件
    toolEvent(index) {
      this.map.off("mousemove", this.drawSite);
      if (this.choosed === index) {
        this.choosed = 0;
        if (this.mouseTool) {
          this.mouseTool.close(true);
          this.mouseTool = "";
        }
        if (this.siteMark) {
          this.map.remove(this.siteMark);
          this.siteMark = "";
        }
        if (this.centerMarker.length > 0) {
          this.map.remove(this.centerMarker);
          this.centerMarker = [];
        }
        if (this.textLabel.length > 0) {
          this.map.remove(this.textLabel);
          this.textLabel = [];
        }
        if (this.rangTool) {
          this.rangTool.turnOff(true);
          this.rangTool = "";
        }
      } else {
        if (index !== 1) {
          if (this.siteMark) {
            this.map.remove(this.siteMark);
            this.siteMark = "";
          }
        }
        if (index !== 5) {
          if (this.rangTool) {
            this.rangTool.turnOff(true);
            this.rangTool = "";
          }
        }
        this.choosed = index;
        if (!this.mouseTool && index !== 1) {
          this.mouseTool = new AMap.MouseTool(this.map);
        }
        if (this.mouseTool) {
          this.mouseTool.off("draw", this.drawEvent);
          this.mouseTool.off("draw", this.drawEvent1);
        }
        switch (index) {
          case 1: {
            if (this.drawend) {
              this.drawMark();
            }
            if (this.mouseTool) {
              this.mouseTool.off("draw", this.drawEvent);
              this.mouseTool.close(true);
              this.mouseTool = "";
            }
            if (this.centerMarker.length > 0) {
              this.map.remove(this.centerMarker);
              this.centerMarker = [];
            }
            if (this.textLabel.length > 0) {
              this.map.remove(this.textLabel);
              this.textLabel = [];
            }
            this.map.on("mousemove", this.drawSite);
            break;
          }
          case 2: {
            if (this.drawend) {
              this.drawMark();
            }
            this.mouseTool.rule({
              startMarkerOptions: {
                //可缺省
                icon: new AMap.Icon({
                  size: new AMap.Size(19, 31), //图标大小
                  imageSize: new AMap.Size(19, 31),
                  image: "//webapi.amap.com/theme/v1.3/markers/b/start.png",
                }),
                offset: new AMap.Pixel(-9, -31),
              },
              endMarkerOptions: {
                //可缺省
                icon: new AMap.Icon({
                  size: new AMap.Size(19, 31), //图标大小
                  imageSize: new AMap.Size(19, 31),
                  image: "//webapi.amap.com/theme/v1.3/markers/b/end.png",
                }),
                offset: new AMap.Pixel(-9, -31),
              },
              midMarkerOptions: {
                //可缺省
                icon: new AMap.Icon({
                  size: new AMap.Size(19, 31), //图标大小
                  imageSize: new AMap.Size(19, 31),
                  image: "//webapi.amap.com/theme/v1.3/markers/b/mid.png",
                }),
                offset: new AMap.Pixel(-9, -31),
              },
              lineOptions: {
                //可缺省
                strokeStyle: "solid",
                strokeColor: "#FF33FF",
                strokeOpacity: 1,
                strokeWeight: 2,
              },
              //同 RangingTool 的 自定义 设置，缺省为默认样式
            });
            break;
          }
          case 3: {
            if (this.drawend) {
              this.drawMark();
            }
            this.mouseTool.polygon({
              strokeColor: "#80d8ff",
              fillColor: "#80d8ff",
              fillOpacity: 0.3,
              //同 Polygon 的 Option 设置
            });
            this.mouseTool.on("draw", this.drawEvent1);
            break;
          }
          case 4: {
            if (this.drawend) {
              this.drawMark();
            }
            this.mouseTool.circle({
              fillColor: "#00b0ff",
              strokeColor: "#80d8ff",
            });
            this.mouseTool.on("draw", this.drawEvent);

            break;
          }
          case 5: {
            if (this.drawend) {
              this.drawMark();
            }
            if (this.mouseTool) {
              this.mouseTool.off("draw", this.drawEvent);
              this.mouseTool.close(true);
              this.mouseTool = "";
            }
            if (this.centerMarker.length > 0) {
              this.map.remove(this.centerMarker);
              this.centerMarker = [];
            }
            if (this.textLabel.length > 0) {
              this.map.remove(this.textLabel);
              this.textLabel = [];
            }
            let rulerOptions = {
              startMarkerOptions: {
                //可缺省
                icon: new AMap.Icon({
                  size: new AMap.Size(19, 31), //图标大小
                  imageSize: new AMap.Size(19, 31),
                  image: "//webapi.amap.com/theme/v1.3/markers/b/start.png",
                }),
                offset: new AMap.Pixel(-9, -31),
              },
              endMarkerOptions: {
                //可缺省
                icon: new AMap.Icon({
                  size: new AMap.Size(19, 31), //图标大小
                  imageSize: new AMap.Size(19, 31),
                  image: "//webapi.amap.com/theme/v1.3/markers/b/end.png",
                }),
                offset: new AMap.Pixel(-9, -31),
                id: 1,
              },
              midMarkerOptions: {
                //可缺省
                icon: new AMap.Icon({
                  size: new AMap.Size(19, 31), //图标大小
                  imageSize: new AMap.Size(19, 31),
                  image: "//webapi.amap.com/theme/v1.3/markers/b/mid.png",
                }),
                offset: new AMap.Pixel(-9, -31),
              },
              lineOptions: {
                //可缺省
                strokeStyle: "solid",
                strokeColor: "#FF33FF",
                strokeOpacity: 1,
                strokeWeight: 2,
              },
              //同 RangingTool 的 自定义 设置，缺省为默认样式
            };
            this.rangTool = new AMap.RangingTool(this.map, rulerOptions);
            this.rangTool.turnOn();
            var points = [];
            var self = this;
            this.rangTool.on("addnode", function (e) {
              if (points.length < 2) {
                points.push(e.position);
                if (points.length == 2) {
                  self.rangTool.turnOff();
                  let a = document.getElementsByClassName("amap-ranging-label");
                  let b = a[a.length - 1].getElementsByTagName("span")[0];
                  b.innerHTML =
                    "方位角：" +
                    self.calcAngle(points).toFixed(7) +
                    " ,  " +
                    b.innerHTML;
                  points = [];
                  self.rangTool.turnOn();
                }
              }
            });
            break;
          }
          case 10: {
            if (this.mouseTool) {
              this.mouseTool.close(true);
              this.mouseTool = "";
            }
            if (this.centerMarker.length > 0) {
              this.map.remove(this.centerMarker);
              this.centerMarker = [];
            }
            if (this.textLabel.length > 0) {
              this.map.remove(this.textLabel);
              this.textLabel = [];
            }
            // this.$message.info("已清除所有测量！");
            this.timeOut = setTimeout(() => {
              this.choosed = 0;
            }, 200);
          }
        }
      }
    },
    drawEvent(e) {
      let icon = new AMap.Icon({
        size: new AMap.Size(12, 12),
        image: this.iconImg,
      });
      var marker = new AMap.Marker({
        position: e.obj.getCenter(),
        icon: icon,
        offset: new AMap.Pixel(-5, -5),
        label: {
          content:
            "<div>圆心：[" +
            e.obj.getCenter().lng +
            "，" +
            e.obj.getCenter().lat +
            "]</div><div>面积：" +
            e.obj.getArea() +
            "平方米</div>",
          direction: "top",
        },
      });
      marker.setMap(this.map);
      this.centerMarker.push(marker);
    },
    drawEvent1(e) {
      let lnglatArr = e.obj.getPath();
      let point = this.computeCenter(lnglatArr);

      let b =
        Math.round(this.computedMethod(3, { fence: lnglatArr }) * 10) / 10;
      let textMarker = new AMap.Text({
        text: b + "平方米",
        anchor: "center", // 设置文本标记锚点
        draggable: false,
        style: {
          "background-color": "#fff",
          padding: "3px 7px 3px 2px",
          width: "auto",
          border: "1px soild #CCC",
          "text-align": "center",
          "font-size": "12px",
          "line-height": "14px",
          color: "black",
        },
        position: point,
      });
      textMarker.setMap(this.map);
      this.textLabel.push(textMarker);
    },
    drawSite(e) {
      if (!this.siteMark) {
        let icon = new AMap.Icon({
          // size: new AMap.Size(36, 36),
          imageSize: new AMap.Size(30, 30),
          image:
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAe1BMVEVHcEw3csc+cbw8cr89cLoyc9JBb7Mhd/QldutId7s2csklduw+cbk2c8pBcrhPfL1PfL01c8s4cscmduokd+4tdd2Ou/wjd/FAb7NCcbU0cswnducpdeM7cb4xdNVBcLU8cr8pdeQ9cr4hd/M1c8weePogd/UeePkgePftEfpQAAAAJXRSTlMAtnOLatsd/vwZwfxdxUYEEMa1+/3vAv4wF8v59ofhM4j2ff7KTLmGfwAAATZJREFUSMftlFtzglAMhBdE7qjcFMR7+6H//xf2QW1ri8hx+tR2XzInk51JNjkr/UpMnWXZeF5TLp3pgPJZVvCOIps9qh/53MAf9ZbPE74hmffUbwBYjNeTup7sxgsANvcZCUCbute3m7YAyd3+AaL4cyqOAO7MMfOBaHub3EaA361VBrSxJAXhyvNWYSBJcQtknfsqgFSS7PysT25LUgoUXRt0gIUryT5eFT3aktwF4HQQlsBYUpB/7CAPJI2BZQehBNaSQuBkVZV1AkJJO6DsIDTARNIesCTJAlaSJkDTQfCA+hIrSaoAT1J9iYMJAfDS09JqaEvGQ/fK+voTizM+jZvj2w84PvPzNv5A5l/0iwmsH5uAsc2YG9kTVnkxY4CBZnwGgJHl/0mCD74R4eD7B/3jabwB+hhDfDlm+u4AAAAASUVORK5CYII=",
        });
        this.siteMark = new AMap.Marker({
          position: e.lnglat,
          label: {
            content: "经度" + e.lnglat.lng + ",纬度" + e.lnglat.lat,
            direction: "top",
          },
          icon: icon,
          offset: new AMap.Pixel(-15, -15),
        });
        this.siteMark.setMap(this.map);
      } else {
        this.siteMark.setPosition(e.lnglat);
        this.siteMark.setLabel({
          content: "经度" + e.lnglat.lng + ",纬度" + e.lnglat.lat,
          direction: "top",
        });
      }
    },
    //经纬度计算中心点
    computeCenter(lnglatArr) {
      var total = lnglatArr.length;
      var X = 0,
        Y = 0,
        Z = 0;
      lnglatArr.map((item) => {
        var lng = (item.lng * Math.PI) / 180;
        var lat = (item.lat * Math.PI) / 180;
        var x, y, z;
        x = Math.cos(lat) * Math.cos(lng);
        y = Math.cos(lat) * Math.sin(lng);
        z = Math.sin(lat);
        X += x;
        Y += y;
        Z += z;
      });
      X = X / total;
      Y = Y / total;
      Z = Z / total;
      var Lng = Math.atan2(Y, X);
      var Hyp = Math.sqrt(X * X + Y * Y);
      var Lat = Math.atan2(Z, Hyp);
      return new AMap.LngLat((Lng * 180) / Math.PI, (Lat * 180) / Math.PI);
    },
    //计算方位角
    calcAngle(points) {
      let start = [points[0].lng, points[0].lat];
      let end = [points[1].lng, points[1].lat];
      var p_start = this.map.lngLatToContainer(start),
        p_end = this.map.lngLatToContainer(end);
      var diff_x = p_end.x - p_start.x,
        diff_y = p_end.y - p_start.y;
      let a = (360 * Math.atan2(diff_y, diff_x)) / (2 * Math.PI) + 90;
      if (a > 180) {
        a = -(360 - a);
      }
      return a;
    },
    //点击返回列表页面
    goBack() {
      this.iconCode = true;
      if (this.code == 2 && !this.fenceFromCode) {
        this.originalData();
        this.iconCode = false;
        return false;
      } else if (this.code == 1 && !this.routeFormCode) {
        this.originalData();
        this.iconCode = false;
        return false;
      } else if (this.code == 3 && !this.orthoFormCode) {
        this.originalData();
        this.iconCode = false;
        return false;
      } else {
        this.$confirm(
          this.code == 1 || this.code == 3
            ? this.routeLanguage.placeholder1
            : this.routeLanguage.placeholder2,
          this.routeLanguage.tip,
          {
            confirmButtonText: this.routeLanguage.saveBtn,
            cancelButtonText: this.routeLanguage.cancelBtn,
            type: "warning",
            customClass: "messageTip",
          }
        )
          .then(() => {
            this.originalData();
          })
          .catch((e) => {})
          .finally(() => {
            this.iconCode = false;
          });
      }
    },
    //初始化数据
    originalData() {
      this.pointCodes = [];
      if (this.drawend) {
        this.drawMark();
      }
      this.clickId = 0;
      this.map.remove(this.markers);
      this.map.remove(this.addMarkers);
      this.markers = [];
      this.addMarkers = [];
      this.num = 0;
      if (this.code == 1) {
        this.routeForm = {
          name: "",
          auto_speed: 10,
          max_speed: 20,
          default_height: 100,
          return_height: 100,
          action_completed: 20,
          point_json: [],
        };
        this.routeTitle = this.routeLanguage.routeLine.setNewTask;
        this.allActionList = [];
        this.heights = [];
        this.map.remove(this.line);
        this.line = "";
        this.distance = 0;
        this.estTime = 0;
        this.$store.commit("changeCode", 4);
        this.$store.commit("routeItem", "");
        this.$store.commit("checkType", "");
        this.$refs.planList.page = 1;
        this.$refs.planList.getMissionList();
        this.getFenceItem();
      } else if (this.code == 2) {
        this.fenceForm = {
          name: "",
          point_json: [],
          height_limit: 200,
        };
        this.map.remove(this.polypon);
        this.polypon = "";
        this.startPoints = "";
        this.$store.commit("changeCode", 0);
        this.$refs.planList.page = 1;
        this.$refs.planList.getFenceList();
        this.$store.commit("operateFItem", "");

        this.fenceTitle = this.routeLanguage.fence.addFence;
      } else if (this.code == 3) {
        this.orthoForm = {
          name: "",
          auto_speed: 10,
          max_speed: 20,
          default_height: 100,
          return_height: 100,
          action_completed: 20,
          point_json: [],
          cameraType: "",
          lateral: 80,
          course: 70,
          wheelDist: 10,
          angle: 0,
        };
        this.cameraParamList = [];
        this.routeTitle = this.routeLanguage.routeLine.setNewTask;
        if (this.polypon) {
          this.map.remove(this.polypon);
          this.polypon = "";
        }
        if (this.line) {
          this.map.remove(this.line);
          this.line = "";
        }
        if (this.startMarker) {
          this.map.remove(this.startMarker);
          this.map.remove(this.endMarker);
          this.startMarker = "";
          this.endMarker = "";
        }
        this.$store.commit("changeCode", 4);
        this.$store.commit("routeItem", "");
        this.$store.commit("checkType", "");
        this.$refs.planList.page = 1;
        this.$refs.planList.getMissionList();
        this.getFenceItem();
      }
      this.cacheData = [];
    },
    //绘制围栏列表
    drawFenceList() {
      this.map.remove(this.polyponList);
      this.polyponList = [];
      for (let index = 0; index < this.fence.length; index++) {
        console.log(this.fence[index]);
        let strPoint = [];
        for (let j = 1; j < this.fence[index].point_list.length + 1; j++) {
          for (let i = 0; i < this.fence[index].point_list.length; i++) {
            if (this.fence[index].point_list[i].seq == j) {
              // if(this.layer){
              let paths = "";
              if (this.fence[index].point_list[i].type == 20) {
                paths = gcj02_to_wgs84(
                  this.fence[index].point_list[i].lon_int / 1e7,
                  this.fence[index].point_list[i].lat_int / 1e7
                );
              } else {
                paths = [
                  this.fence[index].point_list[i].lon_int / 1e7,
                  this.fence[index].point_list[i].lat_int / 1e7,
                ];
              }
              // }
              let point = new AMap.LngLat(paths[0], paths[1]);
              strPoint.push(point);
              break;
            }
          }
        }
        let polypon = initMaps.drawPolypon(strPoint);
        polypon.id = this.fence[index].f_id;
        this.polyponList.push(polypon);
        polypon.setMap(this.map);
      }
    },
    //获取具体围栏信息
    getFenceItem() {
      // this.map.setZoom(17);
      for (let index = 0; index < this.fence.length; index++) {
        if (this.fence[index].f_id == this.fenceId) {
          this.fenceItem.title = this.fence[index].title;
          this.fenceItem.id = this.fenceId;
          this.fenceItem.height = this.fence[index].height_limit / 100;
          this.map.setFitView(
            this.polyponList[index],
            true,
            [100, 100, 150, 100],
            30
          );
          this.polyponList[index].show();
          this.fenceItem.paths = this.polyponList[index].getPath();
          this.getWeather(this.fenceItem.paths[0]);
          if (this.distancePoint) {
            this.map.remove(this.distancePoint);
          }
          this.distancePoint = [];
          for (let i = 0; i < this.fenceItem.paths.length - 1; i++) {
            let labelMarker = this.fenceListPoint([
              this.fenceItem.paths[i],
              this.fenceItem.paths[i + 1],
            ]);
            labelMarker.setMap(this.map);
            this.distancePoint.push(labelMarker);
          }
          let labelMarker = this.fenceListPoint([
            this.fenceItem.paths[this.fenceItem.paths.length - 1],
            this.fenceItem.paths[0],
          ]);
          labelMarker.setMap(this.map);
          this.distancePoint.push(labelMarker);
        } else {
          this.polyponList[index].hide();
        }
      }
    },
    //点击具体的航线显示/编辑
    getRouteItem() {
      this.distance = 0;
      this.estTime = 0;
      if (this.routeItem) {
        // getRouteParam(this.routeItem,this.fenceItem)
        this.routePoints = [];
        for (let index = 0; index < this.routeItem.point_list.length; index++) {
          if (this.routeItem.point_list[index].seq == index + 1) {
            // if(this.layer){
            let paths = "";
            if (this.routeItem.point_list[index].type == 20) {
              paths = gcj02_to_wgs84(
                this.routeItem.point_list[index].lon_int / 1e7,
                this.routeItem.point_list[index].lat_int / 1e7
              );
            } else {
              paths = [
                this.routeItem.point_list[index].lon_int / 1e7,
                this.routeItem.point_list[index].lat_int / 1e7,
              ];
            }

            // }
            let point = new AMap.LngLat(paths[0], paths[1]);
            this.routePoints.push(point);
          }
        }
        this.getWeather(this.routePoints[0]);
        if (this.clickRoute) {
          this.map.remove(this.clickRoute);
          this.clickRoute = "";
        }
        if (this.routeMarkers.length > 0) {
          this.map.remove(this.routeMarkers);
          this.routeMarkers = [];
        }
        if (this.routeItem.type == 50) {
          this.routeSpotCount = 0;
          this.area = 0;
          this.photoCount = 0;
          if (this.clickRoutePolypon) {
            this.map.remove(this.clickRoutePolypon);
            this.clickRoutePolypon = "";
          }
        }
        if (this.routeItem.typeCode) {
          if (this.routeItem.type == 20) {
            this.changeCode = true;
            this.noChange = true;
            this.routeTitle = this.routeLanguage.routeLine.editTitle;
            this.routeForm = {
              name: this.routeItem.title,
              auto_speed: this.routeItem.auto_speed / 100,
              max_speed: this.routeItem.max_speed / 100,
              default_height: this.routeItem.default_height / 100,
              return_height: this.routeItem.return_height / 100,
              action_completed: this.routeItem.action_completed,
            };
            this.markers = [];
            this.pointLength = 0;
            this.routeForm.point_json = [];
            this.addMarkers = [];
            this.heights = [];
            this.clickRoute = initMaps.drawPolyline(this.routePoints);
            this.clickRoute.setMap(this.map);
            this.map.setFitView(
              this.clickRoute,
              true,
              [200, 200, 200, 200],
              30
            );
            if (this.clickRoute) {
              this.map.remove(this.clickRoute);
              this.clickRoute = "";
            }
            this.loadingPoint();
          } else if (this.routeItem.type == 50) {
            this.changeCode = true;
            this.noChange = true;
            let camera_json = JSON.parse(this.routeItem.camera_json);
            this.routeTitle = this.routeLanguage.routeLine.editTitle;
            this.orthoForm = {
              name: this.routeItem.title,
              auto_speed: this.routeItem.auto_speed / 100,
              max_speed: this.routeItem.max_speed / 100,
              default_height: this.routeItem.default_height / 100,
              return_height: this.routeItem.return_height / 100,
              action_completed: this.routeItem.action_completed,
              point_json: [],
              cameraType: camera_json.camera_type,
              lateral: camera_json.lateral,
              course: camera_json.course,
              wheelDist: camera_json.wheelDist,
              angle: camera_json.angle,
            };

            if (camera_json.camera_type == 1) {
              this.cameraParamList = camera_json.cameraParamList;
            }
            this.markers = [];
            this.pointLength = 0;
            this.routeForm.point_json = [];
            this.addMarkers = [];
            this.clickRoute = initMaps.drawPolypon(this.routePoints);
            this.clickRoute.setMap(this.map);
            this.map.setFitView(
              this.clickRoute,
              true,
              [200, 200, 200, 200],
              30
            );
            if (this.clickRoute) {
              this.map.remove(this.clickRoute);
              this.clickRoute = "";
            }
            this.loadingPoint1();
          }
        } else {
          for (let index = 0; index < this.routePoints.length; index++) {
            let params = {
              offset: -17,
              clickable: false,
              draggable: false,
            };
            let marker = initMaps.drawMarker(
              index + 1,
              this.routePoints[index],
              "marker-edit",
              params
            );
            marker.setMap(this.map);
            this.routeMarkers.push(marker);
          }
          if (this.routeItem.type == 20) {
            this.clickRoute = initMaps.drawPolyline(this.routePoints);
            this.clickRoute.setMap(this.map);
            for (let index = 0; index < this.routePoints.length; index++) {
              if (index < this.routePoints.length - 1) {
                this.distance += this.computedMethod(1, {
                  point1: this.routePoints[index],
                  point2: this.routePoints[index + 1],
                });
              }
            }
            this.estTime =
              this.distance / (this.routeItem.auto_speed / 100) +
              this.routePoints.length;
            this.map.setFitView(
              this.clickRoute,
              true,
              [200, 200, 200, 200],
              30
            );
          } else if (this.routeItem.type == 50) {
            this.clickRoutePolypon = initMaps.drawPolypon(this.routePoints);
            this.clickRoutePolypon.setMap(this.map);
            this.area = this.computedMethod(6, {
              fence: this.routePoints,
            }).toFixed(2);
            this.clickOrthoRoute();
            this.map.setFitView(
              this.clickRoutePolypon,
              true,
              [200, 200, 200, 200],
              30
            );
          }
        }
      } else {
        this.loading = false;
        if (this.clickRoute) {
          this.map.remove(this.clickRoute);
          this.clickRoute = "";
        }
        if (this.clickRoutePolypon) {
          this.map.remove(this.clickRoutePolypon);
          this.clickRoutePolypon = "";
        }
        if (this.routeMarkers.length > 0) {
          this.map.remove(this.routeMarkers);
          this.routeMarkers = [];
        }
        if (this.startMarker) {
          this.map.remove(this.startMarker);
          this.map.remove(this.endMarker);
          this.startMarker = "";
          this.endMarker = "";
        }
      }
    },
    //绘制已有的点
    async loadingPoint() {
      if (this.num < this.routePoints.length) {
        this.num++;
        await this.drawMarker(this.num, this.routePoints[this.num - 1]);
        let index = this.num - 1;
        if (this.routeItem.point_list[index].action_json) {
          let action_jsons = JSON.parse(
            this.routeItem.point_list[index].action_json
          );
          let total_act = [];
          for (let index = 0; index < action_jsons.length; index++) {
            let act = {
              action_id: action_jsons[index].action_id,
              param_list: JSON.parse(action_jsons[index].param_list),
            };
            total_act.push(act);
          }
          this.allActionList[index].actionList = total_act;
        } else {
          this.routeItem.point_list[index].action_json = "";
        }
        this.heights[index] = this.routeItem.point_list[index].height / 100;
        if (this.num > 1) {
          this.drawline();
          this.addCenterpoint(this.num);
        }
        let p = {
          lng: this.routeItem.point_list[index].lon_int / 1e7,
          lat: this.routeItem.point_list[index].lat_int / 1e7,
        };
        this.routeForm.point_json.push(p);
        setTimeout(() => {
          this.loadingPoint();
        });
      } else {
        this.loading = false;
        this.changeCode = false;
        for (let index = 0; index < this.markers.length; index++) {
          this.markers[index].on("dragstart", this.markerDragStart);
          this.markers[index].on("dragging", this.markerDrag);
          this.markers[index].on("dragend", this.markerDragEnd);
          this.markers[index].on("click", this.markerClick);
        }
        this.drawMark();
        this.markerClick(this.markers[this.markers.length - 1]);
        this.deepCopy();
        setTimeout(() => {
          this.routeFormCode = false;
        });
      }
    },
    //正射影像：绘制已有的点
    async loadingPoint1() {
      if (this.num < this.routePoints.length) {
        this.num++;
        await this.drawMarker(this.num, this.routePoints[this.num - 1]);
        let index = this.num - 1;
        let p = {
          lng: this.routeItem.point_list[index].lon_int / 1e7,
          lat: this.routeItem.point_list[index].lat_int / 1e7,
        };
        this.orthoForm.point_json.push(p);
        if (this.num > 1) {
          this.drawOrthoCenter();
          this.drawOrthoPolypon();
        }
        setTimeout(() => {
          this.loadingPoint1();
        });
      } else {
        this.loading = false;
        this.changeCode = false;
        for (let index = 0; index < this.markers.length; index++) {
          this.markers[index].on("dragstart", this.markerDragStart2);
          this.markers[index].on("dragging", this.markerDrag2);
          this.markers[index].on("dragend", this.markerDragEnd2);
          this.markers[index].on("click", this.markerClick);
        }
        this.drawMark();
        this.markerClick(this.markers[this.markers.length - 1]);
        this.deepCopy();
        setTimeout(() => {
          this.orthoFormCode = false;
        });
      }
    },
    //操作围栏：编辑和删除
    async operateItem() {
      if (this.operateFItem) {
        let point_json = "";
        for (let index = 0; index < this.polyponList.length; index++) {
          if (this.polyponList[index].id == this.operateFItem.f_id) {
            if (this.operateFItem.code == 1) {
              this.map.setFitView(
                this.polyponList[index],
                true,
                [100, 100, 150, 100],
                30
              );
            }
            point_json = this.polyponList[index].getPath();
            this.map.remove(this.polyponList[index]);
            this.polyponList.splice(index, 1);
          }
        }
        if (this.operateFItem.code == 1) {
          let paths = "";
          //   if(this.layer){

          if (this.operateFItem.point_list[0].type == 20) {
            paths = gcj02_to_wgs84(
              this.operateFItem.lon_int / 1e7,
              this.operateFItem.lat_int / 1e7
            );
          } else {
            paths = [
              this.operateFItem.lon_int / 1e7,
              this.operateFItem.lat_int / 1e7,
            ];
          }

          // }

          this.map.setCenter(paths, true);
          this.fenceTitle = this.routeLanguage.fence.editTitle;
          this.fenceForm = {
            name: this.operateFItem.title,
            height_limit: this.operateFItem.height_limit / 100,
          };
          this.markers = [];
          this.fenceForm.point_json = [];
          this.addMarkers = [];
          this.distancePoint = [];
          this.drawMark();
          for (let index = 0; index < point_json.length; index++) {
            this.num++;
            await this.drawPoint(this.num, point_json[index]);
            this.drawPolypon();
          }
          this.pointCodes.push(0);
          this.pointCodes.pop();
          this.deepCopy();
          setTimeout(() => {
            this.fenceFromCode = false;
          }, 500);
          for (let index = 0; index < this.polyponList.length; index++) {
            if (this.polyponList[index].id == this.operateFItem.f_id) {
              this.polyponList[index].show();
            } else {
              this.polyponList[index].hide();
            }
          }
        } else {
          this.$store.commit("operateFItem", "");
        }
      }
    },
    //点击标点
    drawMark() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (!this.drawend) {
        this.toolEvent(10);
        this.drawend = true;
        this.map.setDefaultCursor("pointer");
        for (let index = 0; index < this.addMarkers.length; index++) {
          this.addMarkers[index].setMap(this.map);
        }
        for (let index = 0; index < this.markers.length; index++) {
          this.markers[index]._opts.clickable = true;
          this.markers[index]._opts.draggable = true;
        }
        this.map.on("click", this.clickEvents);
        if (this.clickId) {
          this.markerClick(this.markers[this.clickId - 1]);
        }
      } else {
        this.drawend = false;
        this.map.setDefaultCursor("");
        this.map.off("click", this.clickEvents);
        this.map.remove(this.addMarkers);
        for (let index = 0; index < this.markers.length; index++) {
          this.markers[index]._opts.clickable = false;
          this.markers[index]._opts.draggable = false;
        }
        if (this.clickId > 0) {
          let str = this.markers[this.clickId - 1]
            .getContent()
            .split(" active");
          let content = str[0] + str[1];
          this.markers[this.clickId - 1].setContent(content);
        }
        // this.markerClick();
      }
    },
    //点击地图触发
    clickEvents(e) {
      this.num++;
      if (this.code == 1) {
        let a = this.computedMethod(2, {
          point1: e.lnglat,
          fence: this.fenceItem.paths,
        });
        if (!a) {
          this.$message.error(this.routeLanguage.placeholder4);
          this.num--;
          return false;
        }
        if (this.num > 1) {
          let b = this.computedMethod(4, {
            point1: this.markers[this.num - 2].getPosition(),
            point2: e.lnglat,
            fence: this.fenceItem.paths,
          });
          if (b) {
            this.$message.error(this.routeLanguage.placeholder5);
            this.num--;
            return false;
          }
          let isCreate = this.computedDistance(
            e.lnglat,
            this.markers[this.num - 2].getPosition()
          );
          if (!isCreate) {
            this.$message.warning(this.routeLanguage.placeholder6);
            this.num--;
            return false;
          }
          this.drawMarker(this.num, e.lnglat);
          if (this.num > 1) {
            this.drawline();
            this.addCenterpoint(this.num);
          }
        } else {
          this.drawMarker(this.num, e.lnglat);
        }
        if (!this.changeCode) {
          setTimeout(() => {
            this.deepCopy();
          }, 100);
        }
      } else if (this.code == 2) {
        this.drawPoint(this.num, e.lnglat);
        this.drawPolypon();
        this.deepCopy();
      } else if (this.code == 3) {
        let a = this.computedMethod(2, {
          point1: e.lnglat,
          fence: this.fenceItem.paths,
        });
        if (!a) {
          this.$message.error(this.routeLanguage.placeholder4);
          this.num--;
          return false;
        }
        if (this.num > 1) {
          let b = this.computedMethod(4, {
            point1: this.markers[this.num - 2].getPosition(),
            point2: e.lnglat,
            fence: this.fenceItem.paths,
          });
          let b1 = this.computedMethod(4, {
            point1: this.markers[0].getPosition(),
            point2: e.lnglat,
            fence: this.fenceItem.paths,
          });
          if (b || b1) {
            this.$message.error(this.routeLanguage.placeholder7);
            this.num--;
            return false;
          }
          // let isCreate = this.computedDistance(
          //   e.lnglat,
          //   this.markers[this.num - 2].getPosition()
          // );
          // if (!isCreate) {
          //   this.$message.warning("相邻航点有效距离不能超过2千米！");
          //   this.num--;
          //   return false;
          // }
          this.drawOrthoPoint(this.num, e.lnglat);
          this.drawOrthoPolypon();
          // this.addCenterpoint(this.num);
        } else {
          this.drawOrthoPoint(this.num, e.lnglat);
        }
        this.deepCopy();
      }
    },
    //计算总距离和总时间
    computedData() {
      this.distance = 0;
      for (let index = 0; index < this.markers.length - 1; index++) {
        this.distance += this.computedMethod(1, {
          point1: this.markers[index].getPosition(),
          point2: this.markers[index + 1].getPosition(),
        });
      }
      this.estTime =
        this.distance / this.routeForm.auto_speed + this.markers.length;
    },
    //点击删除选中的点
    removeMarker() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (this.drawend) {
        if (!this.removeClick) {
          this.removeClick = true;
          if (this.markers.length > 0) {
            this.map.remove(this.markers[this.clickId - 1]);
            this.markers.splice(this.clickId - 1, 1);
            if (this.code == 1) {
              for (let i = this.clickId - 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                  "<div class='marker-edit'><span class='text'>" +
                  (i + 1) +
                  "</span></div>";
                this.markers[i].setContent(content);
              }
              this.num--;
              this.choosePoint = this.clickId;
              this.routeForm.point_json.splice(this.clickId - 1, 1);
              if (this.num > 0) {
                if (this.clickId > 1) {
                  this.map.remove(this.addMarkers[this.clickId - 2]);
                  this.addMarkers.splice(this.clickId - 2, 1);
                } else {
                  this.map.remove(this.addMarkers[this.clickId - 1]);
                  this.addMarkers.splice(this.clickId - 1, 1);
                }
                for (
                  let index =
                    this.clickId > 1 ? this.clickId - 2 : this.clickId - 1;
                  index < this.addMarkers.length;
                  index++
                ) {
                  this.addMarkers[index].id = index + 1;
                  this.editCenterpoint(this.clickId - 1);
                }
                this.drawline();
              }
              if (this.markers.length > 0) {
                this.markerClick(this.markers[this.markers.length - 1]);
              }
              if (this.noChange) {
                this.allActionList.splice(this.clickId - 1, 1);
                this.heights.pop();
              }
              this.distance = 0;
              this.estTime = 0;
              if (this.routeForm.point_json.length > 1) {
                for (
                  let index = 0;
                  index < this.routeForm.point_json.length - 1;
                  index++
                ) {
                  this.distance += this.computedMethod(1, {
                    point1: this.markers[index].getPosition(),
                    point2: this.markers[index + 1].getPosition(),
                  });
                  this.estTime =
                    this.distance / this.routeForm.auto_speed +
                    this.markers.length;
                }
              }
            } else if (this.code == 2) {
              for (let i = this.clickId - 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                  "<div class='marker-o-edit'><span class='text'>" +
                  (i + 1) +
                  "</span></div>";
                this.markers[i].setContent(content);
              }
              this.num--;
              this.fenceForm.point_json.splice(this.clickId - 1, 1);
              if (this.num > 0) {
                if (this.num == 1 && this.clickId == 2) {
                  this.map.remove(this.addMarkers[this.clickId - 2]);
                  this.addMarkers.splice(this.clickId - 2, 1);
                  this.map.remove(this.distancePoint[this.clickId - 2]);
                  this.distancePoint.splice(this.clickId - 2, 1);
                } else {
                  this.map.remove(this.addMarkers[this.clickId - 1]);
                  this.addMarkers.splice(this.clickId - 1, 1);
                  this.map.remove(this.distancePoint[this.clickId - 1]);
                  this.distancePoint.splice(this.clickId - 1, 1);
                }
              }
              if (this.num > 2) {
                if (this.clickId == 1) {
                  this.drawEditPoint(this.markers.length);
                } else {
                  this.drawEditPoint(this.clickId - 1);
                }
                for (
                  let i = this.clickId - 1;
                  i < this.addMarkers.length;
                  i++
                ) {
                  this.addMarkers[i].id = i + 1;
                }
              }
              if (this.num == 2) {
                if (this.clickId > 1) {
                  this.map.remove(this.addMarkers[this.clickId - 2]);
                  this.addMarkers.splice(this.clickId - 2, 1);
                  this.map.remove(this.distancePoint[this.clickId - 2]);
                  this.distancePoint.splice(this.clickId - 2, 1);
                } else {
                  this.map.remove(this.addMarkers[this.addMarkers.length - 1]);
                  this.addMarkers.pop();
                  this.map.remove(
                    this.distancePoint[this.distancePoint.length - 1]
                  );
                  this.distancePoint.pop();
                }
              }
              this.drawPolypon();
              if (this.markers.length > 0) {
                this.markerClick(this.markers[this.markers.length - 1]);
              }
            } else if (this.code == 3) {
              for (let i = this.clickId - 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                  "<div class='marker-edit'><span class='text'>" +
                  (i + 1) +
                  "</span></div>";
                this.markers[i].setContent(content);
              }
              this.num--;
              this.orthoForm.point_json.splice(this.clickId - 1, 1);
              if (this.num > 0) {
                if (this.num == 1 && this.clickId == 2) {
                  this.map.remove(this.addMarkers[this.clickId - 2]);
                  this.addMarkers.splice(this.clickId - 2, 1);
                } else {
                  this.map.remove(this.addMarkers[this.clickId - 1]);
                  this.addMarkers.splice(this.clickId - 1, 1);
                }
              }
              if (this.num > 2) {
                if (this.clickId == 1) {
                  this.editOrthoCenter(this.markers.length);
                } else {
                  this.editOrthoCenter(this.clickId - 1);
                }
                for (
                  let i = this.clickId - 1;
                  i < this.addMarkers.length;
                  i++
                ) {
                  this.addMarkers[i].id = i + 1;
                }
              }
              if (this.num == 2) {
                if (this.clickId > 1) {
                  this.map.remove(this.addMarkers[this.clickId - 2]);
                  this.addMarkers.splice(this.clickId - 2, 1);
                } else {
                  this.map.remove(this.addMarkers[this.addMarkers.length - 1]);
                  this.addMarkers.pop();
                }
              }
              this.drawOrthoPolypon();
              if (this.markers.length > 0) {
                this.markerClick(this.markers[this.markers.length - 1]);
              }
            }
            this.deepCopy();
          }
        }
        this.timeOut = setTimeout(() => {
          this.removeClick = false;
        }, 200);
      }
    },
    //点击删除所有点
    delMarker() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (this.markers.length > 0) {
        this.delClick = true;
        this.$confirm(
          this.code == 1 || this.code == 3
            ? this.routeLanguage.placeholder8
            : this.routeLanguage.placeholder9,
          this.routeLanguage.tip,
          {
            confirmButtonText: this.routeLanguage.saveBtn,
            cancelButtonText: this.routeLanguage.cancelBtn,
            type: "warning",
            customClass: "messageTip",
          }
        )
          .then(() => {
            this.map.remove(this.markers);
            this.map.remove(this.addMarkers);
            this.markers = [];
            this.addMarkers = [];
            this.num = 0;
            this.clickId = 0;
            if (this.code == 2) {
              this.map.remove(this.distancePoint);
              this.distancePoint = [];
              this.fenceForm.point_json = [];
              this.map.remove(this.polypon);
              this.polypon = "";
              this.startPoints = "";
            } else if (this.code == 1) {
              this.routeForm.point_json = [];
              this.map.remove(this.line);
              this.line = "";
              this.allActionList = [];
              this.heights = [];
              this.distance = 0;
              this.estTime = 0;
            } else if (this.code == 3) {
              this.orthoForm.point_json = [];
              if (this.line) {
                this.map.remove(this.line);
                this.line = "";
              }
              if (this.polypon) {
                this.map.remove(this.polypon);
                this.polypon = "";
              }
              this.startPoints = "";
            }
            this.deepCopy();
            this.$message({
              type: "success",
              message: this.routeLanguage.messageInfo,
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: this.routeLanguage.messageInfo1,
            });
          })
          .finally(() => {
            this.delClick = false;
          });
      }
    },
    //点击撤回数据
    recallMarker() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (this.drawend) {
        if (!this.recallClick) {
          this.recallClick = true;
          if (this.cacheData.length > 1) {
            let oldArr = this.cacheData[this.cacheData.length - 2];
            let newArr = this.cacheData[this.cacheData.length - 1];
            let arrLength =
              oldArr.markers.length > newArr.markers.length
                ? newArr.markers.length
                : oldArr.markers.length;
            let arrLength1 =
              oldArr.addMarkers.length > newArr.addMarkers.length
                ? newArr.addMarkers.length
                : oldArr.addMarkers.length;
            if (oldArr.markers.length < newArr.markers.length) {
              this.map.remove(this.markers[this.markers.length - 1]);
              this.markers.pop();
              if (this.code == 1) {
                this.routeForm.point_json.pop();
                this.map.remove(this.addMarkers[this.addMarkers.length - 1]);
                this.addMarkers.pop();
              } else if (this.code == 2) {
                this.fenceForm.point_json.pop();
                if (oldArr.addMarkers.length < newArr.addMarkers.length) {
                  let len = newArr.addMarkers.length - oldArr.addMarkers.length;
                  for (let i = 0; i < len; i++) {
                    this.map.remove(
                      this.addMarkers[this.addMarkers.length - 1]
                    );
                    this.addMarkers.pop();
                  }
                }
              } else if (this.code == 3) {
                this.orthoForm.point_json.pop();
                if (oldArr.addMarkers.length < newArr.addMarkers.length) {
                  let len = newArr.addMarkers.length - oldArr.addMarkers.length;
                  for (let i = 0; i < len; i++) {
                    this.map.remove(
                      this.addMarkers[this.addMarkers.length - 1]
                    );
                    this.addMarkers.pop();
                  }
                }
              }
              this.pointCodes.push(0);
              this.pointCodes.pop();
            }
            if (this.code == 1) {
              this.map.remove(this.line);
              this.line = oldArr.line;
              this.map.add(oldArr.line);
              this.distance = oldArr.distance;
              this.estTime = oldArr.estTime;
              this.heights = oldArr.heights;

              for (let index = 0; index < arrLength; index++) {
                this.markers[index].setPosition(oldArr.markers[index].paths);
                this.markers[index].setContent(oldArr.markers[index].content);
                this.routeForm.point_json[index] = oldArr.point_json[index];
              }
              setTimeout(() => {
                this.allActionList = JSON.parse(
                  JSON.stringify(oldArr.allActionList)
                );
              }, 100);
            } else if (this.code == 2) {
              this.map.remove(this.polypon);
              this.polypon = oldArr.polypon;
              this.map.add(oldArr.polypon);
              for (let index = 0; index < arrLength; index++) {
                this.markers[index].setPosition(oldArr.markers[index].paths);
                this.markers[index].setContent(oldArr.markers[index].content);
                this.fenceForm.point_json[index] = oldArr.markers[index].paths;
              }
              this.map.remove(this.distancePoint);
              this.distancePoint = [];
              for (
                let index = 0;
                index < oldArr.distancePoint.length;
                index++
              ) {
                let labelMarker = initMaps.textMarker(
                  oldArr.distancePoint[index].text,
                  oldArr.distancePoint[index].paths,
                  oldArr.distancePoint[index].rotate
                );
                labelMarker.setMap(this.map);
                this.distancePoint.push(labelMarker);
              }
            } else if (this.code == 3) {
              this.map.remove(this.polypon);
              this.polypon = "";
              this.map.remove(this.line);
              this.line = "";
              // this.map.add(oldArr.line);
              for (let index = 0; index < arrLength; index++) {
                this.markers[index].setPosition(oldArr.markers[index].paths);
                this.markers[index].setContent(oldArr.markers[index].content);
                this.orthoForm.point_json[index] = oldArr.markers[index].paths;
              }
            }

            // this.pointCodes=oldArr.pointCodes

            for (let index = 0; index < arrLength1; index++) {
              this.addMarkers[index].setPosition(
                oldArr.addMarkers[index].paths
              );
            }

            if (oldArr.markers.length > newArr.markers.length) {
              for (
                let index = newArr.markers.length;
                index < oldArr.markers.length;
                index++
              ) {
                let params = {
                  offset: -17,
                  clickable: true,
                  draggable: true,
                  zIndex: 55,
                };
                let marker = initMaps.drawMarker(
                  index + 1,
                  oldArr.markers[index].paths,
                  this.code == 1 || this.code == 3
                    ? "marker-edit"
                    : "marker-o-edit",
                  params
                );
                marker.setContent(oldArr.markers[index].content);
                marker.setMap(this.map);
                marker.id = index + 1;
                if (this.code == 1) {
                  this.routeForm.point_json.push(marker.getPosition());
                } else if (this.code == 2) {
                  this.fenceForm.point_json.push(marker.getPosition());
                } else if (this.code == 3) {
                  this.orthoForm.point_json.push(marker.getPosition());
                }

                this.markers.push(marker);
                marker.on("dragstart", this.markerDragStart);
                marker.on("dragging", this.markerDrag);
                marker.on("dragend", this.markerDragEnd);
                marker.on("click", this.markerClick);
              }
              for (
                let index = newArr.addMarkers.length;
                index < oldArr.addMarkers.length;
                index++
              ) {
                let params = {
                  offset: -11,
                  clickable: true,
                  draggable: false,
                };
                let addMarker = initMaps.drawMarker(
                  "+",
                  oldArr.addMarkers[index].paths,
                  this.code == 1 || this.code == 3
                    ? "marker-edit-i"
                    : "marker-o-edit-i",
                  params
                );
                addMarker.setMap(this.map);
                addMarker.id = index + 1;
                this.addMarkers.push(addMarker);
                addMarker.on("click", this.clickAdd);
              }
            }
            for (let index = 0; index < this.pointCodes.length; index++) {
              if (this.pointCodes[index] > this.markers.length) {
                this.pointCodes.splice(index, 1);
              }
            }
            this.num = oldArr.num;
            this.clickId = oldArr.clickId;
            if (this.code == 3) {
              this.drawOrthoPolypon();
            }
            this.cacheData.pop();
          } else {
            this.$message.warning(this.routeLanguage.placeholder10);
          }
        }
        setTimeout(() => {
          this.recallClick = false;
        }, 200);
      }
    },
    //围栏输入框输入lat/lng后失去焦点触发
    changeLatLng(index, code) {
      this.fenceFromCode = true;
      if (this.fenceForm.point_json.length > 3) {
        this.isCross(index, code);
      } else {
        this.drawPolypon();
        if (this.fenceForm.point_json.length > 1) {
          this.drawEditPoint(index + 1);
        }
        this.deepCopy();
      }
    },
    //判断填入之后是否交叉
    isCross(index, code) {
      let a, b;
      if (index == 0) {
        a = this.isLineCross1(
          this.fenceForm.point_json[0],
          this.fenceForm.point_json[index + 1],
          0
        );
        b = this.isLineCross1(
          this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
          this.fenceForm.point_json[0],
          0,
          1
        );
      } else if (index == this.fenceForm.point_json.length - 1) {
        a = this.isLineCross1(
          this.fenceForm.point_json[0],
          this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
          0,
          1
        );
        b = this.isLineCross1(
          this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
          this.fenceForm.point_json[index - 1],
          this.fenceForm.point_json.length - 2
        );
      } else {
        a = this.isLineCross1(
          this.fenceForm.point_json[index],
          this.fenceForm.point_json[index - 1],
          index - 1
        );
        b = this.isLineCross1(
          this.fenceForm.point_json[index + 1],
          this.fenceForm.point_json[index],
          index
        );
      }
      if (a || b) {
        this.$message.warning({
          message: this.routeLanguage.messageInfo2,
          duration: 1000,
        });
        if (code == 1) {
          this.fenceForm.point_json[index].lat = this.lat;
          this.markers[index]._position.lat = this.lat;
        } else {
          this.fenceForm.point_json[index].lng = this.lng;
          this.markers[index]._position.lng = this.lng;
        }
      } else {
        this.drawPolypon();
        if (this.fenceForm.point_json.length > 1) {
          this.drawEditPoint(index + 1);
        }
        this.deepCopy();
      }
    },
    //航线、正射影像输入框修改
    changeLngLat(index, code) {
      if (this.code == 1) {
        this.routeFormCode = true;
        let a = this.computedMethod(2, {
          point1: new AMap.LngLat(
            this.routeForm.point_json[index].lng,
            this.routeForm.point_json[index].lat
          ),
          fence: this.fenceItem.paths,
        });
        if (a) {
          this.markers[index].setPosition(
            new AMap.LngLat(
              this.routeForm.point_json[index].lng,
              this.routeForm.point_json[index].lat
            )
          );
          if (this.markers.length > 1) {
            this.drawline();
            this.editCenterpoint(index);
            this.distance = 0;
            this.estTime = 0;
            for (let i = 0; i < this.markers.length - 1; i++) {
              this.distance += this.computedMethod(1, {
                point1: this.markers[i].getPosition(),
                point2: this.markers[i + 1].getPosition(),
              });
            }
            this.estTime =
              this.distance / this.routeForm.auto_speed + this.markers.length;
            this.deepCopy();
          }
        } else {
          this.$message.error(this.routeLanguage.placeholder4);
          if (code == 1) {
            this.markers[index].getPosition().lng = this.focusPoint.lng;
            let a = this.pointCodes.indexOf(index + 1);
            if (a != -1) {
              this.pointCodes.splice(a, 1);
            }
            setTimeout(() => {
              this.routeForm.point_json[index].lng = this.focusPoint.lng;
              this.pointCodes.push(index + 1);
            }, 1);
          } else {
            this.markers[index].getPosition().lat = this.focusPoint.lat;
            let a = this.pointCodes.indexOf(index + 1);
            if (a != -1) {
              this.pointCodes.splice(a, 1);
            }
            setTimeout(() => {
              this.routeForm.point_json[index].lat = this.focusPoint.lat;
              this.pointCodes.push(index + 1);
            }, 1);
          }
        }
      } else if (this.code == 3) {
        this.orthoFormCode = true;
        let a = this.computedMethod(2, {
          point1: new AMap.LngLat(
            this.orthoForm.point_json[index].lng,
            this.orthoForm.point_json[index].lat
          ),
          fence: this.fenceItem.paths,
        });
        if (a) {
          let c, b;
          let num = index + 1;
          if (num == 1) {
            c = this.isLineOrthoCross1(
              this.markers[0].getPosition(),
              this.markers[num].getPosition(),
              0
            );
            b = this.isLineOrthoCross1(
              this.markers[this.markers.length - 1].getPosition(),
              this.markers[0].getPosition(),
              0,
              1
            );
          } else if (num == this.markers.length) {
            c = this.isLineOrthoCross1(
              this.markers[0].getPosition(),
              this.markers[this.markers.length - 1].getPosition(),
              0,
              1
            );
            b = this.isLineOrthoCross1(
              this.markers[this.markers.length - 1].getPosition(),
              this.markers[num - 2].getPosition(),
              this.markers.length - 2
            );
          } else {
            c = this.isLineOrthoCross1(
              this.markers[num - 1].getPosition(),
              this.markers[num - 2].getPosition(),
              index - 2
            );
            b = this.isLineOrthoCross1(
              this.markers[num - 1].getPosition(),
              this.markers[num].getPosition(),
              index - 1
            );
          }
          if (c || b) {
            if (code == 1) {
              this.markers[index].getPosition().lng = this.focusPoint.lng;
              let a = this.pointCodes.indexOf(index + 1);
              if (a != -1) {
                this.pointCodes.splice(a, 1);
              }
              setTimeout(() => {
                this.orthoForm.point_json[index].lng = this.focusPoint.lng;
                this.pointCodes.push(index + 1);
              }, 1);
            } else {
              this.markers[index].getPosition().lat = this.focusPoint.lat;
              let a = this.pointCodes.indexOf(index + 1);
              if (a != -1) {
                this.pointCodes.splice(a, 1);
              }
              setTimeout(() => {
                this.orthoForm.point_json[index].lat = this.focusPoint.lat;
                this.pointCodes.push(index + 1);
              }, 1);
            }

            this.$message.warning({
              message: this.routeLanguage.messageInfo3,
              duration: 1000,
            });
            return false;
          }
          this.markers[index].setPosition(
            new AMap.LngLat(
              this.orthoForm.point_json[index].lng,
              this.orthoForm.point_json[index].lat
            )
          );
          if (this.markers.length > 1) {
            this.drawOrthoPolypon();
            this.editOrthoCenter(index + 1);

            // this.distance = 0;
            // this.estTime = 0;
            // for (let i = 0; i < this.markers.length - 1; i++) {
            //   this.distance += this.computedMethod(1, {
            //     point1: this.markers[i].getPosition(),
            //     point2: this.markers[i + 1].getPosition(),
            //   });
            // }
            // this.estTime = this.distance / this.routeForm.auto_speed;
          }
          this.deepCopy();
        } else {
          this.$message.error(this.routeLanguage.placeholder4);
          if (code == 1) {
            this.markers[index].getPosition().lng = this.focusPoint.lng;
            let a = this.pointCodes.indexOf(index + 1);
            if (a != -1) {
              this.pointCodes.splice(a, 1);
            }
            setTimeout(() => {
              this.orthoForm.point_json[index].lng = this.focusPoint.lng;
              this.pointCodes.push(index + 1);
            }, 1);
          } else {
            this.markers[index].getPosition().lat = this.focusPoint.lat;
            let a = this.pointCodes.indexOf(index + 1);
            if (a != -1) {
              this.pointCodes.splice(a, 1);
            }
            setTimeout(() => {
              this.orthoForm.point_json[index].lat = this.focusPoint.lat;
              this.pointCodes.push(index + 1);
            }, 1);
          }
        }
      }
    },
    //航点输入框获取焦点
    getFocus(index) {
      let point = "";
      if (this.code == 1) {
        point = this.routeForm.point_json[index];
      } else if (this.code == 3) {
        point = this.orthoForm.point_json[index];
      }
      this.focusPoint = Object.assign({}, point);
    },
    //校验是否为0
    numFormat(rule, value, callback) {
      if (value == 0) {
        callback(new Error(this.routeLanguage.errorMessage));
      } else {
        callback();
      }
    },
    //选择作业围栏后，确定围栏
    chooseFence(e) {
      for (let index = 0; index < this.fenceList.length; index++) {
        if (this.fenceList[index].f_id == e) {
          this.map.setCenter(
            [
              this.fenceList[index].lon_int / 1e7,
              this.fenceList[index].lat_int / 1e7,
            ],
            true
          );
          this.map.setZoom(17);
        }
      }
    },
    //添加航点动作
    addAction(e) {
      this.addActionCode = e;
      this.allActionList[e - 1].actionList.push({
        action_id: "",
        param_list: "",
      });
      this.timeOut = setTimeout(() => {
        this.addActionCode = 0;
      }, 200);
    },
    //选中航点动作时触发
    chooseAction(e, index, i) {
      if (e == "takephoto") {
        this.allActionList[index].actionList[i].param_list = "";
      } else {
        this.allActionList[index].actionList[i].param_list = [];
        if (e == "uav_yaw") {
          let a = {
            param_id: "yaw",
            value: 0,
          };
          this.allActionList[index].actionList[i].param_list.push(a);
        }
        if (e == "hover") {
          let a = {
            param_id: "hovertime",
            value: 3,
          };
          this.allActionList[index].actionList[i].param_list.push(a);
        }
        if (e == "gimbal_ctrl") {
          let a = {
            param_id: "gimbal_yaw",
            value: 0,
          };
          this.allActionList[index].actionList[i].param_list.push(a);
          let b = {
            param_id: "gimbal_pitch",
            value: 0,
          };
          this.allActionList[index].actionList[i].param_list.push(b);
        }
        if (e == "cam_trig_dist") {
          let a = {
            param_id: "dist",
            value: 1,
          };
          this.allActionList[index].actionList[i].param_list.push(a);
        }
        if (e == "speed") {
          let a = {
            param_id: "speed",
            value: this.routeForm.auto_speed,
          };
          this.allActionList[index].actionList[i].param_list.push(a);
        }
      }
      this.deepCopy();
    },
    //删除航点动作
    delAction(e, index, i) {
      this.allActionList[index].actionList.splice(i, 1);
      this.deepCopy();
    },
    //提交围栏信息新增和编辑
    async sumbitFence(e) {
      if (!this.saveCode) {
        this.saveCode = true;
        let center = {
          lat: 0,
          lng: 0,
        };
        await this.$refs[e].validate((valid) => {
          if (valid && this.saveCode) {
            if (this.fenceForm.point_json.length > 2) {
              var point_jsons = [];
              for (
                let index = 0;
                index < this.fenceForm.point_json.length;
                index++
              ) {
                let point_json = {
                  seq: index + 1,
                  lat_int: parseInt(this.fenceForm.point_json[index].lat * 1e7),
                  lon_int: parseInt(this.fenceForm.point_json[index].lng * 1e7),
                  type: 10,
                };
                point_jsons.push(point_json);
                center.lat += parseInt(
                  this.fenceForm.point_json[index].lat * 1e7
                );
                center.lng += parseInt(
                  this.fenceForm.point_json[index].lng * 1e7
                );
              }
              center.lat = parseInt(
                center.lat / this.fenceForm.point_json.length
              );
              center.lng = parseInt(
                center.lng / this.fenceForm.point_json.length
              );
              this.fenceForm.point_jsons = point_jsons;
              if (this.drawend) {
                this.drawMark();
              }
              let data = {
                title: this.fenceForm.name,
                type: 10,
                height_limit: parseInt(this.fenceForm.height_limit * 100),
                lat_int: center.lat,
                lon_int: center.lng,
                solid_color: "#801357B1",
                stroke_color: "#FF1352FF",
                stroke_width: 5,
              };
              if (this.operateFItem) {
                data.f_id = this.operateFItem.f_id;
                data.state = this.operateFItem.state;
                for (let index = 0; index < point_jsons.length; index++) {
                  for (
                    let i = 0;
                    i < this.operateFItem.point_list.length;
                    i++
                  ) {
                    if (
                      point_jsons[index].seq ==
                      this.operateFItem.point_list[i].seq
                    ) {
                      point_jsons[index].id =
                        this.operateFItem.point_list[i].id;
                    }
                  }
                  point_jsons[index].state = 10;
                }
                if (point_jsons.length < this.operateFItem.point_list.length) {
                  let point_jsons1 = [];
                  for (
                    let index = 0;
                    index < this.operateFItem.point_list.length;
                    index++
                  ) {
                    if (
                      this.operateFItem.point_list[index].seq >
                      point_jsons.length
                    ) {
                      let a = {
                        seq: this.operateFItem.point_list[index].seq,
                        id: this.operateFItem.point_list[index].id,
                        state: 30,
                        lon_int: this.operateFItem.point_list[index].lon_int,
                        lat_int: this.operateFItem.point_list[index].lat_int,
                        type: 20,
                      };
                      point_jsons1.push(a);
                    }
                  }
                  point_jsons = point_jsons.concat(point_jsons1);
                }
                data.point_json = JSON.stringify(point_jsons);
                data.pmd =
                  data.title +
                  data.point_json +
                  data.type.toString() +
                  data.lat_int.toString() +
                  data.lon_int.toString() +
                  data.f_id.toString() +
                  data.state.toString();
              } else {
                data.point_json = JSON.stringify(point_jsons);
                data.pmd =
                  data.title +
                  data.point_json +
                  data.type.toString() +
                  data.lat_int.toString() +
                  data.lon_int.toString();
              }
              requestHttp(
                this.operateFItem ? "fenceEdit" : "fenceAdd",
                data
              ).then((res) => {
                this.$message.success(
                  this.operateFItem
                    ? this.routeLanguage.successMessage
                    : this.routeLanguage.successMessage1
                );
                this.pointCodes = [];
                this.clickId = 0;
                if (this.drawend) {
                  this.drawMark();
                }
                this.map.remove(this.markers);
                this.map.remove(this.addMarkers);
                this.markers = [];
                this.addMarkers = [];
                this.num = 0;
                this.fenceForm = {
                  name: "",
                  point_json: [],
                  height_limit: 200,
                };
                this.map.remove(this.polypon);
                this.polypon = "";
                this.startPoints = "";
                this.cacheData = [];
                this.map.setCenter([center.lng / 1e7, center.lat / 1e7], true);
                this.$store.commit("changeCode", 0);
                this.$refs.planList.getFenceList();
                this.$store.commit("fenceItemId", "");
                this.$store.commit("operateFItem", "");
                this.fenceTitle = this.routeLanguage.fence.addFence;
              });
            } else {
              this.$message.info(this.routeLanguage.messageInfo4);
            }
          }
        });
      }
      this.timeOut = setTimeout(() => {
        this.saveCode = false;
      }, 200);
    },
    //提交航线信息
    sumbitRoute(e) {
      if (!this.saveCode) {
        this.saveCode = true;
        if (e == "routeForm") {
          this.$refs[e].validate((valid, validator) => {
            if (valid) {
              if (this.routeForm.point_json.length > 1) {
                localStorage.removeItem("items");

                var point_jsons = [];
                let arr = [];
                for (
                  let index = 0;
                  index < this.routeForm.point_json.length;
                  index++
                ) {
                  let point_json = {
                    seq: index + 1,
                    lat_int: parseInt(
                      this.routeForm.point_json[index].lat * 1e7
                    ),
                    lon_int: parseInt(
                      this.routeForm.point_json[index].lng * 1e7
                    ),
                    height: parseInt(this.heights[index] * 100),
                    type: 10,
                  };
                  let action_jsons = [];
                  for (
                    let i = 0;
                    i < this.allActionList[index].actionList.length;
                    i++
                  ) {
                    if (this.allActionList[index].actionList[i].action_id) {
                      let action = {
                        action_id:
                          this.allActionList[index].actionList[i].action_id,
                        param_list: JSON.stringify(
                          this.allActionList[index].actionList[i].param_list
                        ),
                      };
                      action_jsons.push(action);
                    }
                  }
                  if (action_jsons.length > 0) {
                    point_json.action_json = JSON.stringify(action_jsons);
                  } else {
                    point_json.action_json = "";
                  }
                  point_jsons.push(point_json);
                }
                let data = {
                  title: this.routeForm.name,
                  f_id: this.fenceItem.id,
                  type: this.checkType.value,
                  auto_speed: parseInt(this.routeForm.auto_speed * 100),
                  max_speed: parseInt(this.routeForm.max_speed * 100),
                  default_height: parseInt(this.routeForm.default_height * 100),
                  return_height: parseInt(this.routeForm.return_height * 100),
                  action_completed: this.routeForm.action_completed,
                };
                if (this.routeItem) {
                  data.m_id = this.routeItem.m_id;
                  data.state = this.routeItem.state;
                  for (let index = 0; index < point_jsons.length; index++) {
                    for (let i = 0; i < this.routeItem.point_list.length; i++) {
                      if (
                        point_jsons[index].seq ==
                        this.routeItem.point_list[i].seq
                      ) {
                        point_jsons[index].id = this.routeItem.point_list[i].id;
                      }
                    }
                    point_jsons[index].state = 10;
                  }
                  if (point_jsons.length < this.routeItem.point_list.length) {
                    let point_jsons1 = [];
                    for (
                      let index = 0;
                      index < this.routeItem.point_list.length;
                      index++
                    ) {
                      if (
                        this.routeItem.point_list[index].seq >
                        point_jsons.length
                      ) {
                        let a = {
                          seq: this.routeItem.point_list[index].seq,
                          id: this.routeItem.point_list[index].id,
                          state: 30,
                          lon_int: this.routeItem.point_list[index].lon_int,
                          lat_int: this.routeItem.point_list[index].lat_int,
                          height: this.routeItem.point_list[index].height,
                          type: 20,
                        };
                        point_jsons1.push(a);
                      }
                    }
                    point_jsons = point_jsons.concat(point_jsons1);
                  }
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString() +
                    data.m_id.toString() +
                    data.state.toString();
                } else {
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title.toString() +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString();
                }
                requestHttp(
                  this.routeItem ? "missionEdit" : "missionAdd",
                  data
                ).then((res) => {
                  this.$message.success(
                    this.routeItem
                      ? this.routeLanguage.successMessage2
                      : this.routeLanguage.successMessage3
                  );
                  if (this.drawend) {
                    this.drawMark();
                  }
                  this.map.remove(this.markers);
                  this.map.remove(this.addMarkers);
                  this.markers = [];
                  this.addMarkers = [];
                  this.num = 0;
                  this.routeForm = {
                    name: "",
                    auto_speed: 10,
                    max_speed: 20,
                    default_height: 100,
                    return_height: 100,
                    action_completed: 20,
                    point_json: [],
                  };
                  this.routeTitle = this.routeLanguage.routeLine.setNewTask;
                  this.heights = [];
                  this.allActionList = [];
                  this.allActionList.length = 0;
                  this.map.remove(this.line);
                  this.line = "";
                  this.$store.commit("changeCode", 4);
                  this.$store.commit("routeItem", "");
                  this.$store.commit("checkType", "");
                  this.clickId = 0;
                  this.pointCodes = [];
                  this.distance = 0;
                  this.estTime = 0;
                  this.cacheData = [];
                  // this.$refs.planList.checkCode = this.checkType.value;
                  this.$refs.planList.getMissionList();
                  this.getFenceItem();
                });
              } else {
                this.$message.warning(this.routeLanguage.messageInfo5);
              }
            } else {
              if (validator) {
                for (const key in validator) {
                  this.$message.error(validator[key][0].message);
                  break;
                }
              }
            }
          });
        } else if (e == "orthoForm") {
          this.$refs[e].validate((valid, validator) => {
            if (valid) {
              if (this.orthoForm.point_json.length > 2) {
                var point_jsons = [];
                for (
                  let index = 0;
                  index < this.orthoForm.point_json.length;
                  index++
                ) {
                  let point_json = {
                    seq: index + 1,
                    lat_int: parseInt(
                      this.orthoForm.point_json[index].lat * 1e7
                    ),
                    lon_int: parseInt(
                      this.orthoForm.point_json[index].lng * 1e7
                    ),
                    height: parseInt(this.orthoForm.default_height * 100),
                    type: 10,
                  };
                  point_jsons.push(point_json);
                }
                let data = {
                  title: this.orthoForm.name,
                  f_id: this.fenceItem.id,
                  type: this.checkType.value,
                  auto_speed: parseInt(this.orthoForm.auto_speed * 100),
                  max_speed: parseInt(this.orthoForm.max_speed * 100),
                  default_height: parseInt(this.orthoForm.default_height * 100),
                  return_height: parseInt(this.orthoForm.return_height * 100),
                  action_completed: this.orthoForm.action_completed,
                };
                let jsonParams = {
                  camera_type: this.orthoForm.cameraType,
                  cameraParamList: this.cameraParamList,
                  lateral: this.orthoForm.lateral,
                  course: this.orthoForm.course,
                  wheelDist: this.orthoForm.wheelDist,
                  angle: this.orthoForm.angle,
                };
                data.camera_json = JSON.stringify(jsonParams);
                if (this.routeItem) {
                  data.m_id = this.routeItem.m_id;
                  data.state = this.routeItem.state;
                  for (let index = 0; index < point_jsons.length; index++) {
                    for (let i = 0; i < this.routeItem.point_list.length; i++) {
                      if (
                        point_jsons[index].seq ==
                        this.routeItem.point_list[i].seq
                      ) {
                        point_jsons[index].id = this.routeItem.point_list[i].id;
                      }
                    }
                    point_jsons[index].state = 10;
                  }
                  if (point_jsons.length < this.routeItem.point_list.length) {
                    let point_jsons1 = [];
                    for (
                      let index = 0;
                      index < this.routeItem.point_list.length;
                      index++
                    ) {
                      if (
                        this.routeItem.point_list[index].seq >
                        point_jsons.length
                      ) {
                        let a = {
                          seq: this.routeItem.point_list[index].seq,
                          id: this.routeItem.point_list[index].id,
                          state: 30,
                          lon_int: this.routeItem.point_list[index].lon_int,
                          lat_int: this.routeItem.point_list[index].lat_int,
                          height: this.routeItem.point_list[index].height,
                          type: 20,
                        };
                        point_jsons1.push(a);
                      }
                    }
                    point_jsons = point_jsons.concat(point_jsons1);
                  }
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString() +
                    data.m_id.toString() +
                    data.state.toString();
                } else {
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title.toString() +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString();
                }
                requestHttp(
                  this.routeItem ? "missionEdit" : "missionAdd",
                  data
                ).then((res) => {
                  this.$message.success(
                    this.routeItem
                      ? this.routeLanguage.successMessage2
                      : this.routeLanguage.successMessage3
                  );
                  if (this.drawend) {
                    this.drawMark();
                  }
                  this.map.remove(this.markers);
                  this.map.remove(this.addMarkers);
                  this.markers = [];
                  this.addMarkers = [];
                  this.num = 0;
                  this.orthoForm = {
                    name: "",
                    auto_speed: 10,
                    max_speed: 20,
                    default_height: 100,
                    return_height: 100,
                    action_completed: 20,
                    point_json: [],
                    lateral: 80,
                    course: 70,
                    wheelDist: 70,
                    angle: 0,
                    cameraType: "",
                  };
                  this.cameraParamList = [];
                  this.routeTitle = this.routeLanguage.routeLine.setNewTask;
                  this.map.remove(this.line);
                  this.line = "";
                  this.map.remove(this.polypon);
                  this.polypon = "";
                  if (this.startMarker) {
                    this.map.remove(this.startMarker);
                    this.map.remove(this.endMarker);
                    this.startMarker = "";
                    this.endMarker = "";
                  }
                  this.clickId = 0;
                  this.pointCodes = [];
                  this.distance = 0;
                  this.estTime = 0;
                  this.routeSpotCount = 0;
                  this.area = 0;
                  this.photoCount = 0;
                  this.cacheData = [];
                  this.$store.commit("changeCode", 4);
                  this.$store.commit("routeItem", "");
                  this.$store.commit("checkType", "");
                  // this.$refs.planList.checkCode = this.checkType.value;
                  this.$refs.planList.getMissionList();
                  this.getFenceItem();
                });
              } else {
                this.$message.warning(this.routeLanguage.messageInfo5);
              }
            } else {
              if (validator) {
                for (const key in validator) {
                  this.$message.error(validator[key][0].message);
                  break;
                }
              }
            }
          });
        }
      }
      this.timeOut = setTimeout(() => {
        this.saveCode = false;
      }, 200);
    },
    //点击导入kml文件
    importEvent() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (!this.importClick) {
        this.importClick = true;
        if (this.markers.length > 0) {
          this.$confirm(
            this.code == 1 || this.code == 3
              ? this.routeLanguage.placeholder11
              : this.routeLanguage.placeholder12,
            this.routeLanguage.tip,
            {
              confirmButtonText: this.routeLanguage.saveBtn,
              cancelButtonText: this.routeLanguage.cancelBtn,
              type: "warning",
            }
          )
            .then(() => {
              this.$refs.files.click();
              this.map.remove(this.markers);
              this.map.remove(this.addMarkers);
              this.markers = [];
              this.addMarkers = [];
              this.num = 0;
              this.clickId = 0;
              if (this.code == 1) {
                this.routeForm.point_json = [];
                this.map.remove(this.line);
                this.line = "";
                this.allActionList = [];
                this.heights = [];
                this.distance = 0;
                this.estTime = 0;
              } else if (this.code == 2) {
                this.fenceForm.point_json = [];
                this.map.remove(this.polypon);
                this.polypon = "";
                this.startPoints = "";
              } else if (this.code == 3) {
                this.orthoForm.point_json = [];
                if (this.line) {
                  this.map.remove(this.line);
                  this.line = "";
                }
                if (this.polypon) {
                  this.map.remove(this.polypon);
                }
                this.routeSpotCount = 0;
                this.area = 0;
                this.photoCount = 0;
                this.distance = 0;
                this.estTime = 0;
              }
            })
            .catch(() => {})
            .finally(() => {
              this.importClick = false;
            });
        } else {
          this.$refs.files.click();
          this.timeOut = setTimeout(() => {
            this.importClick = false;
          }, 300);
        }
      }
    },
    //解析上传的kml文件
    chooseFileAfter(e) {
      let file = e.target.files[0];
      let a = file.name.split(".");
      if (a[a.length - 1] == "kml") {
        let file_reader = new FileReader();
        file_reader.readAsText(file, "UTF-8");
        file_reader.onload = () => {
          const xml = new DOMParser().parseFromString(
            file_reader.result,
            "text/xml"
          );
          let kml = togeojson.kml(xml, {
            style: true,
          });
          this.kmlFormat(kml);
        };
        let obj = this.$refs.files;
        obj.value = "";
      } else {
        this.$message.error(this.routeLanguage.errorMessage1);
      }
    },
    //获取kml坐标数据
    getKmlData(kml) {
      let datas = "";
      if (kml.features.length > 1) {
        datas = [];
        for (let index = 0; index < kml.features.length; index++) {
          datas.push(kml.features[index].geometry.coordinates);
        }
      } else {
        let type = kml.features[0].geometry.type;
        switch (type) {
          case "LineString":
            datas = kml.features[0].geometry.coordinates;
            break;
          case "Polygon":
            datas = kml.features[0].geometry.coordinates[0];
            break;
          default:
            break;
        }
      }
      return datas;
    },
    //kml转换成坐标
    async kmlFormat(kml) {
      if (this.code == 1) {
        // if (kml.features[0].geometry.type == "LineString") {
        if (
          !kml.features[0].properties.type ||
          kml.features[0].properties.type == this.checkType.value
        ) {
          let datas = this.getKmlData(kml);
          if (!datas) {
            this.$message.error(this.routeLanguage.errorMessage2);
            return false;
          }
          if (!this.routeForm.name) {
            this.routeForm.name = kml.features[0].properties.name;
          }
          this.importCode = true;
          this.loading = true;
          // for (let index = 0; index < datas.length; index++) {
          //   let str = new AMap.LngLat(datas[index][0], datas[index][1]);
          //   pointstr.push(str);
          // }
          await this.drawImport(datas);
        } else {
          this.$message.error(this.routeLanguage.errorMessage3);
        }
        // } else {
        //   this.$message.error("选择的kml文件不是任务航线！");
        // }
      } else if (this.code == 2) {
        // kml.features[0].geometry.type == "Polygon"
        if (
          !kml.features[0].properties.type ||
          kml.features[0].properties.type == 0
        ) {
          let datas = this.getKmlData(kml);
          if (!datas) {
            this.$message.error(this.routeLanguage.errorMessage2);
            return false;
          }
          if (!this.fenceForm.name) {
            this.fenceForm.name = kml.features[0].properties.name;
          }

          let pointstr = [];
          for (let index = 0; index < datas.length; index++) {
            let str = new AMap.LngLat(datas[index][0], datas[index][1]);
            pointstr.push(str);
          }
          if (!this.drawend) {
            this.drawMark();
          }
          for (let index = 0; index < pointstr.length; index++) {
            this.num++;
            this.drawPoint(this.num, pointstr[index]);
            this.drawPolypon();
          }
          this.map.setFitView(this.polypon, true);
        } else {
          this.$message.error(this.routeLanguage.errorMessage4);
        }
      } else if (this.code == 3) {
        if (
          !kml.features[0].properties.type ||
          kml.features[0].properties.type == this.checkType.value
        ) {
          let datas = this.getKmlData(kml);
          if (!datas) {
            this.$message.error(this.routeLanguage.errorMessage2);
            return false;
          }
          if (!this.orthoForm.name) {
            this.orthoForm.name = kml.features[0].properties.name;
          }
          // this.orthoForm.lateral=kml.features[0].properties.lateral||this.orthoForm.lateral
          // this.orthoForm.course=kml.features[0].properties.course||this.orthoForm.course
          // this.orthoForm.wheelDist=kml.features[0].properties.wheelDist||this.orthoForm.wheelDist
          // this.orthoForm.angle=kml.features[0].properties.angle||this.orthoForm.angle
          await this.drawImportOrtho(datas);
          this.importCode = true;
          this.loading = true;
        } else {
          this.$message.error(this.routeLanguage.errorMessage3);
        }
      }
    },
    //绘制导入的航点
    async drawImport(pointstr) {
      if (this.num < pointstr.length) {
        let point = new AMap.LngLat(
          pointstr[this.num][0],
          pointstr[this.num][1]
        );
        let a = this.computedMethod(2, {
          point1: point,
          fence: this.fenceItem.paths,
        });
        if (a) {
          if (this.num > 0) {
            let b = this.computedMethod(4, {
              point1: this.markers[this.num - 1].getPosition(),
              point2: point,
              fence: this.fenceItem.paths,
            });
            if (b) {
              if (this.routeLanguage.language == "en-US") {
                this.$message.error(
                  "The connection between waypoint" +
                    this.num +
                    "and waypoint" +
                    (this.num + 1) +
                    "exceeds the fence"
                );
              } else {
                this.$message.error(
                  this.num +
                    this.routeLanguage.errorMessage5 +
                    (this.num + 1) +
                    this.routeLanguage.errorMessage6
                );
              }

              this.loading = false;
              this.importCode = false;
              if (!this.drawend) {
                this.drawMark();
              }
              this.markerClick(this.markers[this.markers.length - 1]);
            } else {
              this.routeForm.point_json.push({
                lng: pointstr[this.num][0],
                lat: pointstr[this.num][1],
              });
              this.num++;
              await this.drawMarker(this.num, point);
              if (this.num > 1) {
                await this.drawline();
                await this.addCenterpoint(this.num);
              }
              this.allActionList.push({ actionList: [] });
              this.heights[this.num - 1] =
                pointstr[this.num - 1][2] || this.routeForm.default_height;
              setTimeout(() => {
                this.drawImport(pointstr);
              });
            }
          } else {
            this.routeForm.point_json.push({
              lng: pointstr[this.num][0],
              lat: pointstr[this.num][1],
            });
            this.num++;
            await this.drawMarker(this.num, point);
            // if (this.num > 1) {
            //   await this.drawline();
            //   await this.addCenterpoint(this.num);
            // }
            this.allActionList.push({ actionList: [] });
            this.heights[this.num - 1] =
              pointstr[this.num - 1][2] || this.routeForm.default_height;
            setTimeout(() => {
              this.drawImport(pointstr);
            });
          }
        } else {
          if (this.routeLanguage.language == "en-US") {
            this.$message.error(
              this.routeLanguage.waypoint.title +
                this.num +
                1 +
                this.routeLanguage.errorMessage7
            );
          } else {
            this.$message.error(
              this.num + 1 + this.routeLanguage.errorMessage7
            );
          }

          this.importCode = false;
          this.loading = false;
          if (!this.drawend) {
            this.drawMark();
          }
          this.markerClick(this.markers[this.markers.length - 1]);
        }
      } else {
        this.loading = false;
        this.importCode = false;
        if (!this.drawend) {
          this.drawMark();
        }
        this.markerClick(this.markers[this.markers.length - 1]);
      }
    },
    //绘制正射影像导入的点
    async drawImportOrtho(points) {
      if (this.num < points.length) {
        let point = new AMap.LngLat(points[this.num][0], points[this.num][1]);
        let a = this.computedMethod(2, {
          point1: point,
          fence: this.fenceItem.paths,
        });
        if (a) {
          if (this.num > 0) {
            let b = this.computedMethod(4, {
              point1: this.markers[this.num - 1].getPosition(),
              point2: point,
              fence: this.fenceItem.paths,
            });
            if (b) {
              if (this.routeLanguage.language == "en-US") {
                this.$message.error(
                  "The connection between waypoint" +
                    this.num +
                    "and waypoint" +
                    (this.num + 1) +
                    "exceeds the fence"
                );
              } else {
                this.$message.error(
                  this.num +
                    this.routeLanguage.errorMessage5 +
                    (this.num + 1) +
                    this.routeLanguage.errorMessage6
                );
              }
              this.loading = false;
              this.importCode = false;
              if (!this.drawend) {
                this.drawMark();
              }
              this.markerClick(this.markers[this.markers.length - 1]);
            } else {
              this.orthoForm.point_json.push({
                lng: points[this.num][0],
                lat: points[this.num][1],
              });
              this.num++;
              await this.drawOrthoPoint(this.num, point, 1);
              await this.drawOrthoPolypon();
              setTimeout(() => {
                this.drawImportOrtho(points);
              });
            }
          } else {
            this.orthoForm.point_json.push({
              lng: points[this.num][0],
              lat: points[this.num][1],
            });
            this.num++;
            await this.drawOrthoPoint(this.num, point, 1);
            setTimeout(() => {
              this.drawImportOrtho(points);
            });
          }
        } else {
          if (this.routeLanguage.language == "en-US") {
            this.$message.error(
              this.routeLanguage.waypoint.title +
                this.num +
                1 +
                this.routeLanguage.errorMessage7
            );
          } else {
            this.$message.error(
              this.num + 1 + this.routeLanguage.errorMessage7
            );
          }
          this.importCode = false;
          this.loading = false;
          if (!this.drawend) {
            this.drawMark();
          }
          this.markerClick(this.markers[this.markers.length - 1]);
        }
      } else {
        this.loading = false;
        this.importCode = false;
        if (!this.drawend) {
          this.drawMark();
        }
        this.markerClick(this.markers[this.markers.length - 1]);
      }
    },
    //点击导出kml文件
    exportEvent() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (!this.exportClick) {
        this.exportClick = true;
        this.$confirm(
          this.code == 1 || this.code == 3
            ? this.routeLanguage.placeholder13
            : this.routeLanguage.placeholder14,
          this.routeLanguage.tip,
          {
            confirmButtonText: this.routeLanguage.saveBtn,
            cancelButtonText: this.routeLanguage.cancelBtn,
            type: "warning",
          }
        )
          .then(() => {
            if (this.code == 1) {
              if (!this.routeForm.name) {
                this.$message.error(this.routeLanguage.routeLine.placeholder);
              } else {
                if (this.routeForm.point_json.length > 1) {
                  let str =
                    `<?xml version='1.0' encoding='utf-8'?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>
                <Placemark><name>` +
                    this.routeForm.name +
                    `</name><type></type><ExtendedData><Data name="type"><value>` +
                    this.checkType.value +
                    `</value></Data></ExtendedData><LineString><coordinates>`;
                  for (
                    let index = 0;
                    index < this.routeForm.point_json.length;
                    index++
                  ) {
                    str +=
                      this.routeForm.point_json[index].lng +
                      "," +
                      this.routeForm.point_json[index].lat +
                      "," +
                      this.heights[index] +
                      " ";
                  }
                  str += `</coordinates></LineString><styleUrl>style-id</styleUrl></Placemark><Style id="style-id"><LineStyle><color>ffffffff</color><width>6</width></LineStyle></Style></Document></kml>`;
                  const domObj = document.createElement("a");
                  domObj.setAttribute(
                    "href",
                    "data:text/xml;charset=utf-8," + encodeURIComponent(str)
                  ); //注：如存储数组 or JSON需将其转换为JSON字符串
                  domObj.setAttribute(
                    "download",
                    this.routeForm.name + this.returnDate() + ".kml"
                  );
                  if (document.createEvent) {
                    const event = document.createEvent("MouseEvents");
                    event.initEvent("click", true, true);
                    domObj.dispatchEvent(event);
                  } else {
                    domObj.click();
                  }
                } else {
                  this.$message.error(this.routeLanguage.messageInfo5);
                }
              }
            } else if (this.code == 2) {
              if (!this.fenceForm.name) {
                this.$message.error(this.routeLanguage.fence.placeholder1);
              } else {
                if (this.fenceForm.point_json.length > 2) {
                  let str =
                    `<?xml version='1.0' encoding='utf-8'?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>
                <Placemark><name>` +
                    this.fenceForm.name +
                    `</name><type></type><ExtendedData><Data name="type"><value>0</value></Data></ExtendedData><Polygon><outerBoundaryIs><LinearRing><coordinates>`;
                  for (
                    let index = 0;
                    index < this.fenceForm.point_json.length;
                    index++
                  ) {
                    str +=
                      this.fenceForm.point_json[index].lng +
                      "," +
                      this.fenceForm.point_json[index].lat +
                      " ";
                  }
                  str += `</coordinates></LinearRing></outerBoundaryIs></Polygon><styleUrl>style-id</styleUrl></Placemark><Style id="style-id"><LineStyle><color>ffff5213</color><width>6</width></LineStyle><PolyStyle><color>80B15713</color></PolyStyle></Style></Document></kml>`;
                  const domObj = document.createElement("a");
                  domObj.setAttribute(
                    "href",
                    "data:text/xml;charset=utf-8," + encodeURIComponent(str)
                  ); //注：如存储数组 or JSON需将其转换为JSON字符串
                  domObj.setAttribute(
                    "download",
                    this.fenceForm.name + this.returnDate() + ".kml"
                  );
                  if (document.createEvent) {
                    const event = document.createEvent("MouseEvents");
                    event.initEvent("click", true, true);
                    domObj.dispatchEvent(event);
                  } else {
                    domObj.click();
                  }
                } else {
                  this.$message.error(this.routeLanguage.messageInfo4);
                }
              }
            } else if (this.code == 3) {
              if (!this.orthoForm.name) {
                this.$message.error(this.routeLanguage.routeLine.placeholder);
              } else {
                if (this.orthoForm.point_json.length > 1) {
                  let str =
                    `<?xml version='1.0' encoding='utf-8'?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>
                <Placemark><name>` +
                    this.orthoForm.name +
                    `</name><type></type><ExtendedData><Data name="type"><value>` +
                    this.checkType.value +
                    // `</value></Data><Data name="lateral"><value>` +
                    // this.orthoForm.lateral +
                    // `</value></Data><Data name="course"><value>` +
                    // this.orthoForm.course +
                    // `</value></Data>
                    // <Data name="wheelDist"><value>` +
                    // this.orthoForm.wheelDist +
                    // `</value></Data>
                    // <Data name="angle"><value>` +
                    // this.orthoForm.angle +
                    `</value></Data></ExtendedData><Polygon><outerBoundaryIs><LinearRing><coordinates>`;
                  for (
                    let index = 0;
                    index < this.orthoForm.point_json.length;
                    index++
                  ) {
                    str +=
                      this.orthoForm.point_json[index].lng +
                      "," +
                      this.orthoForm.point_json[index].lat +
                      "," +
                      this.orthoForm.default_height +
                      " ";
                  }
                  str += `</coordinates></LinearRing></outerBoundaryIs></Polygon><styleUrl>style-id</styleUrl></Placemark><Style id="style-id"><LineStyle><color>ffffffff</color><width>6</width></LineStyle></Style></Document></kml>`;
                  const domObj = document.createElement("a");
                  domObj.setAttribute(
                    "href",
                    "data:text/xml;charset=utf-8," + encodeURIComponent(str)
                  ); //注：如存储数组 or JSON需将其转换为JSON字符串
                  domObj.setAttribute(
                    "download",
                    this.orthoForm.name + this.returnDate() + ".kml"
                  );
                  if (document.createEvent) {
                    const event = document.createEvent("MouseEvents");
                    event.initEvent("click", true, true);
                    domObj.dispatchEvent(event);
                  } else {
                    domObj.click();
                  }
                } else {
                  this.$message.error(this.routeLanguage.messageInfo5);
                }
              }
            }
          })
          .catch(() => {
            this.$message.info(this.routeLanguage.cancelexport);
          })
          .finally(() => {
            this.exportClick = false;
          });
      }
    },
    //计算当前的日期yyyyMMddhhmmss
    returnDate() {
      let time = new Date();
      time =
        time.getFullYear() +
        (time.getMonth() + 1 > 9
          ? time.getMonth() + 1
          : "0" + (time.getMonth() + 1)) +
        (time.getDate() > 9 ? time.getDate() : "0" + time.getDate()) +
        (time.getHours() > 9 ? time.getHours() : "0" + time.getHours()) +
        (time.getMinutes() > 9 ? time.getMinutes() : "0" + time.getMinutes()) +
        (time.getSeconds() > 9 ? time.getSeconds() : "0" + time.getSeconds());
      return time;
    },
    //高德地图的数学计算方法
    computedMethod(value, param) {
      let { point1, point2, fence } = param;
      let result = "";
      switch (value) {
        case 1: //计算两点距离
          result = AMap.GeometryUtil.distance(point1, point2);
          break;
        case 2: //计算点是否在环内
          result = AMap.GeometryUtil.isPointInRing(point1, fence);
          break;
        case 3: //计算经纬度围成的环的面积
          result = AMap.GeometryUtil.ringArea(fence);
          break;
        case 4: //计算线段是否与环相交
          result = AMap.GeometryUtil.doesSegmentRingIntersect(
            point1,
            point2,
            fence
          );
          break;
        case 5: //计算线段是否与路径相交
          result = AMap.GeometryUtil.doesSegmentLineIntersect(
            point1,
            point2,
            fence
          );
          break;
        case 6: //计算区域面积
          result = AMap.GeometryUtil.ringArea(fence);
          break;
        default:
          break;
      }
      return result;
    },
    //计算两点间的距离是否大于2千米
    computedDistance(point_1, point_2) {
      let distance = this.computedMethod(1, {
        point1: point_1,
        point2: point_2,
      });
      if (distance > 2000) {
        return false;
      } else return true;
    },
    //计算围栏中两点的距离并显示
    fenceListPoint(points) {
      let center = this.computeCenter(points);
      let str = this.computedMethod(1, {
        point1: points[0],
        point2: points[1],
      });
      str = str.toFixed(2) + "m";
      let rotate = this.calcAngle([points[0], points[1]]);
      let labelMarker = initMaps.textMarker(str, center, rotate);
      return labelMarker;
    },
    //websocket返回的数据
    getMessage(msg_id, data) {
      // console.log(msg_id, data);
      if (msg_id == 110) {
        for (let index = 0; index < this.deviceList.length; index++) {
          if (data.sn_id == this.deviceList[index].sn_id) {
            if (this.deviceList[index].is_push_on != data.is_push_on) {
              let icon,
                title = "";
              let x, y;
              if (this.map.getZoom() >= 9 && this.map.getZoom() <= 17) {
                x = (50 * this.map.getZoom()) / 20;
                y = (50 * this.map.getZoom()) / 20;
              } else if (this.map.getZoom() > 17) {
                x = 50;
                y = 50;
              } else if (this.map.getZoom() < 9) {
                x = 0;
                y = 0;
              }
              if (data.is_push_on) {
                icon = new AMap.Icon({
                  // 图标的取图地址
                  image: require("../../assets/img/inLineHome.png"),
                  imageSize: new AMap.Size(x, y),
                });
                title = this.deviceList[index].name + " \n在线";
              } else {
                icon = new AMap.Icon({
                  // 图标的取图地址
                  image: require("../../assets/img/outLineHome.png"),
                  imageSize: new AMap.Size(x, y),
                });
                title = this.deviceList[index].name + " \n离线";
              }
              this.deviceMarker[index].setIcon(icon);
              this.deviceMarker[index].setTitle(title);
            }
            this.deviceList[index].is_pull_on = data.is_pull_on;
            this.deviceList[index].is_push_on = data.is_push_on;
          }
        }
      }
    },
    //获取禁飞区电子围栏信息
    async getZone() {
      // let zoneData = sessionStorage.getItem("zoneList");
      // if (zoneData) {
      //   this.zoneList = JSON.parse(zoneData);
      //   this.drawNoZone();
      // } else {
      //   this.zoneList = [];
      // }
      // let center = this.map.getCenter();
      // let data = {
      //   lat_int: parseInt(center.lat * 1e7),
      //   lon_int: parseInt(center.lng * 1e7),
      //   type_json:
      //     this.zone_type || this.zone_type.length == 4
      //       ? JSON.stringify(this.zone_type)
      //       : this.zone_type,
      //   fm: this.fm,
      // };
      // data.pmd =
      //   data.lat_int.toString() + data.lon_int.toString() + data.fm.toString();
      // await noZone(data).then((res) => {
      //   for (let index = 0; index < res.length; index++) {
      //     this.saveNoZone(res[index]);
      //   }
      // });
      // await sessionStorage.setItem("zoneList", JSON.stringify(this.zoneList));
      // this.drawNoZone();
    },
    //存储禁飞区数据
    saveNoZone(item) {
      let a = this.zoneList.findIndex((x) => {
        return x.id == item.id;
      });
      if (a == -1) {
        let paths = [];
        for (let index = 0; index < item.point_list.length; index++) {
          paths.push(
            new AMap.LngLat(
              item.point_list[index].lon_int / 1e7,
              item.point_list[index].lat_int / 1e7
            )
          );
        }
        let params = {
          stroke_color: item.stroke_color,
          solid_color: item.solid_color,
          stroke_width: item.stroke_width,
          id: item.id,
          paths: paths,
          type: item.zone_type,
        };
        this.zoneList.push(params);
      }
    },
    //绘制禁飞区
    drawNoZone() {
      for (let index = 0; index < this.zoneList.length; index++) {
        let a = this.polyponZone.findIndex((item) => {
          return item.getExtData().id == this.zoneList[index].id;
        });
        if (a == -1) {
          let polygon = initMaps.drawPolyponZone(this.zoneList[index]);
          this.polyponZone.push(polygon);
        }
      }
    },
    //禁飞区点击开关响应
    async zoneState(e) {
      let zoom = this.map.getZoom();
      if (this.zoomShow && zoom >= 9) {
        this.zoomShow = false;
      }
      if (!this.zone_type) {
        this.zone_type = [];
      }
      if (e.state) {
        this.zone_type.push(e.index);
        if (zoom >= 9) {
          for (let index = 0; index < this.polyponZone.length; index++) {
            if (this.polyponZone[index].getExtData().type == e.index) {
              this.polyponZone[index].setMap(this.map);
            }
          }
        }
      } else {
        let a = this.zone_type.indexOf(e.index);
        this.zone_type.splice(a, 1);
        if (this.zone_type.length == 0) {
          this.zone_type = "";
        }
        if (zoom >= 9) {
          for (let index = 0; index < this.polyponZone.length; index++) {
            if (this.polyponZone[index].getExtData().type == e.index) {
              this.map.remove(this.polyponZone[index]);
            }
          }
        }
      }
    },
    //监听是否绘画区域
    isDrawZone(val, oldVal) {
      if (val > oldVal) {
        let zoom = this.map.getZoom();
        if (zoom >= 9 && this.zone_type) {
          for (let index = oldVal; index < this.polyponZone.length; index++) {
            for (let i = 0; i < this.zone_type.length; i++) {
              if (
                this.polyponZone[index].getExtData().type == this.zone_type[i]
              ) {
                this.polyponZone[index].setMap(this.map);
              }
            }
          }
        }
      }

      // for (let index = 0; index < this.polyponZone.length; index++) {
      //     if(this.polyponZone[index].getExtData().type==e.index){
      //       this.map.remove(this.polyponZone[index])
      //     }
      //   }
    },
    //所有点默认高度
    setDefaultHeight() {
      for (let index = 0; index < this.heights.length; index++) {
        this.heights[index] = this.routeForm.default_height;
      }
      let openList = this.pointCodes;
      if (this.pointCodes.length > 0) {
        this.pointCodes = [];
        this.pointCodes = openList;
      }
      if (this.checkType.value == 50) {
        this.drawOrthoInPolypon();
      }
    },
    //相机参数返回
    getCameraParams(e) {
      this.cameraParamList = e;
      this.drawOrthoInPolypon();
    },
    //相机参数
    changeCamera(e) {
      // this.orthoForm.cameraType=e
      let index = this.cameraOptions.findIndex((item) => {
        return item.value == e;
      });
      this.cameraParamList = this.cameraOptions[index].params;
      this.drawOrthoInPolypon();
    },
  },
  computed: {
    fence() {
      return this.$store.state.route.fence;
    },
    fenceId() {
      return this.$store.state.route.fenceId;
    },
    operateFItem() {
      return this.$store.state.route.operateFItem;
    },
    code() {
      return this.$store.state.route.code;
    },
    checkType() {
      return this.$store.state.route.checkType;
    },
    routeItem() {
      return this.$store.state.route.routeItem;
    },
    routeLanguage() {
      return this.$languagePackage.routes;
    },
  },
  watch: {
    "routeForm.point_json.length"(value) {
      if (!this.noChange) {
        this.heights[value - 1] = this.routeForm.default_height;
        if (this.pointLength < value) {
          this.pointLength = value;
          this.allActionList.push({ actionList: [] });
        } else {
          this.pointLength = value;
          this.allActionList.splice(this.choosePoint - 1, 1);
        }
      }
    },
    "routeForm.auto_speed"(value) {
      this.estTime = this.distance / value + this.markers.length;
    },
    "polyponZone.length"(value, oldValue) {
      this.isDrawZone(value, oldValue);
    },
    fence(value) {
      this.drawFenceList();
    },
    fenceId() {
      this.getFenceItem();
    },
    operateFItem() {
      this.operateItem();
    },
    code(newValue, oldValue) {
      this.toolEvent(10);
      if ((newValue == 0 && oldValue == 4) || newValue == 1 || newValue == 3) {
        if (this.clickRoute) {
          this.map.remove(this.clickRoute);
          this.clickRoute = "";
        }
        if (this.clickRoutePolypon) {
          this.map.remove(this.clickRoutePolypon);
          this.clickRoutePolypon = "";
        }
        if (this.routeMarkers.length > 0) {
          this.map.remove(this.routeMarkers);
          this.routeMarkers = [];
        }
      }
      if (newValue == 0) {
        if (this.distancePoint) {
          this.map.remove(this.distancePoint);
          this.distancePoint = [];
        }
      }
      if (newValue == 1 || newValue == 2 || newValue == 3) {
        this.map.off("dragend", this.dragMap);
        if (newValue == 1 || newValue == 3) {
          if (this.routeItem && this.routeItem.typeCode) {
            this.loading = true;
            setTimeout(() => {
              this.getRouteItem();
            }, 50);
          } else {
            if (newValue == 3) {
              this.orthoForm.cameraType = 2;
              this.cameraParamList = this.cameraOptions[1].params;
            }
          }
        }
      }
      if (
        (oldValue == 1 || oldValue == 2 || oldValue == 3) &&
        newValue != 1 &&
        newValue != 2 &&
        newValue != 3
      ) {
        this.map.on("dragend", this.dragMap);
      }
      if (newValue == 2) {
        if (!this.operateFItem) {
          for (let index = 0; index < this.polyponList.length; index++) {
            this.polyponList[index].hide();
          }
        }
      }
    },
    routeItem(value) {
      if (!value.typeCode) {
        this.getRouteItem();
      }
    },
    fenceForm: {
      handler() {
        this.fenceFromCode = true;
      },
      deep: true,
    },
    routeForm: {
      handler() {
        this.routeFormCode = true;
      },
      deep: true,
    },
    orthoForm: {
      handler() {
        this.orthoFormCode = true;
      },
      deep: true,
    },
    heights() {
      this.routeFormCode = true;
    },
    allActionList: {
      handler() {
        this.routeFormCode = true;
      },
      deep: true,
    },
    layerCode1() {
      this.searchSite = "";
    },
    "orthoForm.point_json.length"(newvalue, oldValue) {
      if (newvalue > oldValue) {
        this.heights[newvalue - 1] = this.orthoForm.default_height;
      }
    },
    "orthoForm.lateral"(val) {
      this.drawOrthoInPolypon();
    },
    "orthoForm.course"(val) {
      this.drawOrthoInPolypon();
    },
    "orthoForm.wheelDist"(val) {
      this.drawOrthoInPolypon();
    },
    "orthoForm.angle"(val) {
      this.drawOrthoInPolypon();
    },
  },
  beforeDestroy() {
    this.map && this.map.destroy();
    this.map = "";
  },
  destroyed() {
    this.$store.commit("changeCode", 0);
    this.$store.commit("setFence", "");
    this.$store.commit("fenceItemId", "");
    this.$store.commit("operateFItem", "");
    this.$store.commit("typeList", "");
    this.$store.commit("checkType", "");
    this.$store.commit("routeItem", "");
    if (this.timeOut) {
      clearTimeout(this.timeout);
    }
  },
};
</script>
<style lang="less" scoped>
.routeplan {
  width: 100%;
  height: 100%;
  #tipinput {
    position: absolute;
    top: 300px;
    left: 500px;
  }
  #maps {
    width: 100%;
    height: 100%;
  }
  .listShow {
    position: absolute;
    width: 350px;
    height: 98%;
    top: 1%;
    left: 1%;
    border-radius: 8px;
    .addContent {
      height: 98%;
      width: 94%;
      padding: 4%;
      padding-bottom: 2%;
      .title {
        height: 4%;
        font-size: 18px;
        .el-button {
          font-size: 22px;

          padding: 0;
        }
      }
      .content-item-1 {
        width: 100%;
        height: 94%;
        overflow-x: hidden;
        overflow-y: auto;
        padding-top: 2%;
        .el-form-item {
          width: 98%;
          .el-input {
            margin-top: 1%;
            width: 100%;
          }
          .el-button {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            letter-spacing: 2px;
            font-weight: 550;
          }

          .slider-class-1 {
            width: 100%;
            display: inline-block;
            margin-left: 2%;
          }
          .el-select {
            width: 100%;
          }
          .el-collapse {
            width: 100%;
            .el-collapse-item {
              width: 100%;
              margin-top: 2%;
              div {
                margin-left: 1%;
              }
              .el-input-number {
                margin-left: 1%;
                width: 98%;
              }
              .addActionBtn {
                margin-top: 2%;
                margin-left: 1%;
                width: 98%;
              }
              .routeActionDiv {
                width: 100%;
                margin-left: 0 !important;
                margin-top: 1%;
                .el-select {
                  width: 85%;
                }
                .el-button {
                  margin-left: 2%;
                  width: auto;
                  font-size: 24px;
                  padding: 0;
                  padding-top: 6px;
                }
                .actionValueDiv {
                  margin: 1%;
                  margin-left: 2%;
                  margin-right: 0;
                  width: 98%;
                  .el-input-number {
                    width: 18%;
                  }
                }
              }
            }
          }
        }
        .title {
          text-align: center;
          font-weight: 520;
          letter-spacing: 2px;
        }
        .saveBut {
          margin: 0.5%;
          width: 98%;
          font-weight: 600;
          font-size: 18px;
        }
      }
    }
  }
  .operateBar_1 {
    position: absolute;
    bottom: 3%;
    right: 7%;
    height: auto;
    border-radius: 8px;
    font-size: 18px;
    .el-button {
      width: 80px;
      padding: 5px 0;
      margin: 0;
      text-align: left;
      font-size: 16px;
      .el-image {
        width: 25%;
        float: left;
        margin: 0 5px;
      }
    }
  }
  .timer {
    width: auto;
    height: auto;
    top: 1%;
    left: 380px;
    position: absolute;
    border-radius: 6px;
    display: flex;
    .timer-item {
      width: 160px;
      margin: 20px 10px;
      font-size: 20px;
      text-align: center;
      font-weight: 550;
      .timer-item-1 {
        font-size: 35px;
      }
    }
  }
  .layerBut {
    width: auto;
    height: auto;
    position: absolute;
    top: 2%;
    right: 1.5%;
    border-radius: 4px;

    .el-button {
      border-radius: 0;
      padding: 10px 8px;
      margin: 0 5px;
      display: block;
      .el-image {
        width: 18px;
      }
    }
  }
  .weatherDiv {
    width: auto;
    height: auto;
    min-width: 200px;
    min-height: 100px;
    position: absolute;
    z-index: 20;
    cursor: move;
    bottom: 350px;
    right: 30px;
    padding: 1% 1.5%;
    border-radius: 8px;
    .el-divider {
      height: 3px;
      margin: 0;
    }
    .weaTitle {
      margin-bottom: 2%;
      letter-spacing: 1.5px;
      font-size: 18px;
      .el-button {
        padding: 0;
        margin: 0 5px;
        border: none;
        .el-image {
          width: 18px;
        }
      }
    }
    .weaContent {
      .el-row {
        .el-col {
          &.temperaClass {
            text-align: center;
            .item_1 {
              font-size: 60px;
            }
          }
          &.temperaClass_1 {
            padding-top: 5px;
          }
        }
      }
    }
  }
  .infoMsg {
    position: absolute;
    top: 2vh;
    left: 30%;
    width: 20%;
  }
}
</style>
<style lang="less">
.routeplan {
  .listShow {
    .addContent {
      .content-item-1 {
        .el-form-item {
          .el-form-item__label {
            padding: 0 !important;
            line-height: 20px !important;
          }
          .el-form-item__content {
            line-height: 10px !important;
          }

          .slider-class-1 {
            .el-slider__runway {
              height: 2px !important;
              margin: 0 !important;
              width: 79%;
              margin-top: 15px !important;
              .el-slider__bar {
                height: 2px !important;
                background-color: #0555ff !important;
              }
            }
            .el-slider__button-wrapper {
              width: 10px !important;
              height: 10px !important;
              top: -10px;
              .el-slider__button {
                width: 8px !important;
                height: 8px !important;
                border: 1px solid #0555ff !important;
              }
            }
            .el-slider__input,
            .el-input-number--small {
              width: 17% !important;
              line-height: 0 !important;
              margin-right: 5px !important;
              .el-input__inner {
                padding: 0 5px !important;
              }
            }
          }
          .silderInput {
            .sliderValue {
              .el-slider__runway {
                width: 100% !important;
              }
            }
          }
          .el-collapse {
            .el-collapse-item {
              .el-input {
                .el-input__inner {
                  text-align: left !important;
                }
              }
              .el-collapse-item__header {
                padding-left: 5% !important;
                border-radius: 6px;
                width: 95% !important;
                letter-spacing: 1px !important;
                .el-collapse-item__arrow {
                  // display: none !important;
                  transform: rotate(90deg) !important;
                }
                .el-collapse-item__arrow.is-active {
                  transform: rotate(0deg) !important;
                }
              }
              .el-collapse-item__wrap {
                width: 100% !important;

                .el-collapse-item__content {
                  width: 100% !important;
                }
              }
              .routeActionDiv {
                .actionValueDiv {
                  width: 98%;
                  .el-input-number {
                    .el-input__inner {
                      padding: 0 5px !important;
                      text-align: center !important;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .el-loading-mask {
        .el-loading-spinner {
          top: 50% !important;
          font-size: 16px !important;
          .el-loading-text {
            font-size: 16px !important;
          }
          i {
            font-size: 30px !important;
          }
        }
      }
    }
  }
  .amap-logo,
  .amap-copyright {
    display: none !important;
  }
  .amap-geolocation {
    bottom: 190px !important;
    right: 30px !important;
  }
  .amap-ui-control-layer-list {
    z-index: 20 !important;
  }
  #maps {
    .amap-markers {
      .amap-marker {
        .marker-edit {
          position: relative;
          height: 30px;
          width: 30px;
          border-radius: 50%;

          text-align: center;

          .text {
            position: absolute;
            top: 50%;
            left: 50%;
            font-weight: 700;
            font-size: 20px;
            transform: translate(-50%, -50%);
          }
        }
        .marker-edit-i {
          height: 18px;
          width: 18px;
          border-radius: 50%;

          text-align: center;

          .text {
            position: absolute;
            top: 47%;
            left: 50%;
            font-weight: 800;
            font-size: 18px;
            vertical-align: middle;
            transform: translate(-50%, -50%);
          }
        }
        .marker-o-edit {
          position: relative;
          height: 30px;
          width: 30px;
          border-radius: 50%;

          text-align: center;

          .text {
            position: absolute;
            top: 50%;
            left: 50%;
            font-weight: 700;
            font-size: 20px;
            transform: translate(-50%, -50%);
          }
        }
        .marker-o-edit-i {
          height: 18px;
          width: 18px;
          border-radius: 50%;

          text-align: center;

          .text {
            position: absolute;
            top: 47%;
            left: 50%;
            font-weight: 800;
            font-size: 18px;
            vertical-align: middle;
            transform: translate(-50%, -50%);
          }
        }
        .startend-marker {
          height: 14px;
          width: 14px;
          border-radius: 50%;

          text-align: center;
          .text {
            position: absolute;
            top: 50%;
            left: 50%;
            font-weight: 800;
            font-size: 14px;
            vertical-align: middle;
            transform: translate(-50%, -50%);
          }
        }
        .renderMarker {
          border-radius: 4px;
          padding: 1px;
          span {
            display: inline-block;
            padding: 2px 4px;
            min-width: 32px;
            border-radius: 4px;
            text-align: center;
          }
        }
      }
    }
    .amap-info {
      .amap-info-sharp {
        display: none !important;
      }
      .amap-info-content {
        padding: 0 !important;
        min-width: 300px !important;
        .divider {
          width: 100%;
          margin-top: 25px;
        }
        .info-item-content {
          margin: 1% 5%;
          padding: 1%;
          .imageStyle {
            width: 8%;
            margin-right: 5%;
          }
        }
      }
    }
  }
  .el-loading-mask {
    .el-loading-spinner {
      top: 5% !important;
      i {
        font-size: 50px !important;
      }
    }
  }
}
.selects {
  .el-select-dropdown__item {
    &.selected {
      border-radius: 4px !important;
    }
    &:hover {
      border-radius: 4px !important;
    }
  }
}
.popover-item {
  &.popover-item-1 {
    padding: 0 !important;
    width: 18vw !important;
    .el-autocomplete {
      width: 100% !important;
      .el-input {
        .el-input__inner {
          width: 100%;
          height: 35px !important;
          line-height: 10px !important;
          font-size: 12px !important;
          letter-spacing: 2px !important;
          padding-top: 1px !important;
          padding-bottom: 1px !important;
        }
      }
    }
    .el-input-group__append {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      .el-button {
        height: 35px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        .el-image {
          width: 14px !important;
        }
      }
    }
  }
  &.popover-item-3 {
    padding: 0 !important;
    width: 300px !important;
  }
  &.popover-item-2 {
    padding: 0 !important;

    width: 500px !important;
    .content {
      border-radius: 6px !important;

      .el-button {
        width: 16% !important;
        padding: 1%;
        margin: 0.3% !important;
        font-size: 100% !important;
        .el-image {
          width: 25% !important;
          // float: left !important;
          &.el-image_1 {
            width: 30% !important;
          }
          &.el-image_2 {
            width: 20% !important;
          }
        }
      }
    }
  }
  .popper__arrow,
  .popper__arrow::after {
    display: none !important;
    // border-left-color: rgba(8, 16, 39, 0.9) !important;
  }
  .searchReturn {
    min-height: 50%;
    width: 100%;
    overflow: auto;
    border-radius: 4px;
    .el-button {
      width: 98%;
      margin: 1%;
      padding: 2%;
      text-align: left;
      font-size: 16px;
    }
  }
}
.autoInput {
  width: 18vw !important;
}
.messageTip {
  vertical-align: top !important;
  margin-top: 20% !important;
}
</style>