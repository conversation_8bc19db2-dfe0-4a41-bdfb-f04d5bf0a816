// 获取当前日期
export function nowDate(time) {
    let date = new Date();
    if (time || time === 0) {
        date = new Date(time);
    }
    let year = date.getFullYear(); // 年
    let month = date.getMonth() + 1; // 月
    month = String(month).padStart(2, "0")
    let day = date.getDate(); // 日
    day = String(day).padStart(2, "0")
    let hour = date.getHours(); // 时
    hour = String(hour).padStart(2, "0")
    let minute = date.getMinutes(); // 分
    minute = String(minute).padStart(2, "0")
    let second = date.getSeconds(); // 秒
    second = String(second).padStart(2, "0")
    let dateTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`
    return {
        year,
        month,
        day,
        hour,
        minute,
        second,
        dateTime
    }
}
// 获取当前日期时间戳
export function getDateTime() {
    let date = new Date();
    return date.getTime();
}

/**
 * 获取时间差,时间不超过一天
 * @param { start } 开始
 * @param { end } 结束
 * @return 时间差，格式 hh:mm:ss
 */
export function getMistiming(start, end) {
    let startTime = new Date(start).getTime();
    let endtTime = new Date(end).getTime();
    // 获取小时
    let time = endtTime - startTime;
    let switchT = timestampSwitch(time);
    return {
        time: switchT.time, // 时间， hh:mm:ss
        list: switchT.list,
        mistiming: time, // 两个日期时间差，毫秒
    };
}

/**
 * 把时间戳转换成hh:mm:ss 时间不要超过一天
 * @param {time} 时间戳
 * @return hh:mm:ss
 */
export function timestampSwitch(time) {
    let hour = Math.floor(time / (60 * 60 * 1000));
    hour = hour > 9 ? hour : ("0" + hour);

    // 获取分钟
    let mm = time % (60 * 60 * 1000);
    let minute = Math.floor(mm / (60 * 1000));
    minute = String(minute).padStart(2, '0');

    // 获取秒
    let ss = mm % (60 * 1000);
    let second = Math.floor(ss / 1000);
    second = String(second).padStart(2, "0");

    let list = [hour, minute, second]
    return {
        time: list.join(":"),
        list: list
    }
}

/**
 * 获取多少天之前的日期
 */
export function getBeforeDate(val) {
    const start = new Date();
    let msec = 3600 * 1000 * 24;
    start.setTime(start.getTime() - msec * val);
    return {
        start,
    }
}

/**
 * 基于elementUi日期快捷选择,适用于类型为“daterange”、“datetimerange”的日期选择器
 * @param { fastList } 快捷方式列表
 * @returns pickerOptions对象
 */
export function getPickerOptions(fastList, type = "range") {
    const dateClick = (val, picker) => {
        const end = new Date();
        const start = getBeforeDate(val).start;
        let date = [start, end];
        if (type !== "range") {
            date = start;
        }
        picker.$emit('pick', date);
    }
    let data = fastList;
    // 不传则默认最近一周、最近一个月、最近三个月
    if (!fastList) {
        data = [{
                label: "最近一周",
                value: 7
            },
            {
                label: "最近一个月",
                value: 30
            },
            {
                label: "最近三个月",
                value: 90
            },
        ]
    }

    let shortcuts = data.map(item => {
        return {
            text: item.label,
            onClick: (picker) => {
                dateClick(item.value, picker)
            }
        }
    });
    return {
        shortcuts
    }
}

/**
 * 基于elementUi日期快捷选择,适用于类型为“datetime”、“date"
 * @param { fastList } 快捷方式列表
 * @returns pickerOptions对象
 */
export function getDatePicker(fastList) {
    let list = fastList;
    if (!list) {
        list = [{
                label: "今天",
                value: 0
            },
            {
                label: "昨天",
                value: 1
            },
            {
                label: "三天前",
                value: 3
            },
            {
                label: "一周前",
                value: 7
            },
        ]
    }
    return getPickerOptions(list);
}

/**
 * 把毫秒转换成秒
 */