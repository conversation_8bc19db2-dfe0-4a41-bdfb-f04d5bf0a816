/**
 * 航行页面
 */

const navigation = {
    rtkStave: {
        0: "未连接",
        1: "未定位",
        2: "单点定位",
        3: "浮动解",
        4: "固定解",
    },
    flightMode: {
        2: "姿态模式",
        // 2: "模式错误",
        3: "自动模式",
        4: "跟随模式",
        5: "GPS模式",
        6: "返航模式",
        9: "降落飞行模式",
        13: "运动模式",
    },
    filghtStep: {
        errorHint: "流程出错，程序已终止,请联系管理员",
        errorInfoTitle: '流程出错步骤：',
        errorStep: {
            1: '无人机开机',
            2: '航线上传',
            3: '机巢一键开舱',
            4: '无人机自检',
            5: '无人机RTK检查',
            6: '无人机解锁',
            7: '无人机切换自动飞行模式',
            8: '机巢一键关仓',
            9: '无人机降落异常',
            10: '起飞异常',
            11: '电量低于50%'
        },
        errorTip: '姿态模式，禁止飞行',
        NoTF: '未检测到TF卡',
        capacity: 'TF卡容量不足',
        interruptHint: "流程已终止",
        suspendHint: "流程已暂停",
        countDown: "起飞倒计时",
        countDown1: "复降倒计时",
        continue: "继 续",
        pause: "暂 停",
        interrupt: "中 断",
        close: "关闭",
        altnlandReason: '备降原因：',
        process: "继续执行流程",
        process1: '继续流程执行',
        pauseProcess: '已暂停流程执行',
        endProcess: '已终止流程',
        takeOffStep: {
            0: {
                title: "无人机开机",
                hintText: "无人机正在开机...",
                voice: "无人机开机"
            },
            1: {
                title: "航线上传",
                hintText: "航线正在上传...",
                voice: "航线传输中"
            },
            2: {
                title: "机巢开舱",
                hintText: "机巢开舱中...",
                voice: "机巢开舱"
            },
            3: {
                title: "平台升起",
                hintText: "机巢平台升起中...",
                voice: "平台升起"
            },
            4: {
                title: "脚架释放",
                hintText: "机巢脚架释放中...",
                voice: "脚架释放"
            },
            5: {
                title: "无人机自检",
                hintText: "无人机自检中...",
                voice: "RTK等待中"
            },
            6: {
                title: "无人机解锁",
                hintText: "无人机解锁中...",
                voice: "无人机解锁"
            },
            7: {
                title: "起飞",
                hintText: "无人机起飞中...",
                voice: "起飞"
            },
            8: {
                title: "机巢关舱",
                hintText: "机巢关舱中...",
                voice: "机巢关舱"
            },
            9: {
                title: "装载飞机电池",
                hintText: "装载电池中...",
                voice: "装载电池"
            }

        },
        landingStep: {
            0: {
                title: "返航",
                hintText: "无人机正在返航...",
                voice: "任务执行完毕，开始返航"
            },
            1: {
                title: "开舱",
                hintText: "机巢开舱中...",
                voice: "机巢开舱"
            },
            2: {
                title: "平台升起",
                hintText: "机巢平台升起中...",
                voice: "平台升起"
            },
            3: {
                title: "脚架释放",
                hintText: "机巢脚架释放中...",
                voice: "脚架释放"
            },
            4: {
                title: "无人机降落",
                hintText: "无人机降落中...",
                voice: "无人机降落"
            },
            5: {
                title: "无人机校正",
                hintText: "机巢脚架校正中...",
                voice: "无人机校正"
            },
            6: {
                title: "平台下降",
                hintText: "机巢平台下降中...",
                voice: "平台下降"
            },
            7: {
                title: "关舱",
                hintText: "机巢关舱中...",
                voice: "机巢关舱"
            },
            8: {
                title: "卸下飞机电池",
                hintText: "卸下电池中...",
                voice: "卸下电池"
            }
        },
        takeOffStep1: {
            0: {
                title: "无人机开机",
                hintText: "无人机正在开机...",
                voice: "无人机开机"
            },
            1: {
                title: "航线上传",
                hintText: "航线正在上传...",
                voice: "航线传输中"
            },
            2: {
                title: "机巢开舱",
                hintText: "机巢开舱中...",
                voice: "机巢开舱"
            },
            3: {
                title: "脚架释放",
                hintText: "机巢脚架释放中...",
                voice: "脚架释放"
            },
            4: {
                title: "无人机自检",
                hintText: "无人机自检中...",
                voice: "RTK等待中"
            },
            5: {
                title: "无人机解锁",
                hintText: "无人机解锁中...",
                voice: "无人机解锁"
            },
            6: {
                title: "起飞",
                hintText: "无人机起飞中...",
                voice: "起飞"
            },
            7: {
                title: "机巢关舱",
                hintText: "机巢关舱中...",
                voice: "机巢关舱"
            }
        },
        landingStep1: {
            0: {
                title: "返航",
                hintText: "无人机正在返航...",
                voice: "任务执行完毕，开始返航"
            },
            1: {
                title: "开舱",
                hintText: "机巢开舱中...",
                voice: "机巢开舱"
            },
            2: {
                title: "脚架释放",
                hintText: "机巢脚架释放中...",
                voice: "脚架释放"
            },
            3: {
                title: "无人机降落",
                hintText: "无人机降落中...",
                voice: "平台升起"
            },
            4: {
                title: "无人机校正",
                hintText: "机巢脚架校正中...",
                voice: "无人机校正"
            },
            5: {
                title: "关舱",
                hintText: "机巢关舱中...",
                voice: "机巢关舱"
            }
        },
        errorList: {
            1: '备降任务加载失败',
            2: '备降任务上传失败',
            3: '备降启动失败',
            4: '复降无人机起飞失败',
            5: '复降启动失败',
            6: '复降等待降落超时',
            7: '复降机巢关舱失败',
            8: '备降机巢一键关舱失败',
            9: '复降任务加载失败',
            10: '复降任务上传失败',
            11: '复降无人机自检失败',
            12: '复降无人机RTK等待超时',
            13: '复降机巢一键开舱失败',
            14: '复降无人机解锁失败',
            15: '无人机电池电量过低',
        },
        stepError: {
            1: "位置偏差过大",
            2: "机巢一键开舱失败",
            3: "视觉识别降落失败",
        },
        alternateSteps: {
            0: {
                title: "上传备降点",
                hintText: "备降点上传中...",
                voice: '备降点上传'
            },
            1: {
                title: "备降动作执行",
                hintText: "备降动作执行中...",
                voice: '备降动作执行中'
            },
            2: {
                title: "等待复降",
                hintText: "等待复降中...",
                voice: '等待复降'
            },
            3: {
                title: "上传复降点",
                hintText: "复降点上传中...",
                voice: '上传复降点'
            },
            4: {
                title: "无人机自检",
                hintText: "无人机自检中...",
                voice: '无人机自检'

            },
            5: {
                title: "开舱",
                hintText: "机巢开舱中...",
                voice: '机巢开舱'
            },
            6: {
                title: "平台升起",
                hintText: "机巢平台升起中...",
                voice: '平台升起'
            },
            7: {
                title: "脚架释放",
                hintText: "机巢脚架释放中...",
                voice: '脚架释放'
            },
            8: {
                title: "无人机解锁",
                hintText: "无人机解锁中...",
                voice: '无人机解锁'
            },
            9: {
                title: "起飞",
                hintText: "无人机起飞中...",
                voice: '无人机起飞'
            },
            10: {
                title: "前往复降点",
                hintText: "无人机前往复降点中...",
                voice: '无人机前往复降点'
            },
            11: {
                title: "脚架校正",
                hintText: "机巢脚架校正中...",
                voice: '脚架校正'
            },
            12: {
                title: "平台下降",
                hintText: "机巢平台下降中...",
                voice: '平台下降'
            },
            13: {
                title: "关舱",
                hintText: "机巢关舱中...",
                voice: '机巢关舱'
            },
        },
        alternateSteps2: {
            0: {
                title: "上传备降点",
                hintText: "备降点上传中...",
                voice: '上传备降点'
            },
            1: {
                title: "备降动作执行",
                hintText: "备降动作执行中...",
                voice: '备降动作执行中'
            },
            2: {
                title: "等待复降",
                hintText: "等待复降中...",
                voice: '等待复降'
            },
            3: {
                title: "上传复降点",
                hintText: "复降点上传中...",
                voice: '上传复降点'
            },
            4: {
                title: "无人机自检",
                hintText: "无人机自检中...",
                voice: '无人机自检'
            },
            5: {
                title: "开舱",
                hintText: "机巢开舱中...",
                voice: '机巢开舱'
            },
            6: {
                title: "脚架释放",
                hintText: "机巢脚架释放中...",
                voice: '脚架释放'
            },
            7: {
                title: "无人机解锁",
                hintText: "无人机解锁中...",
                voice: '无人机解锁'
            },
            8: {
                title: "起飞",
                hintText: "无人机起飞中...",
                voice: '无人机起飞'
            },
            9: {
                title: "前往复降点",
                hintText: "无人机前往复降点中...",
                voice: '前往复降点'
            },
            10: {
                title: "脚架校正",
                hintText: "机巢脚架校正中...",
                voice: '脚架校正'
            },
            11: {
                title: "关舱",
                hintText: "机巢关舱中...",
                voice: '机巢关舱'
            },
        },
        alternateSteps1: {
            0: {
                title: "上传备降点",
                hintText: "备降点上传中...",
                voice: '上传备降点'
            },
            1: {
                title: "备降动作执行",
                hintText: "备降动作执行中...",
                voice: '备降动作执行中'
            },
        },
        changeLinkText: '当前正处于4G端，无法执行备降动作，是否切换至图传端',
        changeLinkTip: '提示',
        changeLinkSubmit: '确定',
    },
    leftVideoMap: {
        title: "视频地图",
        uavTitle: "飞机监控",
        cabinTitle: "舱内监控",
        outboardTitle: "舱外监控",
    },

    uavOperation: {
        title: "控制面板",
        executeTask: "执行任务",
        uav: "无人机",
        payTilt: "云台",
        center: "回中",
        vertical: "垂直",
        uavOperateList: {
            suspend: "暂停",
            keepOn: '恢复任务',
            continueHint1: "即将恢复继续执行任务，请确定",
            errorHint: '当前模式无法恢复继续执行任务',
            continue: "续飞",
            courseReversal: "返航",
            descent: "降落",
            startVideo: "开始录像",
            endVideo: "结束录像",
            photographSuccss: "拍照成功",
            beingShadowed: "正在返航",
            startTask: "开始执行任务",
            hover: "悬停",
            taskError: "请先执行任务",
            photographError: "拍照失败（流地址错误）",
            videoError: "录像失败（流地址错误）",
            executeTaskHint: "正在执行任务，请勿重复执行任务",
            startTaskHint: "请确保设备状态良好后，在安全的环境下执行任务。",
            suspendHint: "确定后无人机将执行暂停任务指令",
            continueHint: "即将继续执行任务，请确定",
            courseReversalHint: "即将执行返航操作，请确定。",
            descentHint: "无人机即将执行降落指令，请确定。",
            uavAutoFlightHint: "无人机即将执行自动飞行指令，请确定。",
            uavTakeOffHint: "无人机即将执行起飞指令，请确定。",
            uavAdviceFlightHint: "无人机即将执行指点飞行锁指令，请确定。",
            uavShuttingHint: "无人机即将执行闭锁指令，请确定。",
            uavUnlockingHint: "无人机即将执行解锁指令，请确定。",
            uavManualFlightHint: "无人机即将执行手动飞行指令，请确定。",
            uavInterruptHint: "即将执行中断指令，请确定。",
            pictureMsg: '录像视频只存于SD卡中，暂无上传到系统中，请确定。',
            uavQRcodeLandHint: '无人机即将开启二维码识别降落，请确定',
            uavcloseQRcodeLandHint: '无人机即将关闭二维码识别降落，请确定',
            openQRError: '开启二维码识别降落失败！',
            closeQRError: '关闭二维码识别降落失败！',
            openQRSuccess: '二维码识别降落已开启！',
            closeQRSuccess: '二维码识别降落已关闭！',
            autoFlight: '自动飞行',
            adviceFlight: "指点飞行",
            manualFlight: "手动飞行",
            uavTakeOff: "起飞",
            uavUnlocking: "解锁",
            atresia: "闭锁",
            interrupt: "中断",
            downRoute: "下载",
            QRcodelanding: "开启二维码识别降落",
            closeQRcodelanding: "关闭二维码识别降落",
            keydownTip: '键盘事件尚未开启(键盘对应事件请参考操作说明)，如需开启请确认。',
            tips: '提示',
            sure: '确定',
            cancel: '取消',
            performTip: "请确保机巢方位角是否设置正确",
            notExecute: '正处于4G网中，无法执行任务，请切换至图传再次执行',
            executeTip: '提示',
            executeSubmit: '确定',
            executeSubmit1: '继续执行',
            executeCancel: '重新执行',
            elevationLoading: '正在获取高程数据',
            breakPointText: '上次执行航线未完成，是否继续执行航线？',
            notChooseRoute: "未选择航线",
            crashStop: '急停',
            crashStopTip: '机巢即将执行急停指令，请确定。',
            rain_fall_msg: '检测到当前有雨，禁止飞行。',
            wind_speed_msg: '检测到当前风速过大，禁止飞行。'
        }
    },

    dataOverview: {
        distance: "距离",
        height: "高度(米)",
        horizontalVelocity: "水平速度(m/s)",
        verticalVelocity: "垂直速度(m/s)",
        cameraZoom: "变焦倍数"
    },

    // 操作说明页面
    instructions: {
        uavMap: {
            uav: {
                title: "飞机视频",
                content: "飞机实时视频回传画面"
            },
            cabin: {
                title: "舱内监控",
                content: "视频监测的方式实时监测固定机场内部的变化情况"
            },
            outboard: {
                title: "舱外监控",
                content: "视频监测的方式实时监测固定机场内部的变化情况"
            },
            map: {
                title: "地图功能区",
                content: "展示任务围栏，航线，飞机实时位置"
            }
        },
        centerData: {
            title: "飞行数据显示区",
            content: "展示飞行距离，飞行高度等实时飞行数据"
        },
        instruct: {
            title: "无人机操控区",
            content: [
                "无人机的各项操作",
                "全自动的飞机控制",
                "手动的飞机控制"
            ]
        },
        warning: {
            title: "实时信息显示区",
            content: "显示实时信息",
            type: {
                0: "红色严重警告",
                1: "黄色警告",
                2: "绿色正常数据信息"
            }
        },

        achievement: {
            title: "成果展示",
            content: [
                "显示成果",
                "照片",
                "经纬度",
                "时间"
            ]
        }

    },

    // 相机操作
    cameraConfig: {
        shut: "关闭",

        headerNav: {
            patterns: "基本设置",
            camera: "拍照",
            videos: "录像",
            more: "更多"
        },

        patterns: {
            shutter: "快门",
            exp: "曝光补偿",
            white: "白平衡",
            pseudoColor: "颜色",
            areaThermal: '热成像区域'
        },

        camera: {
            mode: "拍照模式",
            format: {
                title: "照片格式",
                front: "格式选择"
            },
            size: "照片大小"
        },

        videos: {
            previewResolution: "预览分辨率",
            previewCodeRate: "预览码率",
            codeRate: "录像分辨率",
            resolution: "录像码率",
            hz: "录像HZ"
        },

        more: {
            shut: "关闭",
            grid: "网格",
            defogging: "去雾",
            flicker: "抗闪烁",
            cardCapacity: "TF卡容量",
            noSD: "未检测到SD卡",
            formatting1: "SD卡",
            formatting: "格式化SD卡",
            capacity: "SD卡剩余容量",
            TFcapacity: 'TF卡容量：',
            return: "返回",
            gridLine: "网格线",
            centerPoint: '中心点',
            gridlinesALines: '网格线和对角线',
            control: '控制',
            close: '关闭',
            formatTip: '格式化处理将清除存储卡上所有拍摄存储的影像文件和声音文件，清除后文件无法恢复',
            submit: '确 定',
            cancel: '取 消',
            formatLoad: '格式中。。。',
            formatSuccess: "SD卡格式化成功！",
            formatError: "SD卡格式化失败！",
            del: '删除',
            complete: "完成",
            latLngList: {
                longitude: '经度：',
                latitude: '纬度：',
                altitude: '海拔高度（m）：'
            }

        },

        rtkset: {
            url: '地址',
            port: '端口',
            user: '用户名',
            password: '密码',
            endpoint: '端点',
            state: '状态',
            network: '网络',
            ntrip: 'Ntrip',
            getMountpoint: '获\xa0取\xa0挂\xa0载\xa0点',
            set: '设\xa0置',
            inLine: '在线',
            outLine: '离线',
            nodata: '无',
            ntrip_state: [
                "初始状态",
                "连接中",
                "已连接",
                "连接失败",
                "认证开始",
                "认证成功",
                "认证失败",
                "账户未绑定",
                "账户密码错误",
                "账户未激活",
                "账户过期",
                "网络不可用",
            ],
            urlEmpty: '请输入地址',
            portEmpty: '请输入端口',
            getMountpointError: '获取挂载点失败，请检查地址和端口是否正确'
        },

        moreInside: {
            formatting: {
                title: "格式化SD卡",
                content: " 格式化处理将清除存储卡上所有拍摄存储的影像文件和声音文件，清除后文件无法恢复",
                affirm: "确 认",
                cancel: "取 消"
            }
        }
    },
    //天气模块
    weatherModule: {
        title: "天气模块",
        title1: "气象站信息",
        title2: "网络天气模块",
        // weatherLabel: ["空气温度(℃)", "空气湿度(%)", "风速(m/s)", "风力", "风向(°,[0-360])", "瞬时雨量(mm)"],
        weatherLabel: ["舱内温度(℃)", "舱外温度(℃)", "风速(m/s)", "风力", "雨量(mm)", "湿度(%)"],
        weatherLabel1: ["温度(℃)", "湿度(%)", "风力", "风向", "天气"]
    },
    link: {
        auto: "自动",
        fpv: '机巢端',
        uav: '飞机端'
    },
    errorMsg: {
        0: "成功",
        1: "一键流程步骤错误",
        2: "机巢方位角未设置或设置值无效",
        3: "机巢放置点未设置或设置值无效",
        4: "机巢备降点未设置或设置值无效",
        5: "上传到无人机航线数据解析失败",
        6: "流程启动失败"
    },
    errorInfo: {
        openOperate: '开启键盘操作',
        closeOperate: '关闭键盘操作',
        openMsg: '已成功开启键盘操作',
        closeMsg: '已关闭键盘操作'
    },
    batteryBox: {
        title: "电池信息",
        batteryInfo: {
            voltage: '电压',
            current: '电流',
            battery_remaining: '电量',
            battery_temperature: '温度',
        },
        tip: '低电量警告',
        content: '当前电量较低([num%])，请及时充电！',
        submit: '确定'
    },
    shout: {
        checkError: "浏览器不支持实时喊话（Opus）功能",
        realTimeShout: '实时喊话',
        timbre: '音色',
        timbrePlaceholder: '请选择音色',
        speech_speed: '音速',
        broadcast: "录制广播",
        textToAudio: '文本转语音',
        textPlaceholder: '请输入文本',
        clear: '清除',
        save: '保存',
        add: '新增',
        operateList: {
            1: '录制',
            2: '文本转语音',
            3: '录音文件'
        },
        timbreList: [
            { id: 0x01, name: "女声" },
            { id: 0x02, name: "男声" },
            { id: 0x11, name: "英文女声" },
            { id: 0x12, name: "英文男声" },
            { id: 0x31, name: "东北话" },
            { id: 0x32, name: "四川话" },
            { id: 0x33, name: "河南话" },
            { id: 0x34, name: "湖南话" },
            { id: 0x35, name: "陕西话" },
            { id: 0x36, name: "广东话" },
            { id: 0x41, name: "俄语" },
            { id: 0x42, name: "法语" },
            { id: 0x43, name: "德语" },
            { id: 0x44, name: "韩语" },
            { id: 0x45, name: "意大利语" },
            { id: 0x46, name: "波兰语" },
            { id: 0x47, name: "西班牙语" },
            { id: 0x48, name: "葡萄牙语" },
        ],
        close: '关',
        ledMessage: {
            led_mo1: '爆闪',
            led_mo2: '照明',
            led_psm0: ' 交替快闪',
            led_psm1: '交替满闪',
            led_psm2: '交替齐闪'
        }
    }

}

export default navigation;