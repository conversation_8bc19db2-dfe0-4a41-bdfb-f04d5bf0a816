.group {
  .coordination {
    background-color: #040404;

    .searchDiv {
      .tip {
        color: #ffffff;
      }

      .el-button {
        background-color: white;
        color: #0b58de;
        border: none;

        &.active {
          background-color: #0b58de;
          color: white;
        }
      }

      .backBtn {
        color: white;
        background-color: transparent;
        border: none;
      }

      .el-input {
        .el-input__inner {
          background-color: transparent !important;
          color: white !important;
        }
      }
    }

    .custom-table {
      .el-tag {
        color: white;
        border: none;
      }

      .firstDiv {
        background-color: rgb(51, 233, 51);
      }

      .secondDiv {
        background-color: rgb(53, 53, 204);
      }

      .noneDiv {
        background-color: #909399;
      }

      .el-button {
        background-color: transparent;
        border: none;
        color: white;

        &.active {
          color: #0b58de;
        }
      }

      .el-checkbox__inner {
        background-color: transparent !important;
      }

      .el-checkbox__input.is-focus .el-checkbox__inner {
        border-color: #dcdfe6 !important;
      }

      .el-checkbox__input.is-checked .el-checkbox__inner,
      .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: transparent !important;
        border: 2px solid #0b58de !important;
      }

      .el-checkbox__inner::after {
        border-color: #0b58de !important;
      }
    }

    input::-webkit-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-moz-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-ms-input-placeholder {
      color: #5e5e5e !important;
    }
  }

  .networkingItem {
    background-color: #040404;

    .el-container {
      .el-aside {
        background-color: rgba(47, 47, 57, 0.9);

        .header {
          color: white;
        }

        .backBtn {
          background-color: transparent;
          border: none;
          color: white;
        }

        .title {
          background-color: rgba(8, 8, 55, 0.5);
          color: white;

          .title-2 {
            color: #9f9898;
          }

          .title-3 {
            .el-button {
              color: #0b58de;
              background-color: white;
            }

          }
        }

        .deviceListDiv {
          color: white;

          .el-row {
            background-color: rgba(4, 4, 4, 0.5);

            &.active {
              border: 2px solid rgb(4, 4, 119);
            }

            .fontColor {
              color: #c7ff07;
            }

            .fontColor-1 {
              color: #1dae22;
            }

            .fontColor-2 {
              color: #9a2323;
            }
          }
        }

        // scrollbar-width: thin !important;
        // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
        // -ms-overflow-style: none !important;
        // scrollbar-color: #777777 #ccc;

        &::-webkit-scrollbar {
          width: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: #ccc;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #777777;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
      }

      .el-main {
        .main-header {
          .el-button {
            background-color: transparent !important;
            border: none;
            color: #acbad2;

            .el-row {
              .el-col {
                .item-1 {
                  background-color: #acbad2;

                }
              }
            }

            &.active {
              color: #ffffff;

              .el-row {
                .el-col {
                  .item-1 {
                    background-color: #ffffff;
                  }
                }
              }
            }
          }
        }

        .main-content {
          .mian-content-item {
            .webrtcVideo {
              &.active1 {
                border: 2px solid #11459e;
              }
            }

            .item-title {
              background-color: rgba(36, 32, 32, 0.78);
            }

            .item-button {
              .el-button {
                background-color: rgba(1, 1, 1, 0.7);
                color: #f8faff;
                border: none;

                &.active {
                  background-color: rgba(11, 88, 222, 0.7);
                }
              }
            }

            .webrtcVideo {
              background-color: rgba(61, 67, 72, 1);

              .item-title {
                background-color: rgba(36, 32, 32, 0.78);
                color: white;
              }

              .item-button {
                .el-button {
                  background-color: rgba(1, 1, 1, 0.7);
                  color: #f8faff;
                  border: none;

                  &.active {
                    background-color: rgba(11, 88, 222, 0.7);
                  }
                }
              }

              .videoTip {
                color: rgb(185, 55, 55);
              }

              .item-footer {
                background-color: rgba(83, 83, 83, 0.78);
                color: white;
              }

            }
          }
        }

        .main-foot {
          .el-pagination {

            .btn-next,
            .btn-prev {
              border: none !important;
              background: rgba(0, 0, 0, 0) !important;
              color: #fff !important;
            }
          }

        }
      }

    }
  }

  .editWork {
    .deviceListDiv {
      background-color: rgba(47, 47, 57, 0.94);

      .el-container {
        .backBtn {
          background-color: transparent;
          border: none;
        }

        .el-main {
          .el-row {
            background-color: rgba(4, 4, 4, 0.5);
            color: white;

            &.active {
              border: 1px solid #0b58de;
            }

            .content {
              .content-title {
                background-color: rgba(112, 112, 112, 0.14);
              }

              .isbind {
                color: #9499a2;

                .isbind-item-1 {
                  background-color: #9499a2;

                  &.active {
                    background-color: #1dae22;
                  }
                }

                .isbind-item-2 {
                  &.active {
                    color: #1dae22;
                  }
                }
              }
            }
          }

          // scrollbar-width: thin !important;
          // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
          // -ms-overflow-style: none !important;
          // scrollbar-color: #777777 #ccc;

          &::-webkit-scrollbar {
            width: 3px;
          }

          &::-webkit-scrollbar-track {
            background-color: #ccc;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #777777;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }
        }
      }
    }

    .taskList {
      background-color: rgba(53, 57, 68, 0.94);

      .el-container {
        .el-header {
          color: white;
        }

        .el-button {
          color: white;
          background-color: transparent;
          border: none;
        }

        .el-main {
          .main-content {
            background-color: rgba(4, 4, 4, 0.5);
            color: white;

            &.active {
              border: 1px solid #0b58de;
            }

            .content-1 {
              .content-item-1 {
                .content-item-1-1 {
                  color: #afa6a6;
                }
              }
            }

            .content-2 {
              background-color: rgba(48, 48, 106, 0.9);
            }
          }

          .main-content-entry {
            background-color: rgba(4, 4, 4, 0.5);
            color: white;
          }

          .main-content-1 {
            color: white;
          }

          // scrollbar-width: thin !important;
          // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
          // -ms-overflow-style: none !important;
          // scrollbar-color: #777777 #ccc;

          &::-webkit-scrollbar {
            width: 3px;
          }

          &::-webkit-scrollbar-track {
            background-color: #ccc;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #777777;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }

        }
      }

      .renameDialog {
        .dividerDiv {
          background-color: #0b58de;
        }

        .savebtn {
          background-color: #0b58de;
          color: white;
        }

        .closeBtn {
          background-color: white;
          color: #0b58de;
        }
      }
    }

    #map {
      .amap-markers {
        .amap-marker {
          .marker-edit {
            background-color: #ffffff;
            border: 3px solid #0092f8;
            color: #0092f8;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);

            &.active {
              background-color: #0c1865;
              color: #ffffff;
            }
          }

          .marker-edit-i {
            background-color: #ffffff;
            border: 2px solid #0728fc;
            color: #0728fc;
          }

          .renderMarker {
            border: 1px solid #c84141;

            span {

              background-color: #c84141;
              color: white;
            }
          }

          .startend-marker {

            background-color: #ffffff;

            border: 1px solid #0728fc;
            color: #0015a1;

          }
        }
      }

      .amap-info {

        .amap-info-content {


          .divider {

            border-bottom: 1px solid #c84141;
          }

          .info-item-content {

            border-bottom: 1px solid rgb(231, 231, 231);

          }
        }
      }
    }
  }
}
