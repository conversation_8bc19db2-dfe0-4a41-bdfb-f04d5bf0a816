import {
    renderPolyline
} from './orthophoto'
import {
    gcj02_to_wgs84,
    wgs84_to_gcj02
} from "@/utils/wgs84_to_gcj02";
let map = ''
export function orthoPhotoComputer(orthoForm, amap, leafletMap, code) {
    map = amap
    let camera_json = ''
    if (!code) {
        camera_json = JSON.parse(orthoForm.camera_json)
    } else {
        camera_json = {
            cameraParamList: orthoForm.cameraParamList,
            course: orthoForm.course,
            lateral: orthoForm.lateral,
            wheelDist: orthoForm.wheelDist,
            angle: orthoForm.angle
        }
    }
    let params = {
        sensorH: 0,
        sensorW: 0,
        focalLength: 0,
        flightH: code ? orthoForm.default_height : (orthoForm.default_height / 100),
        courseOverlap: camera_json.course / 100,
        lateralOverlap: camera_json.lateral / 100
    }
    for (let index = 0; index < camera_json.cameraParamList.length; index++) {
        if (camera_json.cameraParamList[index].id == "sensor") {
            params.sensorH = camera_json.cameraParamList[index].height
            params.sensorW = camera_json.cameraParamList[index].width
        }
        if (camera_json.cameraParamList[index].id == "focalLength") {
            params.focalLength = camera_json.cameraParamList[index].height
        }
    }
    let {
        triggerDist,
        airLineDist
    } = cameraParamComputer(params)
    let paths = []
    if (code) {
        paths = orthoForm.paths
    } else {
        for (let index = 0; index < orthoForm.point_list.length; index++) {
            if (orthoForm.point_list[index].seq == index + 1) {
                let point = {
                    lng: orthoForm.point_list[index].lon_int / 1e7,
                    lat: orthoForm.point_list[index].lat_int / 1e7,
                    height: orthoForm.point_list[index].height / 100,
                    id: orthoForm.point_list[index].seq
                }
                paths.push(point)
            }
        }
    }
    let points = renderPolyline({
        paths: paths,
        stepRotate: camera_json.angle,
        spaceInp: airLineDist,
        amap: leafletMap
    })
    if (points.length > 0) {
        let angle = 0
        angle = calcAngle([points[0], points[1]])
        if (angle < 0) {
            angle = 360 + angle
        }
        for (let index = 0; index < points.length; index++) {
            if (index == 0) {
                angle = angle - 180
            } else if (index % 2 !== 0) {
                angle = angle + 180
            }
            let param = {
                lng: points[index].lng,
                lat: points[index].lat,
                dist: camera_json.wheelDist,
                brng: angle
            }
            points[index] = wheelDistComputer(param)
        }
    }
    return {
        points,
        triggerDist
    }


}
//相机参数计算
function cameraParamComputer(param) {
    let {
        sensorH,
        sensorW,
        focalLength,
        flightH,
        courseOverlap,
        lateralOverlap
    } = param
    let triggerDist = sensorH * flightH * (1 - courseOverlap) / focalLength //相机触发距离
    let airLineDist = sensorW * flightH * (1 - lateralOverlap) / focalLength //相邻航线距离
    return {
        triggerDist,
        airLineDist
    }
}
var mapNumberUtil = {
        rad: function rad(d) {
            return d * Math.PI / 180.0;
        },
        deg: function deg(d) {
            return d * 180 / Math.PI
        }
    }
    //转弯距离计算
function wheelDistComputer(param) {
    let {
        lng,
        lat,
        dist,
        brng
    } = param
    // 长半径a=6378137 短半径b=6356752.3142 扁率f=1/298.2572236
    var a = 6378137;
    var b = 6356752.3142;
    var f = 1 / 298.257223563;

    var lon1 = lng * 1;
    var lat1 = lat * 1;
    var s = dist;
    var alpha1 = mapNumberUtil.rad(brng);
    var sinAlpha1 = Math.sin(alpha1);
    var cosAlpha1 = Math.cos(alpha1);

    var tanU1 = (1 - f) * Math.tan(mapNumberUtil.rad(lat1));
    var cosU1 = 1 / Math.sqrt((1 + tanU1 * tanU1)),
        sinU1 = tanU1 * cosU1;
    var sigma1 = Math.atan2(tanU1, cosAlpha1);
    var sinAlpha = cosU1 * sinAlpha1;
    var cosSqAlpha = 1 - sinAlpha * sinAlpha;
    var uSq = cosSqAlpha * (a * a - b * b) / (b * b);
    var A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
    var B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));

    var sigma = s / (b * A),
        sigmaP = 2 * Math.PI;
    while (Math.abs(sigma - sigmaP) > 1e-12) {
        var cos2SigmaM = Math.cos(2 * sigma1 + sigma);
        var sinSigma = Math.sin(sigma);
        var cosSigma = Math.cos(sigma);
        var deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
            B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));
        sigmaP = sigma;
        sigma = s / (b * A) + deltaSigma;
    }

    var tmp = sinU1 * sinSigma - cosU1 * cosSigma * cosAlpha1;
    var lat2 = Math.atan2(sinU1 * cosSigma + cosU1 * sinSigma * cosAlpha1,
        (1 - f) * Math.sqrt(sinAlpha * sinAlpha + tmp * tmp));
    var lambda = Math.atan2(sinSigma * sinAlpha1, cosU1 * cosSigma - sinU1 * sinSigma * cosAlpha1);
    var C = f / 16 * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
    var L = lambda - (1 - C) * f * sinAlpha *
        (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));

    var revAz = Math.atan2(sinAlpha, -tmp); // final bearing
    var lngLatObj = {
        lat: mapNumberUtil.deg(lat2),
        lng: lon1 + mapNumberUtil.deg(L)
    }
    return lngLatObj;

}
//计算方位角
function calcAngle(points) {
    let start_1 = Cesium.Cartesian3.fromDegrees(points[0].lng, points[0].lat, points[0].height)
    let end_1 = Cesium.Cartesian3.fromDegrees(points[1].lng, points[1].lat, points[1].height)
    var p_start = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
            map.scene,
            start_1
        ),
        p_end = Cesium.SceneTransforms.wgs84ToWindowCoordinates(
            map.scene,
            end_1
        );
    var diff_x = p_end.x - p_start.x,
        diff_y = p_end.y - p_start.y;
    let a = (360 * Math.atan2(diff_y, diff_x)) / (2 * Math.PI) + 90;
    if (a > 180) {
        a = -(360 - a);
    }
    return a;
}