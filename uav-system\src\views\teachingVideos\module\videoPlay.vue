<!-- 添加视频 -->
<template>
    <el-dialog width="60%" :visible.sync="isShow" :title="title" :destroy-on-close="true">
      <video
        class="video"
        style="width: 100%;"
        controls
        v-if="videoShow"
      >
        <source :src="videoUrl" type="video/mp4" />
      </video>
    </el-dialog>
  </template>
  
  <script>
  export default {
    data() {
      return {
        isShow: false,
        title: "",
        videoUrl: "",
        videoShow: true,
      };
    },
    methods: {
      open: function (item) {
        this.isShow = true;
        this.title = item.title;
        this.videoUrl = item.source_url;
        this.videoShow = false;
        setTimeout(()=>{
          this.videoShow = true;
        }, 10)
      },
    },
  };
  </script>
  
  <style lang="less">
  </style>