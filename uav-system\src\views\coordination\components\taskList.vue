<template>
  <div class="taskList">
    <el-container>
      <el-header> {{ groupLanguage.deviceList.bind }} </el-header>
      <el-button
        icon="el-icon-arrow-left"
        @click="closeEventList"
        class="backBtn"
      ></el-button>
      <el-main>
        <div
          class="main-content"
          v-for="item in routeListBind"
          :key="item.m_id"
          :class="routId == item.m_id ? 'active' : ''"
          @click="changeRoute(item)"
        >
          <div class="content-1">
            <div class="content-item-1">
              <div>{{ item.title }}</div>
              <div class="content-item-1-1">
                {{ groupLanguage.deviceList.type
                }}{{ item.type | getTypeTitle(typeList) }}
              </div>
            </div>
            <div class="content-item-2">
              <el-popover
                placement="right"
                trigger="hover"
                :open-delay="200"
                popper-class="popover-item-class"
                @show="showbtnR(item)"
                @hide="clickSetCode = ''"
              >
                <el-button
                  type="text"
                  class="btn1"
                  @click="recoverName(item)"
                  >{{ groupLanguage.deviceList.rename }}</el-button
                >
                <el-button type="text" @click="delRoute(item)">{{
                  groupLanguage.deviceList.del
                }}</el-button>
                <el-button slot="reference">
                  <el-image
                    :src="clickSetCode == item.m_id ? setImg_1 : setImg"
                    fit="contain"
                  ></el-image
                ></el-button>
              </el-popover>
            </div>
          </div>
          <div class="content-2">
            <el-button @click="unBind(item)">
              <el-image :src="planImg"></el-image>
              {{ groupLanguage.deviceList.unbind }}
            </el-button>
            <el-button class="fitBtn" @click="edit(item)">
              <el-image :src="fitImg"></el-image>
              {{ groupLanguage.deviceList.edit }}
            </el-button>
          </div>
        </div>
        <div class="main-content-entry" v-if="routeListBind.length < 1">
          {{ groupLanguage.deviceList.noData }}
        </div>
        <div class="main-content-1">
          {{ groupLanguage.deviceList.waitBind }}
        </div>
        <route-type
          ref="routeType"
          :typeCode="typeCode"
          @clickType="clickType"
        ></route-type>
        <div
          class="main-content"
          v-for="item in routeList"
          :key="item.m_id"
          :class="routId == item.m_id ? 'active' : ''"
          @click="changeRoute(item)"
        >
          <div class="content-1">
            <div class="content-item-1">
              <div>{{ item.title }}</div>
              <div class="content-item-1-1">
                {{ groupLanguage.deviceList.type
                }}{{ item.type | getTypeTitle(typeList) }}
              </div>
            </div>
            <div class="content-item-2">
              <el-popover
                placement="right"
                trigger="hover"
                :open-delay="200"
                popper-class="popover-item-class"
                @show="showbtnR(item)"
                @hide="clickSetCode = ''"
              >
                <el-button
                  type="text"
                  class="btn1"
                  @click="recoverName(item)"
                  >{{ groupLanguage.deviceList.rename }}</el-button
                >
                <el-button type="text" @click="delRoute(item)">{{
                  groupLanguage.deviceList.del
                }}</el-button>
                <el-button slot="reference">
                  <el-image
                    :src="clickSetCode == item.m_id ? setImg_1 : setImg"
                    fit="contain"
                  ></el-image
                ></el-button>
              </el-popover>
            </div>
          </div>
          <div class="content-2">
            <el-button @click="bindplan(item)">
              <el-image :src="planImg"></el-image>
              {{ groupLanguage.deviceList.binds }}
            </el-button>
            <el-button class="fitBtn" @click.stop="edit(item)">
              <el-image :src="fitImg"></el-image>
              {{ groupLanguage.deviceList.edit }}
            </el-button>
          </div>
        </div>
        <div class="main-content-entry" v-if="routeList.length < 1">
          {{ groupLanguage.deviceList.nodata1 }}
        </div>
      </el-main>
    </el-container>
    <el-dialog
      :title="groupLanguage.deviceList.taskRename"
      :visible.sync="renameCode"
      :close-on-click-modal="false"
      :show-close="false"
      width="35%"
      center
      class="renameDialog"
    >
      <el-divider class="dividerDiv"></el-divider>
      <el-form
        :model="planFrom"
        ref="planFrom"
        label-position="right"
        label-width="20%"
      >
        <el-form-item
          :label="groupLanguage.deviceList.taskName"
          prop="name"
          :rules="[
            {
              required: true,
              message: groupLanguage.deviceList.nameMessage,
              trigger: 'blur',
            },
          ]"
        >
          <el-input v-model="planFrom.name" style="width: 80%; float: left">
          </el-input>
        </el-form-item>
      </el-form>
      <div>
        <el-button class="savebtn" @click="submitRename">{{
          groupLanguage.deviceList.save
        }}</el-button>
        <el-button class="closeBtn" @click="closeEvent">{{
          groupLanguage.deviceList.cancel
        }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "../../../utils/api";
import routeType from "../../../components/routeType/index";
export default {
  name: "taskList",
  props: {
    fenceList: {
      type: [String, Array],
      default() {
        return {};
      },
    },
    listCode: {
      type: Boolean,
      default: false,
    },
    deviceItem: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
    editCode: {
      type: Number,
      default: 0,
    },
    checkType: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  components: {
    routeType,
  },
  data() {
    return {
      typeCode: "",
      routeList: [],
      routeListBind: [],
      routId: "",
      clickSetCode: "",
      renameCode: false,
      planFrom: {
        name: "",
      },
      planItem: "",
      setImg: require("../../../assets/img/routeplan/set.png"),
      setImg_1: require("../../../assets/img/routeplan/set_1.png"),
      fitImg: require("../../../assets/img/routeplan/fit.png"),
      fitImg_1: require("../../../assets/img/routeplan/fit_1.png"),
      planImg: require("../../../assets/img/routeplan/plan.png"),
      planImg_1: require("../../../assets/img/routeplan/plan_1.png"),
    };
  },
  mounted() {
    // this.getType();
    // console.log(this.typeList)
  },
  methods: {
    //点击类型
    async clickType(item) {
      if (this.typeCode != item.value) {
        this.typeCode = item.value;
        await this.getRouteList();
        this.routId = "";
        this.$emit("clickType", item);
        return false;
      }
      if (!this.checkType.name_cn) {
        this.$emit("clickType", item);
      }
    },
    //获取航线信息
    async getRouteList() {
      let a = this.$store.state.route.routeItem;
      this.routeList = [];
      this.routeListBind = [];
      for (let index = 0; index < this.fenceList.length; index++) {
        let data = {
          f_id: this.fenceList[index].f_id,
          type: this.typeCode,
          page: 0,
          size: 100,
        };
        data.pmd = data.page.toString() + data.f_id + data.type.toString();
        await requestHttp("missionList", data).then((res) => {
          if (res.data.list) {
            for (let i = 0; i < res.data.list.length; i++) {
              let routePoints = [];
              for (let j = 0; j < res.data.list[i].point_list.length; j++) {
                if (res.data.list[i].point_list[j].seq == j + 1) {
                  let point = new AMap.LngLat(
                    res.data.list[i].point_list[j].lon_int / 1e7,
                    res.data.list[i].point_list[j].lat_int / 1e7
                  );
                  routePoints.push(point);
                }
              }
              res.data.list[i].paths = routePoints;
              this.routeList.push(res.data.list[i]);
              if (a) {
                if (a.m_id == this.routeList[i].m_id) {
                  this.changeRoute(this.routeList[i]);
                }
              }
            }
          }
        });
      }
      if (this.deviceItem.routeList && this.deviceItem.routeList.length > 0) {
        for (let j = 0; j < this.deviceItem.routeList.length; j++) {
          let i = this.routeList.findIndex((x) => {
            return x.m_id == this.deviceItem.routeList[j].m_id;
          });
          if (i !== -1) {
            this.routeListBind.push(this.routeList[i]);
            this.routeList.splice(i, 1);
          }
        }
      }
    },
    //关闭事件
    closeEventList() {
      this.$emit("update:listCode", false);
    },
    //点击切换航线
    changeRoute(item) {
      this.routId = item.m_id;
      this.$emit("changeRoute", item);
    },
    //切换按钮颜色
    showbtnR(item) {
      setTimeout(() => {
        this.clickSetCode = item.m_id;
      }, 100);
    },
    //重命名
    recoverName(item) {
      this.planFrom.name = item.title;
      this.planItem = item;
      this.renameCode = true;
    },
    //点击绑定任务
    bindplan(item) {
      let i = this.routeList.findIndex((x) => {
        return x.m_id == item.m_id;
      });
      this.routeListBind.push(this.routeList[i]);
      this.routeList.splice(i, 1);
      this.deviceItem.routeList = this.routeListBind;
      this.$emit("deviceChange", this.deviceItem);
    },
    //点击解除绑定
    unBind(item) {
      this.$confirm(
        this.groupLanguage.deviceList.bindtip,
        this.groupLanguage.deviceList.tips,
        {
          confirmButtonText: this.groupLanguage.deviceList.sure,
          cancelButtonText: this.groupLanguage.deviceList.cancel1,
          type: "warning",
        }
      )
        .then(() => {
          let i = this.routeListBind.findIndex((x) => {
            return x.m_id == item.m_id;
          });
          this.routeList.push(this.routeListBind[i]);
          this.routeListBind.splice(i, 1);
          this.deviceItem.routeList = this.routeListBind;
          this.$message.success(this.groupLanguage.deviceList.messageInfo);
          this.$emit("deviceChange", this.deviceItem);
        })
        .catch(() => {
          this.$message.info(this.groupLanguage.deviceList.messageInfo1);
        });
    },
    //删除任务
    delRoute(item) {
      this.$confirm(
        this.groupLanguage.deviceList.delMessage,
        this.groupLanguage.deviceList.tips,
        {
          confirmButtonText: this.groupLanguage.deviceList.sure,
          cancelButtonText: this.groupLanguage.deviceList.cancel1,
          type: "warning",
          customClass: "messageTip",
        }
      )
        .then(() => {
          let data = {
            m_id: item.m_id,
            f_id: item.f_id,
            state: 30,
            type: item.type,
            title: item.title,
            auto_speed: item.auto_speed,
            max_speed: item.max_speed,
            default_height: item.default_height,
            return_height: item.return_height,
            action_completed: item.action_completed,
            point_json: JSON.stringify(item.point_list),
          };
          data.pmd = data.m_id.toString() + data.state.toString();
          requestHttp("missionEdit", data).then((res) => {
            this.$message.success(this.groupLanguage.deviceList.delSuccess);
            this.getRouteList();
            if (this.routId == item.m_id) {
              this.$emit("changeRoute", 0);
            }
          });
        })
        .catch(() => {
          this.$message.info(this.groupLanguage.deviceList.delInfo);
        });
    },
    //提交重命名
    submitRename() {
      let data = {
        m_id: this.planItem.m_id,
        f_id: this.planItem.f_id,
        state: this.planItem.state,
        type: this.planItem.type,
        title: this.planFrom.name,
        auto_speed: this.planItem.auto_speed,
        max_speed: this.planItem.max_speed,
        default_height: this.planItem.default_height,
        return_height: this.planItem.return_height,
        action_completed: this.planItem.action_completed,
      };
      for (let index = 0; index < this.planItem.point_list.length; index++) {
        this.planItem.point_list[index].state = 10;
      }
      data.point_json = JSON.stringify(this.planItem.point_list);
      data.pmd =
        data.f_id.toString() +
        data.title +
        data.point_json +
        data.type.toString() +
        data.max_speed.toString() +
        data.default_height.toString() +
        data.return_height.toString() +
        data.action_completed.toString() +
        data.m_id.toString() +
        data.state.toString();
      requestHttp("missionEdit", data).then((res) => {
        this.$message.success(this.groupLanguage.deviceList.renameSuccess);
        this.renameCode = false;
        this.planFrom.name = "";
        this.planItem = "";
        this.getRouteList();
      });
    },
    //取消重命名
    closeEvent() {
      this.renameCode = false;
      this.planFrom.name = "";
      this.planItem = "";
      this.$message.info(this.groupLanguage.deviceList.renameInfo);
    },
    //编辑航线
    edit(item) {
      this.$store.commit("routeItem", item);
      let code = 0;
      if (item.type == 20) {
        code = 1;
      } else if (item.type == 50) {
        code = 3;
      }
      this.$emit("update:editCode", code);
      this.$emit("changeRoute", item);
    },
  },
  computed: {
    typeList() {
      return this.$store.state.route.typeList;
    },
    groupLanguage() {
      return this.$languagePackage.coordination;
    },
  },
};
</script>
<style lang="less" scoped>
.taskList {
  .el-container {
    width: 100%;
    height: 100%;
    .el-header {
      height: 6% !important;
      padding-top: 6%;
      text-align: center;
      font-size: 16px;
      letter-spacing: 4px;
    }
    .el-button {
      padding: 0;
    }
    .backBtn {
      font-size: large;
      position: absolute;
      top: 2.5%;
      left: 2%;
    }
    .el-main {
      padding: 2%;
      padding-top: 4%;
      overflow-y: auto;
      overflow-x: hidden;
      .main-content {
        width: 96%;
        padding: 2%;
        padding-top: 5%;
        border-radius: 4px;
        margin-bottom: 2%;
        .content-1 {
          width: 100%;
          display: flex;
          letter-spacing: 2px;
          .content-item-1 {
            width: 90%;
            font-size: 14px;
            .content-item-1-1 {
              font-size: 12px;
              margin: 2% 0;
            }
          }
          .content-item-2 {
            width: 10%;
          }
        }
        .content-2 {
          width: 94%;
          border-radius: 4px;
          padding: 1% 3%;
          margin-top: 2%;
          margin-bottom: 2%;
          .fitBtn {
            margin-left: 45%;
          }
          .el-image {
            float: left;
            margin-right: 10%;
          }
        }
      }
      .main-content-entry {
        width: 96%;
        padding: 3% 2%;
        border-radius: 4px;
        text-align: center;
        letter-spacing: 2px;
      }
      .main-content-1 {
        text-align: center;
        font-size: 16px;
        margin-top: 10%;
        margin-bottom: 10%;
        letter-spacing: 4px;
      }
      .routeType {
        width: 100%;
        margin-top: 2%;
        margin-bottom: 6%;
        .el-button {
          .typeTitle {
            transform: scale(0.8) !important;
          }
        }
      }
    }
  }
  .renameDialog {
    .dividerDiv {
      margin: 5px 0;
      height: 2px;
    }
    .el-form {
      margin-top: 50px;
    }
    .savebtn {
      border-radius: 8px;
      width: 130px;
    }
    .closeBtn {
      border-radius: 8px;
      width: 130px;
    }
  }
}
</style>
<style lang="less">
.taskList {
  .el-container {
    i[class*=" el-icon-"],
    i[class^="el-icon-"] {
      font-weight: 1000 !important;
    }
    .el-main {
      .routeType {
        .el-button {
          .typeTitle {
            transform: scale(0.8) !important;
          }
        }
      }
    }
  }
  .renameDialog {
    .el-dialog {
      border-radius: 6px !important;
    }
    .el-dialog__header {
      padding: 5px !important;
      text-align: left !important;
    }
    .el-dialog__body {
      padding-top: 0 !important;
      padding-left: 5px !important;
      padding-right: 5px !important;
      text-align: center !important;
    }
  }
}
.popover-item-class {
  &.el-popover {
    width: auto !important;
    min-width: 0 !important;
    padding: 0 !important;
    margin-left: 26px !important;
  }
  .popper__arrow,
  .popper__arrow::after {
    display: none;
  }
  .el-button {
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    padding: 6px 12px !important;
    margin: 0 !important;
    width: 100%;
    &.btn1 {
      margin-bottom: 5px !important;
    }
  }
}
</style>