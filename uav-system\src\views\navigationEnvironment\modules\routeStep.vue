<template>
  <div class="routeStep">
    <el-dialog
      :title="singleLanguage.dialogContent.titleUpload"
      :visible.sync="dialogCode"
      width="40%"
      center
      :close-on-click-modal="false"
      :modal="false"
      :show-close="false"
      :close-on-press-escape="false"
      custom-class="route-step-dialog"
    >
      <el-progress
        :text-inside="true"
        :stroke-width="26"
        :percentage="percent"
        status="success"
      ></el-progress>
      <div
        v-if="successCode"
        style="text-align: center; margin-top: 10px; color: #33e933"
      >
        {{ singleLanguage.dialogContent.uploadSuccess }}
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dialogCode: false,
      percent: 20,
      waypoint_percent: 0,
      waypoint_status: 0,
      successCode: false,
      waitClose: "",
    };
  },
  computed: {
    singleLanguage() {
      return this.$languagePackage.singleUav;
    },
  },
  // watch: {
  //   waypoint_percent: function (val) {
  //     if (this.waypoint_status == 3 && val == 100) {
  //       this.percent = val;
  //       this.successCode = true;
  //       setTimeout(() => {
  //         this.successCode = false;
  //         this.dialogCode = false;
  //       }, 1500);
  //     }
  //     if (val != 100) {
  //       this.percent = val;
  //     }
  //   },
  // },
  methods: {
    open() {
      this.percent = 0;
      this.dialogCode = true;
    },
    disposeData(msg_id, data) {
      if (msg_id == 434) {
        this.waypoint_percent = data.waypoint_percent;
        this.waypoint_status = data.waypoint_status;
        if (this.dialogCode) {
          if (this.waypoint_status == 3 && this.waypoint_percent == 100) {
            this.percent = this.waypoint_percent;
            this.successCode = true;
            if (!this.waitClose) {
              this.waitClose = setTimeout(() => {
                this.successCode = false;
                this.dialogCode = false;
                clearTimeout(this.waitClose);
                this.waitClose = "";
              }, 1500);
            }
          }
          if (this.waypoint_percent != 100) {
            this.percent = this.waypoint_percent;
          }
        }
      }
    },
  },
};
</script>
<style lang="less">
.routeStep {
  .route-step-dialog {
    .el-dialog__header,
    .el-dialog__body {
      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
    }
    .el-dialog__title {
      color: #fff;
    }
  }
}
</style>