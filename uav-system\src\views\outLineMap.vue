<template>
  <div class="outLineMap">
    <div id="mapContainer"></div>
  </div>
</template>
<script>
import requestHttp from "../utils/api";
import initMaps from "../utils/maps";
export default {
  name: "outLineMap",
  data() {
    return {
      map: "",
      deviceList: "",
      deviceMarker: [],
      fenceList: "",
      polyponList: "",
      missionList: [],
    };
  },
  async mounted() {
    this.initMap();
    this.getDeviceData();
    await this.getFenceList();
    this.getMissionList();
  },
  methods: {
    //初始化地图
    initMap() {
      let titleLayer = new AMap.TileLayer({
        getTileUrl: function (x, y, z) {
          
          return `http://************:8160/tile/amap/vec_w/${z}/${x}/${y}.png`;
        },
        zIndex: 10,
      });
      this.map = new AMap.Map("mapContainer", {
        center: [113.400322, 22.888973],
        zoom: 18,
        zooms: [3, 18],
        layers: [titleLayer],
        dragEnable: false,
      });
      this.map.on("zoomchange", this.zoomEvent);
    },
    //缩放事件
    zoomEvent(e) {
      this.map.setCenter([113.400322, 22.888973]);
    },
    //获取设备列表数据
    async getDeviceData() {
      let data = {
        page: 0,
        size: 100,
        type: 10,
      };
      data.pmd = data.page.toString() + data.type.toString();
      await requestHttp("deviceList", data).then((res) => {
        this.deviceList = res.data.list?res.data.list:[];
      });
      this.drawDevicePoint();
    },
    //绘画设备坐标
    drawDevicePoint() {
      for (let index = 0; index < this.deviceList.length; index++) {
        let icon = new AMap.Icon({
          // 图标的取图地址
          image: require("../assets/img/outLineHome.png"),
          imageSize: new AMap.Size(50, 50),
        });
        let title = "离线";
        if (this.deviceList[index].is_push_on) {
          icon = new AMap.Icon({
            // 图标的取图地址
            image: require("../assets/img/inLineHome.png"),
            imageSize: new AMap.Size(50, 50),
          });
          title = "在线";
        }
        let marker = new AMap.Marker({
          position: new AMap.LngLat(
            this.deviceList[index].lon_int / 1e7,
            this.deviceList[index].lat_int / 1e7
          ),
          title: this.deviceList[index].name + " \n" + title,
          icon: icon,
          offset: new AMap.Pixel(-15, -30),
          zIndex: 50,
        });
        this.map.add(marker);
        this.deviceMarker.push(marker);
      }
    },
    //获取围栏信息
    async getFenceList() {
      let data = {
        page: 0,
        size: 100,
      };
      data.pmd = data.page.toString();
      await requestHttp("fenceList", data).then((res) => {
        this.fenceList = res.data.list?res.data.list:[];
      });
      this.drawFenceList();
    },
    //绘画围栏
    drawFenceList() {
      if (this.polyponList) {
        this.map.remove(this.polyponList);
        this.polyponList = "";
      }
      this.polyponList = [];
      for (let index = 0; index < this.fenceList.length; index++) {
        let strPoint = [];
        for (let j = 1; j < this.fenceList[index].point_list.length + 1; j++) {
          for (let i = 0; i < this.fenceList[index].point_list.length; i++) {
            if (this.fenceList[index].point_list[i].seq == j) {
              let point = new AMap.LngLat(
                this.fenceList[index].point_list[i].lon_int / 1e7,
                this.fenceList[index].point_list[i].lat_int / 1e7
              );
              strPoint.push(point);
              break;
            }
          }
        }
        let polypon = initMaps.drawPolypon(strPoint);
        polypon.id = this.fenceList[index].f_id;
        this.polyponList.push(polypon);
        polypon.setMap(this.map);
      }
    },
    //获取航线信息
    async getMissionList() {
      for (let index = 0; index < this.fenceList.length; index++) {
        let data = {
          f_id: this.fenceList[index].f_id,
          type: 20,
          page: 0,
          size: 100,
        };
        data.pmd = data.page.toString() + data.f_id + data.type.toString();
        await requestHttp("missionList", data).then((res) => {
          if(res.data.list){
            for (let i = 0; i < res.data.list.length; i++) {
            this.missionList.push(res.data.list[i]);
          }
          }
        });
      }
      this.drawRoute();
    },
    //绘制航线信息
    drawRoute() {
      for (let i = 0; i < this.missionList.length; i++) {
        console.log(this.missionList[i]);
        let routePoints = [];
        for (
          let index = 0;
          index < this.missionList[i].point_list.length;
          index++
        ) {
          if (this.missionList[i].point_list[index].seq == index + 1) {
            let point = new AMap.LngLat(
              this.missionList[i].point_list[index].lon_int / 1e7,
              this.missionList[i].point_list[index].lat_int / 1e7
            );
            routePoints.push(point);
            let params = {
              offset: -17,
              clickable: false,
              draggable: false,
            };
            let marker = initMaps.drawMarker(
              index + 1,
              point,
              "marker-edit",
              params
            );
            marker.setMap(this.map);
          }
        }
        let polyline = initMaps.drawPolyline(routePoints);
        (polyline.id = this.missionList[i].m_id), polyline.setMap(this.map);

        // console.log(routePoints)
      }
    },
  },
};
</script>
<style lang="less" scoped>
.outLineMap {
  width: 100%;
  height: 100%;
  #mapContainer {
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="less">
.outLineMap {
  #mapContainer {
    .amap-markers {
      .amap-marker {
        .marker-edit {
          position: relative;
          height: 30px;
          width: 30px;
          border-radius: 50%;
          background-color: #ffffff;
          text-align: center;
          border: 3px solid #0092f8;
          color: #0092f8;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
          .text {
            position: absolute;
            top: 50%;
            left: 50%;
            font-weight: 700;
            font-size: 20px;
            transform: translate(-50%, -50%);
          }
          &.active {
            background-color: #0c1865;
            color: #ffffff;
          }
        }
        .marker-edit-i {
          height: 18px;
          width: 18px;
          border-radius: 50%;
          background-color: #ffffff;
          text-align: center;
          border: 2px solid #0728fc;
          color: #0728fc;
          .text {
            position: absolute;
            top: 47%;
            left: 50%;
            font-weight: 800;
            font-size: 18px;
            vertical-align: middle;
            transform: translate(-50%, -50%);
          }
        }
        .marker-o-edit {
          position: relative;
          height: 30px;
          width: 30px;
          border-radius: 50%;
          background-color: #ffffff;
          text-align: center;
          border: 3px solid #07ff0e;
          color: #000000;
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);
          .text {
            position: absolute;
            top: 50%;
            left: 50%;
            font-weight: 700;
            font-size: 20px;
            transform: translate(-50%, -50%);
          }
          &.active {
            background-color: #004310;
            color: #ffffff;
          }
        }
        .marker-o-edit-i {
          height: 18px;
          width: 18px;
          border-radius: 50%;
          background-color: #ffffff;
          text-align: center;
          border: 2px solid #eeff00;
          color: #000000;
          .text {
            position: absolute;
            top: 47%;
            left: 50%;
            font-weight: 800;
            font-size: 18px;
            vertical-align: middle;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }
}
</style>