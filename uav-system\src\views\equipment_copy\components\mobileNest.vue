<template>
  <div class="mobileNest">
    <div class="content-item-1">
      <div class="content-item-1-state">
        <el-row class="content-item-1-state-row">
          <el-col :span="12">{{ equipLanguage.uavStatus.lng }}</el-col>
          <el-col :span="12">{{ equipLanguage.uavStatus.lat }}</el-col>
        </el-row>
        <el-row class="content-item-1-state-row-1">
          <el-col :span="12">{{
            uavItemList.longitude ? uavItemList.longitude : 0
          }}</el-col>
          <el-col :span="12">{{
            uavItemList.latitude ? uavItemList.latitude : 0
          }}</el-col>
        </el-row>
        <el-row class="content-item-1-state-row">
          <el-col :span="12">{{ equipLanguage.uavStatus.direction }}</el-col>
          <el-col :span="12">{{ equipLanguage.uavStatus.beaconValue }}</el-col>
        </el-row>
        <el-row class="content-item-1-state-row-1">
          <el-col :span="12">{{
            uavItemList.vihicle_direction ? uavItemList.vihicle_direction : 0
          }}</el-col>
          <el-col :span="12">{{
            uavItemList.fixtype ? uavItemList.fixtype : 0
          }}</el-col>
        </el-row>
      </div>
      <div class="content-item-1-video" ref="contentItemVideo">
        <div
          :style="video.inCabin.style"
          class="inCabinStyle"
          v-if="deviceItem && equipState"
        >
          <div class="tipinCabin">{{ equipLanguage.novideoTip }}</div>
          <div class="inCabinTitle">{{ equipLanguage.inCabin }}</div>
          <!-- <live-video
            v-if="deviceItem"
            :title="equipLanguage.inCabin"
            :videoId="'inVideo'"
            :url="deviceItem.stream_in_list ? deviceItem.stream_in_list[0] : ''"
            :autoPlay="true"
            @clickVideo="changeVideo('inCabin')"
          ></live-video> -->
        </div>
        <div v-if="deviceItem && !equipState" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>
      </div>

      <div class="content-item-1-video" ref="contentItemVideo1">
        <div :style="video.uav.style">
          <live-video
            v-if="deviceItem && equipState"
            :title="equipLanguage.uav"
            @clickVideo="changeVideo('uav')"
            :videoId="'uavVideo'"
            :url="
              deviceItem.stream_uav_list ? deviceItem.stream_uav_list[0] : ''
            "
            :autoPlay="true"
          ></live-video>
          <div v-if="deviceItem && !equipState" class="videoOutTip">
            <div class="titleTip">
              {{ equipLanguage.equipInfo.equipStateOut1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-item-2" ref="contentVideo">
      <div :style="video.outCabin.style">
        <live-video
          v-if="deviceItem && equipState"
          :title="equipLanguage.outCabin"
          :videoId="'outVideo'"
          :url="deviceItem.stream_out_list ? deviceItem.stream_out_list[0] : ''"
          :autoPlay="true"
          :isFill="true"
          @clickVideo="changeVideo('outCabin')"
        ></live-video>
        <div v-if="deviceItem && !equipState" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>
        <!-- :url="deviceItem.stream_out_list ? deviceItem.stream_out_list[0] : ''" -->
      </div>
    </div>
    <div class="content-item-3">
      <div class="content-item-3-title">
        {{ equipLanguage.airportStatus.title }}
      </div>
      <div
        class="content-item-3-btn"
        ref="contentBtn"
        :class="
          equipLanguage.language == 'en-US' ? 'content-item-3-btn-en' : ''
        "
      >
        <el-button
          :class="onKey.open == 3 || openCode ? 'active' : ''"
          :loading="onKey.open == 3 || openCode ? true : false"
          :disabled="errorCode"
          @click="UpCloseEvent(1)"
          >{{
            onKey.open == 3 || openCode
              ? equipLanguage.airportBtn.starting
              : equipLanguage.airportBtn.onKeyState
          }}</el-button
        >
        <el-button
          :class="onKey.close == 3 || closeCode ? 'active' : ''"
          :loading="onKey.close == 3 || closeCode ? true : false"
          class="lastBtn"
          ref="allclose"
          :style="isfloat"
          :disabled="errorCode"
          @click="UpCloseEvent(0)"
          >{{
            onKey.close == 3 || closeCode
              ? equipLanguage.airportBtn.closing
              : equipLanguage.airportBtn.onKeyClose
          }}</el-button
        >
      </div>
      <div class="content-item-3-content">
        <div
          class="content-index"
          v-for="(item, index) in operationList"
          :key="index"
          :class="
            item.value
              ? 'active'
              : item.value1 == 2 || item.value1 == 3
              ? 'active'
              : ''
          "
        >
          {{ item.title }}
          <el-switch
            v-if="item.value1 != 2 && item.value1 != 3 ? true : false"
            class="content-switch"
            v-model="item.value"
            active-color="#0A2550"
            inactive-color="#2C2C31"
            :disabled="
              onKey.open == 3 ||
              onKey.close == 3 ||
              openCode ||
              closeCode ||
              errorCode
                ? true
                : false
            "
            @change="changeState(item)"
          >
          </el-switch>

          <el-button
            type="text"
            :loading="loadingCode"
            v-if="item.value1 == 2 || item.value1 == 3 ? true : false"
            >{{ item.value1 | getTipTitle(item) }}</el-button
          >
        </div>
        <div class="content-index" :class="uavState ? 'active' : ''">
          {{ equipLanguage.airportStatus.drone }}
          <el-switch
            class="content-switch"
            v-model="uavState1"
            active-color="#0A2550"
            inactive-color="#2C2C31"
            :disabled="true"
          >
          </el-switch>
        </div>
      </div>
      <div class="content-item-3-btn">
        <el-button
          class="errorBtn"
          v-if="errorCode"
          @click="errorCode = false"
          >{{ equipLanguage.airportBtn.removeError }}</el-button
        >
        <el-button
          @click="clickEvent(3)"
          :class="clickCode == 3 ? 'active' : ''"
          >{{ equipLanguage.airportBtn.stop }}</el-button
        >
        <el-button
          @click="clickEvent(1)"
          :class="clickCode == 1 ? 'active' : ''"
          >{{ equipLanguage.airportBtn.continue }}</el-button
        >
        <el-button
          @click="clickEvent(2)"
          :class="clickCode == 2 ? 'active' : ''"
          >{{ equipLanguage.airportBtn.reset }}</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import liveVideo from "@/components/video/webrtcVideoHttps.vue";
export default {
  name: "mobileNest",
  props: {
    video: {
      type: Object,
      default() {
        return {};
      },
    },
    onKey: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    operationList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    isfloat: {
      type: Object,
      default() {
        return {};
      },
    },
    websocket1: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    uavItemList: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    deviceItem: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    watchError: {
      type: Number,
    },
    uavState: {
      type: Boolean,
    },
    changeStateCode: {
      type: Object,
      default() {
        return {};
      },
    },
    equipState: {
      type: Boolean,
      default: false,
    },
    equipLanguage: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
  },
  components: {
    liveVideo,
  },
  data() {
    return {
      time: 0,
      loadingCode: true,
      openCode: false,
      closeCode: false,
      errorCode: false,
      errorCodeClass: false,
      clickCode: 0,
      indexCode: "",
      // uavState1:false,
      temperatureImg: require("../../../assets/img/equipment/temperature.png"),
      weatherImg: require("../../../assets/img/equipment/weather.png"),
      windImg: require("../../../assets/img/equipment/wind.png"),
    };
  },
  mounted() {
    // console.log(this.operationList)
    // console.log(navigator.getUserMedia )
    // console.log(this.deviceItem);
  },
  methods: {
    //切换视频位置
    changeVideo(item) {
      if (this.video[item].code !== 2) {
        let keys;
        for (let key in this.video) {
          if (this.video[key].code == 2) {
            keys = key;
          }
        }
        this.video[keys].code = this.video[item].code;
        this.video[item].code = 2;
        let style = this.video[item].style;
        this.video[item].style = this.video[keys].style;
        this.video[keys].style = style;
      }
    },
    //点击改变状态
    changeState(item) {
      let stateCode = "";
      let index = this.operationList.findIndex((x) => {
        return x.id == item.id;
      });
      let changeStateCode = {
        num: index,
        state: true,
      };
      this.$emit("update:changeStateCode", changeStateCode);
      // this.changeStateCode=JSON.parse(JSON.stringify(this.operationList))
      // this.indexCode=this.operationList.findIndex(x=>{
      //   return x.id==item.id
      // })
      if (item.id == "Hatch") {
        if (item.value) {
          stateCode = 100;
        } else {
          stateCode = 101;
        }
      } else if (item.id == "Lift") {
        if (item.value) {
          stateCode = 102;
        } else {
          stateCode = 103;
        }
      } else if (item.id == "center") {
        if (item.value) {
          stateCode = 104;
        } else {
          stateCode = 105;
        }
      } else if (item.id == "charger") {
        if (item.value) {
          stateCode = 117;
        } else {
          stateCode = 118;
        }
      }
      let data = {
        nest_action_cmd: stateCode,
      };
      this.websocket1.manualSend(data, 403);
    },
    //一键启动一键关闭事件
    UpCloseEvent(index) {
      let time = new Date().getTime();
      if (time - this.time > 1000) {
        if (index == 0) {
          this.closeCode = true;
          if (this.onKey.close != 1) {
            if (this.onKey.open != 3) {
              let a = 0;
              for (let index = 0; index < this.operationList.length; index++) {
                if (this.operationList[index].value1 == 2) {
                  this.$message.warning({
                    message: this.operationList[index].title + "正在关闭",
                    customClass: "message-info",
                  });
                  a = 1;
                  this.closeCode = false;
                }
                if (this.operationList[index].value1 == 3) {
                  this.$message.warning({
                    message: this.operationList[index].title + "正在打开",
                    customClass: "message-info",
                  });
                  a = 1;
                  this.closeCode = false;
                }
              }
              if (a == 0) {
                let data = {
                  nest_action_cmd: 116,
                };
                console.log(data);
                this.websocket1.manualSend(data, 403);
              }
            } else {
              this.closeCode = false;
              this.$message.warning({
                message: "设备正在启动，暂无法关闭！",
                customClass: "message-info",
              });
            }
          } else {
            this.closeCode = false;
            this.$message.warning({
              message: "设备正处在关闭状态！",
              customClass: "message-info",
            });
          }
        } else if (index == 1) {
          this.openCode = true;
          if (this.onKey.open != 1) {
            if (this.onKey.close != 3) {
              let a = 0;
              for (let index = 0; index < this.operationList.length; index++) {
                if (this.operationList[index].value1 == 2) {
                  this.$message.warning({
                    message: this.operationList[index].title + "正在关闭",
                    customClass: "message-info",
                  });
                  a = 1;
                  this.openCode = false;
                }
                if (this.operationList[index].value1 == 3) {
                  this.$message.warning({
                    message: this.operationList[index].title + "正在打开",
                    customClass: "message-info",
                  });
                  a = 1;
                  this.openCode = false;
                }
              }
              if (a == 0) {
                let data = {
                  nest_action_cmd: 115,
                };
                console.log(data);
                this.websocket1.manualSend(data, 403);
              }
            } else {
              this.openCode = false;
              this.$message.warning({
                message: "设备正在关闭，暂无法打开！",
                customClass: "message-info",
              });
            }
          } else {
            this.openCode = false;
            this.$message.warning({
              message: "设备正处于开启状态！",
              customClass: "message-info",
            });
          }
        }
      }
      this.time = time;
      // if (this.onKey.open!=1 && this.onKey.close!=1) {
      //   if (index == 1) {
      //     stateCode = 30;
      //   } else if (index == 0) {
      //     stateCode = 31;
      //   }
      //   let data = {
      //     nest_action_cmd: stateCode,
      //   };
      //   console.log(data);
      //   this.websocket1.manualSend(data, 403);
      // } else {
      //   // if (this.onKey.open==1 && index == 0) {
      //   //   this.$message.warning("设备正在启动，暂时无法关闭！");
      //   // }
      //   // if (this.onKey.close==1 && index == 1) {
      //   //   this.$message.warning("设备正在关闭，暂时无法启动！");
      //   // }
      // }
    },
    //暂停，继续，复位
    clickEvent(index) {
      if (!this.clickCode) {
        let num = 114;
        if (index == 3) {
          num = 114;
        }
        if (index == 1) {
          num = 119;
        }
        if (index == 2) {
          num = 108;
        }
        let data = {
          nest_action_cmd: num,
        };
        console.log(data);
        this.websocket1.manualSend(data, 403);
        this.clickCode = index;
        setTimeout(() => {
          this.clickCode = 0;
        }, 1000);
      }
    },
    //改变飞机状态
    // changeUavState(){
    //   this.$emit("update:uavState",false)
    //   this.websocket1.manualSend({}, 429);
    // }
  },
  computed: {
    uavState1() {
      if (this.uavState) {
        return this.uavState;
      } else {
        return false;
      }
    },
  },
  watch: {
    watchError(value) {
      if (value > 12 && value < 21 && !this.errorCode) {
        this.errorCode = true;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .mobileNest {
    .content-item-1 {
      .content-item-1-state {
        // display: flex;
        border-radius: @zoomIndex * 6px !important;
        .content-item-1-state-row {
          .el-col {
            font-size: @zoomIndex * 12px !important;
          }
        }
        .content-item-1-state-row-1 {
          .el-col {
            font-size: @zoomIndex * 16px !important;
          }
        }
      }
      .content-item-1-video {
        .videoOutTip {
          .titleTip {
            font-size: @zoomIndex * 18px !important;
          }
        }
        .inCabinStyle {
          .tipinCabin {
            font-size: @zoomIndex * 16px !important;
          }
          .inCabinTitle {
            padding: @zoomIndex * 3px @zoomIndex * 10px !important;
          }
        }
      }
    }
    .content-item-2 {
      .videoOutTip {
        .titleTip {
          font-size: @zoomIndex * 18px !important;
        }
      }
    }
    .content-item-3 {
      border-radius: @zoomIndex * 6px !important;
      .content-item-3-title {
        font-size: @zoomIndex * 14px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
      .content-item-3-btn {
        .el-button {
          min-width: @zoomIndex * 120px !important;
          font-size: @zoomIndex * 14px !important;
          letter-spacing: @zoomIndex * 2px !important;
        }
      }
      .content-item-3-content {
        .content-index {
          font-size: @zoomIndex * 18px !important;
          letter-spacing: @zoomIndex * 2px !important;
          .el-button {
            padding: @zoomIndex * 6px 0 !important;
            &.el-button--text {
              font-size: @zoomIndex * 16px !important;
            }
          }
        }
      }
    }
  }
}
.mobileNest {
  width: 100%;
  height: 100%;
  display: flex;
  .content-item-1 {
    width: 20%;
    margin: 2% 0 2% 1%;
    .content-item-1-state {
      width: 100%;
      height: 18%;
      margin-bottom: 2%;
      background-color: #000000;
      // display: flex;
      border-radius: 6px;
      color: white;
      .content-item-1-state-row {
        width: 96%;
        height: 25%;
        margin: 0 2%;
        .el-col {
          font-size: 12px;
          text-align: center;
          color: #6b6b6b;
          font-weight: 650;
        }
      }
      .content-item-1-state-row-1 {
        width: 96%;
        height: 25%;
        margin: 0 2%;
        .el-col {
          font-size: 16px;
          text-align: center;
        }
      }
    }
    .content-item-1-video {
      width: 100%;
      height: 40%;
      .video-module {
        height: 100%;
        width: 100%;
      }
      .videoOutTip {
        width: 100%;
        height: 100%;
        color: white;
        background-color: #3d4348;
        position: relative;
        .titleTip {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 18px;
        }
      }
      .inCabinStyle {
        background-color: rgba(61, 67, 72, 1);
        position: relative;
        .tipinCabin {
          width: 100%;
          color: white;
          text-align: center;
          vertical-align: middle;
          padding-top: 25%;
          font-size: 16px;
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-80%);
        }
        .inCabinTitle {
          position: absolute;
          top: 0;
          left: 0;
          background-color: #010101;
          color: #fff;
          border: none;
          padding: 3px 10px;
        }
      }
    }
  }
  .content-item-2 {
    margin: 2% 1%;
    width: 56%;
    .video-module {
      height: 100%;
      width: 100%;
    }
    .videoOutTip {
      width: 100%;
      height: 100%;
      color: white;
      background-color: #3d4348;
      position: relative;
      .titleTip {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
      }
    }
  }
  .content-item-3 {
    width: 20%;
    margin: 2% 1% 2% 0;
    background-color: #060606;
    border-radius: 6px;
    .content-item-3-title {
      margin: 2% 8%;
      color: white;
      font-weight: 550;
      font-size: 14px;
      letter-spacing: 2px;
    }
    .content-item-3-btn {
      margin: 2% 8%;
      .el-button {
        white-space: normal;
        margin-left: 0;
        width: 45%;
        min-width: 120px;
        background-color: #0b58de;
        color: white;
        border: none;
        font-size: 14px;
        font-weight: 550;
        letter-spacing: 2px;
        margin-bottom: 1%;
        &.is-disabled {
          background-color: #638bd1;
        }
        &.active {
          background-color: white;
          color: #0b58de;
          &.is-disabled {
            background-color: #638bd1;
          }
        }
      }
      .errorBtn {
        background-color: #ee3434;
        color: white;
      }
    }
    // .content-item-3-btn-en{
    //   margin: 2% 5%;
    //   .el-button{
    //     width: 48%;
    //     white-space:normal;
    //   }

    // }
    .content-item-3-content {
      margin: 2% 8%;
      .content-index {
        margin: 10% 0;
        font-size: 18px;
        text-align: left;
        margin-left: 7%;
        color: #79818e;
        font-weight: 500;
        letter-spacing: 2px;
        &.active {
          color: #0b58de;
        }
        .content-switch,
        .el-button {
          float: right;
        }
        .el-switch {
          height: 50%;
        }
        .el-button {
          background-color: transparent;
          border: none;
          padding: 6px 0;
          &.el-button--text {
            color: #0b58de !important;
            font-size: 16px;
          }
          &.is-loading:before {
            background-color: transparent !important;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.mobileNest {
  .content-item-3 {
    .content-item-3-content {
      .el-switch {
        .el-switch__core {
          height: 36px !important;
          width: 100px !important;
          border-radius: 20px !important;
          &::after {
            height: 32px !important;
            width: 32px !important;
          }
        }
      }
      .el-switch.is-checked .el-switch__core::after {
        margin-left: -30px !important;
        background-color: #0b58de !important;
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .mobileNest {
    .content-item-3 {
      .content-item-3-content {
        .el-switch {
          .el-switch__core {
            height: @zoomIndex * 36px !important;
            width: @zoomIndex * 100px !important;
            border-radius: @zoomIndex * 20px !important;
            &::after {
              height: @zoomIndex * 32px !important;
              width: @zoomIndex * 32px !important;
            }
          }
        }
        .el-switch.is-checked .el-switch__core::after {
          margin-left: @zoomIndex * -30px !important;
        }
      }
    }
  }
}
</style>