<template>
  <div class="animation-bar">
    <div
      class="animation-bar-item"
      v-for="item in barNumber"
      :key="item"
      :style="[style, valueStyle[item - 1]]"
    ></div>
  </div>
</template>
<script>
export default {
  props: {
    barNumber: {
      type: Number,
      default: 5,
    },
    barWidth: {
      type: String,
      default: "9px",
    },
    fullWidth: {
      type: Boolean,
      default: false,
    },
    barBorderRadius: {
      type: String,
      default: "6px",
    },
    barBackgroundColor: {
      type: String,
      default: "#fff",
    },
    value: {
      type: [Number, String],
      default: 0,
    },
  },
  computed: {
    style() {
      let style_1 = {
        width: this.barWidth,
        marginRight: this.barWidth,
        borderRadius: this.barBorderRadius,
        backgroundColor: this.barBackgroundColor,
      };
      if (this.fullWidth) {
        let a = 100 / (this.barNumber * 2) + "%";
        style_1.width = a;
        style_1.marginRight = a;
      }
      return style_1;
    },
  },
  watch: {
    value: {
      handler(val) {
        let barValue = 100 / this.barNumber;
        this.valueStyle = Array.from({ length: this.barNumber }, (_, i) => {
          let a = "100%";
          if ((i + 1) * barValue > val) {
            if (i * barValue >= val) {
              a = "100%";
            } else {
              a = (1 - (val % barValue) / barValue) * 100 + "%";
            }
          } else {
            a = 0;
          }
          return {
            background: `linear-gradient(to bottom,${this.barBackgroundColor} ${a},#ffde09 ${a})`,
          };
        });
      },
      immediate: true,
    },
  },
  data() {
    return {
      valueStyle: new Array(this.barNumber).fill({}),
    };
  },
};
</script>
<style lang="less" scoped>
.animation-bar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  overflow: hidden;
  .animation-bar-item {
    flex-shrink: 0;
    height: 100%;
  }
}
</style>