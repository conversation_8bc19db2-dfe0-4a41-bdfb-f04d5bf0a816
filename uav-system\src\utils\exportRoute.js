import { gcj02_to_wgs84, wgs84_to_gcj02 } from './wgs84_to_gcj02'
import Vue from 'vue'
const coordinateType = Vue.prototype.$coordinateType
const typeList = {
    "wgs84": 10,
    "gcj02": 20,
}
export function exportRoute(params = {}) {
    let {
        operateItem = '',
        routeItem = ""
    } = params;
    let points = []
    if (!operateItem) {
        return false;
    }
    let originPoints = routeItem ? routeItem.point_list : []
    for (let index = 0; index < operateItem.point_json.length; index++) {
        points.push(changeCoordinates(operateItem.point_json[index], originPoints[index]))
    }
    fileFormat(operateItem.name, points)
}

function changeCoordinates(points1, points2) {
    let points = Object.assign({}, points1)
    let wgcsPoint = [points.lng, points.lat]
    if(points.type){
        if(points.type!=10){
            wgcsPoint = gcj02_to_wgs84(points.lng, points.lat)
        }
    }else{
        if (coordinateType == "gcj02") {
            wgcsPoint = gcj02_to_wgs84(points.lng, points.lat)
        }
    }
    points.lng = parseFloat(wgcsPoint[0].toFixed(7))
    points.lat = parseFloat(wgcsPoint[1].toFixed(7))
    if (coordinateType != "gcj02" || !points2) {
        return points
    }
    if (points2.type !== 10) {
        return points
    }

    let a = wgs84_to_gcj02(points2.lon_int / 1e7, points2.lat_int / 1e7)
    if (points1.lng == a[0].toFixed(7) && points1.lat == a[1].toFixed(7)) {
        points1.lng = points2.lon_int / 1e7
        points1.lat = points2.lat_int / 1e7
        return points1
    }
    return points
}

function returnPointsFormat(points) {
    let str = ''
    for (let index = 0; index < points.length; index++) {
        str += `<Placemark><name>${index + 1}号航点</name><styleUrl>#m_ylw-pushpin</styleUrl><Point><coordinates>${points[index].lng},${points[index].lat},${points[index].height}</coordinates></Point></Placemark>`
    }
    return str
}

function fileFormat(name, points) {
    let routeName = `<name>${name}</name>` //航线名称
    let num = `<utmn>${points.length}</utmn>` //航线数量
    let style = `<style></style>` //航线样式
    let pointsContent = returnPointsFormat(points) //航点内容
    let content = `<?xml version='1.0' encoding='utf-8'?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>${routeName}${num}${style}${pointsContent}</Document></kml>`

    const domObj = document.createElement("a");
    domObj.setAttribute(
        "href",
        "data:text/xml;charset=utf-8," + encodeURIComponent(content)
    ); //注：如存储数组 or JSON需将其转换为JSON字符串
    domObj.setAttribute(
        "download",
        name + returnDate() + ".kml"
    );
    if (document.createEvent) {
        const event = document.createEvent("MouseEvents");
        event.initEvent("click", true, true);
        domObj.dispatchEvent(event);
    } else {
        domObj.click();
    }
}

function returnDate() {
    let time = new Date();
    time = time.getFullYear() + timeFormat(time.getMonth() + 1) + timeFormat(time.getDate()) + timeFormat(time.getHours()) + timeFormat(time.getMinutes()) + timeFormat(time.getSeconds())
    return time;
}

function timeFormat(num) {
    return String(num).padStart(2, "0")
}