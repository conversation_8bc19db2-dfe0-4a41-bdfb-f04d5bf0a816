<template>
  <div class="cesium3D">
    <div id="map" style="height: 100%"></div>
    <div id="leafletMap" style="height: 0"></div>
    <input
      type="file"
      style="display: none"
      ref="files"
      accept=".kml,.json,.plan"
      @change="chooseFileAfter"
    />
    <div class="listShow" v-show="!mapLoading" ref="listShow">
      <plan-list
        ref="planList"
        v-show="code == 0 || code == 4 ? true : false"
        :routeLanguage="routeLanguage"
        @openDeviceEvent="openDeviceEvent"
        @exportRouteEvent="exportRouteEvent"
      ></plan-list>
      <fence-edit
        ref="fenceEdit"
        v-if="code == 2"
        @changeMarker="changeFenceMarker"
        :changePoint="changePoint"
        mapType="cesium3D"
        @goBack="goBacks"
        v-loading="loading"
        :element-loading-text="routeLanguage.routeLine.routeLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(10, 10, 10, 0.8)"
      ></fence-edit>
      <route-edit
        ref="routeEdit"
        :fenceItem="fenceItem"
        :checkType="checkType"
        :changePoint="changePoint"
        :extra.sync="extra"
        :importPoints.sync="importPoints"
        :elevationSuccess="elevationSuccess"
        :elevationData="elevationData"
        v-if="code == 1"
        @goBack="goBacks"
        @changeMarker="changeRouteMarker"
        @changeSpeed="changeSpeed"
        @showHeightEvent="showHeightEvent"
        @isComputeHightEvent="isComputeHightEvent"
        @changeIntervalHeight="changeIntervalHeight"
        mapType="cesium3D"
        v-loading="loading"
        :element-loading-text="routeLanguage.routeLine.routeLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(10, 10, 10, 0.8)"
      ></route-edit>
      <ortho-edit
        ref="orthoEdit"
        v-if="code == 3"
        :fenceItem="fenceItem"
        :checkType="checkType"
        :changePoint="changePoint"
        :extra.sync="extra"
        :elevationSuccess="elevationSuccess"
        :elevationData="elevationData"
        :orthoAllPoints="orthoAllPoints"
        mapType="cesium3D"
        @changeMarker="changeOrthoMarker"
        @refresh="refresh"
        @mouseupEvent="mouseupEvent"
        @goBack="goBacks"
        @showHeightEvent="showHeightEvent"
        @isComputeHightEvent="isComputeHightEvent"
        @changeIntervalHeight="changeIntervalHeight"
        v-loading="loading"
        :element-loading-text="routeLanguage.routeLine.routeLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(10, 10, 10, 0.8)"
      ></ortho-edit>
    </div>
    <div
      class="operateBar_1"
      v-if="code == 1 || code == 2 || code == 3 ? true : false"
    >
      <operate-bar
        :drawend="drawend"
        :removeClick="removeClick"
        :delClick="delClick"
        :recallClick="recallClick"
        :importClick="importClick"
        :exportClick="exportClick"
        @operateEvent="operateEvent"
      ></operate-bar>
    </div>
    <div
      class="timer"
      v-if="code == 1 || code == 3 || (code == 4 && routeItem) ? true : false"
    >
      <div class="timer-item">
        <div>{{ routeLanguage.routeLine.ET }}</div>
        <div class="timer-item-1">{{ estTime | estTimeFormat }}</div>
      </div>
      <div class="timer-item">
        <div>
          {{ routeLanguage.routeLine.distance }}（{{
            distance >= 1000 ? "KM" : "M"
          }}）
        </div>
        <div class="timer-item-1">{{ distance | distanceFormat }}</div>
      </div>
      <div class="timer-item" v-if="code == 3 || routeItem.type == 50">
        <div>{{ routeLanguage.routeLine.waypointCount }}</div>
        <div class="timer-item-1">{{ routeSpotCount }}</div>
      </div>
      <div class="timer-item" v-if="code == 3 || routeItem.type == 50">
        <div>
          {{ routeLanguage.routeLine.area }}（{{
            area >= 10000 ? "km²" : "m²"
          }}）
        </div>
        <div class="timer-item-1">{{ area | areaFormat }}</div>
      </div>
      <div class="timer-item" v-if="code == 3 || routeItem.type == 50">
        <div>{{ routeLanguage.routeLine.photoNumber }}</div>
        <div class="timer-item-1">{{ photoCount }}</div>
      </div>
    </div>
    <div class="layerBut">
      <el-popover
        placement="left"
        trigger="manual"
        popper-class="popover-item popover-item-1"
        v-model="layerCode1"
      >
        <div>
          <el-autocomplete
            popper-class="autoInput"
            v-model="searchSite"
            :fetch-suggestions="searchSiteTip"
            :placeholder="routeLanguage.tool.placeholder"
            @keyup.enter.native="chooseSite()"
            clearable
            @select="chooseSite"
          >
            <el-button slot="append" @click="chooseSite()"
              ><el-image :src="searchImg1"></el-image
            ></el-button>
            <template slot-scope="{ item }">
              <div class="name">{{ item.name }}</div>
            </template>
          </el-autocomplete>
        </div>
        <el-button slot="reference" @click="layerCode1 = !layerCode1">
          <el-image
            v-show="layerCode1"
            :src="searchImg1_1"
            fit="fill"
          ></el-image>
          <el-image
            v-show="!layerCode1"
            :src="searchImg1"
            fit="fill"
          ></el-image>
        </el-button>
      </el-popover>
      <el-popover
        placement="left"
        trigger="manual"
        popper-class="popover-item popover-item-2"
        v-model="layerCode2"
      >
        <div class="content">
          <el-button
            v-for="(item, index) in toolMenu"
            :key="item.value"
            :class="choosed == item.value ? 'actived' : ''"
            @click="toolEvent(item.value)"
          >
            <el-image
              v-show="choosed === item.value"
              :src="item.imgSrc_1"
              fit="fill"
            ></el-image>
            <el-image
              v-show="choosed !== item.value"
              :src="item.imgSrc"
              fit="fill"
            ></el-image>
            {{ routeLanguage.toolMenu[index] }}
          </el-button>
        </div>
        <el-button slot="reference" @click="layerCode2 = !layerCode2">
          <el-image v-show="layerCode2" :src="toolImg_1" fit="fill"></el-image>
          <el-image v-show="!layerCode2" :src="toolImg" fit="fill"></el-image>
        </el-button>
      </el-popover>
      <!-- <el-popover
        placement="left-start"
        trigger="manual"
        v-model="layerCode"
        popper-class="popover-item popover-item-3"
      >
        <layer-div
          :weatherCode="weatherCode"
          @weacode="weacode"
          @zoneState="zoneState"
          ref="layerDiv"
          :toolLanguage="routeLanguage.tool"
        ></layer-div>
        <el-button slot="reference" @click="layerCode = !layerCode">
          <el-image
            :src="layerCode ? layerImg_1 : layerImg"
            fit="fill"
          ></el-image>
        </el-button>
      </el-popover> -->
    </div>
    <div class="map-control" style="display: none">
      <div class="outer-control">
        <div
          class="inner-control"
          :style="`transform: rotateX(${
            90 - pitch
          }deg) rotateZ(${rotation}deg);`"
          @click="resetPitch"
        >
          <div class="pointer-control"></div>
        </div>
        <div
          class="pitchUp"
          @mousedown="changePitch('up')"
          @mouseup="upPitch"
        ></div>
        <div
          class="pitchDown"
          @mousedown="changePitch('down')"
          @mouseup="upPitch"
        ></div>
        <div
          class="rotateLeft"
          @mousedown="changePitch('left')"
          @mouseup="upPitch"
        ></div>
        <div
          class="rotateRight"
          @mousedown="changePitch('right')"
          @mouseup="upPitch"
        ></div>
      </div>
    </div>
    <div
      v-if="
        (code == 3 || routeItem.type == 50) &&
        (getElevationcode || zoomMaxHeight) &&
        isComputeHight
      "
      class="zoom-max-height"
    >
      {{
        getElevationcode
          ? `${routeLanguage.getHeightLoading}(${progress}%)`
          : `${routeLanguage.heightLabel}${zoomMaxHeight}m`
      }}
    </div>
    <device-choose
      ref="deviceChoose"
      :planCode.sync="planCode"
      :deviceLanguage="routeLanguage.dialogEquip"
    ></device-choose>
  </div>
</template>
<script>
import createMap from "@/utils/cesium/createMap";
import mapMethods from "@/utils/cesium/mapMethods";
import dialogInfo from "@/components/dialogInfo/index";
import planList from "./components/planList.vue";
import routeEdit from "./components/routeEdit.vue";
import orthoEdit from "./components/orthoEdit.vue";
import requestHttp from "@/utils/api";
import { pointsConvert } from "@/utils/coordinateConvert";
import { computedMapMethods } from "@/utils/cesium/computedMapMethods";
import { orthoPhotoComputer } from "@/utils/cesium/orthoPhotoComputer";
import {
  computerElevationPoints,
  computerElevationArray,
} from "@/utils/cesium/computerElevationData";
import { searchTip1, searchPoi1 } from "../../utils/mapApi";
import L from "leaflet";
import operateMixins from "./operateMixins";
import measureMixins from "./measureMixins";
let measureMixin = new measureMixins({
  mapMethods: mapMethods,
  computedMapMethods: computedMapMethods,
});
let operateMixin = new operateMixins({
  mapMethods: mapMethods,
  computedMapMethods: computedMapMethods,
  pointsConvert: pointsConvert,
});
export default {
  mixins: [operateMixin, measureMixin],
  data() {
    return {
      planCode: 0,
      deviceList: [],
      windowInfo: "",
      mapLoading: false,
      drawend: false,
      removeClick: false,
      delClick: false,
      recallClick: false,
      importClick: false,
      exportClick: false,
      handler: "",
      num: 0,
      markerPoint: [],
      linearr: [],
      orthoarr: [],
      operateEntrty: "",
      operateOrtho: "",
      clickCode: "",
      fencePaths: {},
      moveBeforePoint: {},
      changePoint: {},
      loading: false,
      leftDownCode: false,
      changeCode: false,
      fenceItem: {},
      extra: false,
      importPoints: [],
      mouseupCode: true,
      estTime: 0,
      distance: 0,
      routeSpotCount: 0,
      area: 0,
      photoCount: 0,
      messageCodeWarning: false,
      timer: null,
      routeItemParam: {},
      cacheData: [],
      measureEntries: [],
      orthoAllPoints: "",
      elevationPoints: {},
      elevationTime: {},
      elevationSuccess: {},
      elevationData: {},
      clearElevationCode: 0,
      showHeight: false,
      default_distance: 100,
      isComputeHight: false,
      toolMenu: [
        {
          imgSrc: require("@/assets/img/routeplan/site.png"),
          imgSrc_1: require("@/assets/img/routeplan/site_1.png"),
          value: 1,
        },
        {
          imgSrc: require("@/assets/img/routeplan/distance.png"),
          imgSrc_1: require("@/assets/img/routeplan/distance_1.png"),
          value: 2,
        },
        {
          imgSrc: require("@/assets/img/routeplan/area.png"),
          imgSrc_1: require("@/assets/img/routeplan/area_1.png"),
          value: 3,
        },
        {
          imgSrc: require("@/assets/img/routeplan/circle.png"),
          imgSrc_1: require("@/assets/img/routeplan/circle_1.png"),
          value: 4,
        },
        {
          imgSrc: require("@/assets/img/routeplan/direction.png"),
          imgSrc_1: require("@/assets/img/routeplan/direction_1.png"),
          value: 5,
        },
        {
          imgSrc: require("@/assets/img/routeplan/delall.png"),
          imgSrc_1: require("@/assets/img/routeplan/delall_1.png"),
          value: 10,
        },
      ],
      layerCode1: false,
      layerCode2: false,
      searchSite: "",
      searchTips: "",
      choosed: "",
      defaultHeight: 0,
      zoomMaxHeight: 0,
      getElevationcode: false,
      progress: 0,
      searchImg1: require("@/assets/img/routeplan/toolSearch.png"),
      toolImg: require("@/assets/img/routeplan/tool.png"),
      layerImg: require("@/assets/img/routeplan/toolLayer.png"),
      searchImg1_1: require("@/assets/img/routeplan/toolSearch_1.png"),
      toolImg_1: require("@/assets/img/routeplan/tool_1.png"),
      layerImg_1: require("@/assets/img/routeplan/toolLayer_1.png"),
      setImg: require("@/assets/img/routeplan/set.png"),
      setImg_1: require("@/assets/img/routeplan/set_1.png"),
      iconImg: require("@/assets/img/routeplan/pldot.png"),
      pitch: 90,
      rotation: 0,
      pitchLoop: "",
    };
  },
  components: {
    planList,
    operateBar: () => import("./components/operaterBar.vue"),
    fenceEdit: () => import("./components/fenceEdit.vue"),
    routeEdit,
    orthoEdit,
    deviceChoose: () => import("./components/deviceList.vue"),
  },
  computed: {
    code() {
      return this.$store.state.route.code;
    },
    routeLanguage() {
      return this.$languagePackage.routes;
    },
    fence() {
      return this.$store.state.route.fence;
    },
    fenceId() {
      return this.$store.state.route.fenceId;
    },
    routeItem() {
      return this.$store.state.route.routeItem;
    },
    operateFItem() {
      return this.$store.state.route.operateFItem;
    },
    checkType() {
      return this.$store.state.route.checkType;
    },
  },
  watch: {
    fence() {
      this.drawFenceList();
    },
    fenceId(value) {
      if (value) {
        this.getFenceItem();
      }
    },
    routeItem(value) {
      if (!value.typeCode) {
        this.getRouteItem();
      }
    },
    operateFItem() {
      this.operateItem();
    },
    code(newValue) {
      this.toolEvent(10);
      if (newValue == 0) {
        this.clearEntity();
      }
      if (newValue == 2) {
        if (!this.operateFItem) {
          this.fence.forEach((item) => {
            if (item.f_id !== this.fenceId) {
              let x = this.map.entities.getById(item.f_id);
              x.show = false;
            }
          });
        }
      }
      if (newValue == 1 || newValue == 3) {
        this.clearElevationMarker();
        if (this.routeItem && this.routeItem.typeCode) {
          this.loading = true;
          this.routeFormCode = true;
          setTimeout(() => {
            this.getRouteItem();
          }, 50);
        }
        if (newValue == 1) {
          this.$nextTick(() => {
            this.default_distance =
              this.$refs.routeEdit.routeForm.interval_height;
            this.showHeight = this.$refs.routeEdit.routeForm.showHeight;
            this.isComputeHight = this.$refs.routeEdit.routeForm.isComputeHight;
          });
        } else if (newValue == 3) {
          this.$nextTick(() => {
            this.default_distance =
              this.$refs.orthoEdit.orthoForm.interval_height;
            this.showHeight = this.$refs.orthoEdit.orthoForm.showHeight;
            this.isComputeHight = this.$refs.orthoEdit.orthoForm.isComputeHight;
          });
        }
      }

      this.$nextTick(() => {
        this.setDefaultHeight(newValue);
      });
    },
    layerCode1() {
      this.searchSite = "";
    },
  },
  created() {
    this.$store.dispatch("requestTypeList");
  },
  mounted() {
    this.initMap();
    this.getDevice();
    this.$refs.planList.getFenceList();
    window.addEventListener("mouseup", this.upPitch);
    this.$store.commit("setMultiMessage", {
      key: "routePlan",
      message: this.getMessage,
    });
  },
  methods: {
    getMessage(msg_id, data) {
      if (msg_id == 110) {
        let a = this.deviceList.findIndex((item) => {
          return item.sn_id == data.sn_id;
        });
        if (a !== -1) {
          if (
            !(
              this.deviceList[a].is_push_on == data.is_push_on &&
              this.deviceList[a].is_pull_on == data.is_pull_on
            )
          ) {
            if (this.windowInfo) {
              this.windowInfo.windowClose();
            }
            this.getDevice();
          }
        }
      }
    },
    initMap() {
      this.map = createMap.createMap("map", {
        modeIndex: 1,
        layerIndex: 1,
        sceneModeButton: true,
      });
      this.leafletMap = L.map("leafletMap").setView(
        [23.129704666325214, 113.26445102691652],
        22
      );
      this.$store.commit("setMaps", this.map);
      this.$store.commit("setLeafletMaps", this.leafletMap);
      this.handler = new Cesium.ScreenSpaceEventHandler(this.map.scene.canvas);
      this.handler.setInputAction(
        this.mapClick,
        Cesium.ScreenSpaceEventType.LEFT_CLICK
      );
      this.map.trackedEntity = undefined;
      this.map.cesiumWidget.screenSpaceEventHandler.removeInputAction(
        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
      );
    },
    //获取设备列表
    getDevice() {
      let data = {
        page: 0,
        size: 100,
        type: 0,
      };
      data.pmd = data.page.toString() + data.type.toString();
      requestHttp("deviceList", data).then((res) => {
        this.deviceList = res.data.list ? res.data.list : [];
        mapMethods.drawCluster(this.deviceList, this.map);
      });
    },
    //点击聚合点
    openInfo(pickedLabel) {
      if (this.windowInfo) {
        this.windowInfo.windowClose();
      }
      const ids = Array.isArray(pickedLabel.id)
        ? pickedLabel.id
        : [pickedLabel.id];
      let list = ids.map((item) => {
        let obj = {
          id: item.id,
          name: item.name,
          state: item.properties.state._value,
          position: item.properties.position._value,
        };
        return obj;
      });
      this.windowInfo = new dialogInfo({
        viewer: this.map,
        list: list,
      });
    },
    //地图点击事件
    mapClick(e) {
      const pickedLabel = this.map.scene.pick(e.position);
      if (Cesium.defined(pickedLabel) && pickedLabel.id) {
        if (
          pickedLabel.id.length ||
          (pickedLabel.id.description &&
            pickedLabel.id.description.getValue() == "isDevice")
        ) {
          this.openInfo(pickedLabel); //打开聚合点列表
          return false;
        }
      }
      if (this.windowInfo) {
        this.windowInfo.windowClose();
      }
      if (this.drawend) {
        if (this.code == 2) {
          this.clickFence(e, pickedLabel); //绘制围栏点
        } else if (this.code == 1) {
          this.clickEvent(e, pickedLabel); //绘制航线点
        } else if (this.code == 3) {
          this.clickOrtho(e, pickedLabel); //绘制正射影像的点
        }
      }
      if (this.choosed == 2) {
        this.startMeasure(e);
      }
      if (this.choosed == 3) {
        this.startMeasureArea(e);
      }
      if (this.choosed == 4) {
        this.startMeasureCircular(e);
      }
      if (this.choosed == 5) {
        this.startMeasureAzimuth(e);
      }
    },
    //绘制多边形
    drawFenceList() {
      Object.keys(this.fencePaths).forEach((item) => {
        let x = this.map.entities.getById(item);
        if (x && !x.show) {
          x.show = true;
        }
      });
      for (let index = 0; index < this.fence.length; index++) {
        let strPoint = [];
        for (let j = 1; j < this.fence[index].point_list.length + 1; j++) {
          for (let i = 0; i < this.fence[index].point_list.length; i++) {
            if (this.fence[index].point_list[i].seq == j) {
              let paths = pointsConvert({
                point: [
                  this.fence[index].point_list[i].lon_int / 1e7,
                  this.fence[index].point_list[i].lat_int / 1e7,
                ],
                type: this.fence[index].point_list[i].type,
              });
              let point = {
                lat: paths[1],
                lng: paths[0],
                height: 0,
              };
              strPoint.push(point);
              break;
            }
          }
        }
        this.fencePaths[this.fence[index].f_id] = strPoint;
        let polygon = mapMethods.drawPolygon(strPoint);
        let entity = this.map.entities.getById(this.fence[index].f_id);
        if (entity) {
          entity.polygon = polygon;
          entity.show = true;
        } else {
          this.map.entities.add({
            id: this.fence[index].f_id,
            polygon: polygon,
          });
        }
      }
    },
    //点击具体多边形
    getFenceItem() {
      let fenceItem = this.map.entities.getById(this.fenceId);
      var polyPositions = fenceItem.polygon.hierarchy.getValue().positions;
      var polyCenter = Cesium.BoundingSphere.fromPoints(polyPositions).center;
      polyCenter = Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polyCenter);
      let cartographic = Cesium.Cartographic.fromCartesian(
        polyCenter,
        this.map.scene.globe.ellipsoid,
        new Cesium.Cartographic()
      );
      let lat = Cesium.Math.toDegrees(cartographic.latitude);
      let lng = Cesium.Math.toDegrees(cartographic.longitude);
      // this.map.scene.camera.flyTo({
      //   destination: Cesium.Cartesian3.fromDegrees(lng, lat, 10000),
      //   duration: 1,
      //   maximumHeight: 2000,
      //   pitchAdjustHeight: 20,
      //   orientation: {
      //     heading: Cesium.Math.toRadians(0),
      //     pitch: Cesium.Math.toRadians(-90),
      //     roll: Cesium.Math.toRadians(0),
      //   },
      // });
      let entity = this.map.entities.getById(this.fenceId);
      this.map.zoomTo(
        entity,
        new Cesium.HeadingPitchRange(
          Cesium.Math.toRadians(90),
          Cesium.Math.toRadians(-90),
          Cesium.Math.toRadians(0)
        )
      );
      this.fence.forEach((item) => {
        if (item.f_id !== this.fenceId) {
          let x = this.map.entities.getById(item.f_id);
          x.show = false;
        } else {
          this.fenceItem.title = item.title;
          this.fenceItem.id = this.fenceId;
          this.fenceItem.height = item.height_limit / 100;
          this.fenceItem.paths = this.fencePaths[this.fenceId];
        }
      });
      Object.keys(this.fencePaths).forEach((item) => {
        if (item !== this.fenceId) {
          let x = this.map.entities.getById(item);
          if (x && x.show) {
            x.show = false;
          }
        }
      });

      // this.map.scene.camera.setView({
      //   destination:fenceItem
      // })
    },
    //绘制操作围栏
    drawFencePolygon() {
      let self = this;
      let polygon = mapMethods.drawPolygon([], { noHavePaths: true });
      polygon.hierarchy = new Cesium.CallbackProperty(function () {
        let arrPoint = new Cesium.PolygonHierarchy(self.linearr);
        return arrPoint;
      }, false);
      this.operateEntrty = this.map.entities.add({
        id: "operateEntrty",
        polygon: polygon,
      });
    },
    //绘制操作航线
    drawRouteLine() {
      let self = this;
      let polyLine = mapMethods.drawLine([], { noHavePaths: true });
      polyLine.positions = new Cesium.CallbackProperty(function () {
        return self.linearr;
      }, false);
      this.operateEntrty = this.map.entities.add({
        id: "operateEntrty",
        polyline: polyLine,
      });
    },
    //点击绘制航线
    getRouteItem() {
      if (this.routeItem) {
        for (let index = 0; index < this.markerPoint.length; index++) {
          this.map.entities.removeById(index + 1);
        }
        this.markerPoint = [];
        this.linearr = [];
        let pointArr = [];
        this.num = 0;
        for (let index = 0; index < this.routeItem.point_list.length; index++) {
          if (this.routeItem.point_list[index].seq == index + 1) {
            let paths = pointsConvert({
              point: [
                this.routeItem.point_list[index].lon_int / 1e7,
                this.routeItem.point_list[index].lat_int / 1e7,
              ],
              type: this.routeItem.point_list[index].type,
            });
            let point = {
              lat: paths[1],
              lng: paths[0],
              height: this.routeItem.point_list[index].height / 100,
            };
            pointArr.push(point);
          }
        }
        if (this.routeItem.typeCode) {
          this.changeCode = true;
          // let lng = pointArr[0].lng;
          // let lat = pointArr[0].lat;
          // this.map.scene.camera.flyTo({
          //   destination: Cesium.Cartesian3.fromDegrees(lng, lat, 10000),
          //   duration: 1,
          //   maximumHeight: 2000,
          //   pitchAdjustHeight: 20,
          //   orientation: {
          //     heading: Cesium.Math.toRadians(0),
          //     pitch: Cesium.Math.toRadians(-90),
          //     roll: Cesium.Math.toRadians(0),
          //   },
          // });
          if (this.routeItem.type == 50) {
            let cameraParam = JSON.parse(this.routeItem.camera_json);
            this.routeItemParam = {
              default_height: this.routeItem.default_height / 100,
              cameraParamList: cameraParam.cameraParamList,
              course: cameraParam.course,
              lateral: cameraParam.lateral,
              angle: cameraParam.angle,
              wheelDist: cameraParam.wheelDist,
            };
            this.loadingPoint1(pointArr);
          } else {
            this.loadingPoint(pointArr);
          }
        } else {
          if (this.routeItem.type == 50) {
            let cameraParam = JSON.parse(this.routeItem.camera_json);
            this.routeItemParam = {
              default_height: this.routeItem.default_height / 100,
              cameraParamList: cameraParam.cameraParamList,
              course: cameraParam.course,
              lateral: cameraParam.lateral,
              angle: cameraParam.angle,
              wheelDist: cameraParam.wheelDist,
              auto_speed: this.routeItem.auto_speed / 100,
            };
            this.orthoarr = [];
            pointArr.forEach((point) => {
              this.showPointOrthoEntrty(point);
            });
          } else {
            pointArr.forEach((point) => {
              this.showPointRouteEntrty(point);
            });
            this.computedData(this.routeItem.auto_speed / 100);
            this.$store.commit("setFirstPoint", pointArr[0]);
            this.$store.commit("setLastPoint", pointArr[pointArr.length - 1]);
          }
        }
      } else {
        this.clearEntity();
      }
    },
    //绘制显示的线与点
    showPointRouteEntrty(point) {
      this.num++;
      point.id = this.num;
      let marker = mapMethods.drawPointLabel(point, {
        text: this.num,
        id: this.num,
        className: "marker-route",
      });
      this.map.entities.add(marker);
      this.markerPoint.push(point);
      if (this.operateEntrty) {
        this.linearr[this.linearr.length] = new Cesium.Cartesian3.fromDegrees(
          point.lng,
          point.lat,
          point.height ? point.height : this.defaultHeight
        );
      } else {
        this.linearr.push(
          new Cesium.Cartesian3.fromDegrees(
            point.lng,
            point.lat,
            point.height ? point.height : this.defaultHeight
          )
        );
        if (this.linearr.length < 1) {
          return false;
        }
        this.drawRouteLine();
      }
      let operateItem = this.map.entities.getById("operateEntrty");
      if (!this.drawend && operateItem) {
        this.map.zoomTo(operateItem);
      }
      if (this.code == 1) {
        this.computedData(this.$refs.routeEdit.routeForm.auto_speed);
      }
    },
    //绘制已有的点
    async loadingPoint(pointArr) {
      if (this.num < pointArr.length) {
        await this.drawPointRouteEntrty(pointArr[this.num]);
        // this.num++;
        // await this.drawMarker(this.num, this.routePoints[this.num - 1]);
        // if (this.num > 1) {
        //   this.drawline();
        //   this.addCenterpoint(this.num);
        // }
        setTimeout(() => {
          this.loadingPoint(pointArr);
        });
      } else {
        this.loading = false;
        this.changeCode = false;
        this.routeFormCode = false;
        this.drawMark();
        this.deepCopy();
      }
    },
    //绘制点和线
    drawPointRouteEntrty(point, isCheck, importCode) {
      if (isCheck) {
        let isJudge = this.judgeInArea(point, this.num, true);
        if (!isJudge) {
          return false;
        }
      }
      this.showPointRouteEntrty(point);
      if (!this.changeCode) {
        this.changePoint = {
          index: this.num - 1,
          lat: parseFloat(point.lat.toFixed(7)),
          lng: parseFloat(point.lng.toFixed(7)),
          height: point.height || point.height == 0 ? point.height : "",
          action: point.action && point.action.length ? point.action : "",
          type: importCode ? "import" : "add",
        };
      }
      this.clickMarker(this.num);
      if (this.markerPoint.length > 1) {
        this.routeCenter();
        let point1 = this.markerPoint[this.markerPoint.length - 1];
        let point2 = this.markerPoint[this.markerPoint.length - 2];
        this.computerElevationRoute(
          point1,
          point2,
          this.default_distance,
          this.num - 1
        );
        this.clearElevationCode = 0;
      }
      setTimeout(() => {
        this.deepCopy();
      });
      return true;
    },
    //判断航点航线是否在围栏内
    judgeInArea(point, index, lastPoint) {
      let isArea = computedMapMethods("pointInPolygon", {
        point: [point.lng, point.lat],
        polygon: this.fencePaths[this.fenceId],
      });
      if (!isArea) {
        !lastPoint && this.returnToInitial();
        this.$message.error({
          message: this.routeLanguage.placeholder4,
          customClass: "message-info-tip",
        });
        return false;
      }
      let len = lastPoint ? 0 : 1;
      if (this.code == 3) {
        if (this.markerPoint.length > len) {
          let isCross = false;
          if (lastPoint) {
            let a = computedMapMethods("lineCross", {
              point,
              point1: this.markerPoint[this.num - 1],
              fence: this.fencePaths[this.fenceId],
              type: true,
            });
            let b = false;
            if (this.markerPoint.length > 1) {
              b = computedMapMethods("lineCross", {
                point,
                point1: this.markerPoint[0],
                fence: this.fencePaths[this.fenceId],
                type: true,
              });
            }
            isCross = a || b;
          } else {
            let beforePoint = "";
            let afterPoint = "";
            let a = false;
            let b = false;
            if (index == 0) {
              afterPoint = this.markerPoint[index + 1];
              if (this.markerPoint.length > 2) {
                beforePoint = this.markerPoint[this.markerPoint.length - 1];
              }
            } else {
              beforePoint = this.markerPoint[index - 1];
              if (index < this.markerPoint.length - 1) {
                afterPoint = this.markerPoint[index + 1];
              } else {
                afterPoint = this.markerPoint[0];
              }
            }
            if (beforePoint) {
              a = computedMapMethods("lineCross", {
                point,
                point1: beforePoint,
                fence: this.fencePaths[this.fenceId],
                type: true,
              });
            }
            if (afterPoint) {
              b = computedMapMethods("lineCross", {
                point,
                point1: afterPoint,
                fence: this.fencePaths[this.fenceId],
                type: true,
              });
            }
            isCross = a || b;
          }
          if (isCross) {
            !lastPoint && this.returnToInitial();
            this.$message.error({
              message: this.routeLanguage.placeholder7,
              customClass: "message-info-tip",
            });
            return false;
          }
        }
      } else {
        if (this.markerPoint.length > len) {
          let isCross = false;
          if (lastPoint) {
            isCross = computedMapMethods("lineCross", {
              point,
              point1: this.markerPoint[this.num - 1],
              fence: this.fencePaths[this.fenceId],
              type: true,
            });
          } else {
            let beforePoint = "";
            let afterPoint = "";
            let a = false;
            let b = false;
            if (index == 0) {
              afterPoint = this.markerPoint[index + 1];
            } else {
              beforePoint = this.markerPoint[index - 1];
              if (index < this.markerPoint.length - 1) {
                afterPoint = this.markerPoint[index + 1];
              }
            }
            if (beforePoint) {
              a = computedMapMethods("lineCross", {
                point,
                point1: beforePoint,
                fence: this.fencePaths[this.fenceId],
                type: true,
              });
            }
            if (afterPoint) {
              b = computedMapMethods("lineCross", {
                point,
                point1: afterPoint,
                fence: this.fencePaths[this.fenceId],
                type: true,
              });
            }
            isCross = a || b;
          }
          if (isCross) {
            !lastPoint && this.returnToInitial();
            this.$message.error({
              message: this.routeLanguage.placeholder5,
              customClass: "message-info-tip",
            });
            return false;
          }
        }
      }
      return true;
    },
    //绘制航线中点的中心点
    routeCenter() {
      let center = computedMapMethods("computeCenter", {
        lnglatArr: [
          this.markerPoint[this.markerPoint.length - 1],
          this.markerPoint[this.markerPoint.length - 2],
        ],
      });
      let id = "center" + (this.markerPoint.length - 1);
      let addMarker = mapMethods.drawPointLabel(center, {
        text: "+",
        id,
        className: "marker-route-center",
      });
      this.map.entities.add(addMarker);
    },
    //拖拽修改中心点
    dragEditRouteCenter(index) {
      if (this.markerPoint.length < 2) {
        return false;
      }
      let beforeCenter = "";
      let afterCenter = "";
      if (index == 0) {
        afterCenter = computedMapMethods("computeCenter", {
          lnglatArr: [this.markerPoint[index], this.markerPoint[index + 1]],
        });
      } else {
        beforeCenter = computedMapMethods("computeCenter", {
          lnglatArr: [this.markerPoint[index - 1], this.markerPoint[index]],
        });
        if (index < this.markerPoint.length - 1) {
          afterCenter = computedMapMethods("computeCenter", {
            lnglatArr: [this.markerPoint[index], this.markerPoint[index + 1]],
          });
        }
      }
      if (beforeCenter) {
        let beforeEntity = this.map.entities.getById("center" + index);
        beforeEntity.position = new Cesium.Cartesian3.fromDegrees(
          beforeCenter.lng,
          beforeCenter.lat,
          beforeCenter.height
        );
      }
      if (afterCenter) {
        let afterEntity = this.map.entities.getById("center" + (index + 1));
        afterEntity.position = new Cesium.Cartesian3.fromDegrees(
          afterCenter.lng,
          afterCenter.lat,
          afterCenter.height
        );
      }
    },
    //点击修改中心点
    editRouteCenter(index) {
      for (let i = index; i < this.markerPoint.length - 1; i++) {
        let center = computedMapMethods("computeCenter", {
          lnglatArr: [this.markerPoint[i - 1], this.markerPoint[i]],
        });
        let entity = this.map.entities.getById("center" + i);
        entity.position = new Cesium.Cartesian3.fromDegrees(
          center.lng,
          center.lat,
          center.height
        );
      }
      this.routeCenter();
      this.reElevation();
    },
    //航线点击事件
    clickEvent(e, pickedLabel) {
      if (
        Cesium.defined(pickedLabel) &&
        pickedLabel.id.point &&
        pickedLabel.id.label
      ) {
        let id = pickedLabel.id.id.toString();
        if (id.includes("center")) {
          this.clickCenterEvent(pickedLabel.id.id);
        }
        return false;
      }
      if (!Cesium.defined(pickedLabel)) {
        this.$message.error({
          message: this.routeLanguage.placeholder4,
          customClass: "message-info-tip",
        });
        return false;
      }
      let point = mapMethods.changeLatLng(e.position, this.map);
      point.height = this.$refs.routeEdit.routeForm.default_height;
      this.drawPointRouteEntrty(point, true);
    },
    //输入经纬度航线改变
    changeRouteMarker(point) {
      let entity = this.map.entities.getById(point.index + 1);
      entity.position = new Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        point.height
      );
      this.markerPoint[point.index] = {
        lat: point.lat,
        lng: point.lng,
        height: point.height,
      };
      let cartesian3 = new Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        point.height
      );
      this.linearr[point.index] = cartesian3;
      this.clickMarker(point.index + 1);
      this.dragEditRouteCenter(point.index);
      this.deepCopy();
      this.changeElevation(point.index, this.markerPoint[point.index]);
    },
    //计算总距离和总时间
    computedData(auto_speed) {
      this.distance = 0;
      for (let index = 0; index < this.markerPoint.length - 1; index++) {
        this.distance += computedMapMethods("computedDistance", {
          point1: this.markerPoint[index],
          point2: this.markerPoint[index + 1],
        });
      }
      this.estTime = this.distance / auto_speed + this.markerPoint.length;
      this.$store.commit("setEstTime", this.estTime);
    },
    //点的点击事件
    clickMarker(id) {
      if (this.clickCode === id) {
        return false;
      }
      let originColor = "#ffffff";
      let originFillColor = "#0092f8";
      let color = "#0c1865";
      let fillColor = "#ffffff";
      if (this.code == 2) {
        originColor = "#ffffff";
        originFillColor = "#000000";
        color = "#004310";
        fillColor = "#ffffff";
      }
      if (this.clickCode) {
        let markerBefor = this.map.entities.getById(this.clickCode);
        if (markerBefor) {
          markerBefor.point.color = new Cesium.Color.fromCssColorString(
            originColor
          );
          markerBefor.label.fillColor = new Cesium.Color.fromCssColorString(
            originFillColor
          );
        }
      }
      let marker = this.map.entities.getById(id);
      if (marker) {
        marker.point.color = new Cesium.Color.fromCssColorString(color);
        marker.label.fillColor = new Cesium.Color.fromCssColorString(fillColor);
        this.clickCode = id;
      }
    },
    //围栏点击事件
    clickFence(e, pickedLabel) {
      if (
        Cesium.defined(pickedLabel) &&
        pickedLabel.id.point &&
        pickedLabel.id.label
      ) {
        let id = pickedLabel.id.id.toString();
        if (id.includes("center")) {
          this.clickCenterEvent(pickedLabel.id.id);
        }
        return false;
      }
      let point = mapMethods.changeLatLng(e.position, this.map);
      point.height = 1;
      this.drawFenceEntrty(point);
    },
    drawFenceEntrty(point) {
      if (this.markerPoint.length > 2) {
        let isCross = this.judgeCross({ point: point });
        if (isCross) {
          this.$message.warning({
            message: this.routeLanguage.messageInfo2,
            duration: 1000,
          });
          return false;
        }
      }
      this.num++;
      point.id = this.num;
      point.height = 1;
      this.markerPoint.push(point);
      let marker = mapMethods.drawPointLabel(point, {
        text: this.num,
        id: this.num,
        className: "marker-fence",
      });
      this.map.entities.add(marker);
      if (!this.changeCode) {
        this.changePoint = {
          index: this.num - 1,
          lat: parseFloat(point.lat.toFixed(7)),
          lng: parseFloat(point.lng.toFixed(7)),
          type: "add",
        };
      }

      this.clickMarker(this.num);
      if (this.markerPoint.length > 1) {
        this.fenceOrthoCenter();
      }
      if (this.operateEntrty) {
        this.linearr[this.linearr.length] = new Cesium.Cartesian3.fromDegrees(
          point.lng,
          point.lat,
          point.height ? point.height : this.defaultHeight
        );
      } else {
        this.linearr.push(
          new Cesium.Cartesian3.fromDegrees(
            point.lng,
            point.lat,
            point.height ? point.height : this.defaultHeight
          )
        );
        if (this.linearr.length < 1) {
          return true;
        }
        this.drawFencePolygon();
      }
      setTimeout(() => {
        this.deepCopy();
      });
      return true;
    },
    //围栏/正射影像中心点
    fenceOrthoCenter() {
      let className =
        this.code == 2 ? "marker-fence-center" : "marker-route-center";
      let center = computedMapMethods("computeCenter", {
        lnglatArr: [
          this.markerPoint[this.markerPoint.length - 1],
          this.markerPoint[this.markerPoint.length - 2],
        ],
      });
      let id = "center" + (this.markerPoint.length - 1);
      let entity = this.map.entities.getById(id);
      if (entity) {
        let cartesian1 = new Cesium.Cartesian3.fromDegrees(
          center.lng,
          center.lat,
          center.height
        );
        entity.position = cartesian1;
      } else {
        let addMarker = mapMethods.drawPointLabel(center, {
          text: "+",
          id,
          className: className,
        });
        this.map.entities.add(addMarker);
      }
      if (this.markerPoint.length > 2) {
        let center1 = computedMapMethods("computeCenter", {
          lnglatArr: [
            this.markerPoint[this.markerPoint.length - 1],
            this.markerPoint[0],
          ],
        });
        let addMarker = mapMethods.drawPointLabel(center1, {
          text: "+",
          id: "center" + this.markerPoint.length,
          className: className,
        });
        this.map.entities.add(addMarker);
      }
    },
    //点击中心点
    clickCenterEvent(id) {
      if (this.clickCode) {
        if (this.code == 2) {
          let markerBefor = this.map.entities.getById(this.clickCode);
          markerBefor.point.color = new Cesium.Color.fromCssColorString(
            "#ffffff"
          );
          markerBefor.label.fillColor = new Cesium.Color.fromCssColorString(
            "#000000"
          );
        } else {
          let markerBefor = this.map.entities.getById(this.clickCode);
          markerBefor.point.color = new Cesium.Color.fromCssColorString(
            "#ffffff"
          );
          markerBefor.label.fillColor = new Cesium.Color.fromCssColorString(
            "#0092f8"
          );
        }
        this.clickCode = "";
      }
      let entity = this.map.entities.getById(id);
      var cartographic = Cesium.Cartographic.fromCartesian(
        entity.position._value
      );
      //弧度转经纬度
      let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
      let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
      this.num++;
      let index = parseInt(entity.id.replace("center", ""));
      let point = {
        lng,
        lat,
        height: 1,
        id: index + 1,
      };
      if (this.code == 1) {
        point.height = this.$refs.routeEdit.routeForm.default_height;
      } else if (this.code == 2) {
        point.height = 1;
      } else {
        point.height = this.$refs.orthoEdit.orthoForm.default_height;
      }
      this.markerPoint.splice(index, 0, point);
      let markers = [];
      for (let i = index + 1; i < this.markerPoint.length; i++) {
        let entityItem = this.map.entities.getById(i);
        // entity.id=index
        entityItem.label.text = (i + 1).toString();
        let marker = {
          id: i + 1,
          position: entityItem.position,
          point: entityItem.point,
          label: entityItem.label,
        };
        markers.push(marker);
        this.map.entities.remove(entityItem);
        this.markerPoint[i].id = i + 1;
      }
      markers.forEach((marker) => {
        this.map.entities.add(marker);
      });
      let className = this.code == 2 ? "marker-fence" : "marker-route";
      let marker = mapMethods.drawPointLabel(point, {
        text: index + 1,
        id: index + 1,
        className: className,
      });
      this.map.entities.add(marker);
      if (this.code == 1) {
        this.editRouteCenter(index);
      } else {
        this.editFenceOrthoCenter(index);
      }
      let cartesian3 = new Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        point.height ? point.height : this.defaultHeight
      );
      this.linearr.splice(index, 0, cartesian3);
      this.changePoint = {
        index: index,
        lat: point.lat.toFixed(7),
        lng: point.lng.toFixed(7),
        type: "editAdd",
      };

      this.clickMarker(index + 1);
      setTimeout(() => {
        this.deepCopy();
      });
    },
    //点击修改中心点,围栏和正射影像
    editFenceOrthoCenter(index) {
      let className =
        this.code == 2 ? "marker-fence-center" : "marker-route-center";
      for (let i = index; i < this.markerPoint.length; i++) {
        let center = computedMapMethods("computeCenter", {
          lnglatArr: [this.markerPoint[i - 1], this.markerPoint[i]],
        });
        let entity = this.map.entities.getById("center" + i);
        entity.position = new Cesium.Cartesian3.fromDegrees(
          center.lng,
          center.lat,
          center.height
        );
      }
      let center1 = computedMapMethods("computeCenter", {
        lnglatArr: [
          this.markerPoint[this.markerPoint.length - 1],
          this.markerPoint[0],
        ],
      });
      let addMarker = mapMethods.drawPointLabel(center1, {
        text: "+",
        id: "center" + this.markerPoint.length,
        className: className,
      });
      this.map.entities.add(addMarker);
    },
    //拖拽时修改中心点,围栏和正射影像
    dragEditFenceOrthoCenter() {
      if (this.markerPoint.length < 2) {
        return false;
      }
      let beforeCenter = "";
      let AfterCenter = "";
      if (this.clickCode == 1) {
        beforeCenter = computedMapMethods("computeCenter", {
          lnglatArr: [
            this.markerPoint[this.clickCode - 1],
            this.markerPoint[this.markerPoint.length - 1],
          ],
        });
        AfterCenter = computedMapMethods("computeCenter", {
          lnglatArr: [
            this.markerPoint[this.clickCode - 1],
            this.markerPoint[this.clickCode],
          ],
        });
      } else if (this.clickCode == this.markerPoint.length) {
        beforeCenter = computedMapMethods("computeCenter", {
          lnglatArr: [
            this.markerPoint[this.clickCode - 1],
            this.markerPoint[this.clickCode - 2],
          ],
        });
        AfterCenter = computedMapMethods("computeCenter", {
          lnglatArr: [
            this.markerPoint[this.clickCode - 1],
            this.markerPoint[0],
          ],
        });
      } else {
        beforeCenter = computedMapMethods("computeCenter", {
          lnglatArr: [
            this.markerPoint[this.clickCode - 1],
            this.markerPoint[this.clickCode - 2],
          ],
        });
        AfterCenter = computedMapMethods("computeCenter", {
          lnglatArr: [
            this.markerPoint[this.clickCode - 1],
            this.markerPoint[this.clickCode],
          ],
        });
      }
      let beforeIndex =
        this.clickCode == 1 ? this.markerPoint.length : this.clickCode - 1;
      let beforeEntity = this.map.entities.getById("center" + beforeIndex);
      let afterEntity = this.map.entities.getById("center" + this.clickCode);
      beforeEntity.position = new Cesium.Cartesian3.fromDegrees(
        beforeCenter.lng,
        beforeCenter.lat,
        beforeCenter.height
      );
      afterEntity.position = new Cesium.Cartesian3.fromDegrees(
        AfterCenter.lng,
        AfterCenter.lat,
        AfterCenter.height
      );
    },
    //判断是否交叉
    judgeCross({ isSaved, point }) {
      if (isSaved) {
        //操作点与前一个点形成的线判断
        let arr = [];
        let arr1 = [];
        let point1 = this.markerPoint[this.markerPoint.length - 1];
        if (this.clickCode == 1) {
          arr1 = this.markerPoint.slice(
            this.clickCode,
            this.markerPoint.length - 1
          );
        } else {
          arr = this.markerPoint.slice(0, this.clickCode - 2);
          point1 = this.markerPoint[this.clickCode - 2];
          arr1 = this.markerPoint.slice(
            this.clickCode,
            this.markerPoint.length
          );
        }
        let arrconcat = [...arr1, ...arr];
        let isCross = false;
        if (arrconcat.length > 1) {
          isCross = computedMapMethods("lineCross", {
            point,
            point1,
            fence: arrconcat,
          });
          if (isCross) {
            return true;
          }
        }
        //操作点与后一个点形成的线判断
        let arr2 = [];
        let point2 = "";
        if (this.clickCode == this.markerPoint.length) {
          arr2 = this.markerPoint.slice(1, this.clickCode - 1);
          point2 = this.markerPoint[0];
        } else {
          let arr3 = this.markerPoint.slice(0, this.clickCode - 1);
          point2 = this.markerPoint[this.clickCode];
          let arr4 = [];
          if (this.clickCode + 1 < this.markerPoint.length) {
            arr4 = this.markerPoint.slice(
              this.clickCode + 1,
              this.markerPoint.length
            );
          }
          arr2 = [...arr4, ...arr3];
        }
        let isCross1 = false;
        if (arrconcat.length > 1) {
          isCross1 = computedMapMethods("lineCross", {
            point,
            point1: point2,
            fence: arr2,
          });
          if (isCross1) {
            return true;
          }
        }
        return false;
      } else {
        let arr = this.markerPoint.slice(1, this.markerPoint.length);
        let isCross = computedMapMethods("lineCross", {
          point,
          point1: this.markerPoint[0],
          fence: arr,
        });
        if (isCross) {
          return true;
        }
        let arr1 = this.markerPoint.slice(0, this.markerPoint.length - 1);
        let isCross1 = computedMapMethods("lineCross", {
          point,
          point1: this.markerPoint[this.markerPoint.length - 1],
          fence: arr1,
        });
        if (isCross1) {
          return true;
        }
        return false;
      }
    },
    //围栏输入框返回
    changeFenceMarker(point) {
      let entity = this.map.entities.getById(point.index + 1);
      entity.position = new Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        1
      );
      this.markerPoint[point.index].lng = point.lng;
      this.markerPoint[point.index].lat = point.lat;
      let cartesian3 = new Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        point.height ? point.height : this.defaultHeight
      );
      this.linearr[point.index] = cartesian3;
      this.clickMarker(point.index + 1);
      this.dragEditFenceOrthoCenter();
      this.deepCopy();
    },
    //操作围栏：编辑和删除
    async operateItem() {
      if (this.operateFItem) {
        this.fence.forEach((fenceItem) => {
          let entity = this.map.entities.getById(fenceItem.f_id);
          entity.show = false;
        });
        this.map.entities.removeById(this.operateFItem.f_id);
        if (this.operateFItem.code == 1) {
          this.loading = true;
          let lng = this.operateFItem.lon_int / 1e7;
          let lat = this.operateFItem.lat_int / 1e7;
          this.map.scene.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(lng, lat, 10000),
            duration: 1,
            maximumHeight: 2000,
            pitchAdjustHeight: 20,
            orientation: {
              heading: Cesium.Math.toRadians(0),
              pitch: Cesium.Math.toRadians(-90),
              roll: Cesium.Math.toRadians(0),
            },
          });
          let point_json = this.fencePaths[this.operateFItem.f_id];
          this.changeCode = true;
          await this.loadingPoint2(point_json);
        } else {
          this.$store.commit("operateFItem", "");
        }
      }
    },
    //围栏:绘制已有的点
    async loadingPoint2(point_json) {
      if (this.num < point_json.length) {
        await this.drawFenceEntrty(point_json[this.num]);
        setTimeout(() => {
          this.loadingPoint2(point_json);
        });
      } else {
        this.loading = false;
        this.changeCode = false;
        this.routeFormCode = false;
        this.drawMark();
        this.deepCopy();
      }
    },
    //正射影像：绘制已有的点
    async loadingPoint1(pointArr) {
      if (this.num < pointArr.length) {
        await this.drawOrthoEntrty(pointArr[this.num], false, true);
        setTimeout(() => {
          this.loadingPoint1(pointArr);
        });
      } else {
        this.loading = false;
        this.changeCode = false;
        this.routeFormCode = false;
        this.drawMark();
        this.timer = setTimeout(() => {
          this.drawOrthoInPolyLine();
        });
        this.deepCopy();
      }
    },
    //正射影像点击事件
    clickOrtho(e, pickedLabel) {
      if (
        Cesium.defined(pickedLabel) &&
        pickedLabel.id.point &&
        pickedLabel.id.label
      ) {
        let id = pickedLabel.id.id.toString();
        if (id.includes("center")) {
          this.clickCenterEvent(pickedLabel.id.id);
        }
        return false;
      }
      let point = mapMethods.changeLatLng(e.position, this.map);
      point.height = this.$refs.orthoEdit.orthoForm.default_height;
      this.drawOrthoEntrty(point, true);
    },
    //绘制显示的线与点，正射影像
    showPointOrthoEntrty(point, orthoInCode) {
      this.num++;
      point.id = this.num;
      this.markerPoint.push(point);
      let marker = mapMethods.drawPointLabel(point, {
        text: this.num,
        id: this.num,
        className: "marker-route",
      });
      this.map.entities.add(marker);
      if (this.operateEntrty) {
        this.linearr[this.linearr.length] = new Cesium.Cartesian3.fromDegrees(
          point.lng,
          point.lat,
          point.height ? point.height : this.defaultHeight
        );
      } else {
        this.linearr.push(
          new Cesium.Cartesian3.fromDegrees(
            point.lng,
            point.lat,
            point.height ? point.height : this.defaultHeight
          )
        );
        if (this.linearr.length < 1) {
          return false;
        }
        this.drawFencePolygon();
      }
      if (!this.drawend && this.operateEntrty) {
        this.map.zoomTo(this.operateEntrty);
      }
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      if (!orthoInCode) {
        this.timer = setTimeout(() => {
          this.drawOrthoInPolyLine();
        });
      }
      if (this.markerPoint.length > 2) {
        this.area = computedMapMethods("computedArea", {
          positions: this.markerPoint,
        });
      }
    },
    //正射影像绘制
    drawOrthoEntrty(point, isCheck, orthoIncode) {
      if (isCheck) {
        //判断点跟线是否在围栏内
        let isJudge = this.judgeInArea(point, this.num, true);
        if (!isJudge) {
          return false;
        }
        if (this.markerPoint.length > 2) {
          let isCross = this.judgeCross({ point: point });
          if (isCross) {
            this.$message.warning({
              message: this.routeLanguage.messageInfo3,
              duration: 1000,
            });
            return false;
          }
        }
      }

      this.showPointOrthoEntrty(point, orthoIncode);
      if (!this.changeCode) {
        this.changePoint = {
          index: this.num - 1,
          lat: parseFloat(point.lat.toFixed(7)),
          lng: parseFloat(point.lng.toFixed(7)),
          type: "add",
        };
      }
      this.clickMarker(this.num);
      if (this.markerPoint.length > 1) {
        this.fenceOrthoCenter();
      }
      setTimeout(() => {
        this.deepCopy();
      });
    },
    //绘制内置正射影像航线
    drawOrthoInPolyLine() {
      if (this.markerPoint.length > 2) {
        let ortho =
          this.code == 3 ? this.$refs.orthoEdit.orthoForm : this.routeItemParam;
        let cameraParamList =
          this.code == 3
            ? this.$refs.orthoEdit.cameraParamList
            : this.routeItemParam.cameraParamList;
        let params = {
          default_height: ortho.default_height,
          cameraParamList: cameraParamList,
          course: ortho.course,
          lateral: ortho.lateral,
          angle: ortho.angle,
          wheelDist: ortho.wheelDist,
          paths: this.markerPoint,
        };
        let { points, triggerDist } = orthoPhotoComputer(
          params,
          this.map,
          this.leafletMap,
          1
        );
        this.orthoAllPoints = [];
        if (!(points && points.length)) {
          this.orthoarr = [];
          this.distance = 0;
          this.estTime = 0;
          this.photoCount = 0;
          this.routeSpotCount = 0;
          if (!this.messageCodeWarning) {
            this.$message.warning(this.routeLanguage.errorMessage9);
            this.messageCodeWarning = true;
          }
          return false;
        }
        this.messageCodeWarning = false;
        let arr = points.map((point) => {
          return new Cesium.Cartesian3.fromDegrees(
            point.lng,
            point.lat,
            ortho.default_height
          );
        });
        this.orthoarr = arr;
        if (!this.operateOrtho) {
          if (this.orthoarr.length > 0) {
            this.drawOrthoLine();
          }
        }
        this.$store.commit("setFirstPoint", points[0]);
        this.$store.commit("setLastPoint", points[points.length - 1]);
        this.computedContent(points, triggerDist);
        points.forEach((item) => {
          item.height = ortho.default_height;
          this.orthoAllPoints.push(item);
        });
        this.drawMarkerStartEnd(points[0], points[points.length - 1]);
        if (this.mouseupCode && !this.loading) {
          this.computedPoints(this.orthoAllPoints);
        }
      } else {
        this.orthoarr = [];
        this.area = 0;
        this.distance = 0;
        this.estTime = 0;
        this.photoCount = 0;
        this.routeSpotCount = 0;
        this.orthoAllPoints = [];
        this.clearElevationMarker();
      }
    },
    //绘制正射影像起始点和终点
    drawMarkerStartEnd(startPoint, endPoint) {
      let markerStart = this.map.entities.getById("markerStart");
      let markerEnd = this.map.entities.getById("markerEnd");
      if (markerStart) {
        markerStart.position = Cesium.Cartesian3.fromDegrees(
          startPoint.lng,
          startPoint.lat,
          startPoint.height
        );
      } else {
        let startMarker = mapMethods.drawPointLabel(startPoint, {
          text: "S",
          id: "markerStart",
          className: "marker-start-end",
        });
        this.map.entities.add(startMarker);
      }
      if (markerEnd) {
        markerEnd.position = Cesium.Cartesian3.fromDegrees(
          endPoint.lng,
          endPoint.lat,
          endPoint.height
        );
      } else {
        let endMarker = mapMethods.drawPointLabel(endPoint, {
          text: "E",
          id: "markerEnd",
          className: "marker-start-end",
        });
        this.map.entities.add(endMarker);
      }
    },
    computedContent(points, triggerDist) {
      this.distance = 0;
      for (let index = 0; index < points.length - 1; index++) {
        this.distance += computedMapMethods("computedDistance", {
          point1: points[index],
          point2: points[index + 1],
        });
      }
      let ortho =
        this.code == 3 ? this.$refs.orthoEdit.orthoForm : this.routeItemParam;
      this.estTime = this.distance / ortho.auto_speed + points.length;
      this.$store.commit("setEstTime", this.estTime);
      this.photoCount = parseInt(this.distance / triggerDist);
      this.routeSpotCount = points.length;
    },
    //计算正射影像高程
    computedPoints(points) {
      this.clearElevationMarker();
      if (!this.isComputeHight) {
        return false;
      }
      this.progress = 0;
      this.getElevationcode = true;
      computerElevationArray(
        points,
        120,
        this.progressFun,
        this.default_distance,
        this.map
      ).then((res) => {
        this.getElevationcode = false;
        this.zoomMaxHeight = this.getZoomMaxHeight(res);
        this.clearElevationMarker();
        for (let index = 0; index < res.length; index++) {
          this.elevationData[index + 1] = res[index];
          this.elevationPoints[index + 1] = [];
          this.elevationSuccess[index + 1] = false;
          if (this.showHeight && this.code == 3 && this.isComputeHight) {
            this.elevationTime[index + 1] = setTimeout(() => {
              this.drawElevationPoint(res[index].arr, index + 1);
            });
          } else {
            this.elevationSuccess[index + 1] = true;
          }
        }
      });
    },
    getZoomMaxHeight(res) {
      let maxArray = [];
      for (let index = 0; index < res.length; index++) {
        maxArray.push(
          Math.max.apply(
            Math,
            res[index].arr.map((item) => {
              return item.height;
            })
          )
        );
      }
      return Math.max(...maxArray) / 100;
    },
    //绘制正射影像航线
    drawOrthoLine() {
      let self = this;
      let polyLine = mapMethods.drawLine([], {
        noHavePaths: true,
        color: "#07ff0e",
        width: 3,
      });
      polyLine.positions = new Cesium.CallbackProperty(function () {
        return self.orthoarr;
      }, false);
      this.operateOrtho = this.map.entities.add({
        id: "operateOrtho",
        polyline: polyLine,
      });
    },
    //输入经纬度航线改变正射影像
    changeOrthoMarker(point) {
      let entity = this.map.entities.getById(point.index + 1);
      entity.position = new Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        point.height
      );
      this.markerPoint[point.index] = {
        lat: point.lat,
        lng: point.lng,
        height: point.height,
      };
      let cartesian3 = new Cesium.Cartesian3.fromDegrees(
        point.lng,
        point.lat,
        point.height
      );
      this.linearr[point.index] = cartesian3;
      this.clickMarker(point.index + 1);
      this.dragEditFenceOrthoCenter();
      setTimeout(() => {
        this.deepCopy();
        this.drawOrthoInPolyLine();
      });
    },
    mouseupEvent() {
      this.mouseupCode = true;
      this.drawOrthoInPolyLine();
    },
    //点击操作回调
    operateEvent(e) {
      this[e]();
    },
    //绘制编辑点击返回
    goBacks() {
      if (this.drawend) {
        this.drawMark();
      }
      this.clearEntity();
      this.$refs.planList.page = 1;
      this.changePoint = {
        index: 0,
        lat: 0,
        lng: 0,
        type: "",
      };
      if (this.code == 2) {
        this.$store.commit("changeCode", 0);
        this.$refs.planList.getFenceList();
        this.$store.commit("fenceItemId", "");
        this.$store.commit("operateFItem", "");
      } else {
        this.$store.commit("changeCode", 4);
        this.$store.commit("routeItem", "");
        this.$store.commit("checkType", "");
        this.$refs.planList.getMissionList();
        this.getFenceItem();
        this.clearElevationMarker();
      }
    },
    //速度改变
    changeSpeed(e) {
      this.estTime = this.distance / e + this.markerPoint.length;
    },
    //暂存上一次操作记录，便于撤回
    deepCopy() {
      if (
        !this.loading &&
        !this.routeFormCode &&
        !this.recallClick &&
        !this.importCode
      ) {
        let formData = {};
        switch (this.code) {
          case 1:
            formData = this.$refs.routeEdit.routeForm;
            break;
          case 2:
            formData = this.$refs.fenceEdit.fenceForm;
            break;
          case 3:
            formData = this.$refs.orthoEdit.orthoForm;
            break;

          default:
            break;
        }
        let paths = {
          markerPoints: JSON.parse(JSON.stringify(this.markerPoint)),
          formData: JSON.parse(JSON.stringify(formData)),
          clickCode: this.clickCode,
        };
        this.cacheData.push(paths);
      }
    },
    toolEvent(value) {
      this.clearToolEvent();
      if (this.choosed === value) {
        this.choosed = 0;
        return false;
      }
      if (this.drawend && value !== 10) {
        this.drawMark();
      }
      this.choosed = value;
      switch (value) {
        case 1:
          this.map._container.style.cursor = "pointer";
          this.drawStartSite();
          this.handler &&
            this.handler.setInputAction(
              this.changeSite,
              Cesium.ScreenSpaceEventType.MOUSE_MOVE
            );
          break;
        case 2:
        case 3:
        case 4:
        case 5:
          this.map._container.style.cursor = "crosshair";
          this.measureEntries.push({
            measureMarkers: [],
            measureEntry: "",
            entityId: Math.random().toString(36).substring(7),
            distance: 0,
          });
          this.drawMeasureTipLabel();
          this.handler.setInputAction(
            this.measureMove,
            Cesium.ScreenSpaceEventType.MOUSE_MOVE
          );
          break;
        case 10:
          setTimeout(() => {
            this.choosed = 0;
          }, 500);
        default:
          break;
      }
    },
    //计算当前的日期yyyyMMddhhmmss
    returnDate() {
      let time = new Date();
      time =
        time.getFullYear() +
        (time.getMonth() + 1 > 9
          ? time.getMonth() + 1
          : "0" + (time.getMonth() + 1)) +
        (time.getDate() > 9 ? time.getDate() : "0" + time.getDate()) +
        (time.getHours() > 9 ? time.getHours() : "0" + time.getHours()) +
        (time.getMinutes() > 9 ? time.getMinutes() : "0" + time.getMinutes()) +
        (time.getSeconds() > 9 ? time.getSeconds() : "0" + time.getSeconds());
      return time;
    },
    //赋值默认高度
    setDefaultHeight(code) {
      if (code == 1) {
        this.defaultHeight = this.$refs.routeEdit.routeForm.default_height;
      } else if (code == 2) {
        this.defaultHeight = 0;
      } else if (code == 3) {
        this.defaultHeight = this.$refs.orthoEdit.orthoForm.default_height;
      } else {
        this.defaultHeight = 0;
      }
    },
    //改变俯仰角
    changePitch(type) {
      this.upPitch();
      switch (type) {
        case "up":
          this.pitchLoop = setInterval(() => {
            if (this.pitch > 0) {
              this.pitch -= 5;
            } else {
              this.pitch = 0;
            }
            this.setCamera();
          }, 200);

          break;
        case "down":
          this.pitchLoop = setInterval(() => {
            if (this.pitch < 85) {
              this.pitch += 5;
            } else {
              this.pitch = 85;
            }
            this.setCamera();
          }, 200);

          break;
        case "left":
          this.pitchLoop = setInterval(() => {
            if (this.rotation > -360) {
              this.rotation -= 5;
            } else {
              this.rotation = 0;
            }
            this.setCamera();
          }, 200);

          break;
        case "right":
          this.pitchLoop = setInterval(() => {
            if (this.rotation < 360) {
              this.rotation += 5;
            } else {
              this.rotation = 360;
            }
            this.setCamera();
          }, 200);

          break;

        default:
          break;
      }
    },
    setCamera() {
      var camera = this.map.camera;
      let heading = camera.heading;
      let destination = camera.position;

      camera.setView({
        orientation: {
          destination: destination,
          heading: heading,
          pitch: Cesium.Math.toRadians(-this.pitch),
          roll: Cesium.Math.toRadians(this.rotation),
        },
      });
    },
    //鼠标抬起，停止改变
    upPitch() {
      if (this.pitchLoop) {
        clearInterval(this.pitchLoop);
        this.pitchLoop = "";
      }
    },
    resetPitch() {
      this.pitch = 90;
      this.rotation = 0;
    },
    //改变高程
    changeIntervalHeight(val) {
      this.default_distance = val;
      if (this.code == 1) {
        this.reElevation();
      } else {
        if (this.orthoAllPoints && this.orthoAllPoints.length) {
          this.computedPoints(this.orthoAllPoints);
        }
      }
    },
    //获取是否显示高程点
    showHeightEvent(val) {
      this.showHeight = val;
      if (val) {
        for (const key in this.elevationData) {
          if (
            this.elevationData[key] &&
            this.elevationData[key].arr &&
            this.elevationData[key].arr.length
          ) {
            this.drawElevationPoint(this.elevationData[key].arr, key);
          }
        }
      } else {
        this.clearElevationMarker(true);
      }
    },
    //是否计算高程
    isComputeHightEvent(val) {
      this.isComputeHight = val;
      if (this.code == 1) {
        this.reElevation();
      } else {
        if (!this.loading) {
          if (this.orthoAllPoints && this.orthoAllPoints.length) {
            this.computedPoints(this.orthoAllPoints);
          }
        }
      }
    },
    //计算两点间高程
    computerElevationRoute(point1, point2, distance, id) {
      if (!this.isComputeHight) {
        return false;
      }
      if (this.elevationTime[id]) {
        clearTimeout(this.elevationTime[id]);
      }
      if (this.elevationPoints[id] && this.elevationPoints[id].length) {
        this.delElevationEntity(id, this.elevationPoints[id].length);
      }
      this.elevationPoints[id] = [];
      this.elevationData[id] = [];
      this.elevationSuccess[id] = false;
      computerElevationPoints(
        point1,
        point2,
        120,
        this.progressFun,
        distance,
        this.map,
        id
      ).then((res) => {
        this.elevationData[id] = res;
        if (this.showHeight && this.isComputeHight) {
          this.elevationTime[id] = setTimeout(() => {
            this.drawElevationPoint(res.arr, id);
          });
        } else {
          this.elevationSuccess[id] = true;
        }
      });
    },
    //绘制高程点
    async drawElevationPoint(arr, id) {
      if (this.elevationPoints[id].length < arr.length) {
        let path = arr[this.elevationPoints[id].length].points;
        let point = Cesium.Cartesian3.fromDegrees(
          path.lng,
          path.lat,
          path.height ? path.height : 0
        );
        let marker = await mapMethods.drawPoint(point, {
          id:
            "marker-elevation" +
            id.toString() +
            (this.elevationPoints[id].length + 1),
          imageUrl: require("@/assets/img/routeplan/pldot1.png"),
          widthImg: 12,
          heightImg: 12,
        });
        this.elevationPoints[id].push(marker);
        this.map.entities.add(marker);
        this.elevationTime[id] = setTimeout(() => {
          this.drawElevationPoint(arr, id);
        }, 100);
      } else {
        this.elevationSuccess[id] = true;
      }
    },
    //判断是否需要改变计算高程
    changeElevation(index, point) {
      if (this.markerPoint.length > 1) {
        let before = index == 0 ? "" : this.markerPoint[index - 1];
        let after = this.markerPoint[index + 1]
          ? this.markerPoint[index + 1]
          : "";
        if (before) {
          this.computerElevationRoute(
            point,
            before,
            this.default_distance,
            index
          );
        }
        if (after) {
          this.computerElevationRoute(
            point,
            after,
            this.default_distance,
            index + 1
          );
        }
      }
    },
    //重新全部计算高程
    reElevation() {
      this.clearElevationMarker();
      if (!this.isComputeHight) {
        return false;
      }
      for (let index = 0; index < this.markerPoint.length - 1; index++) {
        let point1 = this.markerPoint[index];
        let point2 = this.markerPoint[index + 1];
        this.computerElevationRoute(
          point1,
          point2,
          this.default_distance,
          index + 1
        );
      }
    },
    //清除正在绘制的高程点
    clearElevation(index) {
      if (this.markerPoint.length > 1) {
        let before = index == 0 ? "" : true;
        let after = this.markerPoint[index + 1] ? true : "";
        if (before) {
          if (this.elevationTime[index]) {
            clearTimeout(this.elevationTime[index]);
          }
          if (
            this.elevationPoints[index] &&
            this.elevationPoints[index].length
          ) {
            this.delElevationEntity(index, this.elevationPoints[index].length);
          }
          this.elevationPoints[index] = [];
          this.elevationData[index] = [];
        }
        if (after) {
          if (this.elevationTime[index + 1]) {
            clearTimeout(this.elevationTime[index + 1]);
          }
          if (
            this.elevationPoints[index + 1] &&
            this.elevationPoints[index + 1].length
          ) {
            this.delElevationEntity(
              index + 1,
              this.elevationPoints[index + 1].length
            );
          }
          this.elevationPoints[index + 1] = [];
          this.elevationData[index + 1] = [];
        }
      }
    },
    delElevationEntity(id, length) {
      for (let index = 0; index < length; index++) {
        this.map.entities.removeById(
          "marker-elevation" + id.toString() + (index + 1).toString()
        );
      }
    },
    //清除高程点
    clearElevationMarker(code) {
      for (const key in this.elevationPoints) {
        if (this.elevationPoints[key] && this.elevationPoints[key].length) {
          if (this.elevationTime[key]) {
            clearTimeout(this.elevationTime[key]);
          }
          this.delElevationEntity(key, this.elevationPoints[key].length);
          this.elevationPoints[key] = [];
        }
      }
      if (!code) {
        this.elevationData = {};
      }
    },
    //高程计算进度
    progressFun(progress) {
      this.progress = progress;
    },
    //刷新正射影像
    refresh() {
      this.mouseupCode = false;
      this.drawOrthoInPolyLine();
    },
    openDeviceEvent(item) {
      this.planCode = item.m_id;
      if (
        !this.$refs.deviceChoose.successCode &&
        !this.$refs.deviceChoose.sendSuccess
      ) {
        this.$refs.deviceChoose.openChoose();
      } else {
        this.$message.warning({
          message: this.routeLanguage.routeLine.uploading,
          customClass: "message-info-tip",
        });
        this.planCode = 0;
      }
    },
    //地址提示
    async searchSiteTip(queryString, callback) {
      if (queryString) {
        await searchTip1(this.searchSite).then((res) => {
          if (res.data.pois) {
            this.searchTips = res.data.pois;
          } else this.searchTips = [];
        });
      } else this.searchTips = [];
      callback(this.searchTips);
    },
    //点击搜索地址
    chooseSite(item) {
      this.searchSite = item ? item.name : this.searchSite;
      if (item) {
        let a = item.lonlat.split(",");
        let point = {
          lat: Number(a[1]),
          lng: Number(a[0]),
        };
        this.changeMarkerCenter(point);
        return false;
      }
      searchPoi1(this.searchSite).then((res) => {
        if (res.data.status == 0) {
          let point = {
            lat: Number(res.data.location.lat),
            lng: Number(res.data.location.lon),
          };
          this.changeMarkerCenter(point);
        }
      });
    },
    //修改中心点
    changeMarkerCenter(point) {
      // let points = wgs84_to_gcj02(point.lng, point.lat);
      let points = pointsConvert({ point: [point.lng, point.lat], type: 10 });

      var camera = this.map.camera;
      camera.setView({
        destination: new Cesium.Cartesian3.fromDegrees(
          points[0],
          points[1],
          1000
        ),
        orientation: {
          heading: Cesium.Math.toRadians(0),
          pitch: Cesium.Math.toRadians(-90),
          roll: Cesium.Math.toRadians(0),
        },
      });
    },
    //选中后的值
    // handleSelect(e) {
    //   this.searchSite = e.name;
    //   this.chooseSite(e);
    // },
  },
  destroyed() {
    this.$store.commit("changeCode", 0);
    this.$store.commit("setFence", "");
    this.$store.commit("fenceItemId", "");
    this.$store.commit("operateFItem", "");
    this.$store.commit("typeList", "");
    this.$store.commit("checkType", "");
    this.$store.commit("routeItem", "");
    if (this.timeOut) {
      clearTimeout(this.timeout);
    }
  },
};
</script>
<style lang="less" scoped>
.cesium3D {
  width: 100%;
  height: 100%;
  #map {
    height: 100%;
    z-index: 0;
  }
  .listShow {
    position: absolute;
    width: 350px;
    height: 98%;
    top: 1%;
    left: 1%;
    border-radius: 8px;
    z-index: 100;
    background-color: rgba(20, 20, 20, 0.9);

    .routeEdit,
    .fenceEdit,
    .orthoEdit {
      height: 100%;
      width: 100%;
      padding: 4%;
      padding-bottom: 2%;
      box-sizing: border-box;
    }
  }
  .operateBar_1 {
    position: absolute;
    bottom: 3%;
    right: 7%;
    height: auto;
    border-radius: 8px;
    font-size: 18px;
  }
  .timer {
    width: auto;
    height: auto;
    top: 1%;
    left: 380px;
    position: absolute;
    border-radius: 6px;
    display: flex;
    background-color: rgba(8, 16, 39, 0.8);
    .timer-item {
      width: 160px;
      margin: 20px 10px;
      font-size: 20px;
      text-align: center;
      font-weight: 550;
      color: #0092f8;
      .timer-item-1 {
        font-size: 35px;
        color: #0092f8;
      }
    }
  }
  .layerBut {
    width: auto;
    height: auto;
    position: absolute;
    top: 2%;
    right: 1.5%;
    border-radius: 4px;
    background-color: rgba(8, 16, 39, 0.86);
    .el-popover {
      background: transparent;
    }

    .el-button {
      border-radius: 0;
      padding: 8px;
      margin: 5px;
      margin-top: 8px;
      display: block;
      background-color: transparent;
      border: none;
      color: white;
      border-bottom: 1px solid #eee;
      .el-image {
        width: 18px;
      }
    }
  }
  .map-control {
    position: fixed;
    bottom: 20px;
    right: 20px;
    @imgUrl: url(https://webapi.amap.com/theme/v1.3/controlbar/ctb.png);
    .outer-control {
      width: 90px;
      height: 90px;
      background: @imgUrl -22px -30px no-repeat;
      background-size: 348px 270px;
      user-select: none;
      .inner-control {
        top: 46px;
        left: 50%;
        position: absolute;
        margin: -24px;
        width: 48px;
        height: 48px;
        z-index: 10;
        background: @imgUrl -231px -26px no-repeat;
        background-size: 348px 270px;
        .pointer-control {
          position: absolute;
          width: 30px;
          height: 48px;
          top: 0;
          left: 9px;
          border: none;
          z-index: 2;
          background: @imgUrl -281px -26px no-repeat;
          background-size: 348px 270px;
        }
      }
      .pitchUp,
      .pitchDown {
        @width: 30px;
        width: @width;
        height: @width * 25.5px / 30px;
        position: absolute;
        top: 3.5px;
        margin-left: -@width / 2;
        left: 50%;
        z-index: 1;
        background: @imgUrl -302.5px -49px no-repeat;
        background-size: 348px 270px;
        &:hover {
          background: @imgUrl no-repeat -302.5px -23.5px;
          background-size: 348px 270px;
        }
      }

      .pitchDown {
        top: 66px;
        transform: rotate(180deg);
        -webkit-transform: rotate(180deg);
      }
      .rotateLeft,
      .rotateRight {
        width: 21px;
        height: 52px;
        top: 19px;
        position: absolute;
        z-index: 2;
        background: @imgUrl -301.5px -77px no-repeat;
        background-size: 348px 270px;
        &:hover {
          background: @imgUrl no-repeat -278.5px -76.5px;
          background-size: 348px 270px;
        }
      }
      .rotateLeft {
        left: 5px;
      }
      .rotateRight {
        right: 2px;
        transform: rotateY(180deg);
        -webkit-transform: rotateY(180deg);
      }
    }
  }
  .zoom-max-height {
    position: absolute;
    left: calc(1% + 370px);
    bottom: 1%;
    color: rgb(70, 216, 25);
  }
}
</style>
<style lang="less">
.cesium3D {
  #map {
    .marker {
      width: 100px;
      height: 100px;
      background-color: red;
      z-index: 9999;
    }
  }
  .listShow {
    .planList {
      .title {
        background-color: #040404;
        color: white;

        .title-item {
          .el-button {
            background-color: transparent;
            color: white;

            &.active {
              color: #0b58de;
            }
          }
        }

        .el-button {
          background-color: white;
          color: #0b58de;
          border: none;

          &.checked {
            background-color: #0b58de;
            color: white;
          }
        }
      }

      .addWord {
        background-color: rgba(14, 18, 42, 0.8);
        color: white;
        border: 2px dashed white;

        &.checked {
          border: 2px dashed #0b58de;
        }
      }

      .routeType {
        background-color: transparent;
      }

      .content-list {
        .fenceContent {
          background-color: rgba(14, 18, 42, 0.9);
          border: 1px solid #0b58de;

          .content-item-1 {
            background-color: #1349a7;
            color: white;
          }

          .content-item-2 {
            color: white;
          }

          .content-item-3 {
            .el-popover__reference-wrapper {
              .el-button {
                background: transparent !important;

                border: none !important;
              }
            }
          }
        }

        .entryDiv {
          background-color: rgba(14, 18, 42, 0.9);
          border: 1px solid #0b58de;

          color: white;
        }

        .content-list-item {
          background-color: rgba(14, 18, 42, 0.9);
          border: 1px solid #434343;

          color: white;

          &.active {
            border: 1px solid #0b58de;
          }

          .el-row {
            .el-col {
              .wordType {
                color: #afa6a6;
              }

              &.el-col-end {
                .el-button {
                  background-color: transparent;
                  border: none;
                }
              }
            }
          }

          .operateBar {
            background-color: #11336d;

            &.operateBar_task {
              background-color: #31116d;
            }

            //

            .el-button {
              background-color: transparent;
              border: none;

              color: white;

              &.active {
                color: #468aff;
              }
            }
          }

          &.task_time {
            background-color: rgba(39, 13, 53, 0.9);

            &.active {
              border: 1px solid #4b0fb9;
            }
          }
        }

        // scrollbar-width: thin !important;
        // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
        // -ms-overflow-style: none !important;
        // scrollbar-color: #777777 #ccc;

        &::-webkit-scrollbar {
          width: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: #ccc;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #777777;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
      }

      .el-pagination {
        .el-pager li {
          color: white;
        }

        .el-pager li:not(.disabled).active {
          background-color: #124093 !important;
          border: 1px solid #124093;
        }
      }

      .searchFence {
        .el-input__inner {
          background-color: #141414 !important;
          color: white !important;
          border: 1px solid #dcdfe6 !important;
        }
      }

      .el-input-group__append {
        background-color: transparent !important;
      }
    }

    .dialogType {
      .el-dialog {
        background-color: rgba(20, 20, 20, 0.86) !important;
        border-radius: 8px !important;

        .tipInfo {
          color: white;
          border: 1px solid #fe0000;
          background-color: #131313;
        }

        .content-type {
          .el-button {
            background-color: transparent;
            color: #0092f8;
            border: none;
            background-color: rgba(0, 0, 0, 0.75);

            &.active {
              border: 1px solid white !important;
              color: white;
            }

            &.noActive {
              border: none;
              color: #6d6e70;
            }
          }
        }
      }
    }

    .deviceList {
      .el-dialog {
        background-color: rgba(8, 16, 39, 0.9) !important;

        .contentDiv {
          .check-group {
            .el-button {
              background-color: #ffffff;
              border: 6px solid rgba(112, 112, 112, 1);

              &.active {
                border: 6px solid rgba(11, 89, 222, 0.8);
              }

              .nameDiv {
                color: #040404;
              }

              .idDiv {
                // margin-top: 1%;
                color: #707070;
              }
            }
          }

          .entryDevice {
            color: white;
          }

          // scrollbar-width: thin !important;
          // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
          // -ms-overflow-style: none !important;
          // scrollbar-color: #777777 #ccc;

          &::-webkit-scrollbar {
            width: 3px;
          }

          &::-webkit-scrollbar-track {
            background-color: #ccc;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #777777;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }
        }

        .btnDiv {
          .sureBtn {
            background-color: #0b58de;
            color: white;
            border: none;
          }

          .closeBtn {
            background-color: #123571;
            color: white;
            border: none;
          }
        }

        .el-dialog__header {
          .el-dialog__title {
            color: white !important;
          }
        }

        .pageDiv {
          .el-pagination {
            .btn-prev,
            .btn-next {
              background-color: transparent !important;
              color: white !important;
              border: 1px solid white !important;
            }

            .el-pager {
              li {
                background-color: transparent !important;
                border: 1px solid #ffffff !important;
                color: white !important;
              }

              li:not(.disabled).active {
                background-color: #409eff !important;
                color: white !important;
              }
            }
          }
        }
      }
    }
  }
}
.renameDialog {
  .dividerDiv {
    background-color: #0b58de;
  }

  .savebtn {
    background-color: #0b58de;
    color: white;
  }

  .closeBtn {
    background-color: white;
    color: #0b58de;
  }
}
.popover-item {
  &.popover-item-1 {
    padding: 0 !important;
    width: 18vw !important;
    background-color: rgba(8, 16, 39, 0.9) !important;
    border: none !important;
    .el-autocomplete {
      width: 100% !important;
      .el-input {
        .el-input__inner {
          background-color: rgba(8, 16, 39, 0.9) !important;
          color: white !important;
          width: 100%;
          height: 40px !important;
          // line-height: 10px !important;
          font-size: 14px !important;
          // letter-spacing: 2px !important;
          padding-top: 1px !important;
          padding-bottom: 1px !important;
          border: none !important;
        }
      }
    }
    .el-input-group__append {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      background-color: transparent;
      border: none !important;
      .el-button {
        height: 40px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        border: none !important;
        background-color: rgba(36, 36, 36, 0.5) !important;
        color: white !important;
        .el-image {
          width: 16px !important;
        }
      }
    }
  }
  &.popover-item-3 {
    padding: 0 !important;
    width: 300px !important;
    background-color: rgba(8, 16, 39, 0.9) !important;
    border: none !important;
  }
  &.popover-item-2 {
    padding: 0 !important;
    background-color: rgba(8, 16, 39, 0.9) !important;
    border: none !important;
    .content {
      display: flex;
      align-items: center;
      border-radius: 6px !important;
      height: 40px;
      background-color: rgba(8, 16, 39, 0.9) !important;
      // border: 2px solid #000000 !important;

      color: white !important;

      .el-button {
        width: 86px;
        // padding: 1%;
        padding: 0;
        margin: 4px;
        text-align: left;
        font-size: 14px;
        background-color: transparent !important;

        color: white !important;
        border: none !important;
        &.actived {
          color: #0091f7 !important;
        }
        .el-image {
          width: 38px !important;
          vertical-align: middle;
          margin-right: -6px;
          // float: left !important;
        }
      }
    }
  }
  .popper__arrow,
  .popper__arrow::after {
    display: none !important;
    // border-left-color: rgba(8, 16, 39, 0.9) !important;
  }
  .searchReturn {
    min-height: 50%;
    width: 100%;
    overflow: auto;
    border-radius: 4px;
    .el-button {
      width: 98%;
      margin: 1%;
      padding: 2%;
      text-align: left;
      font-size: 16px;
    }
  }
  .el-input {
    input::-webkit-input-placeholder {
      color: #a7a7a7 !important;
    }

    input::-moz-input-placeholder {
      color: #a7a7a7 !important;
    }

    input::-ms-input-placeholder {
      color: #a7a7a7 !important;
    }
  }

  .searchReturn {
    background-color: white;

    border: 1px solid #eee;

    .el-button {
      border: none;
    }
  }
}
</style>
