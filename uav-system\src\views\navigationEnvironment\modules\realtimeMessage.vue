<!-- 实时信息 -->
<template>
  <put-away
    class="realtime-message"
    v-model="isOpen"
    :styles="{ top: 0, left: 0 }"
    :buttonStyle="buttonStyle"
    tooltipText="实时信息"
  >
    <template v-slot:main>
      <div class="realtime-message-main">
        <div class="header">
          <div>实时信息</div>
          <div @click="emptyInfo"><i class="el-icon-delete"></i></div>
        </div>

        <!-- 表格数据 -->
        <div class="table-data pb5">
          
            <!-- urlName="realtimeMessage" -->
          <scroll-list
            class="table-tbody scrollbar-style"
            :isJson="true"
            urlName="realtimeMessage"
          >
            <template v-slot:content="scope">
              <div
                class="table-tbody-tr mb5 mr5"
                style="width: 100%; display: flex"
                v-for="(item, index) in scope.data"
                :key="index"
                :style="{ color: colorList[item.grade] }"
              >
                <div style="width: 30%" class="tbody-cell">
                  {{ item.date }}
                </div>
                <div style="width: 70%" class="tbody-cell">
                  {{ item.title }}
                </div>
              </div>
            </template>
          </scroll-list>
        </div>
      </div>
    </template>
    <template v-slot:awayIcon>
      <i class="el-icon-arrow-left"></i>
    </template>
  </put-away>
</template>

<script>
import scrollList from "@/components/scrollList/index.vue";
import putAway from "../components/putAway.vue";
export default {
  components: {
    scrollList,
    putAway,
  },
  data() {
    return {
      data: [
        { date: "15:47:40", title: "开始搜索附近航标", grade: 1 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 2 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 3 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 1 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 2 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 3 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 1 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 2 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 3 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 1 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 2 },
        { date: "15:47:40", title: "开始搜索附近航标", grade: 3 },
      ],
      colorList: {
        1: "rgba(29, 245, 14, 1)",
        2: "rgba(222, 245, 14, 1)",
        3: "#FC0900",
      },
      isOpen: true,
      buttonStyle: {
        right: "-24px",
        "border-top-right-radius": "5px",
        "border-bottom-right-radius": "5px",
      }
    };
  },
  methods: {
    emptyInfo: function () {
      // this.data = [];
    },
    disposeData: function(){

    },
    close: function(state){
      this.isOpen = state;
    }
  },
};
</script>

<style lang="less" scoped>
.realtime-message {
  // width: 100%;
  //   padding: 0 10px;
  // background-color: rgba(143, 146, 151, 0.5);
  position: relative;
  .realtime-message-main{
    border-radius: 5px;
    width: 100%;
    height: 100%;
    // background-color: rgba(0, 0, 0, 0.5);
  }
  .header {
    padding: 0 10px;
    // width: 100%;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // color: #fff;
    font-size: 12px;
    border-radius: 5px;
  }
  .table-data {
    height: calc(100% - 35px);
    position: relative;
    padding: 0 5px 0 10px;
    .table-tbody {
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      .table-tbody-tr {
        border-radius: 5px;
        overflow: hidden;
      }
    }
    .tbody-cell {
      padding: 8px 0;
      font-size: 12px;
      //   color: #fff;
      text-align: center;
      // background-color: rgba(0, 0, 0, 0.7);
    }
  }
}
</style>