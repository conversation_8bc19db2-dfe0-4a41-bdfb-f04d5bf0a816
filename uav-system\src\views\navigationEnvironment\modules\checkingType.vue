<!-- 巡检类型 -->
<template>
  <put-away
    class="checking-type"
    v-model="isOpen"
    :styles="{ top: 0, right: 0 }"
    :buttonStyle="buttonStyle"
    tooltipText="成果列表"
  >
    <template v-slot:main>
      <div class="checking-type-main">
        <div
          class="scrollbar-style"
          style="overflow: hidden; overflow-y: auto; height: calc(100%)"
        >
        <!-- urlName="checkingType" -->
          <scroll-list  :isJson="true"  urlName="checkingType">
            <template v-slot:content="scope">
              <div
                class="checking-type-cell"
                v-for="(item, index) in scope.data || []"
                :key="index"
              >
                <div class="cell-left">
                  <!-- <img :src="imgTransition(item.imageUrl)" alt="" /> -->
                  <img :src="item.img_url" alt="">
                </div>
                <div class="cell-right">
                  <div class="cell-right-title">{{item.name}}</div>
                  <div
                    class="cell-right-time font-size-12 fons-color-ccc mt5 mb5"
                  >
                    {{item.time}}
                  </div>
                  <div class="" style="display: flex; flex-wrap: wrap">
                    <div class="cell-right-item" style="width: 60%">
                      <div class="font-size-12 fons-color-ccc">经度：</div>
                      <div class="font-size-12 fons-color-fff">{{item.lat}}</div>
                    </div>
                    <!-- <div class="cell-right-item" style="width: 40%">
                      <div class="font-size-12 fons-color-ccc">颜色：</div>
                      <div class="font-size-12 fons-color-fff">{{item.color}}</div>
                    </div> -->
                    <div class="cell-right-item">
                      <div class="font-size-12 fons-color-ccc">纬度：</div>
                      <div class="font-size-12 fons-color-fff">{{item.lng}}</div>
                    </div>
                    <!-- <div class="cell-right-item">
                      <div class="font-size-12 fons-color-ccc">编号：</div>
                      <div class="font-size-12 fons-color-fff">
                        {{item.code}}
                      </div>
                    </div> -->
                    <div class="cell-right-item">
                      <div class="font-size-12 fons-color-ccc">错误：</div>
                      <div class="font-size-12 fons-color-fff">无</div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </scroll-list>
        </div>
      </div>
    </template>
    <template v-slot:showContent>
      <div class="mt5" style="display: flex; justify-content: space-between">
        <img-icon name="telecontrol01" :size="16"></img-icon>
        <img-icon name="path01" :size="16"></img-icon>
      </div>
    </template>
  </put-away>
</template>

<script>
import imgIcon from "@/components/imgIcon/index";
import putAway from "../components/putAway.vue";
import scrollList from "@/components/scrollList/index.vue";

export default {
  components: {
    imgIcon,
    putAway,
    scrollList,
  },
  data() {
    return {
      isOpen: true,
      buttonStyle: {
        left: "-24px",
        "border-top-left-radius": "5px",
        "border-bottom-left-radius": "5px",
      },
      imageUrl: require("@/assets/icon/fleter.png"),
      data: []
    };
  },
  created(){
    this.$store.commit("setWsMmessageFun", {
      key: "checkingType",
      message: this.disposeData,
    });
  },
  methods: {
    disposeData: function (msg_id, data) {
      if(msg_id == 404){
        console.log("拍照成功----->", data);
      }else if(msg_id == 220){
        this.data.splice(0,0, data);
      }
    },
    close: function (state) {
      this.isOpen = state;
    },
    imgTransition: function(url){
      if(!url){
        return false;
      }
      return require(`@/assets/icon/${url}.png`);
    }
  },
};
</script>

<style lang="less" scoped>
.checking-type {
  //   width: calc(100% - 32px);
  width: 100%;
  .checking-type-main {
    height: calc(100% - 16px);
    border-radius: 5px;
    // background-color: rgba(0, 0, 0, 0.5);
    width: 100%;
    padding: 8px 0;
  }
  .checking-type-cell {
    // background-color: rgba(0, 0, 0, 0.7);
    margin: 0px 8px 8px 8px;
    padding: 8px;
    display: flex;
    border-radius: 5px;
    &:last-child {
      margin-bottom: 0px !important;
    }

    .cell-left {
      width: 56px;
      min-width: 56px;
      height: 56px;
      border-radius: 5px;
      // background-color: #ccc;
      overflow: hidden;
      img {
        width: 100%;
      }
    }
    .cell-right {
      flex-grow: 1;
      margin-left: 10px;
      // display: flex;
      // flex-wrap: wrap;
      .cell-right-title {
        // color: rgba(11, 88, 222, 1);
        font-size: 20px;
        width: 100%;
        font-weight: 700;
      }
      .font-size-12 {
        font-size: 12px;
      }
      .fons-color-ccc {
        // color: rgba(132, 141, 133, 1);
      }
      .fons-color-fff {
        // color: #fff;
        font-weight: 700;
      }
      .cell-right-item {
        display: flex;
        width: 100%;
      }
      .cell-right-time {
        width: 100%;
      }
    }
  }
}
</style>