<template>
  <div class="disk">
    <div
      class="disk-btn"
      v-for="item in btnList"
      :key="item.id"
      :class="[clickId == item.id ? 'disAcitve' : '', item.class]"
      @mousedown="clickIcon(item.id)"
      @mouseup="unclickIcon()"
    >
      <i :class="item.icon"></i>
    </div>
    <div
      class="diskTitle"
      @click="clickCenter"
      :class="clickId == 'center' ? 'disAcitve' : ''"
    >
      {{ equipLanguage.holderOperation.holder }}
    </div>
  </div>
</template>
<script>
export default {
  name: "disk",
  props: {
    equipLanguage: {
      type: [Object, String],
      default() {
        return {};
      },
    },
  },

  data() {
    return {
      btnList: [
        {
          icon: "el-icon-arrow-up",
          id: "up",
          class: "disk-btn-up",
        },
        {
          icon: "el-icon-arrow-right",
          id: "right",
          class: "disk-btn-right",
        },
        {
          icon: "el-icon-arrow-left",
          id: "left",
          class: "disk-btn-left",
        },
        {
          icon: "el-icon-arrow-down",
          id: "down",
          class: "disk-btn-down",
        },
      ],
      clickId: "",
      loops: "",
    };
  },
  created() {
    window.document;
  },
  methods: {
    clickIcon(id) {
      this.clickId = id;
      let params = {
        cmd_type: 5,
        action_cmd: "",
        value: 1500,
      };
      this.loops = setInterval(() => {
        switch (id) {
          case "up":
            params.action_cmd = 3;
            params.value += 15;
            if (params.value > 2000) {
              params.value = 2000;
            }
            break;
          case "down":
            params.action_cmd = 4;
            params.value -= 15;
            if (params.value < 1000) {
              params.value = 1000;
            }
            break;
          // case "left":
          //   num = 7;
          //   break;
          // case "right":
          //   num = 8;
          //   break;
          default:
            break;
        }
        this.$emit("getParams", params);
      }, 200);
      // setTimeout(() => {
      //   this.clickId = "";
      // }, 500);
    },
    unclickIcon() {
      clearInterval(this.loops);
      this.clickId = "";
      let params = {
        cmd_type: 5,
        action_cmd: 5,
        value: 1500,
      };
      this.$emit("getParams", params);
    },
    //回正
    clickCenter() {
      this.clickId = "center";
      let params = {
        cmd_type: 5,
        action_cmd: 1,
        value: 0,
      };
      this.$emit("getParams", params);
      setTimeout(() => {
        this.clickId = "";
      }, 200);
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .disk {
    .disk-btn {
      width: @zoomIndex * 75px !important;
      height: @zoomIndex * 75px !important;
      font-size: @zoomIndex * 30px !important;
      i {
        padding: @zoomIndex * 17.5px !important;
      }
    }
    .disk-btn-up {
      border-radius: @zoomIndex * 75px 0 0 0 !important;
      border-right: @zoomIndex * 2px solid rgb(180, 180, 180) !important;
    }
    .disk-btn-right {
      left: @zoomIndex * 75px !important;
      border-radius: 0 @zoomIndex * 75px 0 0 !important;
      border-bottom: @zoomIndex * 2px solid rgb(180, 180, 180) !important;
    }
    .disk-btn-left {
      top: @zoomIndex * 75px !important;
      border-radius: 0 0 0 @zoomIndex * 75px !important;
      border-top: @zoomIndex * 2px solid rgb(180, 180, 180) !important;
    }
    .disk-btn-down {
      top: @zoomIndex * 75px !important;
      left: @zoomIndex * 75px !important;
      border-radius: 0 0 @zoomIndex * 75px 0 !important;
      border-left: @zoomIndex * 2px solid rgb(180, 180, 180) !important;
    }
    .diskTitle {
      width: @zoomIndex * 30px !important;
      height: @zoomIndex * 26px !important;
      padding-top: @zoomIndex * 4px !important;
      top: 50% - (@zoomIndex * 15px) !important;
      left: 50% - (@zoomIndex * 15px) !important;
      border: @zoomIndex * 1px solid rgb(136, 136, 136) !important;
      font-size: @zoomIndex * 14px !important;
    }
  }
}
.disk {
  width: 100%;
  height: 100%;
  background-color: rgb(2, 11, 34);
  position: relative;
  border-radius: 50%;
  transform: rotate(45deg);
  display: flex;
  align-items: center;
  justify-content: center;
  .disk-btn {
    position: absolute;
    width: 75px;
    height: 75px;
    font-size: 30px;
    color: white;
    cursor: pointer;
    i {
      padding: 17.5px;
      transform: rotate(-45deg);
    }
  }
  .disk-btn-up {
    top: 0;
    left: 0;
    border-radius: 75px 0 0 0;
    border-right: 2px solid rgb(180, 180, 180);
  }
  .disk-btn-right {
    top: 0;
    left: 75px;
    border-radius: 0 75px 0 0;
    border-bottom: 2px solid rgb(180, 180, 180);
  }
  .disk-btn-left {
    top: 75px;
    left: 0;
    border-radius: 0 0 0 75px;
    border-top: 2px solid rgb(180, 180, 180);
  }
  .disk-btn-down {
    top: 75px;
    left: 75px;
    border-radius: 0 0 75px 0;
    border-left: 2px solid rgb(180, 180, 180);
  }
  .diskTitle {
    // position: absolute;
    width: 30px;
    min-width: 30px;
    min-height: 26px;
    height: 26px;
    font-size: 14px;
    padding-top: 4px;
    border-radius: 50%;
    // top: calc(50% - 15px);
    // left: calc(50% - 15px);
    border: 1px solid rgb(136, 136, 136);
    text-align: center;
    background-color: white;
    color: rgb(4, 44, 101);
    transform: rotate(-45deg);
    cursor: pointer;
  }
  .disAcitve {
    background-color: rgb(68, 141, 180);
  }
}
</style>