// 遥感页面样式
.remote-sensing {
  .navigation-environment {
    .uav-video {
      background-color: #000;
    }
  }

  .uav-operation {
    .uav-operation-main {
      background-color: rgba(0, 0, 0, 0.5);

      .main-header {
        background-image: linear-gradient(to bottom,
            rgb(86, 128, 156),
            rgba(86, 128, 156, 0.1));

        .header-left {
          background-color: rgba(11, 88, 222, 1);
          color: #fff;
        }

        .header-video {

          border: 3px solid #fff;

          &:hover {
            border-color: #409eff;
          }

          .video-cell {
            background-color: red;
          }

          .open-video-style {
            background-color: #1df50e;

            &::after {
              background-color: #1df50e;
            }
          }
        }
      }

      .main-center {

        .center-cell {
          background-color: rgba(78, 84, 82, 1);
          color: #fff;
        }

        .select-center-style {
          // color: rgba(83, 169, 243, 1);
          color: #0b58de;
          background-color: rgba(0, 0, 0, 0.7);
          // text-align: center;
        }
      }

      .main-footer {

        background-image: linear-gradient(to bottom,
            rgba(4, 11, 43, 0.8),
            rgba(13, 134, 255, 0.5));

        .circle-center {
          background-color: rgba(27, 57, 86, 1);
        }

        .right-circle-center {
          border: 1px solid rgba(27, 57, 86, 1);
          color: rgba(76, 166, 255, 1);

        }

        .cradle-head {

          .head-link {
            &:hover {
              color: #0b58de;
            }
          }

          .head-img {
            background-color: rgba(0, 0, 0, 1);
          }
        }
      }
    }
  }

  .realtime-message {
    .realtime-message-main {
      background-color: rgba(0, 0, 0, 0.5);
    }

    .header {
      color: #fff;
    }

    .table-data {
      .tbody-cell {
        background-color: rgba(0, 0, 0, 0.7);
      }
    }
  }

  .operation-introduced {
    .content-title {
      border: 2px solid blue;
      color: #5c83f8;
      background-color: #000000;
    }

    .main {
      .main-left {
        .main-left-top {
          background-color: rgba(204, 204, 204, 0.4);
          border: 6px solid rgba(0, 0, 255, 0.6);

          .left-top-content {
            .content-main {
              color: #fff;
            }
          }
        }

        .main-left-bot {
          .bot-item {
            background-color: rgba(204, 204, 204, 0.4);
            border: 6px solid rgba(0, 0, 255, 0.6);

            .bot-content {
              .content-main {
                color: #fff;
              }
            }
          }
        }
      }

      .main-center {
        .center-bot {
          background-color: rgba(204, 204, 204, 0.4);
          border: 6px solid rgba(0, 0, 255, 0.6);

          .center-bot-content {
            .content-text {
              color: #fff;
            }
          }
        }
      }

      .main-right {

        .main-right-top,
        .main-right-bot {
          background-color: rgba(204, 204, 204, 0.4);
          border: 6px solid rgba(0, 0, 255, 0.6);

          .top-content {
            .content-main {
              color: #fff;
            }
          }
        }
      }
    }

    .shut-icon {
      color: #cccccc;
    }
  }

  .flight-step {

    background-color: rgba(0, 0, 0, 0.8);
    .flight-step-title{
      color: red;
    }

    .body-content {

      .error-info {
        color: red;
      }

      .content-step {
        .hint-text {
          color: #409eff;
        }
      }

      .progress {
        .el-progress-bar__outer {
          background-color: #ccc !important;
        }
      }
    }

    .body-ul {
      li {
        .li-text {
          color: #409eff;
        }

        .ul-li-icon {
          .icon-square {
            background-color: rgb(121, 187, 255);
          }

          .icon-top,
          .icon-bot {
            background-color: rgb(121, 187, 255);
          }
        }
      }
    }

    .li-text {
      color: #fff;
    }

    .ul-li-icon {
      .icon-square {
        background-color: rgb(121, 187, 255);
      }

      .icon-top,
      .icon-bot {
        background-color: rgb(121, 187, 255);
      }
    }
  }

  .remote-header {

    background-color: rgba(0, 0, 0, 0.7);

    .info-left {
      color: #fc0900;
    }

    .info-right {
      color: #fff;
    }
  }

  .remote-uav-data {

    background-color: rgba(0, 0, 0, 0.3);

    .overview-cell {
      color: #fff;

      &:nth-child(1) .cell-val {
        color: #1df50e;
      }

      &:nth-child(2) .cell-val {
        color: #1df50e;
      }
    }
  }

  .checking-type {
    .checking-type-main {
      background-color: rgba(0, 0, 0, 0.5);
    }

    .checking-type-cell {
      background-color: rgba(0, 0, 0, 0.7);

      .cell-left {
        background-color: #ccc;
      }

      .cell-right {
        .cell-right-title {
          color: rgba(11, 88, 222, 1);
        }

        .fons-color-ccc {
          color: rgba(132, 141, 133, 1);
        }

        .fons-color-fff {
          color: #fff;
        }
      }
    }
  }

  .instructions {
    background-color: rgba(0, 0, 0, 0.1);

    .shut-icon {
      color: #cccccc;
    }
  }

  .instructions-layout {
    background-color: rgba(204, 204, 204, 0.4);
    border: 6px solid rgba(0, 0, 255, 0.6);

    .content-title {
      border: 2px solid blue;
      color: #5c83f8;
      background-color: #000000;
    }
  }

  .instructions-video-map {
    .video-map-item {
      .item-text {
        color: #fff;
      }
    }
  }

  .cell-circle {
    .circle-row {
      background-color: #fff;
      color: #000;
    }

    .select-icon-style {
      color: rgba(76, 166, 255, 1) !important;
    }
  }

  .put-away {
    .put-away-show {
      background-color: rgba(18, 27, 36, 0.5);
      color: rgb(141, 155, 171);
    }

    .put-away-button {
      background-color: #000;
      color: rgba(27, 229, 13, 1);
    }
  }

  .remote-sliding {
    .sliding-block-main {
      background-color: #fff;

      .block-cell {
        background-color: #fff;

        .cell-item {
          background-color: rgb(123, 123, 124);
        }
      }
    }
  }

  .virtual-joystick {
    .circle-text {
      color: #fff;
    }

    .cell-circle {
      .circle-row {
        background-color: #fff;
        color: #000;
      }
    }

    .select-icon-style {
      color: rgba(76, 166, 255, 1) !important;
    }
  }

  // 相机参数
  .camera-config {
    .dialog-body {
      background-color: rgba(0, 0, 0, 0.8);

      .body-header {
        background-color: #000000;
        color: #fff;

        .header-main {
          border-bottom: 1px solid #ccc;

          .main-item {
            .footer-line {
              background-color: #398bcc;
            }
          }
        }

        .secondary-navigation {
          .nav-main {
            border: 1px solid #ccc;
            color: #fff;

            &:hover {
              color: #398bcc;
              border-color: #398bcc;
            }
          }
        }

        .shut-icon {

          border: 1px solid #ccc;
          color: #fff;

          &:hover {
            color: #398bcc;
            border-color: #398bcc;
          }
        }
      }
    } 
  }

  .camera-params-config {
    .camera-item {
      .camera-item-main {
        .main-title {
          color: #fff;
        }

        .main-content {
          .content-row {
            .row-bot {
              color: #fff;
            }
          }
        }

        .mina-size {
          border: 1px solid #c2c3c3;

          .main-row {
            color: #fff;
          }

          .select-row {
            color: #398bcc;

            &::after {
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-bottom: 5px solid #398bcc;

            }
          }
        }

        .img-format {
          border: 1px solid #c2c3c3;

          .format-left,
          .format-right {
            color: #fff;
          }
        }
      }
    }
  }

  .cameta-set-popover {
    .popover-item {
      color: #000;

      &:hover {
        color: #398bcc;
      }
    }

    .select-item-style {
      color: #398bcc;
    }
  }

  .exposure-compensation {
    color: #fff;

    .left,
    .right {
      &:hover {
        color: #398bcc;
      }
    }

    .center {
      .select-item {
        .item-top {
          color: #398bcc !important;
        }

        .item-bot {
          background-color: #398bcc !important;
        }
      }

      .center-item {
        &:hover .item-top {
          color: #398bcc;
        }

        &:hover .item-bot {
          background-color: #398bcc;
        }

        .item-top {
          color: #fff;
        }

        .item-bot {
          background-color: #fff;
        }
      }

      .select-icon {
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid #398bcc;
      }
    }
  }

  .camera-basic {
    .basic-item {
      color: #fff;

      .tf-card {
        color: #398bcc;
      }

      .item-main {
        border: 1px solid #c2c3c3;

        &:hover .main-right {
          color: #398bcc;
        }

        .right-main {
          border: 1px solid #fff;

          .circle {
            background-color: #fff;
          }
        }
      }

      .select-right-style {
        border-color: #398bcc !important;

        .circle {
          background-color: #398bcc !important;
        }
      }
    }
  }

  .basic-inside {
    .flicker {
      .flicker-input {
        background-color: #0b0f15;
        border: 2px solid #787b7d;
        color: #fff;
      }

      .flicker-content {
        .content-item {
          background-color: #0b0f15;
          color: #fff;
        }
      }
    }

    .defogging {
      .defogging-item {
        .item-main {
          border: 1px solid #fff;
          color: #fff;
        }

        .select-item-style {
          color: #398bcc;
          border-color: #398bcc;
        }
      }
    }

    .gridding {
      .gridding-item {
        .item-header {
          color: #fff;
        }

        .gridding-select-style {
          border-color: #398bcc !important;

          .content-row {
            border-color: #398bcc !important;
          }

          .item-center-circle,
          .diagonal {
            background-color: #398bcc !important;
          }
        }

        @border-style: 1px solid #fff;

        .item-one {
          border-right: @border-style;
          border-top: @border-style;
        }

        .item-content {
          .diagonal {
            background-color: #ffffff;
          }

          .content-row {
            border-left: @border-style;
            border-bottom: @border-style;
          }
        }

        .item-center {
          border: @border-style;

          .item-center-circle {
            background-color: #fff;
          }
        }
      }
    }

    .shut-gridding {
      width: 180px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 12px;
      border: 1px solid #fff;
      border-radius: 8px;
      cursor: pointer;

      &:hover {
        color: #398bcc;
        border-color: #398bcc;
      }
    }

    .formatting {
      .formatting-title {
        font-size: 16px;
        color: #fff;
        font-weight: 700;
        text-align: center;
        // padding: 16px 0;
        margin: 32px 0 38px 0;
      }

      .formatting-main {
        margin: 0 125px 0 118px;
        border: 1px solid #ff0000;
        padding: 12px 24px 14px 32px;
        color: #ffffff;
        line-height: 2em;
        border-radius: 8px;
        font-size: 14px;
      }

      .formatting-bot {
        margin: 40px 0 0 0;
        text-align: center;
      }
    }
  }

  .camera-pattern {
    .pattern-header {
      .header-item {
        color: #fff;
      }

      .cell-label {
        border: 2px solid #ffffff;
      }

      .item-cell-style {
        border-color: #398bcc;
        color: #398bcc;
      }
    }

    .pattern-main {
      .item-content {
        .content-title {
          color: #fff;
        }

        .content-main {
          border: 1px solid #c2c3c3;

          .select-row {
            color: #398bcc;

            &::after {
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-bottom: 5px solid #398bcc;
            }
          }
        }

      }
    }
  }

  .progress-bar {

    .left,
    .right {
      border: 1px solid #707070;
      color: #fff;

      &:hover {
        color: #398bcc;
        border-color: #398bcc;
      }
    }

    .num {
      color: #fff;
    }
  }

  .camera-video {
    .video-item {
      .item-content {
        .content-title {
          color: #fff;
        }

        .content-main {
          border: 1px solid #c2c3c3;

          .main-row {
            color: #fff;
          }

          .select-row {
            color: #398bcc;

            &::after {
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-bottom: 5px solid #398bcc;
            }
          }

          .select-bit-rate {
            background-color: rgba(57, 139, 204, 1) !important;

            &::before {
              border-left: 10px solid transparent;
              border-right: 10px solid transparent;
              border-bottom: 10px solid #398bcc;
            }
          }

          .video-bit-rate {
            border: 1px solid #ccc;
            color: #ffff;
            background-color: rgba(26, 19, 17, 1);

            &::after {
              border-left: 10px solid transparent;
              border-right: 10px solid transparent;
              border-bottom: 10px solid rgba(26, 19, 17, 0.3);
            }
          }
        }
      }
    }
  }

  .rtkset{
    .video-item {
      .item-content {
        .content-title {
          color: #fff;
        }

        .content-main {
          .el-input__inner{
            background-color: transparent;
            color: #fff;
            border: 1px solid rgb(105, 105, 105);
          }
        }
      }
      .el-button{
        background-color: transparent;
        color: #fff;
        border: 1px solid rgb(105, 105, 105);
        &.active{
          color: #499cef;
          border-color: #499cef;
        }

      }
    }

  }


}
