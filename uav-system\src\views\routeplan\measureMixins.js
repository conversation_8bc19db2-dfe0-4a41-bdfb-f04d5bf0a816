export default function(config) {
    let { mapMethods, computedMapMethods } = config
    return {
        methods: {
            //开始测量距离
            startMeasure(e) {
                let entry = this.measureEntries[this.measureEntries.length - 1];
                if (entry.measureMarkers.length) {
                    this.measureChangePoint(e.position);
                    this.measureComputer(true);
                    let ray = this.map.camera.getPickRay(e.position);
                    let cartesian = this.map.scene.globe.pick(ray, this.map.scene);
                    entry.measureMarkers.push(cartesian);
                    let label = mapMethods.drawLabel(cartesian, {
                        id: entry.entityId + entry.measureMarkers.length.toString(),
                        text: "距离：" + 0 + "m",
                    });
                    this.map.entities.add(label);
                } else {
                    let point = mapMethods.changeLatLng(e.position, this.map);
                    let cartesian3 = new Cesium.Cartesian3.fromDegrees(
                        point.lng,
                        point.lat,
                        point.height ? point.height : 0
                    );
                    entry.measureMarkers.push(cartesian3);
                    entry.measureMarkers.push(cartesian3);
                    let polyLine = mapMethods.drawLine([], { noHavePaths: true });
                    polyLine.positions = new Cesium.CallbackProperty(function() {
                        return entry.measureMarkers;
                    }, false);
                    this.map.entities.add({
                        id: entry.entityId,
                        polyline: polyLine,
                    });
                    let label = mapMethods.drawLabel(cartesian3, {
                        id: entry.entityId + "1",
                        text: "起点",
                    });
                    this.map.entities.add(label);
                    let label1 = mapMethods.drawLabel(cartesian3, {
                        id: entry.entityId + "2",
                        text: this.measureComputer(),
                    });
                    this.map.entities.add(label1);
                    let entityLabel = this.map.entities.getById("measureTipLable");
                    entityLabel.label.text = "点击继续绘制，双击结束绘制";
                    this.handler.setInputAction(
                        this.measureEnd,
                        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
                    );
                }
            },
            //开始测量面积
            startMeasureArea(e) {
                let entry = this.measureEntries[this.measureEntries.length - 1];
                if (entry.measureMarkers.length) {
                    this.measureChangePoint(e.position);
                    let ray = this.map.camera.getPickRay(e.position);
                    let cartesian = this.map.scene.globe.pick(ray, this.map.scene);
                    entry.measureMarkers.push(cartesian);
                    if (entry.measureMarkers.length == 3) {
                        this.map.entities.removeById(entry.entityId);
                        let polygon = mapMethods.drawPolygon([], { noHavePaths: true });
                        polygon.hierarchy = new Cesium.CallbackProperty(function() {
                            let arrPoint = new Cesium.PolygonHierarchy(entry.measureMarkers);
                            return arrPoint;
                        }, false);

                        this.map.entities.add({
                            id: entry.entityId,
                            polygon: polygon,
                        });
                    }

                    // this.measureComputer(true);
                } else {
                    let point = mapMethods.changeLatLng(e.position, this.map);
                    let cartesian3 = new Cesium.Cartesian3.fromDegrees(
                        point.lng,
                        point.lat,
                        point.height ? point.height : 0
                    );
                    entry.measureMarkers.push(cartesian3);
                    entry.measureMarkers.push(cartesian3);
                    let polyLine = mapMethods.drawLine([], { noHavePaths: true, width: 1 });
                    polyLine.positions = new Cesium.CallbackProperty(function() {
                        return entry.measureMarkers;
                    }, false);
                    this.map.entities.add({
                        id: entry.entityId,
                        polyline: polyLine,
                    });
                    let entityLabel = this.map.entities.getById("measureTipLable");
                    entityLabel.label.text = "点击继续绘制，双击结束绘制";
                    this.handler.setInputAction(
                        this.measureEnd,
                        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
                    );
                    let label = mapMethods.drawLabel([], {
                        id: entry.entityId + "area",
                        text: "",
                    });
                    this.map.entities.add(label);
                }
            },
            //开始测量圆形
            startMeasureCircular(e) {
                let entry = this.measureEntries[this.measureEntries.length - 1];
                if (entry.measureMarkers.length) {
                    this.measureChangePoint(e.position);
                    let entityLabel = this.map.entities.getById("measureTipLable");
                    entityLabel.label.text = "点击开始绘制";
                    this.measureEntries.push({
                        measureMarkers: [],
                        measureEntry: "",
                        entityId: Math.random().toString(36).substring(7),
                        distance: 0,
                    });
                } else {
                    let point = mapMethods.changeLatLng(e.position, this.map);
                    let cartesian3 = new Cesium.Cartesian3.fromDegrees(
                        point.lng,
                        point.lat,
                        point.height ? point.height : 0
                    );
                    entry.measureMarkers.push(cartesian3);
                    let circle = mapMethods.drawCircle(cartesian3, { id: entry.entityId });

                    let self = this;
                    circle.semiMinorAxis = new Cesium.CallbackProperty(() => {
                        // PolygonHierarchy 定义多边形及其孔的线性环的层次结构（空间坐标数组）
                        return entry.distance + 0.01;
                    }, false);
                    circle.semiMajorAxis = new Cesium.CallbackProperty(() => {
                        // PolygonHierarchy 定义多边形及其孔的线性环的层次结构（空间坐标数组）
                        return entry.distance + 0.01;
                    }, false);
                    this.map.entities.add({
                        position: cartesian3,
                        id: entry.entityId,
                        ellipse: circle,
                    });
                    this.map.entities.add(circle);
                    let label = mapMethods.drawLabel(cartesian3, {
                        id: entry.entityId + "1",
                        text: "圆点:[" + point.lng + "," + point.lat + "]",
                        color: "#000000aa",
                        y: -7,
                    });
                    label.point = {
                        pixelSize: 12,
                        color: new Cesium.Color.fromCssColorString("#ffffff"),
                        outlineWidth: 0,
                    };
                    this.map.entities.add(label);
                    let entityLabel = this.map.entities.getById("measureTipLable");
                    entityLabel.label.text = "点击结束绘制";
                }
            },
            //开始测量方位角
            startMeasureAzimuth(e) {
                let entry = this.measureEntries[this.measureEntries.length - 1];
                if (entry.measureMarkers.length) {
                    this.measureChangePoint(e.position);
                    let entityLabel = this.map.entities.getById("measureTipLable");
                    entityLabel.label.text = "点击开始绘制";
                    this.measureEntries.push({
                        measureMarkers: [],
                        measureEntry: "",
                        entityId: Math.random().toString(36).substring(7),
                        distance: 0,
                    });
                } else {
                    let point = mapMethods.changeLatLng(e.position, this.map);
                    let cartesian3 = new Cesium.Cartesian3.fromDegrees(
                        point.lng,
                        point.lat,
                        point.height ? point.height : 0
                    );
                    entry.measureMarkers.push(cartesian3);
                    entry.measureMarkers.push(cartesian3);
                    let polyLine = mapMethods.drawLine([], { noHavePaths: true });
                    polyLine.positions = new Cesium.CallbackProperty(function() {
                        return entry.measureMarkers;
                    }, false);
                    this.map.entities.add({
                        id: entry.entityId,
                        polyline: polyLine,
                    });
                    let label = mapMethods.drawLabel(cartesian3, {
                        id: entry.entityId + "1",
                        text: "起点",
                    });
                    this.map.entities.add(label);
                    let label1 = mapMethods.drawLabel(cartesian3, {
                        id: entry.entityId + "2",
                        text: this.measureComputerAzimuth(),
                    });
                    this.map.entities.add(label1);
                    let entityLabel = this.map.entities.getById("measureTipLable");
                    entityLabel.label.text = "点击结束绘制";
                }
            },
            // 测量时修改点的坐标
            measureChangePoint(point) {
                let entity = this.measureEntries[this.measureEntries.length - 1];
                let len = entity.measureMarkers.length;
                let ray = this.map.camera.getPickRay(point);
                let cartesian = this.map.scene.globe.pick(ray, this.map.scene);
                if (len > 0) {
                    if (this.choosed == 4) {
                        this.measureComputerCricle(entity.measureMarkers[len - 1], cartesian);
                    } else {
                        entity.measureMarkers[len - 1] = cartesian;
                        if (this.choosed == 2 || this.choosed == 5) {
                            let entityId = entity.entityId + len.toString();
                            let entityItem = this.map.entities.getById(entityId);
                            entityItem.position = cartesian;
                            entityItem.label.text =
                                this.choosed == 5 ?
                                this.measureComputerAzimuth() :
                                this.measureComputer();
                        }
                        if (this.choosed == 3 && entity.measureMarkers.length > 2) {
                            let area = computedMapMethods("getAreaFromArrayCartesians", {
                                arrCartesian3: entity.measureMarkers,
                            });
                            let entitiy = this.map.entities.getById(entity.entityId);
                            var polyPositions = entitiy.polygon.hierarchy.getValue(
                                Cesium.JulianDate.now()
                            ).positions;
                            var polyCenter =
                                Cesium.BoundingSphere.fromPoints(polyPositions).center; //中心点
                            polyCenter =
                                Cesium.Ellipsoid.WGS84.scaleToGeodeticSurface(polyCenter);
                            let label = this.map.entities.getById(entity.entityId + "area");
                            label.position = polyCenter;
                            label.label.text = "面积:" + area;
                        }
                    }
                }
                let entityLabel = this.map.entities.getById("measureTipLable");
                entityLabel.position = cartesian;
            },
            //测量计算距离
            measureComputer(code) {
                let distance = 0;
                let markers =
                    this.measureEntries[this.measureEntries.length - 1].measureMarkers;
                let markers1 = markers[markers.length - 2];
                let markers2 = markers[markers.length - 1];
                distance =
                    Cesium.Cartesian3.distance(markers1, markers2) +
                    this.measureEntries[this.measureEntries.length - 1].distance;
                if (code) {
                    this.measureEntries[this.measureEntries.length - 1].distance = distance;
                }
                return "距离：" + distance.toFixed(2) + "m";
            },
            //测量计算离中心点的距离
            measureComputerCricle(center, point) {
                let distance = Cesium.Cartesian3.distance(center, point);
                this.measureEntries[this.measureEntries.length - 1].distance =
                    parseFloat(distance);
            },
            //测量计算方位角和距离
            measureComputerAzimuth() {
                let markers =
                    this.measureEntries[this.measureEntries.length - 1].measureMarkers;
                let markers1 = markers[markers.length - 2];
                let markers2 = markers[markers.length - 1];
                let distance = Cesium.Cartesian3.distance(markers1, markers2);
                let calcAngle = computedMapMethods("calcAngle", {
                    points: markers,
                    map: this.map,
                });
                return (
                    "方位角：" +
                    parseInt(calcAngle) +
                    " ，距离：" +
                    distance.toFixed(2) +
                    "m"
                );
            },
            //监听测量时鼠标移动
            measureMove(e) {
                this.measureChangePoint(e.endPosition);
            },
            //测量距离提示
            drawMeasureTipLabel() {
                let viewer = this.map;
                let point = viewer.camera.pickEllipsoid(
                    new Cesium.Cartesian2(
                        viewer.canvas.clientWidth / 2,
                        viewer.canvas.clientHeight / 2
                    )
                );
                let label = mapMethods.drawLabel(point, {
                    id: "measureTipLable",
                    text: "点击开始绘制",
                    verticalOrigin: Cesium.VerticalOrigin.TOP,
                    horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                    y: 2,
                    color: "#000000ba",
                });
                this.map.entities.add(label);
            },
            //测量双击结束绘制
            measureEnd() {
                this.map.trackedEntity = undefined;
                let entry = this.measureEntries[this.measureEntries.length - 1];
                this.map.entities.removeById(
                    entry.entityId + entry.measureMarkers.length.toString()
                );
                this.map.entities.removeById(
                    entry.entityId + (entry.measureMarkers.length - 1).toString()
                );
                entry.measureMarkers.pop();
                entry.measureMarkers.pop();
                this.measureEntries.push({
                    measureMarkers: [],
                    measureEntry: "",
                    entityId: Math.random().toString(36).substring(7),
                    distance: 0,
                });
                let entityLabel = this.map.entities.getById("measureTipLable");
                entityLabel.label.text = "点击开始绘制";
                this.handler.removeInputAction(
                    Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
                );
            },
            //绘制测量位置图标
            drawStartSite() {
                let viewer = this.map;
                let point = viewer.camera.pickEllipsoid(
                    new Cesium.Cartesian2(
                        viewer.canvas.clientWidth / 2,
                        viewer.canvas.clientHeight / 2
                    )
                );
                var curPosition = Cesium.Ellipsoid.WGS84.cartesianToCartographic(point);
                var lon = (curPosition.longitude * 180) / Math.PI;
                var lat = (curPosition.latitude * 180) / Math.PI;
                let lonlat = [lon, lat];
                // let lonlat = wgs84_to_gcj02(lon, lat);

                let site = mapMethods.drawPoint(point, {
                    id: "sitePoint",
                    text: "经度:" + lonlat[0].toFixed(7) + ",纬度:" + lonlat[1].toFixed(7) + "",
                    imageUrl: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAMAAABg3Am1AAAAe1BMVEVHcEw3csc+cbw8cr89cLoyc9JBb7Mhd/QldutId7s2csklduw+cbk2c8pBcrhPfL1PfL01c8s4cscmduokd+4tdd2Ou/wjd/FAb7NCcbU0cswnducpdeM7cb4xdNVBcLU8cr8pdeQ9cr4hd/M1c8weePogd/UeePkgePftEfpQAAAAJXRSTlMAtnOLatsd/vwZwfxdxUYEEMa1+/3vAv4wF8v59ofhM4j2ff7KTLmGfwAAATZJREFUSMftlFtzglAMhBdE7qjcFMR7+6H//xf2QW1ri8hx+tR2XzInk51JNjkr/UpMnWXZeF5TLp3pgPJZVvCOIps9qh/53MAf9ZbPE74hmffUbwBYjNeTup7sxgsANvcZCUCbute3m7YAyd3+AaL4cyqOAO7MMfOBaHub3EaA361VBrSxJAXhyvNWYSBJcQtknfsqgFSS7PysT25LUgoUXRt0gIUryT5eFT3aktwF4HQQlsBYUpB/7CAPJI2BZQehBNaSQuBkVZV1AkJJO6DsIDTARNIesCTJAlaSJkDTQfCA+hIrSaoAT1J9iYMJAfDS09JqaEvGQ/fK+voTizM+jZvj2w84PvPzNv5A5l/0iwmsH5uAsc2YG9kTVnkxY4CBZnwGgJHl/0mCD74R4eD7B/3jabwB+hhDfDlm+u4AAAAASUVORK5CYII=",
                });
                this.map.entities.add(site);
            },
            //测量位置移动修改
            changeSite(e) {
                let point = e.endPosition;
                let ray = this.map.camera.getPickRay(point);
                let cartesian = this.map.scene.globe.pick(ray, this.map.scene);

                var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                //弧度转经纬度
                let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
                let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
                let entityItem = this.map.entities.getById("sitePoint");
                entityItem.position = cartesian;
                entityItem.label.text =
                    "经度:" + lng.toFixed(7) + ",纬度:" + lat.toFixed(7);
            },
            //清除测量
            clearToolEvent() {
                this.map._container.style.cursor = "default";
                this.handler &&
                    this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
                this.handler &&
                    this.handler.removeInputAction(
                        Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
                    );
                this.map.entities.removeById("sitePoint");
                for (let index = 0; index < this.measureEntries.length; index++) {
                    this.map.entities.removeById(this.measureEntries[index].entityId);
                    this.map.entities.removeById(
                        this.measureEntries[index].entityId + "area"
                    );
                    for (
                        let i = 0; i < this.measureEntries[index].measureMarkers.length; i++
                    ) {
                        this.map.entities.removeById(
                            this.measureEntries[index].entityId + (i + 1).toString()
                        );
                    }
                }
                this.measureEntries = [];
                this.map.entities.removeById("measureTipLable");
            },
        }
    }

}