const singleUav = {
    text: '控制面板',
    upload: '上传航线',
    uav: '无人机',
    yuntai: '云台',
    center: '回中',
    vertical: '垂直',
    dialogContent: {
        titleUpload: '航线上传进度',
        uploadSuccess: '航线上传完成',
        selectTitle: "选择飞行模式",
        placeholder: '请选择飞行模式'
    },
    flightModeList: [{
            label: "姿态模式",
            value: 2,
            disabled: true,
        },
        {
            label: "自动模式",
            value: 3,
        },
        {
            label: "跟随模式",
            value: 4,
        },
        {
            label: "GPS模式",
            value: 5,
        },
        {
            label: "返航模式",
            value: 6,
        },
        {
            label: "降落飞行模式",
            value: 9,
        },
        {
            label: "运动模式",
            value: 13,
        },
    ],
    modelChange: '模式切换',
    lock: '闭锁',
    unlock: '解锁',
    takeOff: '起飞',
    message: {
        messageVideo: '开始录像',
        messageVideo1: '结束录像',
        messagePhoto: '拍照成功',
        messageReturn: '正在返航',
        continue: '继续',
        hover: '悬停',
        land: "降落",
        suspend: '暂停',
        toplane: "开始执行任务",
        errorPhoto: '拍照失败（流地址错误）',
        errorVideo: '录像失败（流地址错误）',
        messageUpload: '正在执行任务，请勿重复上传航线',
        sureUpload: '无人机即将执行上传航线指令，请确定。',
        sureTakeOff: '无人机即将执行起飞指令，请确定。',
        sureLock: '无人机即将执行闭锁指令，请确定。',
        sureunLock: '无人机即将执行开锁指令，请确定。',
        tipsLock: "飞机正在执行任务，无法闭锁"
    }

}
export default singleUav