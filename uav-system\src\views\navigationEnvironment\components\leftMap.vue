<template>
  <div
    id="map"
    ref="leftMaps"
    style="border-radius: 5px"
    class="custom-map zindex-style"
    :class="className"
  >
    <div class="fixed-center" @click.stop="isCenter = !isCenter">
      <img-icon
        :name="isCenter ? 'map-center-select' : 'mapCenter'"
        :size="20"
      />
    </div>
    <div
      class="zoom-height-content"
      v-if="basicData && basicData.type == 50 && isComputeHight"
    >
      {{
        getElevationcode
          ? `${routeLanguage.getHeightLoading}(${progress}%)`
          : `${routeLanguage.heightLabel}${zoomMaxHeight}m`
      }}
    </div>
    <div id="leafletMap" style="height: 0"></div>
  </div>
</template>

<script>
import request from "@/utils/api";
import imgIcon from "@/components/imgIcon/index.vue";
import { wgs84_to_gcj02, gcj02_to_wgs84 } from "@/utils/wgs84_to_gcj02.js";
// import { orthoPhotoComputer } from "@/utils/orthoPhotoComputer.js";
import { computerElevation } from "@/utils/computerElevation";

import createMap from "@/utils/cesium/createMap";
import mapMethods from "@/utils/cesium/mapMethods";
import { orthoPhotoComputer } from "@/utils/cesium/orthoPhotoComputer";
import L from "leaflet";
export default {
  components: {
    imgIcon,
  },
  props: {
    index: Number,
    openIndex: String,
  },
  data() {
    return {
      query: {}, // 路由参数
      map: null,
      polypon: null, //第一个围栏
      polyline: null, // 航线
      magnify: false, // 是否放大
      markerText: [], // 巡航点信息
      uavMarker: null, // 无人机位置
      fenceList: [], // 围栏列表
      taskLine: null,
      taskPath: [],
      basicData: null,

      isCenter: true,
      progress: 0,
      isComputeHight: false,
      className: "",
      layerShow: true,
      f_id: "",
    };
  },
  computed: {
    staveThreeData() {
      return this.$store.state.equipment.staveThreeData;
    },
    // 无人机
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS || {};
    },
    flightTask() {
      return this.$store.state.equipment.flightTask;
    },
    routeLanguage() {
      return this.$languagePackage.routes;
    },
    zoomMaxHeight() {
      return this.$store.state.equipment.zoomMaxHeight;
    },
    getElevationcode() {
      return this.$store.state.equipment.elevationcode;
    },
    delBreakMarkerCode() {
      return this.$store.state.equipment.delBreakMarkerCode;
    },
  },
  watch: {
    // 监听状态3回传
    staveThreeData: {
      handler: function (data) {
        let lon = data.longitude / 1e7,
          lat = data.latitude / 1e7;
        let point = wgs84_to_gcj02(lon, lat);
        let position = {
          lng: point[0],
          lat: point[1],
          height: data.relative_altitude / 100,
        };

        this.updateUavLoca(position); // 更新飞机位置

        this.drawWarkFlight(position); // 绘制任务飞行航线V

        if (data.flight_status == 4) {
          this.$store.commit("setFlightTask", false);
        }
      },
      deep: true,
    },
    openIndex(oldValue, newValue) {
      if (oldValue == "map") {
        this.className = this.layerShow ? "map-active" : "";
      }
      if (newValue == "map") {
        this.className = "";
      }
    },
    delBreakMarkerCode(value) {
      if (value) {
        this.map.entities.removeById("markerBreak");
        this.$store.commit("setDelBreakMarkerCode", false);
      }
    },
  },
  created() {
    this.query = this.$route.query || {};
    this.layerShow =
      this.query.state == 2 && this.query.type != 200 ? false : true;
    this.getElectronicFence();

    this.$store.commit("setCloseFlightTrack", this.closeFilghtPath);
    this.$store.commit("setWsMmessageFun", {
      key: "leftMap",
      message: this.disposeData,
    });
  },
  mounted() {
    this.initMap();
  },
  methods: {
    disposeData: function (msg_id, data) {
      if (msg_id == 401 && data.cmd_type == 7) {
        // 航线下载成功
        let list = [];
        for (let i = 0; i < data.waypoints.length; i++) {
          let lon = data.waypoints[i].longitude / 1e7,
            lat = data.waypoints[i].latitude / 1e7;
          let position = wgs84_to_gcj02(lon, lat);
          list.push(position);
        }

        this.taskLine = new AMap.Polyline({
          map: this.map,
          path: list,
          isOutline: true,
          outlineColor: "#67C23A",
          borderWeight: 1,
          strokeColor: "#67C23A",
          strokeOpacity: 1,
          strokeWeight: 3,
          // 折线样式还支持 'dashed'
          strokeStyle: "solid",
          // strokeStyle是dashed时有效
          strokeDasharray: [10, 5],
          lineJoin: "round",
          lineCap: "round",
          zIndex: 50,
        });
      }
      if (msg_id == 415) {
        console.log("断点415===>", data);
        this.drawBreakMarker(data);
        this.saveRoute(data);
        this.$store.commit("setFlightTask", false);
      }
    },
    //绘制断点
    drawBreakMarker(data) {
      let breakPoint = wgs84_to_gcj02(
        data.longitude / 1e7,
        data.latitude / 1e7
      );
      // breakPoint = new AMap.LngLat(breakPoint[0], breakPoint[1]);
      breakPoint = {
        lng: breakPoint[0],
        lat: breakPoint[1],
        height: data.altitude_relative / 100,
      };
      let breakMarker = this.map.entities.getById("markerBreak");
      if (breakMarker) {
        breakMarker.position = new Cesium.Cartesian3.fromDegrees(
          breakPoint.lng,
          breakPoint.lat,
          breakPoint.height
        );
      } else {
        let marker = mapMethods.drawPointLabel(breakPoint, {
          text: "b",
          id: "markerBreak",
          className: "marker-break",
        });
        this.map.entities.add(marker);
      }
    },

    // 更新/绘制飞机位置
    updateUavLoca: function (location) {
      if (!this.map) {
        return false;
      }
      // console.log("飞机位置-----------", position);
      // this.isCenter && this.map.setCenter(position);

      const yaw = this.staveThreeData.attitude_yaw;
      const pitch_1 = this.staveThreeData.attitude_pitch;
      const roll_1 = this.staveThreeData.attitude_roll;
      let uavMarker = this.map.entities.getById("uavMarker");
      if (uavMarker) {
        let cartesian3 = Cesium.Cartesian3.fromDegrees(
          location.lng,
          location.lat,
          location.height
        );
        uavMarker.position = cartesian3;
        /**
         * 设置模型方向角
         */
        let heading = Cesium.Math.toRadians(yaw - 90);
        let pitch = Cesium.Math.toRadians(pitch_1);
        let roll = Cesium.Math.toRadians(roll_1);
        let hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
        uavMarker.orientation = Cesium.Transforms.headingPitchRollQuaternion(
          cartesian3,
          hpr
        );
        // uavMarker.orientation = cartesian3;
        return false;
      }
      let points = new Cesium.Cartesian3.fromDegrees(
        location.lng,
        location.lat,
        location.height
      );
      let site = mapMethods.drawModel(points, {
        id: "uavMarker",
      });
      this.map.entities.add(site);
      let s = this.map.entities.getById("uavMarker");
      this.map.trackedEntity = s; // 设置相机跟随
    },

    // 绘制任务飞行航线
    drawWarkFlight: function (position) {
      // console.log("绘制飞行航线任务状态--------->", this.flightTask);
      if (this.flightTask && this.map) {
        this.taskPath.push(
          new Cesium.Cartesian3.fromDegrees(
            position.lng,
            position.lat,
            position.height
          )
        );
        if (!this.taskLine) {
          let self = this;
          let polyLine = mapMethods.drawLine([], {
            noHavePaths: true,
            color: "#ff0000",
            width: 3,
          });
          polyLine.positions = new Cesium.CallbackProperty(function () {
            return self.taskPath;
          }, false);
          this.taskLine = this.map.entities.add({
            id: "taskLine",
            polyline: polyLine,
          });
        }
      }
    },

    // 获取电子围栏
    getElectronicFence: function () {
      if (this.query.state == 2) {
        return false;
      }
      let flightParam = sessionStorage.getItem("flightParam");
      this.f_id = "";
      try {
        if (!flightParam) {
          return false;
        }
        let data = JSON.parse(flightParam)[this.query.sn_id];
        this.f_id = data.fence.f_id;
      } catch (error) {
        console.error(error);
      }
      if (!this.f_id) {
        return false;
      }
      request("fenceOne", {
        jointPmd: true,
        pmd: "f_id",
        f_id: this.f_id,
      }).then((res) => {
        this.fenceList = res.data.point_list;
        this.drawFence(this.fenceList);
      });
    },

    // 地图初始化
    initMap: function () {
      this.$nextTick(() => {
        this.map = createMap.createMap("map", {
          layerIndex: 0,
          loadLayerText: false,
          sceneModeButton: true,
        });
        this.leafletMap = L.map("leafletMap").setView(
          [23.129704666325214, 113.26445102691652],
          22
        );
        this.drawMachine();
        this.dataDispose();
        this.handler = new Cesium.ScreenSpaceEventHandler(
          this.map.scene.canvas
        );
        this.handler.setInputAction(
          this.clickMap,
          Cesium.ScreenSpaceEventType.LEFT_CLICK
        );
        this.$store.commit("setMaps", this.map);
        this.$store.commit("setLeafletMaps", this.leafletMap);
        let self = this;
        let divDocument = this.$refs.leftMaps;
        divDocument.addEventListener("mouseover", function (e) {
          self.$emit("mouseEvent", false);
        });
        divDocument.addEventListener("mouseout", function (e) {
          self.$emit("mouseEvent", true);
        });
      });
    },

    // 绘制机巢
    drawMachine: function () {
      if (this.query.type == 200) {
        return false;
      }
      let imageUrl = require("@/assets/img/workingHome.png");
      let flightParam = sessionStorage.getItem("flightParam");
      try {
        if (!flightParam) {
          return false;
        }
        let data = JSON.parse(flightParam)[this.query.sn_id];
        let position1 = data.point;
        let position = wgs84_to_gcj02(position1[0], position1[1]);
        let points = new Cesium.Cartesian3.fromDegrees(
          position[0],
          position[1],
          0
        );
        let machineMarker = mapMethods.drawPoint(points, {
          id: "machineMarker",
          imageUrl: imageUrl,
        });
        this.map.entities.add(machineMarker);
        if (this.query.state == 2) {
          this.map.scene.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(
              position[0],
              position[1],
              5000
            ),
            duration: 1,
            maximumHeight: 2000,
            pitchAdjustHeight: 20,
            orientation: {
              heading: Cesium.Math.toRadians(0),
              pitch: Cesium.Math.toRadians(-90),
              roll: Cesium.Math.toRadians(0),
            },
          });
        }
      } catch (error) {
        console.error("数据解析失败------>", error);
      }
    },

    clickMap: function () {
      if (this.openIndex != "map") {
        this.$emit("clickEvent", "map");
        // this.partRefresh(true);
      }
    },

    // 地图切换修改巡航点样式
    partRefresh: function (stave) {
      this.magnify = stave;
      setTimeout(() => {
        // this.magnifyMap();
        for (let i = 0; i < this.markerText.length; i++) {
          this.markerText[i].setStyle({
            borderWidth: this.magnify ? "5px" : "2px",
            width: this.magnify ? "24px" : "12px",
            height: this.magnify ? "24px" : "12px",
            fontSize: this.magnify ? "24px" : "12px",
          });
        }
        if (this.breakMarker) {
          this.breakMarker.setStyle({
            borderWidth: this.magnify ? "5px" : "2px",
            width: this.magnify ? "16px" : "8px",
            height: this.magnify ? "16px" : "8px",
            fontSize: this.magnify ? "16px" : "8px",
          });
        }
      }, 80);
    },

    // 切换地图时修改围栏左右边距
    magnifyMap: function () {
      let maps = document.querySelector("#map .amap-ui-control-container");
      let icon = document.querySelector("#map .fixed-center");

      if (!maps) {
        return false;
      }
      if (this.magnify) {
        maps.style.right = this.query.state == 2 ? "20px" : "300px";
        icon.style.right = "310px";
      } else {
        maps.style.right = "20px";
        icon.style.right = "20px";
      }
      maps.style.bottom = "20px";

      return false;

      let avoid = this.magnify ? [60, 110, 200, 200] : [10, 10, 10, 10];
      this.map &&
        this.map.setFitView &&
        this.map.setFitView([this.polyline], false, avoid, 30);
    },

    // 绘制围栏
    drawFence: function (data) {
      if (!this.map) {
        setTimeout(() => {
          this.drawFence(data);
        }, 500);
        return console.log("地图未加载，不能绘制围栏");
      }
      let pointItem = data.map((row) => {
        return {
          lng: row.lon_int / 1e7,
          lat: row.lat_int / 1e7,
          height: 0,
        };
      });
      let polygon = mapMethods.drawPolygon(pointItem, { color: "#2a6dc5" });
      this.map.entities.add({
        id: this.f_id,
        polygon: polygon,
      });
    },

    // 正射影像绘制路线
    orthoimageDraw: function (data) {
      let orthimage = orthoPhotoComputer(
        this.basicData,
        this.map,
        this.leafletMap
      );
      if (!(orthimage.points && orthimage.points.length)) {
        return false;
      }
      orthimage.points = orthimage.points.map((item) => {
        return {
          lat: item.lat,
          lng: item.lng,
          height: this.basicData.default_height / 100,
        };
      });
      // let orthimage = orthoPhotoComputer(this.basicData, this.map);
      let cal_alt_json = this.basicData.cal_alt_json
        ? JSON.parse(this.basicData.cal_alt_json)
        : "";
      this.isComputeHight = false;
      if (cal_alt_json && cal_alt_json.isComputeHight) {
        this.isComputeHight = true;
        this.computerMaxHeight(orthimage.points);
      }
      let startMarker = mapMethods.drawPointLabel(orthimage.points[0], {
        text: "S",
        id: "markerStart",
        className: "marker-start-end",
      });
      this.map.entities.add(startMarker);
      let endMarker = mapMethods.drawPointLabel(
        orthimage.points[orthimage.points.length - 1],
        {
          text: "E",
          id: "markerEnd",
          className: "marker-start-end",
        }
      );
      this.map.entities.add(endMarker);
      let operateEntrty = mapMethods.drawLine(orthimage.points, {
        color: "#0092f8A0",
        width: 5,
      });
      this.map.entities.add({
        id: "operateEntrty1",
        polyline: operateEntrty,
      });
    },

    //计算区域峰值
    computerMaxHeight(points) {
      this.progress = 0;
      this.$store.commit("setElevationcode", true);
      let wgs84Points = points.map((x) => {
        let a = gcj02_to_wgs84(x.lng, x.lat);
        return {
          lng: a[0],
          lat: a[1],
        };
      });
      computerElevation(wgs84Points, 120, this.progressFun, this.map).then(
        (res) => {
          this.$store.commit("setElevationcode", false);
          this.$store.commit("setZoomMaxHeight", res);
        }
      );
    },
    progressFun(progress) {
      this.progress = progress;
    },
    // 绘制巡航路线
    drawPath: function (item) {
      if (!this.map) {
        console.log("地图未加载，不能绘制巡航路线");
        return false;
      }

      let index = 1;
      if (!item.point_list) {
        return false;
      }
      let pointItem = item.point_list.map((row) => {
        // 巡航点位置
        let p = [row.lon_int / 1e7, row.lat_int / 1e7];
        if (row.type == 10) {
          p = wgs84_to_gcj02(p[0], p[1]);
        }
        let position = {
          lat: p[1],
          lng: p[0],
          height: row.height / 100,
        };

        this.drawText(index, position);
        index++;
        return position;
      });
      // this.map.scene.camera.flyTo({
      //   destination: Cesium.Cartesian3.fromDegrees(
      //     pointItem[0].lng,
      //     pointItem[0].lat,
      //     5000
      //   ),
      //   duration: 1,
      //   maximumHeight: 2000,
      //   pitchAdjustHeight: 20,
      //   orientation: {
      //     heading: Cesium.Math.toRadians(0),
      //     pitch: Cesium.Math.toRadians(-90),
      //     roll: Cesium.Math.toRadians(0),
      //   },
      // });

      // 正射影像
      let type = this.basicData.type;
      if (type == 50) {
        let polygon = mapMethods.drawPolygon(pointItem, {
          outlineColor: "#0092f8",
          color: "#1357B1",
        });
        this.operateEntrty = this.map.entities.add({
          id: "operateEntrty",
          polygon: polygon,
        });
        this.orthoimageDraw();
      } else {
        let polyLine = mapMethods.drawLine(pointItem);
        this.operateEntrty = this.map.entities.add({
          id: "operateEntrty",
          polyline: polyLine,
        });
      }
      this.map.zoomTo(this.operateEntrty);

      // this.magnifyMap(); // 切换视图
    },

    // 数据处理
    dataDispose: function () {
      if (this.query.state == 1) {
        let flightParam = sessionStorage.getItem("flightParam");
        try {
          if (!flightParam) {
            return false;
          }
          let data = JSON.parse(flightParam)[this.query.sn_id];
          if (!data.route.m_id) {
            return false;
          }

          // 航线
          // let airRoute = data.route.filter((item) => {
          //   return item.m_id == this.query.m_id;
          // })[0];
          let airRoute = data.route;
          this.basicData = airRoute;

          this.drawPath(airRoute);
          if (airRoute.break_json) {
            try {
              let data = JSON.parse(airRoute.break_json);
              data = data.data;
              this.drawBreakMarker(data);
            } catch (error) {
              console.log("解析失败");
            }
          }
        } catch (error) {
          console.error("数据解析失败------>", error);
        }
      } else if (this.query.type == 200) {
        let params = sessionStorage.getItem("wipeOut");
        try {
          if (!params) {
            return false;
          }
          let data = JSON.parse(params);
          this.drawMarker(data[this.query.sn_id].alarmItem);
        } catch (error) {
          console.error("数据解析失败------>", error);
        }
      }
    },

    //存储断点
    saveRoute(breakData) {
      let flightParam = sessionStorage.getItem("flightParam");
      try {
        if (!flightParam) {
          return false;
        }
        let data = JSON.parse(flightParam);
        let data1 = data[this.query.sn_id];
        // 航线
        data1.route.break_json = JSON.stringify({ data: breakData });
        data[this.query.sn_id] = data1;
        sessionStorage.setItem("flightParam", JSON.stringify(data));
        // let j = data.route.findIndex((item) => item.m_id == this.query.m_id);
        // if (j !== -1) {
        //   data.route[j].break_json = JSON.stringify({ data: breakData });
        //   sessionStorage.setItem("flightParam", JSON.stringify(data));
        // }
      } catch (error) {
        console.error("数据解析失败------>", error);
      }
    },
    //绘制火情点
    drawMarker(alarmItem) {
      if (!this.map) {
        console.log("地图未加载，不能绘制巡航路线");
        return false;
      }
      let imgList = {
        1: {
          imgSrc: require("@/assets/img/alarm.png"),
          state: "发现火情",
        },
        10: {
          imgSrc: require("@/assets/img/alarm1.png"),
          state: "处理中",
        },
        20: {
          imgSrc: require("@/assets/img/alarm2.png"),
          state: "处理完成",
        },
      };
      let img = imgList[alarmItem.state].imgSrc;
      let icon = new AMap.Icon({
        // 图标的取图地址
        image: img,
        imageSize: new AMap.Size(35, 50),
      });
      let center =
        alarmItem.coo_type == 1
          ? wgs84_to_gcj02(alarmItem.lon_int / 1e7, alarmItem.lat_int / 1e7)
          : [alarmItem.lon_int / 1e7, alarmItem.lat_int / 1e7];
      new AMap.Marker({
        map: this.map,
        position: center,
        icon: icon,
        offset: new AMap.Pixel(-15, -30),
        title: imgList[alarmItem.state].state + "，经纬度：" + center,
      });
      this.map.setCenter(center);
    },
    // 绘制巡航点
    drawText: function (index, position) {
      let marker = mapMethods.drawPointLabel(position, {
        text: index,
        id: index,
        className: "marker-route",
      });
      this.map.entities.add(marker);
      // let text = new AMap.Text({
      //   text: index,
      //   position: position,
      //   anchor: "center", // 设置文本标记锚点
      //   zIndex: 50,
      //   style: {
      //     borderRadius: "50%",
      //     borderWidth: this.magnify ? "5px" : "2px",
      //     borderColor: "#0092f8",
      //     backgroundColor: "#FFFFFF",
      //     width: this.magnify ? "24px" : "12px",
      //     height: this.magnify ? "24px" : "12px",
      //     fontSize: this.magnify ? "24px" : "12px",
      //     color: "#000000",
      //     display: "flex",
      //     justifyContent: "center",
      //     alignItems: "center"
      //   }
      // });
      // this.markerText.push(text);
    },

    // 下载航线
    downloadAirRoute: function () {
      this.equipmentWS.manualSend({ cmd_type: 7 }, 401);
    },
    // 清空飞行轨迹
    closeFilghtPath: function () {
      if (this.taskLine) {
        this.taskPath = [];
        this.map.entities.removeById("taskLine");
        this.taskLine = null;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.custom-map {
  width: 100%;
  background-color: #000;
  position: relative;
  z-index: 22;

  .fixed-center {
    position: absolute;
    z-index: 20;
    right: 30px;
    bottom: 70px;
    display: none;
  }
  .zoom-height-content {
    position: absolute;
    bottom: 0;
    left: 1%;
    color: rgb(70, 216, 25);
    z-index: inherit;
  }
}
</style>
<style lang="less">
.custom-map {
  .amap-logo,
  .amap-copyright {
    display: none !important;
  }
  &.map-active {
    .layerButton {
      right: 320px;
    }
  }
  .startend-marker {
    height: 12px;
    width: 12px;
    border-radius: 50%;

    text-align: center;
    background-color: #ffffff;
    border: 1px solid #0728fc;
    color: #0015a1;
    font-size: 12px;
    .text {
      position: absolute;
      top: 50%;
      left: 50%;
      font-weight: 800;
      vertical-align: middle;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
