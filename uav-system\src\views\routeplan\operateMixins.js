import { exportRoute } from "@/utils/exportRoute";
import toge<PERSON><PERSON><PERSON> from "@mapbox/togeojson";
import { actionChangeArray } from "./planAction";
export default function(config) {
    let { mapMethods, computedMapMethods, pointsConvert } = config
    return {
        methods: {
            //点击标点
            drawMark() {
                this.drawend = !this.drawend;
                if (this.drawend) {
                    if (this.map.scene.mode === Cesium.SceneMode.SCENE3D) {
                        this.$message.warning(this.routeLanguage.mapModeTip);
                    }
                    this.toolEvent(10);
                    // this.map._container.style.cursor = "crosshair"; //十字
                    this.map._container.style.cursor = "pointer";
                    this.handler &&
                        this.handler.setInputAction(
                            this.leftDownEvent,
                            Cesium.ScreenSpaceEventType.LEFT_DOWN
                        );
                } else {
                    this.map._container.style.cursor = "default";
                    this.handler &&
                        this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
                }
                this.showEntity(this.drawend);
            },
            showEntity(isShow) {
                if (!this.markerPoint.length) {
                    return false;
                }
                for (let index = 0; index < this.markerPoint.length; index++) {
                    let entity = this.map.entities.getById("center" + (index + 1));
                    if (entity) {
                        entity.show = isShow;
                    }
                }
                // if (this.code == 2) {
                let entity = this.map.entities.getById(
                    "center" + this.markerPoint.length
                );
                if (entity) {
                    entity.show = isShow;
                }
                if (isShow) {
                    this.clickMarker(this.markerPoint.length);
                    return false;
                }
                let markerBefor = this.map.entities.getById(this.clickCode);
                let color = "#000000";
                if (this.code !== 2) {
                    color = "#0092f8";
                }
                markerBefor.point.color = new Cesium.Color.fromCssColorString("#ffffff");
                markerBefor.label.fillColor = new Cesium.Color.fromCssColorString(color);
                this.clickCode = "";
                // }
            },
            //鼠标按下
            leftDownEvent(e) {
                const pickedLabel = this.map.scene.pick(e.position);
                if (
                    Cesium.defined(pickedLabel) &&
                    pickedLabel.id.point &&
                    pickedLabel.id.label
                ) {
                    let id = pickedLabel.id.id.toString();
                    if (id.includes("center")) {
                        return false;
                    }
                    this.leftDownCode = true;
                    this.map.scene.screenSpaceCameraController.enableRotate = false;
                    this.map.scene.screenSpaceCameraController.enableTranslate = false;
                    this.clickMarker(pickedLabel.id.id);
                    this.handler &&
                        this.handler.setInputAction(
                            this.leftUpEvent,
                            Cesium.ScreenSpaceEventType.LEFT_UP
                        );
                    this.handler &&
                        this.handler.setInputAction(
                            this.mouseMoveEvent,
                            Cesium.ScreenSpaceEventType.MOUSE_MOVE
                        );
                    this.moveBeforePoint = {
                        lat: this.markerPoint[this.clickCode - 1].lat,
                        lng: this.markerPoint[this.clickCode - 1].lng,
                    };
                }
            },
            //鼠标抬起
            leftUpEvent(e) {
                const pickedLabel = this.map.scene.pick(e.position);
                this.handler &&
                    this.handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
                this.handler &&
                    this.handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
                if (this.leftDownCode) {
                    this.map.scene.screenSpaceCameraController.enableRotate = true;
                    this.map.scene.screenSpaceCameraController.enableTranslate = true;
                    let ray = this.map.camera.getPickRay(e.position);
                    let cartesian = this.map.scene.globe.pick(ray, this.map.scene);
                    var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                    //弧度转经纬度
                    var lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
                    var lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
                    if (this.code == 2) {
                        if (this.markerPoint.length > 3) {
                            let isCross = this.judgeCross({
                                isSaved: true,
                                point: { lng, lat },
                            });
                            if (isCross) {
                                this.returnToInitial();
                                this.$message.warning({
                                    message: this.routeLanguage.messageInfo2,
                                    duration: 1000,
                                });
                                this.dragEditFenceOrthoCenter();
                                return false;
                            }
                        }
                    } else if (this.code == 3) {
                        let point = {
                            lng,
                            lat,
                        };
                        let isJudge = this.judgeInArea(point, this.clickCode - 1);
                        if (!isJudge) {
                            this.dragEditFenceOrthoCenter();
                            return false;
                        }
                        if (this.markerPoint.length > 3) {
                            let isCross = this.judgeCross({
                                isSaved: true,
                                point: { lng, lat },
                            });
                            if (isCross) {
                                this.returnToInitial();
                                this.$message.warning({
                                    message: this.routeLanguage.messageInfo3,
                                    duration: 1000,
                                });
                                this.dragEditFenceOrthoCenter();
                                return false;
                            }
                        }
                        this.mouseupCode = true;
                        this.drawOrthoInPolyLine();
                        if (this.markerPoint.length > 2) {
                            this.area = computedMapMethods("computedArea", {
                                positions: this.markerPoint,
                            });
                        } else {
                            this.area = 0;
                        }
                    } else {
                        let point = {
                            lng,
                            lat,
                            height: this.markerPoint[this.clickCode - 1].height,
                        };
                        let isJudge = this.judgeInArea(point, this.clickCode - 1);
                        if (!isJudge) {
                            this.dragEditRouteCenter(this.clickCode - 1);
                            return false;
                        }
                        this.computedData(this.$refs.routeEdit.routeForm.auto_speed);
                        this.changeElevation(this.clickCode - 1, point);
                    }
                    this.moveBeforePoint = {};
                    setTimeout(() => {
                        this.deepCopy();
                    }, 200);
                }
            },
            //回到移动前的位置
            returnToInitial() {
                this.markerPoint[this.clickCode - 1].lat = this.moveBeforePoint.lat;
                this.markerPoint[this.clickCode - 1].lng = this.moveBeforePoint.lng;
                let cartesian1 = new Cesium.Cartesian3.fromDegrees(
                    this.markerPoint[this.clickCode - 1].lng,
                    this.markerPoint[this.clickCode - 1].lat,
                    this.markerPoint[this.clickCode - 1].height
                );
                this.linearr[this.clickCode - 1] = cartesian1;
                let pointDraged = this.map.entities.getById(this.clickCode);
                pointDraged.position = cartesian1;
                this.changePoint = {
                    index: this.clickCode - 1,
                    lat: this.moveBeforePoint.lat,
                    lng: this.moveBeforePoint.lng,
                    type: "edit",
                };
            },
            //鼠标移动
            mouseMoveEvent(e) {
                let pointDraged = this.map.entities.getById(this.clickCode);
                // let ray = this.map.camera.getPickRay(e.endPosition);
                // let cartesian = this.map.scene.globe.pick(ray, this.map.scene);
                let cartesian = this.map.camera.pickEllipsoid(
                    e.endPosition,
                    this.map.scene.globe.ellipsoid
                );
                var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
                //弧度转经纬度
                var lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
                var lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
                this.markerPoint[this.clickCode - 1].lat = lat;
                this.markerPoint[this.clickCode - 1].lng = lng;
                let cartesian1 = new Cesium.Cartesian3.fromDegrees(
                    this.markerPoint[this.clickCode - 1].lng,
                    this.markerPoint[this.clickCode - 1].lat,
                    this.markerPoint[this.clickCode - 1].height
                );
                this.linearr[this.clickCode - 1] = cartesian1;
                pointDraged.position = cartesian1;
                this.changePoint = {
                    index: this.clickCode - 1,
                    lat: parseFloat(lat.toFixed(7)),
                    lng: parseFloat(lng.toFixed(7)),
                    type: "edit",
                };
                if (this.code == 1) {
                    this.clearElevation(this.clickCode - 1);
                    this.dragEditRouteCenter(this.clickCode - 1);
                } else {
                    this.dragEditFenceOrthoCenter();
                }
            },
            //点击删除
            removeMarker() {
                if (this.markerPoint.length < 1) {
                    return false;
                }
                if (this.removeClick) {
                    return false;
                }
                this.removeClick = true;
                this.map.entities.removeById(this.clickCode);
                this.num--;
                for (
                    let index = this.clickCode; index < this.markerPoint.length; index++
                ) {
                    let entity = this.map.entities.getById(index + 1);
                    // entity.id=index
                    entity.label.text = index.toString();
                    let marker = {
                        id: index,
                        position: entity.position,
                        point: entity.point,
                        label: entity.label,
                    };
                    this.map.entities.add(marker);
                    this.map.entities.remove(entity);
                }
                this.linearr.splice(this.clickCode - 1, 1);
                this.markerPoint.splice(this.clickCode - 1, 1);
                if (this.code == 1) {
                    this.removeUpdateCenterRoute(this.clickCode - 1);
                    if (this.markerPoint.length > 1) {
                        this.computedData(this.$refs.routeEdit.routeForm.auto_speed);
                    } else {
                        this.distance = 0;
                        this.estTime = 0;
                    }
                    this.reElevation();
                } else {
                    this.removeUpdateCenterFenceOrtho(this.clickCode - 1);
                }
                this.changePoint = {
                    index: this.clickCode - 1,
                    lat: 0,
                    lng: 0,
                    type: "remove",
                };
                this.clickCode = "";
                if (this.num > 0) {
                    this.clickMarker(this.num);
                }
                if (this.code == 3) {
                    this.drawOrthoInPolyLine();
                    if (this.markerPoint.length > 2) {
                        this.area = computedMapMethods("computedArea", {
                            positions: this.markerPoint,
                        });
                    } else {
                        this.area = 0;
                    }
                }
                setTimeout(() => {
                    this.removeClick = false;
                    this.deepCopy();
                }, 200);
            },
            //删除围栏/正射影像中心点
            removeUpdateCenterFenceOrtho(index) {
                for (let i = index; i < this.markerPoint.length - 1; i++) {
                    let center = computedMapMethods("computeCenter", {
                        lnglatArr: [this.markerPoint[i], this.markerPoint[i + 1]],
                    });
                    let entity = this.map.entities.getById("center" + (i + 1));
                    let cartesian1 = new Cesium.Cartesian3.fromDegrees(
                        center.lng,
                        center.lat,
                        center.height
                    );
                    entity.position = cartesian1;
                }
                if (index > 0 && index < this.markerPoint.length) {
                    let center = computedMapMethods("computeCenter", {
                        lnglatArr: [this.markerPoint[index - 1], this.markerPoint[index]],
                    });
                    let entity = this.map.entities.getById("center" + index);
                    let cartesian1 = new Cesium.Cartesian3.fromDegrees(
                        center.lng,
                        center.lat,
                        center.height
                    );
                    entity.position = cartesian1;
                }
                this.map.entities.removeById("center" + (this.markerPoint.length + 1));
                if (this.markerPoint.length < 3) {
                    this.map.entities.removeById("center" + this.markerPoint.length);
                } else {
                    let center = computedMapMethods("computeCenter", {
                        lnglatArr: [
                            this.markerPoint[0],
                            this.markerPoint[this.markerPoint.length - 1],
                        ],
                    });
                    let entity = this.map.entities.getById(
                        "center" + this.markerPoint.length
                    );
                    let cartesian1 = new Cesium.Cartesian3.fromDegrees(
                        center.lng,
                        center.lat,
                        center.height
                    );
                    entity.position = cartesian1;
                }
            },
            //删除航线中心点
            removeUpdateCenterRoute(index) {
                let num = index > 0 ? index - 1 : index;
                for (let i = num; i < this.markerPoint.length - 1; i++) {
                    let center = computedMapMethods("computeCenter", {
                        lnglatArr: [this.markerPoint[i], this.markerPoint[i + 1]],
                    });
                    let entity = this.map.entities.getById("center" + (i + 1));
                    let cartesian1 = new Cesium.Cartesian3.fromDegrees(
                        center.lng,
                        center.lat,
                        center.height
                    );
                    entity.position = cartesian1;
                }
                this.map.entities.removeById("center" + this.markerPoint.length);
            },
            //清除所有点
            delMarker() {
                if (this.markerPoint.length < 1) {
                    return false;
                }
                this.delClick = true;
                this.$confirm(
                        this.code == 1 || this.code == 3 ?
                        this.routeLanguage.placeholder8 :
                        this.routeLanguage.placeholder9,
                        this.routeLanguage.tip, {
                            confirmButtonText: this.routeLanguage.saveBtn,
                            cancelButtonText: this.routeLanguage.cancelBtn,
                            type: "warning",
                            customClass: "messageTip",
                        }
                    )
                    .then(() => {
                        this.deepCopy();
                        this.clearEntity(true);
                        this.changePoint = {
                            index: 0,
                            lat: 0,
                            lng: 0,
                            type: "del",
                        };
                        if (this.code !== 2) {
                            this.clearElevationMarker();
                        }
                    })
                    .finally(() => {
                        this.delClick = false;
                    });
            },
            //点击撤回
            recallMarker() {
                if (this.loading) {
                    this.$message.warning({
                        message: this.routeLanguage.placeholder3,
                        customClass: "message-info-tip",
                    });
                    return false;
                }
                if (!this.drawend) {
                    return false;
                }
                if (this.recallClick) {
                    return false;
                }
                this.recallClick = true;
                if (this.cacheData.length > 1) {
                    this.recallDraw();
                } else {
                    this.$message.warning({
                        message: this.routeLanguage.placeholder10,
                        customClass: "message-info-tip",
                    });
                }
                setTimeout(() => {
                    this.recallClick = false;
                }, 200);
            },
            //撤回绘制
            recallDraw() {
                let oldArr = this.cacheData[this.cacheData.length - 2];
                let paths = JSON.parse(JSON.stringify(oldArr.markerPoints));
                let len = this.markerPoint.length;
                this.markerPoint = [];
                this.linearr = [];
                for (let index = 0; index < paths.length; index++) {
                    let cartesian3 = new Cesium.Cartesian3.fromDegrees(
                        paths[index].lng,
                        paths[index].lat,
                        paths[index].height
                    );
                    let entity = this.map.entities.getById(index + 1);
                    this.linearr.push(cartesian3);
                    if (entity) {
                        this.markerPoint.push(paths[index]);
                        entity.position = cartesian3;
                    } else {
                        this.recallDrawMarker(paths[index]);
                    }
                }
                if (len > paths.length) {
                    for (let i = paths.length; i < len; i++) {
                        this.map.entities.removeById(i + 1);
                    }
                    this.linearr.splice(paths.length, len - paths.length);
                    this.num = paths.length;
                }
                this.recallDrawCenter();
                let formData = JSON.parse(JSON.stringify(oldArr.formData));
                if (this.code == 1) {
                    this.$refs.routeEdit.routeForm = formData;
                    if (this.markerPoint.length > 1) {
                        this.computedData(this.$refs.routeEdit.routeForm.auto_speed);
                    } else {
                        this.distance = 0;
                        this.estTime = 0;
                    }
                } else if (this.code == 2) {
                    this.$refs.fenceEdit.fenceForm = formData;
                } else if (this.code == 3) {
                    this.$refs.orthoEdit.orthoForm = formData;
                    if (this.markerPoint.length > 2) {
                        this.area = computedMapMethods("computedArea", {
                            positions: this.markerPoint,
                        });
                    } else {
                        this.area = 0;
                    }
                    setTimeout(() => {
                        this.drawOrthoInPolyLine();
                    });
                }
                this.clickMarker(oldArr.clickCode);
                if (!this.operateEntrty) {
                    if (this.code == 1) {
                        this.drawRouteLine();
                    } else {
                        this.drawFencePolygon();
                    }
                }
                this.cacheData.pop();
            },
            recallDrawMarker(point) {
                this.num++;
                this.markerPoint.push(point);
                let marker = mapMethods.drawPointLabel(point, {
                    text: this.num,
                    id: this.num,
                    className: this.code == 2 ? "marker-fence" : "marker-route",
                });
                this.map.entities.add(marker);
            },
            recallDrawCenter() {
                let className =
                    this.code == 2 ? "marker-fence-center" : "marker-route-center";
                for (let i = 0; i < this.markerPoint.length - 1; i++) {
                    let center = computedMapMethods("computeCenter", {
                        lnglatArr: [this.markerPoint[i], this.markerPoint[i + 1]],
                    });
                    let entity = this.map.entities.getById("center" + (i + 1));
                    if (entity) {
                        entity.position = new Cesium.Cartesian3.fromDegrees(
                            center.lng,
                            center.lat,
                            center.height
                        );
                    } else {
                        let addMarker = mapMethods.drawPointLabel(center, {
                            text: "+",
                            id: "center" + (i + 1),
                            className: className,
                        });
                        this.map.entities.add(addMarker);
                    }
                }
                let index = this.markerPoint.length;
                if (this.code !== 1) {
                    if (this.markerPoint.length > 1) {
                        let center1 = computedMapMethods("computeCenter", {
                            lnglatArr: [
                                this.markerPoint[this.markerPoint.length - 1],
                                this.markerPoint[0],
                            ],
                        });
                        let entity = this.map.entities.getById(
                            "center" + this.markerPoint.length
                        );
                        if (entity) {
                            entity.position = new Cesium.Cartesian3.fromDegrees(
                                center1.lng,
                                center1.lat,
                                center1.height
                            );
                        } else {
                            let addMarker = mapMethods.drawPointLabel(center1, {
                                text: "+",
                                id: "center" + this.markerPoint.length,
                                className: className,
                            });
                            this.map.entities.add(addMarker);
                        }
                        index = this.markerPoint.length + 1;
                    }
                }
                while (true) {
                    let entity = this.map.entities.getById("center" + index);
                    if (!entity) {
                        break;
                    }
                    this.map.entities.remove(entity);
                    index += 1;
                }
            },
            //清空操作航线/围栏
            clearEntity(deepCopyCode) {
                for (let index = 0; index < this.markerPoint.length; index++) {
                    this.map.entities.removeById(index + 1);
                    this.map.entities.removeById("center" + (index + 1));
                }
                // this.map.entities.removeById("center" + (this.markerPoint.length + 1));
                this.markerPoint = [];
                this.map.entities.removeById("operateEntrty");
                this.operateEntrty = "";

                this.linearr = [];
                this.clickCode = "";
                this.num = 0;
                if (this.timer) {
                    clearTimeout(this.timer);
                    this.timer = null;
                }
                this.orthoarr = [];
                this.area = 0;
                this.distance = 0;
                this.estTime = 0;
                this.photoCount = 0;
                this.routeSpotCount = 0;

                if (!deepCopyCode) {
                    this.cacheData = [];
                }
                this.map.entities.removeById("operateOrtho");
                this.operateOrtho = "";
                this.map.entities.removeById("markerStart");
                this.map.entities.removeById("markerEnd");
            },
            //导入
            importEvent() {
                if (this.loading) {
                    this.$message.warning({
                        message: this.routeLanguage.placeholder3,
                        customClass: "message-info-tip",
                    });
                    return false;
                }
                if (!this.importClick) {
                    this.importClick = true;
                    if (this.markerPoint.length > 0) {
                        this.$confirm(
                                this.code == 1 || this.code == 3 ?
                                this.routeLanguage.placeholder11 :
                                this.routeLanguage.placeholder12,
                                this.routeLanguage.tip, {
                                    confirmButtonText: this.routeLanguage.saveBtn,
                                    cancelButtonText: this.routeLanguage.cancelBtn,
                                    type: "warning",
                                }
                            )
                            .then(() => {
                                this.$refs.files.click();
                                this.clearEntity();
                                this.changePoint = {
                                    index: 0,
                                    lat: 0,
                                    lng: 0,
                                    type: "del",
                                };
                                if (this.code !== 2) {
                                    this.clearElevationMarker();
                                }
                            })
                            .catch(() => {})
                            .finally(() => {
                                this.importClick = false;
                            });
                    } else {
                        this.$refs.files.click();
                        this.timeOut = setTimeout(() => {
                            this.importClick = false;
                        }, 300);
                    }
                }
            },
            //读取文件
            chooseFileAfter(e) {
                let file = e.target.files[0];
                let a = file.name.split(".");
                let file_reader = new FileReader();
                file_reader.readAsText(file, "UTF-8");
                if (a[a.length - 1] == "kml") {
                    file_reader.onload = () => {
                        const xml = new DOMParser().parseFromString(
                            file_reader.result,
                            "text/xml"
                        );
                        let kml = togeojson.kml(xml, {
                            style: true,
                        });
                        if (kml.features[0].geometry.type == "Point") {
                            this.importPoints = kml.features.map((x) => {
                                return x.geometry.coordinates;
                            });
                        } else {
                            this.importPoints = kml.features[0].geometry.coordinates;
                        }
                        // this.importPoints = kml.features.map((x) => {
                        //   return x.geometry.coordinates;
                        // });
                        let points = this.importPoints.map((x) => {
                            // let a = wgs84_to_gcj02(parseFloat(x[0]), parseFloat(x[1]));
                            let a = pointsConvert({
                                point: [parseFloat(x[0]), parseFloat(x[1])],
                                type: 10,
                            });
                            return {
                                lng: a[0],
                                lat: a[1],
                                height: x[2],
                            };
                        });
                        this.kmlFormat(points);
                    };
                    let obj = this.$refs.files;
                    obj.value = "";
                } else if (a[a.length - 1] == "json") {
                    file_reader.onload = () => {
                        this.exportPoint(JSON.parse(file_reader.result));
                    };
                    let obj = this.$refs.files;
                    obj.value = "";
                } else if (a[a.length - 1] == "plan") {
                    file_reader.onload = () => {
                        this.analysisPlan(JSON.parse(file_reader.result));
                    };
                    let obj = this.$refs.files;
                    obj.value = "";
                } else {
                    this.$message.error({
                        message: this.routeLanguage.errorMessage1,
                        customClass: "message-info-tip",
                    });
                }
            },
            //kml转换城坐标
            async kmlFormat(points) {
                if (this.code == 1) {
                    this.$refs.routeEdit.pointListImport = this.importPoints;
                    // if (kml.features[0].geometry.type == "LineString") {
                    if (!points) {
                        this.$message.error({
                            message: this.routeLanguage.errorMessage2,
                            customClass: "message-info-tip",
                        });
                        return false;
                    }
                    this.importCode = true;
                    this.loading = true;
                    // for (let index = 0; index < datas.length; index++) {
                    //   let str = new AMap.LngLat(datas[index][0], datas[index][1]);
                    //   pointstr.push(str);
                    // }
                    await this.drawImport(points, true);
                    // } else {
                    //   this.$message.error("选择的kml文件不是任务航线！");
                    // }
                } else if (this.code == 2) {
                    // kml.features[0].geometry.type == "Polygon"
                    if (!points) {
                        this.$message.error({
                            message: this.routeLanguage.errorMessage2,
                            customClass: "message-info-tip",
                        });
                        return false;
                    }
                    this.importCode = true;
                    this.loading = true;
                    // let pointstr = [];

                    // for (let index = 0; index < datas.length; index++) {
                    //   let str = new AMap.LngLat(datas[index][0], datas[index][1]);
                    //   pointstr.push(str);
                    // }
                    await this.drawImportfence(points);
                } else if (this.code == 3) {
                    if (!points) {
                        this.$message.error({
                            message: this.routeLanguage.errorMessage2,
                            customClass: "message-info-tip",
                        });
                        return false;
                    }
                    // this.orthoForm.lateral=kml.features[0].properties.lateral||this.orthoForm.lateral
                    // this.orthoForm.course=kml.features[0].properties.course||this.orthoForm.course
                    // this.orthoForm.wheelDist=kml.features[0].properties.wheelDist||this.orthoForm.wheelDist
                    // this.orthoForm.angle=kml.features[0].properties.angle||this.orthoForm.angle
                    this.importCode = true;
                    this.loading = true;
                    await this.drawImportOrtho(points);
                }
            },
            //json文件解析的点
            exportPoint(files) {
                let paths = [];
                for (let index = 0; index < files.points.length; index++) {
                    let obj = {};
                    obj.lng = parseFloat(files.points[index].lng.toFixed(7));
                    obj.lat = parseFloat(files.points[index].lat.toFixed(7));
                    obj.height = files.points[index].height;
                    obj.action = [];
                    if (
                        files.points[index].yawPitchArray &&
                        files.points[index].yawPitchArray.length > 0
                    ) {
                        obj.action.push({
                            action_id: "gimbal_ctrl",
                            param_list: [{
                                    param_id: "gimbal_yaw",
                                    value: files.points[index].yawPitchArray[0].aircraftYaw,
                                },
                                {
                                    param_id: "gimbal_pitch",
                                    value: files.points[index].yawPitchArray[0].gimbalPitch,
                                },
                            ],
                        });
                    }
                    paths.push(obj);
                }
                this.importCode = true;
                this.loading = true;
                if (this.code == 1) {
                    if (!this.$refs.routeEdit.routeForm.name) {
                        this.$refs.routeEdit.routeForm.name = files.taskname;
                    }
                    this.$refs.routeEdit.routeForm.default_height = files.height;
                    this.drawImport(paths);
                } else if (this.code == 3) {
                    if (!this.$refs.orthoEdit.orthoForm.name) {
                        this.$refs.orthoEdit.orthoForm.name = files.taskname;
                    }
                    this.$refs.orthoEdit.orthoForm.default_height = files.height;
                    this.drawImportOrtho(paths);
                } else if (this.code == 2) {
                    if (!this.$refs.fenceEdit.fenceForm.name) {
                        this.$refs.fenceEdit.fenceForm.name = files.taskname;
                    }
                    let path = [];
                    for (let index = 0; index < files.points.length; index++) {
                        path.push([files.points[index].lng, files.points[index].lat]);
                    }
                    this.drawImportfence(path);
                }
            },
            //解析plan文件坐标点
            analysisPlan(resultData) {
                try {
                    let points = [];
                    let mission = resultData.mission;
                    let paths = mission.items;
                    this.importPoints = [];
                    for (let index = 0; index < paths.length; index++) {
                        const item = paths[index];
                        let point = pointsConvert({
                            point: [parseFloat(item.params[5]), parseFloat(item.params[4])],
                            type: 10,
                        });
                        let obj = {
                            lng: point[0],
                            lat: point[1],
                            height: Number(item.Altitude.toFixed(2)),
                            action: actionChangeArray(item.actions),
                        };
                        this.importPoints.push([
                            parseFloat(item.params[5]),
                            parseFloat(item.params[4]),
                            Number(item.Altitude.toFixed(2)),
                        ]);
                        points.push(obj);
                    }
                    if (this.code == 1) {
                        let form = this.$refs.routeEdit.routeForm;
                        form.name = mission.name;
                        form.auto_speed = mission.flySpeed;
                        form.height_type = paths[0].AltitudeMode == 2 ? 1 : 0;
                    } else if (this.code == 3) {
                        let form = this.$refs.orthoEdit.orthoForm;
                        form.name = mission.name;
                        form.auto_speed = mission.flySpeed;
                    } else if (this.code == 2) {
                        let form = this.$refs.fenceEdit.fenceForm;
                        form.name = mission.name;
                    }
                    this.kmlFormat(points);
                } catch (error) {
                    console.log("数据解析失败");
                }
            },
            //绘制导入的航点
            async drawImport(pointstr, importCode) {
                if (this.num < pointstr.length) {
                    let action = [];
                    if (pointstr[this.num].action) {
                        for (let h = 0; h < pointstr[this.num].action.length; h++) {
                            if (pointstr[this.num].action[h].id == "AircraftYaw") {
                                action.push({
                                    action_id: "uav_yaw",
                                    param_list: [{
                                        param_id: "yaw",
                                        value: pointstr[this.num].action[h].param,
                                    }, ],
                                });
                            }
                            if (pointstr[this.num].action[h].id == "GimbalPitch") {
                                action.push({
                                    action_id: "gimbal_ctrl",
                                    param_list: [{
                                            param_id: "gimbal_yaw",
                                            value: 0,
                                        },
                                        {
                                            param_id: "gimbal_pitch",
                                            value: pointstr[this.num].action[h].param,
                                        },
                                    ],
                                });
                            }
                            if (pointstr[this.num].action[h].id == "ShootPhoto") {
                                action.push({
                                    action_id: "takephoto",
                                    param_list: "",
                                });
                            }
                        }
                    }
                    let a = this.drawPointRouteEntrty(
                        pointstr[this.num], !this.extra,
                        importCode
                    );
                    if (!a) {
                        this.extra = true;
                    }
                    setTimeout(() => {
                        this.drawImport(pointstr, importCode);
                    });
                } else {
                    this.loading = false;
                    this.importCode = false;
                    if (!this.drawend) {
                        this.drawMark();
                    }
                    // this.map.setFitView(this.line, true, [100, 100, 150, 100], 30);
                    // this.markerClick(this.markers[this.markers.length - 1]);
                    this.deepCopy();
                }
            },
            //绘制导入的围栏
            async drawImportfence(pointstr) {
                if (this.num < pointstr.length) {
                    let a = await this.drawFenceEntrty(pointstr[this.num]);
                    if (!a) {
                        this.loading = false;
                        this.importCode = false;
                        if (!this.drawend) {
                            this.drawMark();
                        }
                        this.deepCopy();
                        this.changePoint = {
                            index: this.num - 1,
                            lng: 0,
                            lat: 0,
                            type: "remove",
                        };
                        return false;
                    }
                    setTimeout(() => {
                        this.drawImportfence(pointstr);
                    });
                } else {
                    this.loading = false;
                    this.importCode = false;
                    if (!this.drawend) {
                        this.drawMark();
                    }
                    this.deepCopy();
                }
            },
            //绘制正射影像导入的点
            async drawImportOrtho(pointstr) {
                if (this.num < pointstr.length) {
                    let a = this.drawOrthoEntrty(pointstr[this.num], !this.extra, false);
                    if (!a) {
                        this.extra = true;
                    }
                    setTimeout(() => {
                        this.drawImport(pointstr);
                    });
                } else {
                    this.loading = false;
                    this.importCode = false;
                    if (!this.drawend) {
                        this.drawMark();
                    }
                    this.drawOrthoInPolyLine();
                    this.deepCopy();
                }
            },
            //编辑时导出
            exportEvent() {
                if (this.loading) {
                    this.$message.warning({
                        message: this.routeLanguage.placeholder3,
                        customClass: "message-info-tip",
                    });
                    return false;
                }
                if (!this.exportClick) {
                    this.exportClick = true;
                    this.$confirm(
                            this.code == 1 || this.code == 3 ?
                            this.routeLanguage.placeholder13 :
                            this.routeLanguage.placeholder14,
                            this.routeLanguage.tip, {
                                confirmButtonText: this.routeLanguage.saveBtn,
                                cancelButtonText: this.routeLanguage.cancelBtn,
                                type: "warning",
                            }
                        )
                        .then(() => {
                            let formList = {};
                            let message = this.routeLanguage.routeLine.placeholder;
                            let messageInfo = this.routeLanguage.messageInfo5;
                            let routeItem = "";
                            if (this.code == 1) {
                                formList = this.$refs.routeEdit.routeForm;
                                routeItem = this.routeItem;
                            } else if (this.code == 2) {
                                formList = this.$refs.fenceEdit.fenceForm;
                                message = this.routeLanguage.fence.placeholder1;
                                messageInfo = this.routeLanguage.messageInfo4;
                            } else if (this.code == 3) {
                                formList = this.$refs.orthoEdit.orthoForm;
                                formList.point_json = this.orthoAllPoints ?
                                    this.orthoAllPoints : [];
                            }
                            if (!formList.name) {
                                this.$message.error({
                                    message: message,
                                    customClass: "message-info-tip",
                                });
                            } else {
                                if (this.code == 2) {
                                    if (formList.point_json.length > 2) {
                                        let str =
                                            `<?xml version='1.0' encoding='utf-8'?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>
                              <Placemark><name>` +
                                            formList.name +
                                            `</name><type></type><ExtendedData><Data name="type"><value>` +
                                            "GCJ-02" +
                                            `</value></Data></ExtendedData><Polygon><outerBoundaryIs><LinearRing><coordinates>`;
                                        for (
                                            let index = 0; index < formList.point_json.length; index++
                                        ) {
                                            str +=
                                                formList.point_json[index].lng +
                                                "," +
                                                formList.point_json[index].lat +
                                                " ";
                                        }
                                        str += `</coordinates></LinearRing></outerBoundaryIs></Polygon><styleUrl>style-id</styleUrl></Placemark><Style id="style-id"><LineStyle><color>ffff5213</color><width>6</width></LineStyle><PolyStyle><color>80B15713</color></PolyStyle></Style></Document></kml>`;
                                        const domObj = document.createElement("a");
                                        domObj.setAttribute(
                                            "href",
                                            "data:text/xml;charset=utf-8," + encodeURIComponent(str)
                                        ); //注：如存储数组 or JSON需将其转换为JSON字符串
                                        domObj.setAttribute(
                                            "download",
                                            formList.name + this.returnDate() + ".kml"
                                        );
                                        if (document.createEvent) {
                                            const event = document.createEvent("MouseEvents");
                                            event.initEvent("click", true, true);
                                            domObj.dispatchEvent(event);
                                        } else {
                                            domObj.click();
                                        }
                                    } else {
                                        this.$message.error({
                                            message: messageInfo,
                                            customClass: "message-info-tip",
                                        });
                                    }
                                } else {
                                    if (formList.point_json.length > 1) {
                                        exportRoute({
                                            operateItem: formList,
                                            routeItem: routeItem,
                                        });
                                    } else {
                                        this.$message.error({
                                            message: messageInfo,
                                            customClass: "message-info-tip",
                                        });
                                    }
                                }
                            }
                        })
                        .catch(() => {
                            this.$message.info({
                                message: this.routeLanguage.cancelexport,
                                customClass: "message-info-tip",
                            });
                        })
                        .finally(() => {
                            this.exportClick = false;
                        });
                }
            },
            //列表中导出航线
            exportRouteEvent(item) {
                this.$confirm(this.routeLanguage.placeholder13, this.routeLanguage.tip, {
                        confirmButtonText: this.routeLanguage.saveBtn,
                        cancelButtonText: this.routeLanguage.cancelBtn,
                        type: "warning",
                    })
                    .then(() => {
                        let operateItem = {
                            name: item.title,
                            point_json: [],
                        };
                        if (item.type == 50) {
                            operateItem.point_json = this.orthoAllPoints ?
                                this.orthoAllPoints : [];
                        } else {
                            for (let index = 0; index < item.point_list.length; index++) {
                                item.point_list[index].lat = item.point_list[index].lat_int / 1e7;
                                item.point_list[index].lng = item.point_list[index].lon_int / 1e7;
                                item.point_list[index].height =
                                    item.point_list[index].height / 100;
                                operateItem.point_json.push(item.point_list[index]);
                            }
                        }
                        if (operateItem.point_json.length > 1) {
                            exportRoute({
                                operateItem,
                                routeItem: "",
                            });
                        } else {
                            this.$message.error({
                                message: this.routeLanguage.messageInfo4,
                                customClass: "message-info-tip",
                            });
                        }
                    })
                    .catch(() => {
                        this.$message.info({
                            message: this.routeLanguage.cancelexport,
                            customClass: "message-info-tip",
                        });
                    });
            },

        },
    }
}