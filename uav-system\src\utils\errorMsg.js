export function errorMsg(code, language) {
    let msg = "";
    let msg1 = "";

    switch (code) {
        case 5:
            msg = "舱门未打开！";
            msg1 = "The hatch is not open!";
            break;
        case 6:
            msg = "舱门未关闭！";
            msg1 = "The hatch is not closed!";
            break;
        case 7:
            msg = "升降台未升起！";
            msg1 = "The lifting table is not raised!";
            break;
        case 8:
            msg = "升降台未降落！";
            msg1 = "The lifting platform has not landed!";
            break;
        case 9:
            msg = "脚架X方向未张开！";
            msg1 = "The tripod is not opened in the X direction!";
            break;
        case 10:
            msg = "脚架X方向未收缩！";
            msg1 = "The tripod is not contracted in the X direction!";
            break;
        case 11:
            msg = "脚架Y方向未张开！";
            msg1 = "The tripod is not opened in Y direction!";
            break;
        case 12:
            msg = "脚架Y方向未收缩！";
            msg1 = "The tripod is not contracted in the Y direction!";
            break;
        case 13:
            msg = "舱门打开超时！";
            msg1 = "Hatch opening timeout!";
            break;
        case 14:
            msg = "舱门关闭超时！";
            msg1 = "Hatch closing timeout!";
            break;
        case 15:
            msg = "升降台升起超时！";
            msg1 = "Lifting table lifting timeout!";
            break;
        case 16:
            msg = "升降台下降超时！";
            msg1 = "Lifting table lowering timeout!";
            break;
        case 17:
            msg = "脚架X方向打开超时！";
            msg1 = "Tripod X direction opening timeout!";
            break;
        case 18:
            msg = "脚架X方向收缩超时！";
            msg1 = "Tripod X direction contraction timeout!";
            break;
        case 19:
            msg = "脚架Y方向打开超时！";
            msg1 = "Tripod Y direction opening timeout!";
            break;
        case 20:
            msg = "脚架Y方向收缩超时！";
            msg1 = "Tripod Y-direction contraction timeout!";
            break;
        default:
            break;
    }
    if (language == "zh-CN") {
        return msg;
    } else {
        return msg1;
    }
}