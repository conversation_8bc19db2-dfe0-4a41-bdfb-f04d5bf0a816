.userMange {
  .userManage {
    background-color: #040404;

    .searchDiv {
      .el-button {
        background-color: white;
        color: #0b58de;
        border: none;

        &.active {
          background-color: #0b58de;
          color: white;
        }
      }

      .el-input {
        .el-input__inner {
          background-color: transparent !important;
          color: white !important;
        }
      }

    }

    .custom-table {
      .el-button {
        background-color: transparent;
        border: none;
        color: white;

        &.active {
          color: #0b58de;
        }
      }
    }

    .el-dialog {
      .userForm {
        .fromButton {
          .saveBut {
            background-color: #0b58de;
            color: #ffffff;

            &.checked {
              color: #0b58de;
              background-color: transparent;
            }
          }

          .closeBut {
            color: #0b58de;
            background-color: transparent;

            &.checked {
              background-color: #0b58de;
              color: white;
            }
          }
        }
      }

      .delButDiv {
        .saveBut {
          background-color: #de0b0b;
          color: #ffffff;
          &.recoverBtn{
            background-color: #0b58de;
          }

          &.active {
            background-color: transparent;
            color: #0b58de;
          }
        }

        .closeBut {
          background-color: #7c7e82;
          color: #ffffff;

          &.active {
            background-color: #0b58de;
            color: white;
          }
        }
      }
    }

    .el-dialog__wrapper {
      .el-dialog__header {
        .el-dialog__headerbtn {
          border: 1px solid #909399 !important;
        }
      }

      .el-dialog__body {
        .el-divider {
          background-color: #127ED7 !important;
        }
      }

      input::-webkit-input-placeholder {
        color: #cfcfcf !important;
      }

      input::-moz-input-placeholder {
        color: #cfcfcf !important;
      }

      input::-ms-input-placeholder {
        color: #cfcfcf !important;
      }
    }

    .delDialog {
      .el-dialog__body {
        color: #827f7f !important;
      }
    }

    input::-webkit-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-moz-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-ms-input-placeholder {
      color: #5e5e5e !important;
    }

    // scrollbar-width: thin !important;
    // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    // -ms-overflow-style: none !important;
    // scrollbar-color: #777777 #ccc;

    &::-webkit-scrollbar {
      width: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #ccc;
      -webkit-border-radius: 2em;
      -moz-border-radius: 2em;
      border-radius: 2em;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #777777;
      -webkit-border-radius: 2em;
      -moz-border-radius: 2em;
      border-radius: 2em;
    }
  }
}
