<template>
  <div class="">
    <div id="container" style="width: 100%; height: 100%"></div>
    <!-- 操作列表 -->
    <div class="panorama-operate">
      <div class="controlbar">
        <div class="controlbar-main">
          <div class="compass" ref="compass" @click="restore">
            <div class="pointers"></div>
          </div>
          <div class="pitchUp" @mousedown="pitchUp"></div>
          <div class="pitchDown" @mousedown="pitchDown"></div>
          <div class="rotateLeft" @mousedown="rotateLeft"></div>
          <div class="rotateRight" @mousedown="rotateRight"></div>
        </div>
      </div>

      <!-- 放大缩小 -->
      <div class="zooming">
        <div class="zooming-item" @click="buttonWheels('min')">
          <span class="el-icon-zoom-out"></span>
        </div>
        <div class="zooming-item" @click="buttonWheels('max')">
          <span class="el-icon-zoom-in"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import * as THREE from 'three'
import "@/assets/js/three.min.js";

var camera;
var renderer;
var scene;

export default {
  name: "panorama",
  props: {
    // src: {
    //   type: String,
    //   default: "https://app.walkera.cn/nest/result/photo/wk/a/20e1cf62e054f0f2/456x/stitch.jpg"
    // }
    src: String,
  },
  data() {
    return {
      bigImg: this.src, //全景图图片路径
      skyBox: null,
      time: null,
    };
  },
  created() {
    if (!this.src) {
      let router = this.$route.query;
      this.bigImg = router.imgSrc;
    }
  },
  mounted() {
    // 调用全景图函数
    this.$nextTick(() => {
      this.init();
      this.animate();
    });
  },
  methods: {
    // 全景图配置函数---------------
    init() {
      var container = document.getElementById("container");

      // 创建渲染器
      renderer = new THREE.WebGLRenderer();
      renderer.setPixelRatio(window.devicePixelRatio);

      // 设置画布的宽高
      renderer.setSize(window.innerWidth, window.innerHeight);

      // 判断容器中子元素的长度
      let childs = container.childNodes;

      if (container.childNodes.length > 0) {
        container.removeChild(childs[0]);
        container.appendChild(renderer.domElement);
      } else {
        container.appendChild(renderer.domElement);
      }

      

      // 创建场景
      scene = new THREE.Scene();

      // 创建相机
      camera = new THREE.PerspectiveCamera(
        90,
        window.innerWidth / window.innerHeight,
        0.1,
        100
      );

      camera.position.set(0, 0, 0);
      var material = new THREE.MeshBasicMaterial(); //材质
      var texture = new THREE.TextureLoader().load(this.bigImg);
      material.map = texture;

      var skyBox = new THREE.Mesh(
        new THREE.SphereBufferGeometry(100, 100, 100),
        material
      );
      this.skyBox = skyBox;

      skyBox.geometry.scale(1, 1, -1);
      scene.add(skyBox);
      window.addEventListener("resize", this.onWindowResize, false);
      var bMouseDown = false;
      var x = -1;
      var y = -1;

      // 添加鼠标事件
      container.onmousedown = function (event) {
        event.preventDefault(); //取消默认事件
        x = event.clientX;
        y = event.clientY;
        bMouseDown = true;
      };

      container.onmouseup = function (event) {
        event.preventDefault();
        bMouseDown = false;
      };

      // 鼠标移动时旋转物体
      let _this = this;
      container.onmousemove = function (event) {
        event.preventDefault();
        if (bMouseDown) {
          skyBox.rotation.y += -0.005 * (event.clientX - x);
          skyBox.rotation.x += -0.005 * (event.clientY - y);

          if (skyBox.rotation.x > Math.PI / 2) {
            skyBox.rotation.x = Math.PI / 2;
          }

          if (skyBox.rotation.x < -Math.PI / 2) {
            skyBox.rotation.x = -Math.PI / 2;
          }
          x = event.clientX;
          y = event.clientY;

          _this.compassRotate();
        }
      };
      // 鼠标滚轮缩放物体大小
      container.onmousewheel = this.mousewheels;
    },
    // 鼠标滚轮缩放物体大小
    mousewheels: function (event) {
      event.preventDefault && event.preventDefault();

      if (event.wheelDelta != 0) {
        camera.fov += event.wheelDelta > 0 ? 1 : -1;

        if (camera.fov > 160) {
          camera.fov = 160;
        } else if (camera.fov < 20) {
          camera.fov = 20;
        }

        camera.updateProjectionMatrix();
      }
    },

    pitchUp: function () {
      this.skyBox.rotation.x += -0.05;
      document.addEventListener("mouseup", this.mouseUpEvent);
      this.compassRotate();
      this.time = setInterval(() => {
        this.skyBox.rotation.x += -0.05;
        this.compassRotate();
      }, 200);
    },
    pitchDown: function () {
      this.skyBox.rotation.x += 0.05;
      this.compassRotate();
      document.addEventListener("mouseup", this.mouseUpEvent);
      this.time = setInterval(() => {
        this.skyBox.rotation.x += 0.05;
        this.compassRotate();
      }, 200);
    },
    rotateLeft: function () {
      this.skyBox.rotation.y += 0.05;
      document.addEventListener("mouseup", this.mouseUpEvent);
      this.compassRotate();
      this.time = setInterval(() => {
        this.skyBox.rotation.y += 0.05;
        this.compassRotate();
      }, 200);
    },
    rotateRight: function () {
      this.skyBox.rotation.y += -0.05;
      this.compassRotate();
      document.addEventListener("mouseup", this.mouseUpEvent);
      this.time = setInterval(() => {
        this.skyBox.rotation.y += -0.05;
        this.compassRotate();
      }, 200);
    },
    mouseUpEvent: function () {
      clearInterval(this.time);
      document.removeEventListener("mouseUp", this.mouseUpEvent);
    },
    compassRotate: function () {
      let x = this.skyBox.rotation.x * 180 /Math.PI;
      let y = this.skyBox.rotation.y * 180 /Math.PI;
      console.log("this.skyBox.rotation.x", this.skyBox.rotation.x);
      console.log("this.skyBox.rotation.y", this.skyBox.rotation.y);

      this.$refs.compass.style.transform = `rotateX(${x}deg) rotateZ(${y}deg)`;
    },
    restore: function () {
      this.skyBox.rotation.x = 0;
      this.skyBox.rotation.y = 0;
      this.compassRotate();
    },

    // 按钮控制物体大小
    buttonWheels: function (type) {
      camera.fov += type == "min" ? 5 : -5;

      if (camera.fov > 160) {
        camera.fov = 160;
      } else if (camera.fov < 20) {
        camera.fov = 20;
      }

      camera.updateProjectionMatrix();
    },

    onWindowResize() {
      // 窗口缩放的时候,保证场景也跟着一起缩放
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    },

    animate() {
      requestAnimationFrame(this.animate);

      renderer.render(scene, camera);
    },
  },
};
</script>

<style lang="less" scoped>
.panorama-operate {
  position: fixed;
  right: 30px;
  top: 30px;
  // height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  z-index: 20;
  // display: flex;
  .zooming {
    margin-top: 20px;
    .zooming-item {
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      // color: aqua;
      font-weight: 700;
      border: 1px solid #ccc;
      
      height: 30px;
      width: 30px;
      font-size: 18px;
      font-weight: 700;
      &:hover {
        background-color: #409eff;
        color: #fff;
      }
      &:nth-child(1) {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        border-bottom: none;
      }
      &:nth-child(2) {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
      }
    }
  }

  .controlbar {
    width: 92px;
    height: 92px;
    .controlbar-main {
      position: relative;
      width: 100%;
      height: 100%;
      background: url("../../assets/img/ctb.png") -22px -30px
        no-repeat;
      background-size: 348px 270px;
      user-select: none;
      .compass {
        top: 46px;
        left: 50%;
        position: absolute;
        margin: -24px;
        width: 48px;
        height: 48px;
        z-index: 10;
        background: url("../../assets/img/ctb.png") -231px -26px
          no-repeat;
        background-size: 348px 270px;
        .pointers {
          position: absolute;
          width: 30px;
          height: 48px;
          top: 0;
          left: 9px;
          border: none;
          z-index: 2;
          background: url("../../assets/img/ctb.png") -281px -26px
            no-repeat;
          background-size: 348px 270px;
        }
      }
      .pitchUp {
        width: 30px;
        height: 25.5px;
        position: absolute;
        top: 3.5px;
        margin-left: -15px;
        left: 50%;
        z-index: 1;
        background: url("../../assets/img/ctb.png") -302.5px -49px
          no-repeat;
        background-size: 348px 270px;
        &:hover {
          background-position: -302.5px -23.5px;
          // left: 4px;
        }
      }
      .pitchDown {
        width: 30px;
        height: 25.5px;
        position: absolute;
        top: 66px;
        transform: rotate(180deg);
        margin-left: -15px;
        left: 50%;
        z-index: 1;
        background: url("../../assets/img/ctb.png") -302.5px -49px
          no-repeat;
        background-size: 348px 270px;

        &:hover {
          background-position: -302.5px -23.5px;
          // left: 4px;
        }
      }
      .rotateLeft {
        width: 21px;
        height: 52px;
        top: 19px;
        position: absolute;
        z-index: 2;
        background: url("../../assets/img/ctb.png") -301.5px -77px
          no-repeat;
        background-size: 348px 270px;

        &:hover {
          background-position: -280.5px -77px;
          // left: 4px;
        }
      }
      .rotateRight {
        width: 21px;
        height: 52px;
        top: 19px;
        right: 5px;
        position: absolute;
        z-index: 2;
        background: url("../../assets/img/ctb.png") -301.5px -77px
          no-repeat;
        background-size: 348px 270px;
        transform: rotateY(180deg);

        &:hover {
          background-position: -280.5px -77px;
          // right: 2px;
        }
      }
    }
  }
}
</style>