<!-- 进度条 -->
<template>
  <div class="progress-bar">
    <div class="left" @click="subtract">
      <i class="el-icon-minus"></i>
    </div>
    <div class="num ml3">{{ start }}</div>
    <div class="center">
      <el-slider
        v-model="barValue"
        :show-tooltip="false"
        :step="step"
        :min="min"
        :max="max"
        @input="sliderInput"
        @change="sliderChange"
      />
    </div>
    <div class="num mr3">{{ end }}</div>
    <div class="right" @click="plus">
      <i class="el-icon-plus"></i>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    step: {
      type: Number,
      default: 1,
    },
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 0,
    },
    start: Number,
    end: [Number, String],
    value: [Number, String],
  },
  data() {
    return {
      barValue: this.value,
    };
  },
  methods: {
    subtract: function () {
      if (this.barValue <= this.min) {
        return false;
      }
      this.barValue = this.barValue - this.step;
      this.$emit("input", this.barValue);
      this.$emit("onChange", this.barValue);
    },
    plus: function () {
      if (this.barValue >= this.max) {
        return false;
      }
      this.barValue = this.barValue + this.step;
      this.$emit("input", this.barValue);
      this.$emit("onChange", this.barValue);
    },
    sliderInput: function (val) {
      this.$emit("input", val);
    },
    sliderChange: function () {
      this.$emit("onChange", this.barValue);
    },
  },
};
</script>

<style lang="less" scoped>
.progress-bar {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left,
  .right {
    width: 13px;
    height: 13px;
    border-radius: 1px;
    // border: 1px solid #707070;
    display: flex;
    align-items: center;
    justify-content: center;
    // color: #fff;
    font-size: 12px;
    &:hover {
      // color: #398bcc;
      // border-color: #398bcc;
    }
  }
  .center {
    width: 110px;
    padding: 0 5px;
  }
  .num {
    // color: #fff;
    font-size: 12px;
  }
}
</style>

<style lang="less">
.progress-bar {
  .el-slider__button {
    border: none;
    height: 10px;
    width: 10px;
  }
}
</style>