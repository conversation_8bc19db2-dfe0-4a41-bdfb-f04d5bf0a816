import axios from "axios"
const d3 = require('d3-dsv')
export function csvTo<PERSON>son(filePath) {
    return new Promise((resolve, reject) => {
        axios.get(filePath).then((res) => {
            const text = `End`
            let index = res.data.indexOf('End') + 4;
            let list = res.data.substr(index);
            let data = d3.csvParse(list)
            resolve(data);
        }).catch((err) => {
            reject(err);
        })
    })
}