<template>
  <div class="dialogType">
    <el-dialog
      title="选择任务类型"
      :visible.sync="chooseType"
      :close-on-click-modal="true"
      :show-close="false"
      width="50%"
      center
      @close="closeEvent"
    >
      <div
        class="tipInfo"
        v-if="!checkTypes || checkTypes == 20 ? false : true"
      >
        该任务类型暂未开发，敬请期待
      </div>
      <div class="content-type">
        <el-button
          v-for="(item, index) in typeList"
          :key="index"
          @click="chooseEvent(item.value)"
          :class="
            item.value == 20 ? (item.value == type ? 'active' : '') : 'noActive'
          "
          @mouseover.native="enter(item.value)"
          @mouseout.native="leave()"
        >
          <el-image
            fit="fill"
            :src="
              item.value == 20
                ? item.value == type
                  ? item.type_img_1
                  : item.type_img_2
                : item.type_img_3
            "
          ></el-image>
          <div>{{ item.name_cn }}</div>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "dialogType",
  data() {
    return {
      chooseType: false,
      type: 0,
      checkTypes: 0,
    };
  },
  methods: {
    //打开选择框
    openDialog() {
      this.chooseType = true;
      setTimeout(() => {
        let a = document.getElementsByClassName("v-modal")[0];
        let str = a.getAttribute("style") + "opacity:0 !important";
        a.setAttribute("style", str);
      });
    },
    //点击选择类型
    chooseEvent(e) {
      if (!this.checkTypes) {
        this.checkTypes = e;
        if (this.checkTypes == 20) {
          this.chooseType = false;
        } else {
          setTimeout(() => {
            this.checkTypes = 0;
          }, 1000);
        }
      }
    },
    enter(e) {
      this.type = e;
    },
    leave() {
      this.type = 0;
    },
    closeEvent() {
      let typeItem = "";
      for (let index = 0; index < this.typeList.length; index++) {
        if (this.typeList[index].value == this.checkTypes) {
          typeItem = this.typeList[index];
        }
      }
      this.$store.commit("checkType", typeItem);
      if (parseInt(this.checkTypes) == 0) {
        this.checkTypes = "";
      } else {
        this.checkTypes = 0;
      }
    },
  },
  computed: {
    typeList() {
      return this.$store.state.route.typeList;
    },
  },
};
</script>
<style lang="less" scoped>
.dialogType {
  .el-dialog {
    .tipInfo {
      width: 33%;
      margin-left: 33.5%;
      text-align: center;
      color: white;
      font-size: 12px;
      letter-spacing: 2px;
      border: 1px solid #fe0000;
      background-color: #131313;
      border-radius: 6px;
      margin-bottom: 20px;
      padding: 5px;
    }
    .content-type {
      width: 70%;
      margin-left: 15%;
      .el-button {
        text-align: center;
        margin: 1%;
        background-color: transparent;
        width: 23%;
        min-width: 80px;
        color: #0092f8;
        border: none;
        background-color: rgba(0, 0, 0, 0.75);
        padding: 6px 12px;
        font-size: 16px;
        font-weight: 550;
        .el-image {
          width: 90%;
        }
        &.active {
          border: 1px solid white;
          color: white;
        }
        &.noActive {
          border: none;
          color: #6d6e70;
        }
      }
    }
  }
}
</style>
<style lang="less">
.dialogType {
  .el-dialog {
    background-color: rgba(20, 20, 20, 0.86) !important;
    border-radius: 8px !important;
    top: 15vh !important;
    .el-dialog__header {
      text-align: left !important;
      .el-dialog__title {
        color: white !important;
      }
    }
    .el-dialog__body {
      padding-top: 0 !important;
    }
  }
}
</style>