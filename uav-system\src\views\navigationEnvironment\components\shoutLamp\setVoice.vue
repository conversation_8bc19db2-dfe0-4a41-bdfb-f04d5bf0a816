<template>
  <div class="set-voice">
    <div class="set-voice-btn">
      <el-image
        :src="require(`@/assets/img/setVoice.png`)"
        style="height: 40px"
        fit="contain"
        @click="operateSet"
      ></el-image>
    </div>
    <div class="set-voice-box" :style="{ width: width }">
      <div class="voice-box-content">
        <el-form :model="form" label-position="top">
          <el-form-item :label="language.timbre">
            <el-select
              v-model="form.timbre"
              :placeholder="language.timbrePlaceholder"
              popper-class="timbre-choose-box"
              @change="changeTimbre"
            >
              <el-option
                v-for="item in timbreList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            :label="language.speech_speed + ' ' + form.speech_speed_1 + 'x'"
          >
            <div class="volume-slider-item">
              <el-slider
                :min="1"
                :max="100"
                v-model="form.speech_speed"
                :show-tooltip="false"
                @change="changeSoundSpeed"
              ></el-slider>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    sendWs: {
      type: [Function, String],
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      width: 0,
      form: {
        timbre: 18,
        speech_speed: 50,
        speech_speed_1: 1,
      },
      timbreList: [],
      speech_speed: 0,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.navigation.shout;
    },
  },
  created() {
    this.timbreList = this.language.timbreList;
  },
  watch: {
    speech_speed: {
      handler(val) {
        this.form.speech_speed = val;
      },
      immediate: true,
    },
  },
  methods: {
    //获取数据
    getDataObj: function (data) {
      this.form.timbre = data.timbre;
      this.speech_speed = data.speech_speed;
      this.form.speech_speed_1 = Number(
        (0.5 + data.speech_speed / 100).toFixed(1)
      );
    },
    //操作设置显示与关闭
    operateSet: function () {
      this.width = this.width == 0 ? "100%" : 0;
      this.$emit("openSet", this.width);
    },
    //音速
    changeSoundSpeed: function (e) {
      this.sendWs({ action: 22, value: e });
    },
    //切换音色
    changeTimbre: function (e) {
      this.sendWs({ action: 20, value: e });
    },
  },
};
</script>
<style lang="less" scoped>
.set-voice {
  flex: 1;
  padding: 0 5px;
  .set-voice-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    .el-image {
      cursor: pointer;
    }
  }
  .set-voice-box {
    position: absolute;
    top: 0;
    left: calc(100% + 18px);
    height: 100%;
    background-color: rgba(71, 71, 71, 0.5);
    overflow: hidden;
    transition: all 0.1s linear;
    border-radius: 8px;
    .voice-box-content {
      width: 100%;
      height: 100%;
      background-color: transparent;
      border-radius: 8px;
      padding: 28px 23px;
      box-sizing: border-box;
      .el-form {
        .el-form-item {
          .volume-slider-item {
            width: 100%;
            height: 36px;
            background-color: #24272b;
            padding: 0 9px;
            border-radius: 5px;
            box-sizing: border-box;
            .el-slider {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.set-voice {
  .set-voice-box {
    .el-form {
      .el-form-item {
        .el-form-item__label {
          font-size: 26px;
          color: #fff;
          padding-left: 9px;
        }
        .volume-slider-item {
          .el-slider {
            .el-slider__runway {
              height: 3px !important;
              .el-slider__bar {
                height: 3px !important;
              }
              .el-slider__button {
                width: 12px !important;
                height: 12px !important;
                border-width: 1px !important;
              }
            }
          }
        }
        .el-select {
          width: 100%;
          .el-input {
            .el-input__inner {
              background-color: #24272b;
              color: #fff;
              border: none;
              height: 60px;
              line-height: 60px;
              font-size: 28px;
            }
            .el-input__icon {
              line-height: 60px;
            }
            .el-select__caret {
              color: #fff;
              font-size: 28px;
            }
          }
        }
      }
    }
  }
}
.timbre-choose-box {
  background-color: #24272b;
  color: #fff;
  border: none;

  .el-select-dropdown__item {
    font-size: 28px;
    color: #fff;
    margin-bottom: 12px;
    padding: 10px 20px;
    height: auto;
    line-height: 1;
    background-color: transparent;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-select-dropdown__item:hover {
    background-color: #3195ff41;
  }
  .el-select-dropdown__item.selected {
    background-color: #3196ff;
  }

  .popper__arrow {
    display: none;
  }
}
</style>