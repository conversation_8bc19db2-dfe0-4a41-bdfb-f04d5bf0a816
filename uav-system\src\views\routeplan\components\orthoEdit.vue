<template>
  <div class="orthoEdit">
    <div class="title" ref="orthoEditTitle">
      <el-button
        :class="iconCode ? 'returnIcon' : ''"
        icon="el-icon-arrow-left"
        @click="goBack"
      ></el-button>
      {{ routeTitle }}
    </div>
    <el-form
      :model="orthoForm"
      :rules="orthoRules"
      ref="orthoForm"
      class="content-item-1"
      :style="{ height: height }"
    >
      <el-form-item :label="routeLanguage.routeLine.taskName" prop="name">
        <el-input
          v-model="orthoForm.name"
          :placeholder="routeLanguage.routeLine.placeholder"
        ></el-input>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.fenceName">
        <el-button>{{ fenceItem.title }}</el-button>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.taskType">
        <el-button>{{
          $language == "chinese" ? checkType.name_cn : checkType.name_en
        }}</el-button>
      </el-form-item>
      <div class="title">{{ routeLanguage.routeLine.basicSet }}</div>
      <el-form-item
        :label="routeLanguage.routeLine.toPlaneType"
        prop="plane_type"
      >
        <el-select
          v-model="orthoForm.plan_type"
          :placeholder="routeLanguage.routeLine.placeholder4"
          class="actionChoose"
          popper-class="selects"
          @change="changePlanType"
        >
          <el-option
            v-for="item in options2"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.timeNode"
        v-if="orthoForm.plan_type == 1"
      >
        <select-date
          :plan_time.sync="orthoForm.plan_time"
          ref="selectDate"
        ></select-date>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.autoSpeed"
        prop="auto_speed"
      >
        <el-slider
          v-model="orthoForm.auto_speed"
          :show-tooltip="false"
          show-input
          :show-input-controls="false"
          :max="orthoForm.max_speed"
          :step="0.1"
          class="slider-class-1"
        ></el-slider>
      </el-form-item>
      <!-- <el-form-item :label="routeLanguage.routeLine.maxSpeed" prop="max_speed">
            <el-slider
              v-model="orthoForm.max_speed"
              :show-tooltip="false"
              show-input
              :show-input-controls="false"
              :step="0.1"
              :min="5"
              :max="1000"
              class="slider-class-1"
            ></el-slider>
          </el-form-item> -->
      <!-- <el-form-item
        :label="routeLanguage.routeLine.heightType"
        prop="height_type"
      >
        <el-select
          v-model="routeForm.height_type"
          :placeholder="routeLanguage.routeLine.placeholder5"
          class="actionChoose"
          popper-class="selects"
        >
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item
        :label="routeLanguage.routeLine.defaultHeight"
        prop="default_height"
      >
        <el-slider
          v-model="orthoForm.default_height"
          :show-tooltip="false"
          show-input
          :show-input-controls="false"
          :step="1"
          :max="fenceItem.height"
          :min="2"
          @change="setDefaultHeight"
          class="slider-class-1"
        ></el-slider>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.returnHeight"
        prop="return_height"
      >
        <el-slider
          v-model="orthoForm.return_height"
          :show-tooltip="false"
          show-input
          :show-input-controls="false"
          :step="1"
          :max="fenceItem.height"
          :min="2"
          class="slider-class-1"
        ></el-slider>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.taskAction"
        prop="action_completed"
      >
        <el-select
          v-model="orthoForm.action_completed"
          :placeholder="routeLanguage.routeLine.placeholder1"
          class="actionChoose"
          popper-class="selects"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.photoType"
        prop="action_completed"
      >
        <el-select
          v-model="orthoForm.capture_mode"
          :placeholder="routeLanguage.routeLine.placeholder6"
          class="actionChoose"
          popper-class="selects"
        >
          <el-option
            v-for="item in photoTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.cameraType"
        prop="cameraType"
      >
        <el-select
          v-model="orthoForm.cameraType"
          :placeholder="routeLanguage.routeLine.placeholder3"
          class="actionChoose"
          popper-class="selects"
          @change="changeCamera"
        >
          <el-option
            v-for="item in cameraOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <camera-param
        v-if="orthoForm.cameraType == 1"
        @cameraParams="getCameraParams"
        :cameraParamList="cameraParamList"
        :cameraParams="routeLanguage.cameraParams"
      ></camera-param>
      <el-form-item :label="routeLanguage.routeLine.course">
        <silder-input
          :originCode="originCode"
          :numValue.sync="orthoForm.course"
          :max="99"
          :suffixCode="'%'"
          @mouseupEvent="mouseupEvent"
        ></silder-input>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.lateral">
        <silder-input
          :originCode="originCode"
          :numValue.sync="orthoForm.lateral"
          :max="99"
          :suffixCode="'%'"
          @mouseupEvent="mouseupEvent"
        ></silder-input>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.wheelDist">
        <silder-input
          :originCode="originCode"
          :numValue.sync="orthoForm.wheelDist"
          :max="100"
          :suffixCode="'m'"
          @mouseupEvent="mouseupEvent"
        ></silder-input>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.angle" prop="rotate">
        <silder-input
          :originCode="originCode"
          :numValue.sync="orthoForm.angle"
          :max="360"
          :suffixCode="'°'"
          @mouseupEvent="mouseupEvent"
        ></silder-input>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.isPlaneHight"
        prop="interval_height"
      >
        <el-switch
          v-model="orthoForm.isComputeHight"
          active-color="#0555ff"
          inactive-color="rgb(92, 92, 92)"
          style="margin-left: 20px"
        ></el-switch>
      </el-form-item>
      <el-form-item
        :label="routeLanguage.routeLine.intervalHeight"
        prop="interval_height"
      >
        <div class="interval_height">
          <el-input
            v-model="orthoForm.interval_height"
            placeholder=""
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @blur="changeIntervalHeight"
          ></el-input>
          <el-switch
            v-model="orthoForm.showHeight"
            active-color="#0555ff"
            inactive-color="rgb(92, 92, 92)"
          ></el-switch>
        </div>
      </el-form-item>
      <el-form-item :label="routeLanguage.routeLine.waypoint">
        <el-collapse
          v-model="pointCodes"
          v-if="orthoForm.point_json.length > 0 ? true : false"
          class="pointsClass"
        >
          <el-collapse-item
            v-for="(item, index) in orthoForm.point_json"
            :key="index"
            :title="index + 1 + routeLanguage.waypoint.title"
            :name="index + 1"
          >
            <div>{{ routeLanguage.waypoint.lng }}</div>
            <el-input-number
              v-model="item.lng"
              @blur="changeLngLat(index)"
              @focus="getFocus(index)"
              :controls="false"
              :precision="7"
              ref="lngList"
            ></el-input-number>
            <div>{{ routeLanguage.waypoint.lat }}</div>
            <el-input-number
              v-model="item.lat"
              @blur="changeLngLat(index)"
              @focus="getFocus(index)"
              :controls="false"
              :precision="7"
            ></el-input-number>
            <!-- <div>高度（m）</div>
                <el-input-number
                  :controls="false"
                  :precision="2"
                  v-model="heights[index]"
                  :min="2"
                  :max="fenceItem.height"
                ></el-input-number> -->
          </el-collapse-item>
        </el-collapse>
      </el-form-item>
      <el-button
        class="saveBut"
        @click="sumbitRoute('orthoForm')"
        :class="saveCode ? 'active' : ''"
        >{{ routeLanguage.routeLine.save }}</el-button
      >
    </el-form>
  </div>
</template>
<script>
import { computedMethod } from "../../../utils/computedMap";
import requestHttp from "../../../utils/api";
import silderInput from "./silderInput";
import cameraParam from "./cameraParam";
import selectDate from "./selectDate.vue";
import { computedMapMethods } from "@/utils/cesium/computedMapMethods";
import { pointsConvert } from "@/utils/coordinateConvert";
export default {
  name: "orthoEdit",
  data() {
    return {
      iconCode: false,
      routeTitle: "",
      pointCodes: [],
      routeCode: false,
      orthoForm: {
        name: "",
        auto_speed: 10,
        max_speed: 20,
        default_height: 100,
        return_height: 100,
        action_completed: 20,
        point_json: [],
        cameraType: "",
        lateral: 80,
        course: 70,
        wheelDist: 10,
        angle: 0,
        height_type: 0,
        plan_type: 0,
        plan_time: [],
        interval_height: 100,
        showHeight: false,
        isComputeHight: false,
        capture_mode: 3,
      },
      orthoRules: {
        name: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        auto_speed: [{ validator: this.numFormat, trigger: "change" }],
        max_speed: [{ validator: this.numFormat, trigger: "change" }],
        default_height: [{ validator: this.numFormat, trigger: "change" }],
        return_height: [{ validator: this.numFormat, trigger: "change" }],
        action_completed: [
          {
            required: true,
            message: "",
            trigger: "change",
          },
        ],
        cameraType: {
          required: true,
          message: "",
          trigger: "change",
        },
      },
      options: "",
      options2: "",
      cameraOptions: [],
      cameraParamList: [],
      saveCode: false,
      focusPoint: {},
      routeItem: "",
      noDeep: false,
      height: "",
      originCode: false,
      photoTypeList: [],
    };
  },
  components: {
    silderInput,
    cameraParam,
    selectDate,
  },
  props: {
    fenceItem: {
      type: Object,
      default() {
        return {};
      },
    },
    checkType: {
      type: Object,
      default() {
        return {};
      },
    },
    changePoint: {
      type: Object,
      default() {
        return {};
      },
    },
    extra: {
      type: Boolean,
      default: false,
    },
    elevationSuccess: {
      type: Object,
      default() {
        return {};
      },
    },
    elevationData: {
      type: Object,
      default() {
        return {};
      },
    },
    mapType: {
      type: String,
      default: "",
    },
    orthoAllPoints: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  computed: {
    routeLanguage() {
      return this.$languagePackage.routes;
    },
  },
  created() {
    this.routeTitle = this.routeLanguage.routeLine.setNewTask;
    this.orthoRules.name[0].message = this.routeLanguage.routeLine.placeholder;
    this.orthoRules.action_completed[0].message =
      this.routeLanguage.routeLine.placeholder1;
    this.orthoRules.cameraType.message =
      this.routeLanguage.routeLine.placeholder3;
    this.options = this.routeLanguage.options;
    this.options1 = this.routeLanguage.options1;
    this.options2 = this.routeLanguage.options2;
    this.cameraOptions = [
      {
        label: this.routeLanguage.routeLine.customCamera,
        value: 1,
        params: [
          {
            name: this.routeLanguage.routeLine.sensor,
            width: "13.20",
            height: "8.80",
            id: "sensor",
          },
          {
            name: this.routeLanguage.routeLine.image,
            width: "5480",
            height: "3648",
            id: "image",
          },
          {
            name: this.routeLanguage.routeLine.focalLength,
            width: "",
            height: "25.0",
            id: "focalLength",
          },
        ],
      },
      {
        label: this.routeLanguage.routeLine.cameraType1,
        value: 2,
        params: [
          {
            name: this.routeLanguage.routeLine.sensor,
            width: "13.20",
            height: "8.80",
            id: "sensor",
          },
          {
            name: this.routeLanguage.routeLine.image,
            width: "5480",
            height: "3648",
            id: "image",
          },
          {
            name: this.routeLanguage.routeLine.focalLength,
            width: "",
            height: "9.0",
            id: "focalLength",
          },
        ],
      },
      {
        label: this.routeLanguage.routeLine.cameraType2,
        value: 3,
        params: [
          {
            name: this.routeLanguage.routeLine.sensor,
            width: "4.712",
            height: "6.286",
            id: "sensor",
          },
          {
            name: this.routeLanguage.routeLine.image,
            width: "3840",
            height: "2160",
            id: "image",
          },
          {
            name: this.routeLanguage.routeLine.focalLength,
            width: "",
            height: "5.6",
            id: "focalLength",
          },
        ],
      },
    ];
    this.photoTypeList = this.routeLanguage.photoTypeList;
  },
  async mounted() {
    this.orthoForm.cameraType = 2;
    this.cameraParamList = this.cameraOptions[1].params;
    this.routeItem = this.$store.state.route.routeItem;
    this.height =
      "calc(100% - " + (this.$refs.orthoEditTitle.offsetHeight + 10) + "px)";
    if (this.routeItem) {
      await this.originForm();
    }
    this.$nextTick(() => {
      this.originCode = true;
      this.routeCode = false;
    });
  },
  methods: {
    //初始化表单
    originForm() {
      let camera_json = JSON.parse(this.routeItem.camera_json);
      let photo_type_json = this.routeItem.photo_type_json
        ? JSON.parse(this.routeItem.photo_type_json)
        : {};
      this.routeTitle = this.routeLanguage.routeLine.editTitle;
      this.orthoForm = {
        name: this.routeItem.title,
        auto_speed: this.routeItem.auto_speed / 100,
        max_speed: this.routeItem.max_speed / 100,
        default_height: this.routeItem.default_height / 100,
        return_height: this.routeItem.return_height / 100,
        action_completed: this.routeItem.action_completed,
        point_json: [],
        cameraType: camera_json.camera_type,
        height_type: this.routeItem.height_type,
        lateral: camera_json.lateral,
        course: camera_json.course,
        wheelDist: camera_json.wheelDist,
        angle: camera_json.angle,
        plan_time: this.routeItem.task_json
          ? JSON.parse(this.routeItem.task_json)
          : [],
        plan_type: this.routeItem.is_timed_task ? 1 : 0,
        interval_height: 100,
        showHeight: false,
        isComputeHight: false,
        capture_mode: photo_type_json.capture_mode
          ? photo_type_json.capture_mode
          : 3,
      };
      let cal_alt_json = this.routeItem.cal_alt_json
        ? JSON.parse(this.routeItem.cal_alt_json)
        : "";
      if (cal_alt_json) {
        this.orthoForm.interval_height = cal_alt_json.interval_height;
        this.orthoForm.isComputeHight = cal_alt_json.isComputeHight
          ? cal_alt_json.isComputeHight
          : false;
      }
      this.cameraParamList = camera_json.cameraParamList;
      console.log(this.routeItem);
      for (let index = 0; index < this.routeItem.point_list.length; index++) {
        let path = pointsConvert({
          point: [
            this.routeItem.point_list[index].lon_int / 1e7,
            this.routeItem.point_list[index].lat_int / 1e7,
          ],
          type: this.routeItem.point_list[index].type,
        });
        // let path = {
        //   lat: this.routeItem.point_list[index].lat_int / 1e7,
        //   lng: this.routeItem.point_list[index].lon_int / 1e7,
        // };
        this.orthoForm.point_json.push({
          lat: path[1],
          lng: path[0],
        });
      }
    },
    //返回
    goBack() {
      this.iconCode = true;
      if (this.routeCode) {
        this.$confirm(this.routeLanguage.placeholder1, this.routeLanguage.tip, {
          confirmButtonText: this.routeLanguage.saveBtn,
          cancelButtonText: this.routeLanguage.cancelBtn,
          type: "warning",
          customClass: "messageTip",
        })
          .then(() => {
            this.originalData();
            this.$emit("update:extra", false);
          })
          .catch(() => {})
          .finally(() => {
            this.iconCode = false;
          });
      } else {
        this.originalData();
        this.$emit("update:extra", false);
      }
    },
    //返回数据初始化
    originalData() {
      this.pointCodes = [];
      this.$emit("goBack", "");
      this.orthoForm = {
        name: "",
        auto_speed: 10,
        max_speed: 20,
        default_height: 100,
        return_height: 100,
        action_completed: 20,
        point_json: [],
        cameraType: "",
        lateral: 80,
        course: 70,
        wheelDist: 10,
        angle: 0,
        height_type: 0,
        plan_type: 0,
        plan_time: [],
        isComputeHight: false,
        capture_mode: 3,
      };
      this.cameraParamList = [];
      this.routeTitle = this.routeLanguage.routeLine.setNewTask;
    },
    //校验是否为0
    numFormat(rule, value, callback) {
      if (value == 0) {
        callback(new Error(this.routeLanguage.errorMessage));
      } else {
        callback();
      }
    },
    //所有点默认高度
    setDefaultHeight() {
      this.$emit("mouseupEvent", "");
    },
    //相机参数返回
    getCameraParams(e) {
      this.cameraParamList = e;
      // this.cameraOptions[0].params=e
      this.$emit("refresh", "");
      if (this.mapType) {
        this.$emit("mouseupEvent", "");
      }
    },
    //相机参数
    changeCamera(e) {
      // this.orthoForm.cameraType=e
      let index = this.cameraOptions.findIndex((item) => {
        return item.value == e;
      });
      this.cameraParamList = this.cameraOptions[index].params;
      this.$emit("refresh", "");
      if (this.mapType) {
        this.$emit("mouseupEvent", "");
      }
    },
    //获取焦点
    getFocus(index) {
      this.noDeep = true;
      let point = this.orthoForm.point_json[index];
      this.focusPoint = Object.assign({}, point);
    },
    //失去焦点修改坐标
    changeLngLat(index) {
      if (this.mapType) {
        let point = {
          lat: this.orthoForm.point_json[index].lat,
          lng: this.orthoForm.point_json[index].lng,
          height: this.orthoForm.default_height,
        };
        let judgeResult = this.judgePosition(point, index);
        if (!judgeResult) {
          this.orthoForm.point_json[index].lng = this.focusPoint.lng;
          this.orthoForm.point_json[index].lat = this.focusPoint.lat;
        } else {
          let changePoint = {
            index: index,
            lat: this.orthoForm.point_json[index].lat,
            lng: this.orthoForm.point_json[index].lng,
            height: this.orthoForm.default_height,
          };
          this.$emit("changeMarker", changePoint);
          this.$emit("deepCopy", "");
        }
        setTimeout(() => {
          this.noDeep = false;
        }, 100);
        return false;
      }
      let a = computedMethod(2, {
        point1: new AMap.LngLat(
          this.orthoForm.point_json[index].lng,
          this.orthoForm.point_json[index].lat
        ),
        fence: this.fenceItem.paths,
      });
      if (a) {
        let c, b;
        let num = index + 1;
        if (num == 1) {
          c = this.isLineOrthoCross1(
            this.orthoForm.point_json[0],
            this.orthoForm.point_json[num],
            0
          );
          b = this.isLineOrthoCross1(
            this.orthoForm.point_json[this.orthoForm.point_json.length - 1],
            this.orthoForm.point_json[0],
            0,
            1
          );
        } else if (num == this.orthoForm.point_json.length) {
          c = this.isLineOrthoCross1(
            this.orthoForm.point_json[0],
            this.orthoForm.point_json[this.orthoForm.point_json.length - 1],
            0,
            1
          );
          b = this.isLineOrthoCross1(
            this.orthoForm.point_json[this.orthoForm.point_json.length - 1],
            this.orthoForm.point_json[num - 2],
            this.orthoForm.point_json.length - 2
          );
        } else {
          c = this.isLineOrthoCross1(
            this.orthoForm.point_json[num - 1],
            this.orthoForm.point_json[num - 2],
            index - 2
          );
          b = this.isLineOrthoCross1(
            this.orthoForm.point_json[num - 1],
            this.orthoForm.point_json[num],
            index - 1
          );
        }
        if (c || b) {
          this.orthoForm.point_json[index].lng = this.focusPoint.lng;
          this.orthoForm.point_json[index].lat = this.focusPoint.lat;

          this.$message.warning({
            message: this.routeLanguage.messageInfo3,
            duration: 1000,
            customClass: "message-info-tip",
          });
          return false;
        }
        let changePoint = {
          index: index,
          lat: this.orthoForm.point_json[index].lat,
          lng: this.orthoForm.point_json[index].lng,
        };
        this.$emit("changeMarker", changePoint);
        this.$emit("deepCopy", "");
      } else {
        this.$message.error({
          message: this.routeLanguage.placeholder4,
          customClass: "message-info-tip",
        });
        this.orthoForm.point_json[index].lng = this.focusPoint.lng;
        this.orthoForm.point_json[index].lat = this.focusPoint.lat;
      }
      setTimeout(() => {
        this.noDeep = false;
      }, 100);
      //
    },
    judgePosition(point, index) {
      let isArea = computedMapMethods("pointInPolygon", {
        point: [point.lng, point.lat],
        polygon: this.fenceItem.paths,
      });
      if (!isArea) {
        this.$message.error({
          message: this.routeLanguage.placeholder4,
          customClass: "message-info-tip",
        });
        return false;
      }
      if (this.orthoForm.point_json.length > 1) {
        let isCross = false;
        let beforePoint = "";
        let afterPoint = "";
        let a = false;
        let b = false;
        if (index == 0) {
          afterPoint = this.orthoForm.point_json[index + 1];
          if (this.orthoForm.point_json.length > 2) {
            beforePoint =
              this.orthoForm.point_json[this.orthoForm.point_json.length - 1];
          }
        } else {
          beforePoint = this.orthoForm.point_json[index - 1];
          if (index < this.orthoForm.point_json.length - 1) {
            afterPoint = this.orthoForm.point_json[index + 1];
          } else {
            afterPoint = this.orthoForm.point_json[0];
          }
        }
        if (beforePoint) {
          a = computedMapMethods("lineCross", {
            point,
            point1: beforePoint,
            fence: this.fenceItem.paths,
            type: true,
          });
        }
        if (afterPoint) {
          b = computedMapMethods("lineCross", {
            point,
            point1: afterPoint,
            fence: this.fenceItem.paths,
            type: true,
          });
        }
        isCross = a || b;
        if (isCross) {
          this.$message.error({
            message: this.routeLanguage.placeholder7,
            customClass: "message-info-tip",
          });
          return false;
        }
      }
      if (this.orthoForm.point_json.length > 2) {
        let isCross = this.judgeCross(point, index);
        if (isCross) {
          this.$message.warning({
            message: this.routeLanguage.messageInfo3,
            duration: 1000,
          });
          return false;
        }
      }
      return true;
    },
    judgeCross(point, index) {
      //操作点与前一个点形成的线判断
      let arr = [];
      let point1 =
        this.orthoForm.point_json[this.orthoForm.point_json.length - 1];
      if (index > 0) {
        arr = this.orthoForm.point_json.slice(0, index - 1);
        point1 = this.orthoForm.point_json[index - 1];
      }
      let arr1 = this.orthoForm.point_json.slice(
        index + 1,
        this.orthoForm.point_json.length
      );
      let arrconcat = [...arr1, ...arr];
      let isCross = false;
      if (arrconcat.length > 1) {
        isCross = computedMapMethods("lineCross", {
          point,
          point1,
          fence: arrconcat,
        });
        if (isCross) {
          return true;
        }
      }
      //操作点与后一个点形成的线判断
      let arr2 = [];
      let point2 = "";
      if (index == this.orthoForm.point_json.length - 1) {
        arr2 = this.orthoForm.point_json.slice(1, index);
        point2 = this.orthoForm.point_json[0];
      } else {
        let arr3 = this.orthoForm.point_json.slice(0, index);
        point2 = this.orthoForm.point_json[index];
        let arr4 = [];
        if (index + 2 < this.orthoForm.point_json.length) {
          arr4 = this.orthoForm.point_json.slice(
            index + 2,
            this.orthoForm.point_json.length
          );
        }
        arr2 = [...arr4, ...arr3];
      }
      let isCross1 = false;
      if (arrconcat.length > 1) {
        isCross1 = computedMapMethods("lineCross", {
          point,
          point1: point2,
          fence: arr2,
        });
        if (isCross1) {
          return true;
        }
      }
      return false;
    },
    //正射影像：拖拽判断是否出现交叉
    isLineOrthoCross1(marker1, marker2, index, num) {
      let arr1 = this.orthoForm.point_json.slice(0, index);
      let arr2 = this.orthoForm.point_json.slice(
        num ? index + 1 : index + 2,
        num
          ? this.orthoForm.point_json.length - 1
          : this.orthoForm.point_json.length
      );
      let arr = arr2.concat(arr1);
      return computedMethod(5, {
        point1: new AMap.LngLat(marker1.lng, marker1.lat),
        point2: new AMap.LngLat(marker2.lng, marker2.lat),
        fence: arr,
      });
    },
    //提交航线信息
    sumbitRoute(e) {
      if (this.extra) {
        this.$message.error({
          message: this.routeLanguage.errorMessage7,
          customClass: "message-info-tip",
        });
        this.saveCode = false;
        return false;
      }
      if (!this.saveCode) {
        this.saveCode = true;
        let i = 0;
        for (const key in this.elevationSuccess) {
          if (!this.elevationSuccess[key]) {
            i++;
          }
        }
        if (this.orthoForm.isComputeHight && i) {
          this.$message.error({
            message: this.routeLanguage.errorMessage11,
            customClass: "message-info-tip",
          });
          this.saveCode = false;
          return false;
        }
        if (e == "orthoForm") {
          this.$refs[e].validate((valid, validator) => {
            if (valid) {
              if (this.$refs.selectDate) {
                for (
                  let index = 0;
                  index < this.$refs.selectDate.error.length;
                  index++
                ) {
                  if (this.$refs.selectDate.error[index]) {
                    this.$message.error({
                      message: this.routeLanguage.errorMessage10,
                      customClass: "message-info-tip",
                    });
                    this.saveCode = false;
                    return false;
                  }
                }
              }
              if (
                this.orthoForm.point_json.length > 2 &&
                this.orthoAllPoints.length
              ) {
                var point_jsons = [];
                for (
                  let index = 0;
                  index < this.orthoForm.point_json.length;
                  index++
                ) {
                  let point_json = {
                    seq: index + 1,
                    lat_int: parseInt(
                      this.orthoForm.point_json[index].lat * 1e7
                    ),
                    lon_int: parseInt(
                      this.orthoForm.point_json[index].lng * 1e7
                    ),
                    height: parseInt(this.orthoForm.default_height * 100),
                    // type: 20
                    type: this.$coordinateType === "wgs84" ? 10 : 20,
                  };
                  point_jsons.push(point_json);
                }
                let cal_alt_json = {
                  interval_height: this.orthoForm.interval_height,
                  elevationData: this.elevationData,
                  isComputeHight: this.orthoForm.isComputeHight,
                };
                let data = {
                  title: this.orthoForm.name,
                  f_id: this.fenceItem.id,
                  type: this.checkType.value,
                  auto_speed: parseInt(this.orthoForm.auto_speed * 100),
                  max_speed: parseInt(this.orthoForm.max_speed * 100),
                  default_height: parseInt(this.orthoForm.default_height * 100),
                  return_height: parseInt(this.orthoForm.return_height * 100),
                  action_completed: this.orthoForm.action_completed,
                  height_type: this.orthoForm.height_type,
                  is_timed_task: this.orthoForm.plan_type == 0 ? false : true,
                  task_json: this.orthoForm.plan_time
                    ? JSON.stringify(this.orthoForm.plan_time)
                    : "",
                  cal_alt_json: cal_alt_json
                    ? JSON.stringify(cal_alt_json)
                    : "",
                  photo_type_json: JSON.stringify({
                    capture_mode: this.orthoForm.capture_mode,
                  }),
                };
                let jsonParams = {
                  camera_type: this.orthoForm.cameraType,
                  cameraParamList: this.cameraParamList,
                  lateral: this.orthoForm.lateral,
                  course: this.orthoForm.course,
                  wheelDist: this.orthoForm.wheelDist,
                  angle: this.orthoForm.angle,
                };
                data.camera_json = JSON.stringify(jsonParams);
                if (this.routeItem) {
                  data.m_id = this.routeItem.m_id;
                  data.state = this.routeItem.state;
                  for (let index = 0; index < point_jsons.length; index++) {
                    for (let i = 0; i < this.routeItem.point_list.length; i++) {
                      if (
                        point_jsons[index].seq ==
                        this.routeItem.point_list[i].seq
                      ) {
                        point_jsons[index].id = this.routeItem.point_list[i].id;
                      }
                    }
                    point_jsons[index].state = 10;
                  }
                  if (point_jsons.length < this.routeItem.point_list.length) {
                    let point_jsons1 = [];
                    for (
                      let index = 0;
                      index < this.routeItem.point_list.length;
                      index++
                    ) {
                      if (
                        this.routeItem.point_list[index].seq >
                        point_jsons.length
                      ) {
                        let a = {
                          seq: this.routeItem.point_list[index].seq,
                          id: this.routeItem.point_list[index].id,
                          state: 30,
                          lon_int: this.routeItem.point_list[index].lon_int,
                          lat_int: this.routeItem.point_list[index].lat_int,
                          height: this.routeItem.point_list[index].height,
                          type: this.routeItem.point_list[index].type,
                        };
                        point_jsons1.push(a);
                      }
                    }
                    point_jsons = point_jsons.concat(point_jsons1);
                  }
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString() +
                    data.is_timed_task.toString() +
                    data.m_id.toString() +
                    data.state.toString();
                } else {
                  data.point_json = JSON.stringify(point_jsons);
                  data.pmd =
                    data.f_id.toString() +
                    data.title.toString() +
                    data.point_json +
                    data.type.toString() +
                    data.max_speed.toString() +
                    data.default_height.toString() +
                    data.return_height.toString() +
                    data.action_completed.toString() +
                    data.is_timed_task.toString();
                }
                requestHttp(
                  this.routeItem ? "missionEdit" : "missionAdd",
                  data
                ).then((res) => {
                  this.$message.success({
                    message: this.routeItem
                      ? this.routeLanguage.successMessage2
                      : this.routeLanguage.successMessage3,
                    customClass: "message-info-tip",
                  });
                  this.originalData();
                });
              } else {
                this.saveCode = false;
                this.$message.warning({
                  message: this.routeLanguage.messageInfo5,
                  customClass: "message-info-tip",
                });
              }
            } else {
              if (validator) {
                for (const key in validator) {
                  this.$message.error({
                    message: validator[key][0].message,
                    customClass: "message-info-tip",
                  });
                  this.saveCode = false;
                  break;
                }
              }
            }
          });
        }
      }
      this.timeOut = setTimeout(() => {
        this.saveCode = false;
      }, 200);
    },
    //改变状态
    changePlanType(e) {
      if (e) {
        this.orthoForm.plan_time = [];
      } else {
        this.orthoForm.plan_time = "";
      }
    },
    mouseupEvent() {
      this.$emit("mouseupEvent", "");
    },
    //改变高程
    changeIntervalHeight() {
      this.$emit("changeIntervalHeight", this.orthoForm.interval_height);
    },
  },
  watch: {
    changePoint(val) {
      switch (val.type) {
        case "add":
          let path = {
            lat: val.lat,
            lng: val.lng,
          };
          this.orthoForm.point_json.push(path);
          break;
        case "edit":
          this.orthoForm.point_json[val.index].lng = val.lng;
          this.orthoForm.point_json[val.index].lat = val.lat;
          break;
        case "editAdd":
          let path1 = {
            lat: val.lat,
            lng: val.lng,
          };
          this.orthoForm.point_json.splice(val.index, 0, path1);
          break;
        case "remove":
          this.orthoForm.point_json.splice(val.index, 1);
          this.pointCodes = this.pointCodes.filter((x) => x !== val.index + 1);
          if (this.orthoForm.point_json.length == 0) {
            this.$emit("update:extra", false);
          }
          break;
        case "del":
          this.orthoForm.point_json = [];
          this.pointCodes = [];
          this.$emit("update:extra", false);
          break;
        case "import":
          let path2 = {
            lat: val.lat,
            lng: val.lng,
          };
          this.orthoForm.point_json.push(path2);
          break;
        default:
          break;
      }
    },
    "orthoForm.point_json": {
      handler() {
        //存储数据
        if (!this.noDeep) {
          this.$emit("deepCopy", "");
          this.routeCode = true;
        }
      },
      deep: true,
    },
    "orthoForm.lateral"(val) {
      this.$emit("refresh", "");
    },
    "orthoForm.course"(val) {
      this.$emit("refresh", "");
    },
    "orthoForm.wheelDist"(val) {
      this.$emit("refresh", "");
    },
    "orthoForm.angle"(val) {
      this.$emit("refresh", "");
    },
    "orthoForm.auto_speed"(val) {
      this.$emit("refresh", "");
    },
    "orthoForm.showHeight"(val) {
      this.$emit("showHeightEvent", val);
    },
    "orthoForm.isComputeHight"(val) {
      this.$emit("isComputeHightEvent", val);
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .orthoEdit {
    .title {
      // height: 4%;
      font-size: @zoomIndex * 18px !important;
      .el-button {
        font-size: @zoomIndex * 22px !important;
      }
    }
    .content-item-1 {
      margin-top: @zoomIndex * 10px !important;
      .el-form-item {
        .el-button {
          padding: @zoomIndex * 10px !important;
          font-size: @zoomIndex * 16px !important;
          letter-spacing: @zoomIndex * 2px !important;
        }
        .el-collapse {
          .el-collapse-item {
            .routeActionDiv {
              .el-button {
                font-size: @zoomIndex * 24px !important;
                padding-top: @zoomIndex * 6px !important;
              }
            }
          }
        }
      }
      .title {
        letter-spacing: @zoomIndex * 2px !important;
      }
      .saveBut {
        font-size: @zoomIndex * 18px !important;
      }
    }
  }
}
.orthoEdit {
  .title {
    // height: 4%;
    font-size: 18px;
    .el-button {
      font-size: 22px;

      padding: 0;
    }
  }
  .content-item-1 {
    width: 100%;
    // height: 94%;
    overflow-x: hidden;
    overflow-y: auto;
    margin-top: 10px;
    .el-form-item {
      width: 98%;
      .el-input {
        margin-top: 1%;
        width: 100%;
      }
      .el-button {
        width: 100%;
        padding: 10px;
        font-size: 16px;
        letter-spacing: 2px;
        font-weight: 550;
      }

      .slider-class-1 {
        width: 100%;
        display: inline-block;
        margin-left: 2%;
      }
      .el-select {
        width: 100%;
      }
      .el-collapse {
        width: 100%;
        .el-collapse-item {
          width: 100%;
          margin-top: 2%;
          div {
            margin-left: 1%;
          }
          .el-input-number {
            margin-left: 1%;
            width: 98%;
          }
          .addActionBtn {
            margin-top: 2%;
            margin-left: 1%;
            width: 98%;
          }
          .routeActionDiv {
            width: 100%;
            margin-left: 0 !important;
            margin-top: 1%;
            .el-select {
              width: 85%;
            }
            .el-button {
              margin-left: 2%;
              width: auto;
              font-size: 24px;
              padding: 0;
              padding-top: 6px;
            }
            .actionValueDiv {
              margin: 1%;
              margin-left: 2%;
              margin-right: 0;
              width: 98%;
              .el-input-number {
                width: 18%;
              }
            }
          }
        }
      }
    }
    .title {
      text-align: center;
      font-weight: 520;
      letter-spacing: 2px;
    }
    .saveBut {
      margin: 0.5%;
      width: 98%;
      font-weight: 600;
      font-size: 18px;
    }
  }
}
</style>
<style lang="less">
.orthoEdit {
  .content-item-1 {
    .el-form-item {
      .el-form-item__label {
        padding: 0 !important;
        line-height: 20px !important;
      }
      .el-form-item__content {
        line-height: 10px !important;
      }
      .interval_height {
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: space-between;
        .el-input {
          width: 72%;
        }
      }
      .slider-class-1 {
        .el-slider__runway {
          height: 2px !important;
          margin: 0 !important;
          width: 79%;
          margin-top: 15px !important;
          .el-slider__bar {
            height: 2px !important;
            background-color: #0555ff !important;
          }
        }
        .el-slider__button-wrapper {
          width: 10px !important;
          height: 10px !important;
          top: -10px;
          .el-slider__button {
            width: 8px !important;
            height: 8px !important;
            border: 1px solid #0555ff !important;
          }
        }
        .el-slider__input,
        .el-input-number--small {
          width: 17% !important;
          line-height: 0 !important;
          margin-right: 5px !important;
          .el-input__inner {
            padding: 0 5px !important;
          }
        }
      }
      .silderInput {
        .sliderValue {
          .el-slider__runway {
            width: 100% !important;
          }
        }
      }
      .el-collapse {
        .el-collapse-item {
          .el-input {
            .el-input__inner {
              text-align: left !important;
            }
          }
          .el-collapse-item__header {
            padding-left: 5% !important;
            border-radius: 6px;
            width: 95% !important;
            letter-spacing: 1px !important;
            .el-collapse-item__arrow {
              // display: none !important;
              transform: rotate(90deg) !important;
            }
            .el-collapse-item__arrow.is-active {
              transform: rotate(0deg) !important;
            }
          }
          .el-collapse-item__wrap {
            width: 100% !important;

            .el-collapse-item__content {
              width: 100% !important;
            }
          }
          .routeActionDiv {
            .actionValueDiv {
              width: 98%;
              .el-input-number {
                .el-input__inner {
                  padding: 0 5px !important;
                  text-align: center !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .el-loading-mask {
    .el-loading-spinner {
      top: 50% !important;
      font-size: 16px !important;
      .el-loading-text {
        font-size: 16px !important;
      }
      i {
        font-size: 30px !important;
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .orthoEdit {
    .content-item-1 {
      .el-form-item {
        .el-form-item__label {
          line-height: @zoomIndex * 20px !important;
        }
        .el-form-item__content {
          line-height: @zoomIndex * 10px !important;
        }

        .slider-class-1 {
          .el-slider__runway {
            height: @zoomIndex * 2px !important;
            margin-top: @zoomIndex * 15px !important;
            .el-slider__bar {
              height: @zoomIndex * 2px !important;
            }
          }
          .el-slider__button-wrapper {
            width: @zoomIndex * 10px !important;
            height: @zoomIndex * 10px !important;
            top: @zoomIndex * -10px !important;
            .el-slider__button {
              width: @zoomIndex * 8px !important;
              height: @zoomIndex * 8px !important;
              border: @zoomIndex * 1px solid #0555ff !important;
            }
          }
          .el-slider__input,
          .el-input-number--small {
            margin-right: @zoomIndex * 5px !important;
            .el-input__inner {
              padding: 0 @zoomIndex * 5px !important;
            }
          }
        }
        .el-collapse {
          .el-collapse-item {
            .el-collapse-item__header {
              border-radius: @zoomIndex * 6px !important;
              letter-spacing: @zoomIndex * 1px !important;
            }
            .routeActionDiv {
              .actionValueDiv {
                .el-input-number {
                  .el-input__inner {
                    padding: 0 @zoomIndex * 5px !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    .el-loading-mask {
      .el-loading-spinner {
        font-size: @zoomIndex * 16px !important;
        .el-loading-text {
          font-size: @zoomIndex * 16px !important;
        }
        i {
          font-size: @zoomIndex * 30px !important;
        }
      }
    }
  }
}
</style>
