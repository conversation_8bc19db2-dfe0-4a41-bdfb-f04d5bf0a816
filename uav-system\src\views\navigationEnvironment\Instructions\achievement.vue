<!-- 成果列表 -->
<template>
  <layouts :title="achievement.title" direction="leftTop">
    <template v-slot:content>
      <div class="content-main" style="font-size: 12; color: #fff">
        <div class="mb8">{{achievement.content[0]}}</div>
        <div class="mb5">{{achievement.content[1]}}</div>
        <div class="mb5">{{achievement.content[2]}}</div>
        <div class="mb5">{{achievement.content[3]}}</div>
      </div>
    </template>
  </layouts>
</template>

<script>
import layouts from "./layout.vue";
export default {
  components: {
    layouts,
  },
  data() {
    return {};
  },
  
  computed: {
    achievement(){
     return this.$languagePackage.navigation.instructions.achievement
    }
  },
};
</script>