<template>
  <div class="">
    <el-dialog
      :title="title"
      :visible.sync="addEquip"
      :close-on-click-modal="false"
      :show-close="editCode"
      custom-class="dialogEquip"
      center
      :destroy-on-close="true"
      @close="closeDialog"
    >
      <el-divider></el-divider>
      <el-form
        :model="equipForm"
        ref="equipForm"
        :rules="rules"
        class="equipForm"
        label-position="right"
      >
        <el-form-item
          :label="equipLanguage.addeditEquip.type"
          prop="type"
          class="firstItem"
        >
          <!-- <el-radio :label="10">{{
              equipLanguage.addeditEquip.fixAirport
            }}</el-radio>
            <el-radio :label="12">{{
              equipLanguage.addeditEquip.fixLargeAirport
            }}</el-radio>
            <el-radio :label="100">{{
              equipLanguage.addeditEquip.MobileAirport
            }}</el-radio>
            <el-radio :label="200">{{
              equipLanguage.addeditEquip.uav
            }}</el-radio> -->
          <!-- <el-radio-group v-model="equipForm.type" :disabled="editCode">
            <el-radio
              v-for="item in typeList"
              :key="item.value"
              :label="item.value"
              >{{ item.cla_name }}</el-radio
            >
          </el-radio-group> -->
          <el-select
            v-model="equipForm.type"
            :disabled="editCode"
            @change="typeChange"
          >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :value="item.value"
              :label="item.cla_name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.linkType"
          prop="type"
          class="firstItem"
        >
          <el-radio-group v-model="equipForm.linkType" :disabled="editCode">
            <el-radio :label="0">{{
              equipLanguage.addeditEquip.link3
            }}</el-radio>
            <el-radio :label="10">{{
              equipLanguage.addeditEquip.link1
            }}</el-radio>
            <el-radio :label="20">{{
              equipLanguage.addeditEquip.link2
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="
            isAloneUav
              ? equipLanguage.addeditEquip.uavName
              : equipLanguage.addeditEquip.name
          "
          prop="name"
        >
          <el-input
            v-model="equipForm.name"
            :placeholder="
              isAloneUav
                ? equipLanguage.addeditEquip.placeholder
                : equipLanguage.addeditEquip.placeholder1
            "
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.num"
          prop="sn_id"
          v-if="!isAloneUav"
        >
          <el-input
            v-model="equipForm.sn_id"
            :placeholder="equipLanguage.addeditEquip.placeholder2"
            oninput="value=value.replace(/[\u4E00-\u9FA5]/ig,'')"
            :disabled="editCode"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.address"
          prop="address"
          v-if="isNest"
        >
          <el-input
            v-model="equipForm.address"
            style="width: 94%"
            :placeholder="equipLanguage.addeditEquip.placeholder3"
            disabled
          ></el-input>
          <el-button class="sitebtn" @click="chooseSize"
            ><el-image :src="localImg" fit="contain"></el-image
          ></el-button>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.azimuth"
          prop="direction_angle"
          v-if="isNest"
        >
          <el-input-number
            v-model="equipForm.direction_angle"
            :min="-1"
            :max="360"
            :precision="2"
          ></el-input-number>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.airportPoint"
          v-if="isNest"
        >
          <div class="landing-point">
            <div class="point-l">
              <div
                class="lng-lat-label"
                :class="$language == 'chinese' ? '' : 'lng-lat-label-1'"
              >
                {{ equipLanguage.addeditEquip.lng }}
              </div>
              <el-input-number
                v-model="site.lng_int"
                :min="0"
                class="lng-lat"
                :controls="false"
              ></el-input-number>
            </div>
            <div class="point-l">
              <div
                class="lng-lat-label"
                :class="$language == 'chinese' ? '' : 'lng-lat-label-1'"
              >
                {{ equipLanguage.addeditEquip.lat }}
              </div>
              <el-input-number
                v-model="site.lat_int"
                :min="0"
                class="lng-lat"
                :controls="false"
              ></el-input-number>
            </div>
            <el-button class="sitebtn1" @click="chooseSize"
              ><el-image :src="localImg" fit="contain"></el-image
            ></el-button>
          </div>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.landPoint"
          v-if="isNest"
        >
          <div class="landing-point">
            <div class="point-l">
              <div
                class="lng-lat-label"
                :class="$language == 'chinese' ? '' : 'lng-lat-label-1'"
              >
                {{ equipLanguage.addeditEquip.lng }}
              </div>
              <el-input-number
                v-model="equipForm.alternate_lon_int"
                :min="0"
                class="lng-lat"
                :controls="false"
              ></el-input-number>
            </div>
            <div class="point-l">
              <div
                class="lng-lat-label"
                :class="$language == 'chinese' ? '' : 'lng-lat-label-1'"
              >
                {{ equipLanguage.addeditEquip.lat }}
              </div>
              <el-input-number
                v-model="equipForm.alternate_lat_int"
                :min="0"
                class="lng-lat"
                :controls="false"
              ></el-input-number>
            </div>
            <el-button class="sitebtn1" @click="chooseSize(1)"
              ><el-image :src="localImg" fit="contain"></el-image
            ></el-button>
          </div>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.uavModel"
          prop="uavType"
        >
          <el-select
            v-model="equipForm.uavType"
            :placeholder="equipLanguage.addeditEquip.placeholder4"
            popper-class="selectUavType"
          >
            <el-option
              v-for="item in options"
              :key="item.type"
              :label="item.name"
              :value="item.type"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="equipLanguage.addeditEquip.uavNum" prop="uav_sn">
          <el-input
            v-model="equipForm.uav_sn"
            :placeholder="equipLanguage.addeditEquip.placeholder5"
            :disabled="editCode && isAloneUav"
          ></el-input>
        </el-form-item>
        <el-form-item
          :label="equipLanguage.addeditEquip.desc"
          prop="description"
        >
          <el-input
            v-model="equipForm.desc"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
        <div class="fromButton">
          <el-button
            class="saveBut"
            v-if="!editCode"
            @click="sumbitAddEdit()"
            :class="saveCode ? 'checked' : ''"
            >{{ equipLanguage.addeditEquip.save }}</el-button
          >
          <el-button
            class="closeBut"
            v-if="!editCode"
            @click="closeEvent"
            :class="closeCode ? 'checked' : ''"
            >{{ equipLanguage.addeditEquip.cancel }}</el-button
          >
          <el-button
            class="saveBut"
            v-if="editCode"
            :class="saveCode ? 'checked' : ''"
            @click="sumbitAddEdit(10)"
            >{{ equipLanguage.addeditEquip.use }}</el-button
          >
          <el-button
            class="stopBut"
            v-if="editCode"
            @click="sumbitAddEdit(20)"
            :class="banCode ? 'checked' : ''"
            >{{ equipLanguage.addeditEquip.disable }}</el-button
          >
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
import { typeJudge } from "@/utils/deviceTypeJudge";
export default {
  props: {
    uavtypeList: {
      type: [String, Object],
      default: () => {
        return {};
      },
    },
    clickCode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: "",
      editCode: false,
      addEquip: false,
      equipForm: {
        type: 10,
        linkType: 0,
        name: "",
        sn_id: "",
        uavType: "",
        uav_sn: "",
        address: "",
        desc: "",
        alternate_lat_int: 0,
        alternate_lon_int: 0,
        direction_angle: -1,
      },
      rules: {
        type: {
          required: true,
          message: "",
          trigger: "change",
        },
        name: {
          required: true,
          message: "",
          trigger: "blur",
        },
        sn_id: {
          required: true,
          message: "",
          trigger: "blur",
        },
        address: {
          required: true,
          message: "",
          trigger: "blur",
        },
        uavType: {
          required: true,
          message: "",
          trigger: "change",
        },
        uav_sn: {
          required: true,
          message: "",
          trigger: "blur",
        },
      },
      typeItem: {},
      localImg: require("@/assets/img/equipment/location.png"),
      saveCode: false,
      closeCode: false,
      banCode: false,
      site: {
        address: "",
        lat_int: 0,
        lng_int: 0,
      },
      time: 0,
      options: this.$props.uavtypeList.stable_list,
    };
  },
  computed: {
    equipLanguage() {
      return this.$languagePackage.equipment;
    },
    typeList() {
      return this.$store.state.deviceType.typeList;
    },
    isAloneUav() {
      if (this.typeItem && this.typeItem.cla_type == "alone") {
        return true;
      }
      return false;
    },
    isNest() {
      if (this.typeItem) {
        return (
          this.typeItem.cla_type == "stable_mini" ||
          this.typeItem.cla_type == "stable"
        );
      }
      return false;
    },
  },
  watch: {
    "equipForm.type"(value) {
      if (value == 100) {
        this.options = this.uavtypeList.mobile_list;
      } else if (value == 200) {
        this.options = this.uavtypeList.alone_list;
      } else {
        this.options = this.uavtypeList.stable_list;
      }
    },
  },
  created() {
    this.rules.type.message = this.equipLanguage.addeditEquip.placeholder6;
    this.rules.name.message = this.equipLanguage.addeditEquip.placeholder7;
    this.rules.sn_id.message = this.equipLanguage.addeditEquip.placeholder8;
    this.rules.address.message = this.equipLanguage.addeditEquip.placeholder3;
    this.rules.uavType.message = this.equipLanguage.addeditEquip.placeholder4;
    this.rules.uav_sn.message = this.equipLanguage.addeditEquip.placeholder5;
  },
  methods: {
    open(type, item) {
      this.addEquip = true;
      this.originData();
      if (type == "edit") {
        this.$emit("update:clickCode", false);
        this.title = this.equipLanguage.addeditEquip.editTilte;
        this.editCode = true;
        this.equipForm = {
          type: item.type,
          name: item.name,
          linkType: item.from_cor,
          sn_id: item.sn_id,
          uavType: item.uav_type,
          uav_sn: item.uav_sn,
          address: item.address,
          desc: item.desc,
          alternate_lat_int: item.alternate_lat_int
            ? item.alternate_lat_int / 1e7
            : 0,
          alternate_lon_int: item.alternate_lon_int
            ? item.alternate_lon_int / 1e7
            : 0,
          direction_angle:
            !item.direction_angle && item.direction_angle !== 0
              ? -1
              : item.direction_angle / 100,
        };
        this.site = {
          address: item.address,
          lat_int: item.lat_int / 1e7,
          lng_int: item.lon_int / 1e7,
        };
        this.disable = item.state;
      }
      this.typeChange(this.equipForm.type);
      this.$nextTick(() => {
        this.$refs.equipForm.clearValidate();
      });
    },
    originData() {
      this.title = this.equipLanguage.addeditEquip.addTitle;
      this.editCode = false;
      this.$emit("update:clickCode", true);
      this.addEquip = true;
      this.disable = "";
      this.equipForm = {
        type: 50,
        linkType: 0,
        name: "",
        sn_id: "",
        uavType: "",
        uav_sn: "",
        address: "",
        desc: "",
        alternate_lon_int: 0,
        alternate_lat_int: 0,
        direction_angle: -1,
      };
      this.site = {
        address: "",
        lat_int: 0,
        lng_int: 0,
      };
    },
    typeChange(e) {
      this.typeList.forEach((item) => {
        if (item.value == e) {
          this.typeItem = Object.assign({}, item);
        }
      });
    },
    chooseSize(index) {
      this.$emit("chooseSize", index, this.equipForm, this.site);
    },
    //提交新增/编辑
    sumbitAddEdit(code) {
      let equip = typeJudge(this.equipForm);
      let now = new Date().getTime();
      if (this.time == 0 || now - this.time > 2000) {
        if (code != 20) {
          this.saveCode = true;
        } else {
          this.banCode = true;
        }
        this.time = now;
        this.$refs.equipForm.validate((valid) => {
          if (valid) {
            let data = {
              type: this.equipForm.type,
              name: this.equipForm.name,
              from_cor: this.equipForm.linkType,
              uav_type: this.equipForm.uavType,
              uav_sn: this.equipForm.uav_sn,
            };
            if (equip.isAlone) {
              data.sn_id = this.equipForm.uav_sn;
            } else {
              data.sn_id = this.equipForm.sn_id;
            }
            if (equip.isStable) {
              data.address = this.equipForm.address;
              data.lat_int = parseInt(this.site.lat_int * 1e7);
              data.lon_int = parseInt(this.site.lng_int * 1e7);
              data.direction_angle = this.equipForm.direction_angle * 100;
              data.alternate_lon_int = this.equipForm.alternate_lon_int * 1e7;
              data.alternate_lat_int = this.equipForm.alternate_lat_int * 1e7;
              data.pmd =
                data.sn_id.toString() +
                data.type.toString() +
                data.name +
                data.address +
                data.lat_int.toString() +
                data.lon_int.toString() +
                data.uav_type.toString() +
                data.uav_sn.toString();
            } else {
              data.pmd =
                data.sn_id.toString() +
                data.type.toString() +
                data.name +
                data.uav_type.toString() +
                data.uav_sn.toString();
            }
            if (this.equipForm.desc) {
              data.description = this.equipForm.desc;
            }
            if (!this.editCode) {
              data.pmd = data.pmd + data.from_cor.toString();
              requestHttp("deviceAdd", data).then((res) => {
                this.$emit("getMentData");
                this.$message({
                  type: "success",
                  message: this.equipLanguage.addeditEquip.addSuccess,
                  customClass: "message-info",
                });
                setTimeout(() => {
                  this.addEquip = false;
                  this.$emit("update:clickCode", false);
                }, 200);
              });
              setTimeout(() => {
                this.saveCode = false;
              }, 200);
            } else {
              if (code == 20 && this.disable == 20) {
                this.$message.warning({
                  message: this.equipLanguage.addeditEquip.noOption,
                  customClass: "message-info",
                });
                setTimeout(() => {
                  this.banCode = false;
                }, 200);
              } else {
                data.state = code;
                data.pmd =
                  data.pmd + code.toString() + data.from_cor.toString();
                requestHttp("deviceEdit", data).then((res) => {
                  this.$emit("getMentData");
                  if (code == 10) {
                    this.$message.success({
                      message: this.equipLanguage.addeditEquip.editSuccess,
                      customClass: "message-info",
                    });
                  } else {
                    this.$message.success({
                      message: this.equipLanguage.addeditEquip.equipNo,
                      customClass: "message-info",
                    });
                  }
                  setTimeout(() => {
                    this.addEquip = false;
                    this.$emit("update:clickCode", false);
                  }, 200);
                });
                setTimeout(() => {
                  this.saveCode = false;
                  this.banCode = false;
                }, 200);
              }
            }
          } else {
            this.time = 0;
            this.saveCode = false;
          }
        });
      }
    },
    //关闭页面
    closeDialog() {
      this.$emit("closeDialog", "");
    },
    //关闭页面
    closeEvent() {
      this.closeCode = true;
      setTimeout(() => {
        this.addEquip = false;
        this.$emit("update:clickCode", false);
        this.closeCode = false;
      }, 200);
    },
    backData(e, index) {
      if (index == 1) {
        this.equipForm.alternate_lat_int = e.latitude / 1e7;
        this.equipForm.alternate_lon_int = e.longitude / 1e7;
      } else {
        this.site.lat_int = e.lat_int / 1e7;
        this.site.lng_int = e.lng_int / 1e7;
        this.equipForm.address = e.address;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .dialogEquip {
    .equipForm {
      margin-top: @zoomIndex * 20px !important;
      .landing-point {
        .point-l {
          font-size: @zoomIndex * 15px !important;
          .lng-lat-label {
            width: @zoomIndex * 35px !important;
          }
          .lng-lat-label-1 {
            width: @zoomIndex * 80px !important;
          }
          .lng-lat {
            width: calc(100% - @zoomIndex * 35px) !important;
          }
        }
        .sitebtn1 {
          .el-image {
            width: @zoomIndex * 19px !important;
            height: @zoomIndex * 26px !important;
          }
        }
      }
      .fromButton {
        .el-button {
          font-size: @zoomIndex * 18px !important;
          padding: @zoomIndex * 12px @zoomIndex * 30px !important;
          border-radius: @zoomIndex * 8px !important;
        }
      }
      .sitebtn {
        .el-image {
          margin-top: @zoomIndex * 4px !important;
          margin-right: @zoomIndex * 4px !important;
          width: @zoomIndex * 19px !important;
          height: @zoomIndex * 28px !important;
        }
      }
      .point-l {
        font-size: @zoomIndex * 15px !important;
      }
    }
  }
}
.dialogEquip {
  .equipForm {
    margin-top: 20px;
    .el-form-item {
      margin-bottom: 2%;
      .el-input {
        width: 100%;
      }
      .el-select {
        width: 100%;
      }
      .el-input-number {
        width: 100%;
      }
      .landing-point {
        width: 100%;
        display: flex;
        align-items: center;
        .point-l {
          display: flex;
          align-items: center;
          font-size: 15px;
          width: 50%;
          .lng-lat-label {
            width: 35px;
          }
          .lng-lat-label-1 {
            width: 80px;
          }
          .lng-lat {
            width: calc(100% - 35px);
            margin: 0 2%;
            // text-align: left;
          }
        }
        .sitebtn1 {
          border: none;
          padding: 0;
          &:hover {
            background-color: transparent;
          }
          .el-image {
            width: 19px;
            height: 26px;
          }
        }
      }

      .sitebtn {
        border: none;
        padding: 0;
        float: right;
        &:hover {
          background-color: transparent;
        }
        .el-image {
          margin-top: 4px;
          margin-right: 4px;
          width: 19px;
          height: 28px;
        }
      }
    }
    .fromButton {
      width: 100%;
      text-align: center;
      margin-top: 5%;
      .el-button {
        font-size: 18px;
        padding: 12px 30px;
        border-radius: 8px;
      }
      .saveBut {
        margin-right: 15%;
      }
    }
  }
}
</style>
<style lang="less">
.dialogEquip {
  margin-top: 6vh !important;
  border-radius: 8px !important;
  width: 900px !important;
  .el-dialog__header {
    font-weight: 550;
    margin-left: 80px !important;
    margin-right: 80px !important;
    padding: 20px 20px 10px !important;
    .el-dialog__title {
      line-height: 24px !important;
      font-size: 18px !important;
    }
    .el-dialog__headerbtn {
      top: 20px !important;
      right: 20px !important;
      font-size: 16px !important;
    }
  }
  .el-form {
    .el-form-item {
      .el-form-item__label {
        line-height: 35px !important;
        padding-right: 12px !important;
        font-size: 14px !important;
      }
      .el-form-item__content {
        font-size: 14px !important;
        line-height: 40px !important;
        .el-input {
          font-size: 14px !important;
          line-height: 40px !important;
          .el-input__inner {
            border-width: 1px !important;
            border-radius: 4px !important;
            height: 40px !important;
            line-height: 40px !important;
            padding: 0 15px !important;
          }
          .el-input__suffix {
            right: 5px !important;
            .el-select__caret {
              font-size: 14px !important;
              width: 25px !important;
              line-height: 40px !important;
            }
          }
        }
        .el-form-item__error {
          font-size: 12px !important;
          padding-top: 4px !important;
        }
        .el-radio-group {
          display: flex;
          align-items: center;
          .el-radio {
            display: flex;
            align-items: center;
            margin-right: 20px !important;
            .el-radio__label {
              font-size: 14px !important;
              padding-left: 10px !important;
            }
            .el-radio__inner {
              width: 14px !important;
              height: 14px !important;
              border-width: 1px !important;
              &::after {
                width: 4px !important;
                height: 4px !important;
              }
            }
          }
        }
        .el-input-number {
          line-height: 38px !important;
          .el-input-number__decrease,
          .el-input-number__increase {
            top: 1px !important;
            width: 40px !important;
            font-size: 13px !important;
          }
          .el-input-number__decrease {
            left: 1px !important;
            border-radius: 4px 0 0 4px !important;
            border-right-width: 1px !important;
          }
          .el-input-number__increase {
            right: 1px !important;
            border-radius: 0 4px 4px 0 !important;
            border-left-width: 1px !important;
          }
        }
        .el-textarea {
          font-size: 14px !important;
          .el-textarea__inner {
            min-height: 33.6667px !important;
            padding: 5px 15px !important;
            border-radius: 4px !important;
            border-width: 1px !important;
          }
        }
        .landing-point {
          .point-l {
            .el-input-number {
              .el-input__inner {
                text-align: left;
              }
            }
          }
        }
      }
    }
    .firstItem {
      display: flex;
      align-items: center;
      .el-form-item__label {
        line-height: 40px !important;
        padding-right: 12px !important;
        font-size: 14px !important;
      }
    }
  }
  .el-dialog__body {
    padding: 25px 25px 30px !important;
  }
  .el-dialog__body {
    margin-left: 80px !important;
    margin-right: 80px !important;
    padding-top: 0 !important;
    margin-top: 0 !important;
    .el-divider {
      margin: 0 !important;
      height: 5px !important;
      border-radius: 2.5px;
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .dialogEquip {
    margin-top: 6vh !important;
    border-radius: @zoomIndex * 8px !important;
    width: @zoomIndex * 900px !important;
    .el-dialog__header {
      margin-left: @zoomIndex * 80px !important;
      margin-right: @zoomIndex * 80px !important;
      padding: @zoomIndex * 20px @zoomIndex * 20px @zoomIndex * 10px !important;
      .el-dialog__title {
        line-height: @zoomIndex * 24px !important;
        font-size: @zoomIndex * 18px !important;
      }
      .el-dialog__headerbtn {
        top: @zoomIndex * 20px !important;
        right: @zoomIndex * 20px !important;
        font-size: @zoomIndex * 16px !important;
      }
    }
    .el-dialog__body {
      padding: @zoomIndex * 25px @zoomIndex * 25px @zoomIndex * 30px !important;
    }
    .el-form {
      .el-form-item {
        .el-form-item__label {
          line-height: @zoomIndex * 35px !important;
          padding-right: @zoomIndex * 12px !important;
          font-size: @zoomIndex * 14px !important;
        }
        .el-form-item__content {
          font-size: @zoomIndex * 14px !important;
          line-height: @zoomIndex * 40px !important;
          .el-input {
            font-size: @zoomIndex * 14px !important;
            line-height: @zoomIndex * 40px !important;
            .el-input__inner {
              border-width: @zoomIndex * 1px !important;
              border-radius: @zoomIndex * 4px !important;
              height: @zoomIndex * 40px !important;
              line-height: @zoomIndex * 40px !important;
              padding: 0 @zoomIndex * 15px !important;
            }
            .el-input__suffix {
              right: @zoomIndex * 5px !important;
              .el-select__caret {
                font-size: @zoomIndex * 14px !important;
                width: @zoomIndex * 25px !important;
                line-height: @zoomIndex * 40px !important;
              }
            }
          }
          .el-form-item__error {
            font-size: @zoomIndex * 12px !important;
            padding-top: @zoomIndex * 4px !important;
          }
          .el-radio-group {
            .el-radio {
              margin-right: @zoomIndex * 20px !important;
              .el-radio__label {
                font-size: @zoomIndex * 14px !important;
                padding-left: @zoomIndex * 10px !important;
              }
              .el-radio__inner {
                width: @zoomIndex * 14px !important;
                height: @zoomIndex * 14px !important;
                border-width: @zoomIndex * 1px !important;
                &::after {
                  width: @zoomIndex * 4px !important;
                  height: @zoomIndex * 4px !important;
                }
              }
            }
          }
          .el-input-number {
            line-height: @zoomIndex * 38px !important;
            .el-input-number__decrease,
            .el-input-number__increase {
              top: @zoomIndex * 1px !important;
              width: @zoomIndex * 40px !important;
              font-size: @zoomIndex * 13px !important;
            }
            .el-input-number__decrease {
              left: @zoomIndex * 1px !important;
              border-radius: @zoomIndex * 4px 0 0 @zoomIndex * 4px !important;
              border-right-width: @zoomIndex * 1px !important;
            }
            .el-input-number__increase {
              right: @zoomIndex * 1px !important;
              border-radius: 0 @zoomIndex * 4px @zoomIndex * 4px 0 !important;
              border-left-width: @zoomIndex * 1px !important;
            }
          }
          .el-textarea {
            font-size: @zoomIndex * 14px !important;
            .el-textarea__inner {
              min-height: @zoomIndex * 33.6667px !important;
              padding: @zoomIndex * 5px @zoomIndex * 15px !important;
              border-radius: @zoomIndex * 4px !important;
              border-width: @zoomIndex * 1px !important;
            }
          }
        }
      }
      .firstItem {
        .el-form-item__label {
          line-height: @zoomIndex * 40px !important;
        }
      }
    }
    .el-dialog__body {
      margin-left: @zoomIndex * 80px !important;
      margin-right: @zoomIndex * 80px !important;
      .el-divider {
        height: @zoomIndex * 5px !important;
        border-radius: @zoomIndex * 2.5px !important;
      }
    }
  }
}
</style>