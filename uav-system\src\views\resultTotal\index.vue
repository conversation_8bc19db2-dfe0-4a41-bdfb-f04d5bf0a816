<!-- 成果总列表 -->
<template>
  <div id="result-total-list">
    <!-- 表单 -->
    <div class="form el-input-white" style="margin-left: 155px">
      <el-form label-width="0" inline size="small" :model="form">
        <el-form-item>
          <el-input
            :placeholder="formPlaceholder.search"
            :style="formInputStyle"
            v-model="form.search"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="form.uav"
            :placeholder="formPlaceholder.uav"
            :style="formInputStyle"
            :popper-append-to-body="false"
            clearable
          >
            <el-option
              v-for="(item, index) in equipmentList"
              :key="index"
              :value="item.sn_id"
              >{{ item.sn_id }}</el-option
            >
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="form.type"
            :placeholder="formPlaceholder.type"
            :style="formInputStyle"
            :popper-append-to-body="false"
            clearable
          >
            <!-- <el-option :value="0" label="全部"></el-option> -->
            <el-option
              v-for="(item, index) in achievementType"
              :key="index"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <twice-date-picker
            @change="dateChange"
            :startTime.sync="form.start_tms"
            :endTime.sync="form.end_tms"
            :isTimestamp="true"
          ></twice-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">{{
            buttonGroup.search
          }}</el-button>

          <el-button
            type="primary"
            @click="batchEditDownload"
            v-if="!batchDelectState"
          >
            {{
              batchDownloadState ? buttonGroup.submitDown : buttonGroup.bulkDown
            }}
          </el-button>

          <el-button
            type="primary"
            @click="cancelDownload"
            v-if="batchDownloadState"
          >
            {{ buttonGroup.cancelbulkdown }}
          </el-button>

          <el-button
            type="primary"
            @click="batchDelect"
            v-if="!batchDownloadState"
          >
            {{ batchDelectState ? buttonGroup.submitDel : buttonGroup.bulkDel }}
          </el-button>

          <el-button
            type="primary"
            @click="cancelDownload"
            v-if="batchDelectState"
          >
            {{ buttonGroup.cancelDel }}
          </el-button>

          <el-button
            type="primary"
            @click="batchDownload"
            v-if="sortId"
            v-loading="batchDelectLoading"
          >
            {{ buttonGroup.sortiesDown }}
          </el-button>
          <el-button type="primary" @click="toSortieList">
            {{ buttonGroup.sortieList }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 内容 -->
    <div class="right-content">
      <scroll-list
        pmd="page"
        urlName="resultList"
        ref="scrollList"
        :params="form"
        :pageSize="20"
        :isAutoHttp="true"
        :delayed="0.1"
        :distanceBootm="260"
        :getBackTop="true"
        @requestSuccess="getListData"
      >
        <template v-slot:content>
          <div
            v-for="(item, index) in tableData"
            :key="index"
            style="display: flex"
          >
            <div class="list-date-time" style="">
              <div class="content-date">
                <div class="content-date-main">
                  <div class="date-time">
                    <div class="">{{ item.month }}</div>
                    <div class="">{{ item.year }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 内容列表 -->
            <div
              class="content-list"
              :style="{ borderTopLeftRadius: index === 0 ? '8px' : '0' }"
            >
              <achievement-item
                class="content-list-item"
                :edit="batchDelectState || batchDownloadState"
                :item="row"
                v-for="(row, lens) in item.list"
                :key="lens + 'row'"
                :editColor="editType == 'delete' ? 'red' : ''"
                @onDelete="deleteOne(row, item.key, lens)"
                @onSelect="selectAchievement"
              />
            </div>
          </div>
        </template>
      </scroll-list>
    </div>
  </div>
</template>
    
<script>
import achievementItem from "../achievementAdmin/components/achievementItem.vue";
import customTable from "@/components/customTable/index.vue";
import twiceDatePicker from "@/components/twiceDatePicker/index.vue";

import imgIcon from "@/components/imgIcon/index.vue";
import scrollList from "@/components/scrollList/index.vue";

import { nowDate } from "@/utils/date.js";
import axios from "axios";
import request from "@/utils/api";

export default {
  components: {
    achievementItem,
    customTable,
    twiceDatePicker,
    imgIcon,
    scrollList,
  },
  data() {
    return {
      dateList: [{ year: "2023", month: "05" }],
      form: {
        search: "",
        sn_id: "",
        type: undefined,
        start_tms: "",
        end_tms: "",
      },
      formInputStyle: {
        width: "220px",
      },
      isShowPage: false,
      tableData: {},
      batchDelectState: false,
      batchDelectLoading: false,
      batchDownloadState: false,
      editType: "",
      selectList: {},
    };
  },
  computed: {
    // 成果类型
    achievementType() {
      return this.$store.state.dict.achievementType;
    },
    // 设备列表
    equipmentList() {
      return this.$store.state.equipment.equipmentList;
    },
    formPlaceholder() {
      return this.$languagePackage.achievement.form.placeholder;
    },
    buttonGroup() {
      return this.$languagePackage.achievement.form.button;
    },
    sortId() {
      return this.$route.query.sort_id;
    },
  },
  created() {
    if (this.equipmentList.length === 0) {
      this.$store.dispatch("getEquipmentList", {});
    }
    // this.setDate("", 0);

    let query = this.$route.query;
    this.form.search = query.sort_id;

    // window.addEventListener("onresize",()=>{
    //   let window
    // })
  },
  methods: {
    getListData: function (data) {
      // 按照年月进行拆分
      let list = data.list;
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        let time = item.create_time;
        let key = time.slice(0, 7);

        if (!this.tableData[key]) {
          this.$set(this.tableData, key, {
            list: [],
            key: key,
            year: time.slice(0, 4),
            month: time.slice(5, 7),
          });
        }
        this.tableData[key].list.push(item);
      }
    },

    isNoData(data) {
      this.tableData = data;
      this.isShowPage = data.length === 0 ? true : false;
      return data.length === 0;
    },
    search: function () {
      this.tableData = {};
      this.$refs.scrollList.refresh();
    },
    // 设置开始时间
    setDate: function (time, index) {
      let date = nowDate(time);
      this.$set(this.dateList[index], "year", date.year);
      this.$set(this.dateList[index], "month", date.month);
    },

    dateChange: function (time) {
      if (time && time != null) {
        this.setDate(time[0], 0);
        this.dateList.push({});
        this.setDate(time[1], 1);
      } else {
        this.dateList = [{}];
        this.setDate(null, 0);
      }
    },
    batchDownload: function () {
      this.batchDelectLoading = true;
      let params = Object.assign({}, this.form);
      params["page"] = 0;
      params["size"] = this.$route.query.count;
      params.pmd = "0";
      params.search = this.sortId;
      request("resultList", params).then((res) => {
        let list = res.data.list?res.data.list:[];
        // for (let i = 0; i < list.length; i++) {
        //   this.download(list[i]);
        // }
        this.orderDownload(list, 0);
      });
    },
    // 顺序下载
    orderDownload: function (list, index) {
      let item = list[index];
      axios
        .get(item.o_url, { responseType: "blob" })
        .then((response) => {
          const blob = new Blob([response.data]);
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = item.o_name;
          link.click();
          URL.revokeObjectURL(link.href);
          this.batchDelectLoading = false;

          if (list[index + 1]) {
            this.orderDownload(list, index + 1);
          }
        })
        .catch(console.error);
    },
    download: function (item) {
      axios
        .get(item.o_url, { responseType: "blob" })
        .then((response) => {
          const blob = new Blob([response.data]);
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = item.o_name;
          link.click();
          URL.revokeObjectURL(link.href);
          this.batchDelectLoading = false;
        })
        .catch(console.error);
    },
    // 单个删除
    deleteOne: function (item, key, index) {
      this.$confirm("确定删除该成果吗？", "确认删除", {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          id: item.id,
          sn_id: item.sn_id,
          sort_id: item.sort_id,
          pmd: "id,sn_id,sort_id",
          jointPmd: true,
        };

        request("resultDelete", params).then((res) => {
          this.$message({
            type: "success",
            message: "删除成功",
          });
          this.tableData[key].list.splice(index, 1);
        });
      });
    },
    // 批量删除
    batchDelect: function () {
      if (this.batchDelectState) {
        this.$confirm("确定删除选中的成果吗？", "确认删除", {
          confirmButtonText: "确定删除",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          let ids_json = [];

          for (let k in this.selectList) {
            let item = this.selectList[k];
            ids_json.push({
              id: item.id,
              sn_id: item.sn_id,
              sort_id: item.sort_id,
            });
          }

          let params = {
            ids_json: JSON.stringify(ids_json),
            pmd: "ids_json",
            jointPmd: true,
          };

          request("resultDeleteIds", params).then((res) => {
            this.$message({
              type: "success",
              message: "删除成功",
            });
            this.cancelDownload();
            this.search();
          });
        });
      } else {
        this.batchDelectState = true;
        this.editType = "delete";
      }
    },
    selectAchievement: function (item, state) {
      if (state) {
        this.selectList[item.id] = item;
      } else {
        delete this.selectList[item.id];
      }
    },
    batchEditDownload: function () {
      if (this.batchDownloadState) {
        this.$confirm("确认下载选中的成果吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          for (let k in this.selectList) {
            this.download(this.selectList[k]);
          }

          this.cancelDownload();
        });
      } else {
        this.batchDownloadState = true;
        this.editType = "download";
      }
    },
    cancelDownload: function () {
      this.batchDownloadState = false;
      this.batchDelectState = false;
      this.selectList = {};
      for (let k in this.tableData) {
        for (let i = 0; i < this.tableData[k].list.length; i++) {
          this.$set(this.tableData[k].list[i], "isSelect", false);
        }
      }
    },
    toSortieList: function () {
      this.$router.push({ name: "achievementAdmin" });
    },
  },
};
</script>

<style lang="less" scoped>
.media-result-list();
@media screen and (min-width: 1200px) and (max-width: 1440px) {
  .media-result-list(102px);
}

@media screen and(min-width: 1920px) {
  @radit: 100vw / 1920px;
  .list-date-time {
    width: @radit * 138px !important;
    height: @radit * 140px !important;
    margin-right: @radit * 20px !important;
    margin-top: @radit * 20px !important;
    .content-date {
      border-bottom-left-radius: @radit * 10px !important;
      border-bottom-right-radius: @radit * 10px !important;
      overflow: hidden;
      .content-date-main {
        .date-time {
          font-size: @radit * 40px !important;
        }
      }
    }
  }
}

.media-result-list(
    @formH: 51px,
) {
  #result-total-list {
    width: calc(100% - 40px);
    height: calc(100% - 40px);
    // background-color: #000;
    padding: 20px;

    .right-content {
      overflow: hidden;
      height: calc(100% - @formH);

      .list-date-time {
        background-color: rgba(0, 0, 0, 0);
        width: 138px;
        height: 140px;
        margin-right: 20px;
        margin-top: 20px;
        .content-date {
          width: 100%;
          height: 100%;
          margin-right: 20px;
          border-bottom-left-radius: 10px;
          border-bottom-right-radius: 10px;
          overflow: hidden;
          .content-date-main {
            width: 100%;
            height: 100%;
            .date-time {
              font-size: 40px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .content-list {
        width: calc(100% - 158px);
        display: flex;
        flex-wrap: wrap;
        // background-color: rgba(21, 22, 22, 0.98);
        padding: 20px;
      }
    }
  }
}

// 设置
.content-list-item-style();
@media screen and (min-width: 1451px) and (max-width: 1700px) {
  .content-list-item-style(calc(25% - 40px));
}
@media screen and (min-width: 1200px) and (max-width: 1450px) {
  .content-list-item-style(calc(33.333333333333% - 40px));
}
.content-list-item-style(
  @Width: calc(20% - 40px)
) {
  .content-list-item {
    width: @Width !important;
  }
}
</style>