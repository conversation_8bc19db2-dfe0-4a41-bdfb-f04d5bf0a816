<!-- 航线回放 -->
<template>
  <div id="route-replay" class="route-replay">
    <!-- 地图背景 -->
    <div class="map-bg" id="map"></div>

    <!-- 内容左侧 -->
    <div class="main-left">
      <div class="left-content">
        <div class="left-mode">
          <div class="mode-item">
            <div class="item-label">{{ language.mode }}：</div>
            <div class="item-value">{{ flightMode }}</div>
          </div>
        </div>
        <div class="left-top">
          <div
            class="left-top-cell"
            v-for="(item, index) in flightInfo"
            :key="index"
          >
            <div class="cell-label">{{ item.label }}</div>
            <div class="cell-value">{{ item.value }}</div>
          </div>
        </div>

        <!-- 飞行数据变化图 -->
        <div class="flight-data-change">
          <div class="flight-title">{{ language.modeTitle }}</div>
          <div class="color-list">
            <div
              class="color-cell"
              v-for="(item, index) in colorList"
              :key="index"
            >
              <div class="cell-top" :style="{ background: item.color }"></div>
              <div class="cell-bot mt5">{{ item.label }}</div>
            </div>
          </div>
          <div
            class=""
            id="flight-echarts"
            style="width: 100%; height: 240px"
          ></div>
        </div>
      </div>
    </div>

    <!-- 飞机信息 -->
    <div class="uav-info">
      <div class="uav-info-cell" v-for="(item, index) in uavInfo" :key="index">
        <div class="cell-label">{{ item.label }}：</div>
        <div class="cell-value">{{ item.value }}</div>
      </div>
    </div>

    <!-- 播放设置 -->
    <div class="play-set">
      <!-- 重新开始 -->
      <div class="afresh-play" @click="refresh">
        <i class="el-icon-refresh-left"></i>
      </div>

      <!-- 时间 -->
      <div class="ml20 mr20">
        <span>
          {{ playingTime[0] }}:{{ playingTime[1] }}:{{ playingTime[2] }}
        </span>
        <span class="mr5 ml5">/</span>
        <span>{{ showTotalTime }}</span>
      </div>

      <!-- 开始/暂停按钮 -->
      <div class="play-pause mr20" @click="taskPlayback">
        <!-- 播放 -->
        <div class="play-pause-cell" v-show="playState">
          <img src="../../assets/icon/play.png" alt="" style="width: 10px" />
          <img
            src="../../assets/icon/play-houer.png"
            alt=""
            style="width: 10px"
            class="hover-show"
          />
        </div>
        <!-- 暂停 -->
        <div class="play-pause-cell" v-show="!playState">
          <img src="../../assets/icon/pause.png" alt="" style="width: 12px" />
          <img
            src="../../assets/icon/pause-houer.png"
            alt=""
            style="width: 14px"
            class="hover-show"
          />
        </div>
      </div>

      <!-- 进度条 -->
      <div class="progress-bar">
        <el-slider
          v-model="playDuration"
          :show-tooltip="false"
          :max="playTotalDuration"
        />
      </div>

      <!-- 倍速 -->
      <div class="multiple">
        <div class="" @click="multipleVisible = !multipleVisible">
          {{ multipleIndex }}x
        </div>
        <div class="multiple-prpover">
          <transition name="el-zoom-in-bottom">
            <div v-show="multipleVisible" class="multiple-body">
              <div
                class="multiple-cell"
                v-for="(item, index) in 10"
                :key="index"
                @click="cutMultiple(11 - item)"
                :class="multipleIndex == 11 - item ? 'select-style' : ''"
              >
                {{ 11 - item }}x
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>

    <!-- 虚拟摇杆 -->
    <virtual-rocker
      style="position: fixed; right: 0px; bottom: 0px"
      ref="virtualRocker"
    ></virtual-rocker>
  </div>
</template>

<script>
import { timestampSwitch } from "@/utils/date";
import { setFlightlineChart, getCsvData } from "./index.js";
import { getLocalStorage } from "@/utils/storage.js";

import virtualRocker from "./module/virtualRocker.vue";

import createMap from "@/utils/cesium/createMap";
import { setCzmlData } from "@/utils/cesium/csvToczmlPlay.js";
import mapMethods from "@/utils/cesium/mapMethods";
export default {
  components: {
    virtualRocker,
  },
  data() {
    return {
      // 无人机信息
      uavInfo: [
        { label: "任务类型", value: "", key: "mission_type_label" },
        { label: "设备编号", value: "AC50_01", key: "sn_id" },
        { label: "无人机编号", value: "1ZNBJP00C00HX", key: "uav_id" },
        // { label: "无人机机型", value: "D49CDD47F536", key: "" },
        { label: "起飞时间", value: "2022:02:10 13:48:10", key: "start_time" },
        { label: "着陆时间", value: "2022:02:10 13:48:10", key: "end_time" },
      ],
      // 飞行信息
      flightInfo: [
        { label: "经度", value: "0.00", key: "lng" },
        { label: "纬度", value: "0.00", key: "lat" },
        { label: "速度", value: "0.00", key: "hs" },
        { label: "高度(米)", value: "0.00", key: "height" },
        { label: "航向", value: "0.00", key: "yaw" },
        { label: "横滚角", value: "0.00", key: "roll" },
        { label: "俯仰角", value: "0.00", key: "pitch" },
        { label: "飞行距离(米)", value: "0.00", key: "distance" },
      ],

      multipleVisible: false,
      multipleIndex: 1,
      playState: false,

      playingTime: ["00", "00", "00"], // 已播放时间
      playTotalDuration: 0, // 进度条拖动最大值，即总时长(秒)
      showTotalTime: "00:00:00", // 播放总时长 hh:mm:ss

      // 已走过得路径
      passedPolyline: null,

      playDuration: 0, // 进度条进度，int
      playTrackPoint: 0, // 当前点下标
      lineChart: null, // 飞行数据图表实例

      //
      colorList: [
        { color: "#F38E00", label: "高度", key: "altitude" },
        { color: "#4CDC4C", label: "垂直速度", key: "verticalVelocity" },
        { color: "#2947B2", label: "水平速度", key: "horizontalVelocity" },
      ],

      // 飞行数据
      flightData: [],
      flightDataManage: {},

      // 轨迹展示
      map: null,
      uavMarker: null,
      realTimeLine: null,

      // 记录每一步的数据
      routeQuery: {},

      playTotal: 0,
      changePlayIndex: 0,
      flightMode: null,
      returnContent: {},
    };
  },
  computed: {
    flightModeList() {
      return this.$store.state.dict.flightModeList;
    },
    language() {
      return this.$languagePackage.routeReplay;
    },
  },
  watch: {
    // 监听播放坐标点变化
    playDuration: function (val) {
      let index = val;
      // 绘制走过得路径
      let logLatList = this.flightDataManage.logLatList;
      if (this.sliderDown) {
        this.playTotal = val;
        this.returnContent.updateCurrentTime &&
          this.returnContent.updateCurrentTime(val);
      }

      let mode = this.flightData[val]
        ? this.flightData[val].mode
        : this.flightData[this.flightData.length - 1].mode;
      this.flightMode = this.flightModeList[mode];
      if (this.flightData[index]) {
        const time = this.flightData[index].time;
        this.playingTime = timestampSwitch(time).list;

        for (let i = 0; i < this.flightInfo.length; i++) {
          let item = this.flightInfo[i];
          if (item.key) {
            item.value = this.flightData[index][item.key];
          }
        }
      }

      this.setChartShowTip(val);
      this.$refs.virtualRocker.uploadRocker(this.flightData[val].rocker);
    },
  },
  beforeDestroy() {
    localStorage.removeItem(this.routeQuery.sort_id);
    window.removeEventListener("mouseup", this.mouseUpSlider);
  },
  created() {
    this.init();
    window.addEventListener("mouseup", this.mouseUpSlider);
    this.languageInit();
  },
  methods: {
    languageInit: function () {
      let basic = this.language.basic;
      for (let i = 0; i < this.uavInfo.length; i++) {
        let k = this.uavInfo[i].key;
        this.uavInfo[i].label = basic[k];
      }
      let point = this.language.point;
      for (let i = 0; i < this.flightInfo.length; i++) {
        let k = this.flightInfo[i].key;
        this.flightInfo[i].label = point[k];
      }

      let chart = this.language.chart;
      for (let i = 0; i < this.colorList.length; i++) {
        let k = this.colorList[i].key;
        this.colorList[i].label = chart[k];
      }
    },

    init: function () {
      this.mapsInit();
      let sort_id = this.$route.query.sort_id;
      this.routeQuery = this.$route.query;
      if (!sort_id) {
        return this.$message({
          type: "error",
          message: "数据获取失败",
        });
      }
      this.taskInfo = getLocalStorage(sort_id);
      let url = this.taskInfo.file_url;
      for (let i = 0; i < this.uavInfo.length; i++) {
        let item = this.uavInfo[i];
        if (item.key) {
          item.value = this.taskInfo[item.key];
        }
      }
      setTimeout(() => {
        this.dataInit(url);
      });
    },

    // 地图初始化
    mapsInit: function () {
      this.$nextTick(() => {
        this.map = createMap.createMap("map", {
          layerIndex: 0,
          loadLayerText: false,
        });
      });
    },
    //页面数据初始化
    dataInit: function (url) {
      setCzmlData(this.map, {
        url,
        playChange: this.playChange,
        endPlay: this.endPlay,
      }).then((res) => {
        this.flightData = res.returnData.list;
        let index = this.flightData.length - 1;
        this.flightMode = this.flightModeList[this.flightData[0].mode];
        this.playTotalDuration = index;
        this.returnContent = res;
        this.flightDataManage = res.returnData;
        // 获取总时长
        let time = this.flightData[index].time;
        this.showTotalTime = timestampSwitch(time).time;
        this.flightDataChange();
        if (this.flightData.length > 0) {
          this.drawLine();
        }
        // 获取进度条dom，添加鼠标按下
        let elm = document.querySelector(".progress-bar .el-slider__button");
        elm.addEventListener("mousedown", this.mousedownSlider);
        let slider = document.querySelector(".progress-bar .el-slider__runway");
        slider.addEventListener("click", () => {
          // 绘制走过得路径
          if (this.playState) {
            this.returnContent.updatePlayStatus &&
              this.returnContent.updatePlayStatus(false);
            this.returnContent.updateCurrentTime &&
              this.returnContent.updateCurrentTime(this.playDuration);
            this.playPath();
            setTimeout(() => {
              this.returnContent.updatePlayStatus &&
                this.returnContent.updatePlayStatus(true);
            });
          }
        });
      });
    },

    // 滚动条按下
    mousedownSlider: function () {
      this.sliderDown = true;
      if (this.playState) {
        this.returnContent.updatePlayStatus &&
          this.returnContent.updatePlayStatus(false);
      }
    },

    // 滚动条松开
    mouseUpSlider: function () {
      if (this.sliderDown) {
        this.sliderDown = false;
        if (this.playState) {
          this.returnContent.updatePlayStatus &&
            this.returnContent.updatePlayStatus(true);
          this.playPath();
        }
      }
    },

    // 绘制线条
    drawLine: function () {
      let logLatList = this.flightDataManage.list;
      let polyLine = mapMethods.drawLine(logLatList, {
        color: "#2288ff60",
        width: 6,
      });
      this.map.entities.add({ id: "polyline", polyline: polyLine });
    },

    // 飞行数据变化图
    flightDataChange: function () {
      let { hData, hsData, vsData, xAxisData } = this.flightDataManage || {};

      let textList = [
        this.language.chart.altitude,
        this.language.chart.verticalVelocity,
        this.language.chart.horizontalVelocity,
      ];

      let option = setFlightlineChart(
        hData,
        vsData,
        hsData,
        xAxisData,
        textList
      );
      this.lineChart = this.$echarts.init(
        document.getElementById("flight-echarts")
      );

      this.lineChart.setOption(option);
      this.setChartShowTip(0);
    },

    // 设置图表显示的东西
    setChartShowTip: function (index) {
      let chart = this.lineChart;
      let action = {
        type: "showTip",
        seriesIndex: 0,
        dataIndex: index || this.playTrackPoint,
      };
      chart && chart.dispatchAction(action);
    },

    // 切换倍速
    cutMultiple: function (val) {
      this.multipleIndex = val;
      if (this.playState) {
        this.returnContent.updatePlayStatus &&
          this.returnContent.updatePlayStatus(false);
      }
      this.returnContent.updateCurrentMultiplier &&
        this.returnContent.updateCurrentMultiplier(val);
      if (this.playState) {
        setTimeout(() => {
          this.returnContent.updatePlayStatus &&
            this.returnContent.updatePlayStatus(true);
        });
      }
    },
    //监听播放点
    playChange(playIndex, data, currentTime) {
      if (!this.sliderDown) {
        this.playDuration = playIndex; // 获取当前播放点
      }
    },
    //监听播放结束
    endPlay() {
      this.returnContent.updateCurrentTime &&
        this.returnContent.updateCurrentTime(
          this.flightDataManage.list.length - 1
        );
      this.playState = false;
      this.returnContent.updatePlayStatus &&
        this.returnContent.updatePlayStatus(false);
    },
    // 开始/暂停播放
    taskPlayback: function () {
      this.playState = !this.playState;
      if (this.playState) {
        if (this.playDuration == this.playTotalDuration) {
          let item = this.language.message.taskPlayConfirm;

          this.$confirm(item.content, item.title, {
            confirmButtonText: item.confirmText,
            cancelButtonText: item.camceLText,
            type: "warning",
          }).then(() => {
            this.playDuration = 0;
            this.playTotal = 0;

            this.returnContent.updateCurrentTime &&
              this.returnContent.updateCurrentTime(this.playDuration);
            this.playState = false;
            this.$nextTick(() => {
              this.taskPlayback();
            });
          });
          return false;
        }
        this.returnContent.updatePlayStatus &&
          this.returnContent.updatePlayStatus(true);
        this.playPath();
      } else {
        this.returnContent.updatePlayStatus &&
          this.returnContent.updatePlayStatus(false);
      }
    },
    // 播放
    playPath: function () {
      let dataTime = JSON.parse(JSON.stringify(this.flightDataManage.dataTime));
      let index = this.playDuration;

      let list = dataTime.slice(index, dataTime.length);

      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        item.duration = item.duration / this.multipleIndex;
      }
    },

    // 刷新
    refresh: function () {
      let item = this.language.message.refresh;
      this.$confirm(item.content, item.title, {
        confirmButtonText: item.confirmText,
        cancelButtonText: item.camceLText,
        type: "warning",
      }).then(() => {
        if (this.playState) {
          this.returnContent.updatePlayStatus &&
            this.returnContent.updatePlayStatus(false);
        }
        this.playDuration = 0;
        this.playTotal = 0;
        this.returnContent.updateCurrentTime &&
          this.returnContent.updateCurrentTime(this.playDuration);
        if (this.playState) {
          this.returnContent.updatePlayStatus &&
            this.returnContent.updatePlayStatus(true);
          this.playPath();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
// 只适配1200屏幕以上
@media screen and(min-width: 1200px) {
  @radio: 100vw / 1920px;

  .uav-info {
    height: @radio * 60px !important;
    left: @radio * 400px !important;

    @widths: @radio * 440px;
    width: calc(100% - @widths) !important;
    padding: 0 @radio * 20px !important;
    .uav-info-cell {
      margin-right: @radio * 40px !important;
      padding-right: @radio * 5px !important;
      .cell-label {
        font-size: @radio * 16px !important;
      }
      .cell-value {
        font-size: @radio * 20px !important;
      }
    }
  }

  .main-left {
    width: @radio * 400px !important;
    min-width: @radio * 400px !important;
    .left-content {
      .left-mode {
        left: @radio * 30px !important;
        width: @radio * 370px !important;
        height: @radio * 50px !important;
        .mode-item {
          .item-label {
            font-size: @radio * 18px !important;
          }
          .item-value {
            font-size: @radio * 20px !important;
          }
        }
      }
    }
    .left-top {
      top: @radio * 100px !important;
      .left-top-cell {
        @width: @radio * 30px;
        width: calc(50% - @width) !important;
        margin-left: @radio * 30px !important;
        .cell-label {
          font-size: @radio * 16px !important;
        }
        .cell-value {
          font-size: @radio * 24px !important;
        }
      }
    }
    .flight-data-change {
      .flight-title {
        font-size: @radio * 20px !important;
        padding: 0 @radio * 20px !important;
        height: @radio * 30px !important;
      }
      .color-list {
        height: @radio * 100px !important;
        .color-cell {
          margin-left: @radio * 20px !important;
          .cell-top {
            width: @radio * 20px !important;
            height: @radio * 14px !important;
            border-radius: @radio * 5px !important;
          }
          .cell-bot {
            font-size: @radio * 12px !important;
          }
        }
      }
    }
  }

  .route-replay {
    .play-set {
      bottom: @radio * 20px !important;
      left: @radio * 420px !important;

      @width: @radio * 480px !important;
      width: calc(100% - @width - 296px) !important;

      height: @radio * 50px !important;
      border-radius: @radio * 5px !important;

      padding: 0 @radio * 20px !important;
      .multiple {
        height: @radio * 50px !important;
        width: @radio * 46px !important;
        .multiple-prpover {
          top: @radio * -250px !important;
          border-top-left-radius: @radio * 5px !important;
          border-top-right-radius: @radio * 5px !important;
          .multiple-cell {
            line-height: @radio * 25px !important;
            padding: 0 @radio * 10px !important;
          }
        }
      }
    }
  }
}

.uav-info {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.9);
  position: absolute;
  left: 400px;
  top: 0;
  width: calc(100% - 440px);
  padding: 0 20px;
  .uav-info-cell {
    display: flex;
    align-items: center;
    margin-right: 40px;
    flex-wrap: wrap;
    padding-right: 5px;
    &:last-child {
      margin-right: 0 !important;
    }
    .cell-label {
      color: #948d8d;
      font-size: 16px;
    }
    .cell-value {
      color: #fff;
      font-size: 20px;
    }
  }
}

// 左侧信息栏-适配
.main-left {
  width: 400px;
  min-width: 400px;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  position: absolute;
  left: 0;
  top: 0;
  .left-content {
    position: relative;
    width: 100%;
    height: 100%;
    .left-mode {
      position: absolute;
      left: 30px;
      top: 0;
      width: 370px;
      height: 50px;
      .mode-item {
        display: flex;
        height: 100%;
        align-items: center;
        .item-label {
          font-size: 18px;
          color: #989898;
        }
        .item-value {
          font-size: 20px;
          color: #ffffff;
        }
      }
    }
  }
  .left-top {
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    left: 0;
    top: 100px;
    .left-top-cell {
      width: calc(50% - 30px);
      margin-left: 30px;
      .cell-label {
        font-size: 16px;
        color: #989898;
      }
      .cell-value {
        font-size: 24px;
        color: #0590ec;
      }
    }
  }
  .flight-data-change {
    // padding: 0 20px;
    //   margin-top: 40px;
    width: 100%;
    position: absolute;
    bottom: 0px;
    left: 0;
    .flight-title {
      color: #fff;
      font-size: 20px;
      padding: 0 20px;
      height: 30px;
    }
    .color-list {
      display: flex;
      height: 100px;
      align-items: center;
      .color-cell {
        margin-left: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .cell-top {
          width: 20px;
          height: 14px;
          border-radius: 5px;
        }
        .cell-bot {
          font-size: 12px;
          color: #fff;
        }
      }
    }
  }
}

.route-replay {
  height: 100%;
  position: relative;
  .map-bg {
    width: 100%;
    height: 100%;
  }

  .play-set {
    position: absolute;
    bottom: 20px;
    left: 420px;
    width: calc(100% - 80px - 400px - 296px);
    height: 50px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 5px;
    display: flex;
    align-items: center;
    color: #fff;
    padding: 0 20px;
    .progress-bar {
      flex-grow: 1;
    }
    .multiple {
      position: relative;
      height: 50px;
      width: 46px;
      display: flex;
      align-items: center;
      justify-content: center;
      //   text-align: center;
      .multiple-prpover {
        position: absolute;
        left: 0px;
        top: -250px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        .multiple-cell {
          line-height: 25px;
          padding: 0 10px;
          cursor: pointer;
          text-align: center;
          background-color: rgba(0, 0, 0, 0.6);
          &:hover {
            color: #0590ec;
          }
        }
        .select-style {
          color: #0590ec;
        }
      }
    }
    .afresh-play {
      i {
        transition: 0.2s;
      }
      &:hover i {
        color: #0590ec;
      }
    }

    .play-pause {
      .play-pause-cell {
        position: relative;
        display: flex;
        align-items: center;
        .hover-show {
          position: absolute;
          left: 0;
          //   top: 0;
          transition: 0.2s;
          transform: scale(0);
        }
        &:hover .hover-show {
          transform: scale(1);
        }
      }
    }
  }
}
</style>
