const coordination = {
    allBtn: {
        search: '搜\xa0\xa0\xa0\xa0索',
        newSet: '新\xa0建\xa0协\xa0同\xa0组',
        sure: "确\xa0定\xa0组\xa0网",
        set: '设置',
        del: '删除',
        sure: '确定',
        cancel: '取消',
        toGroup: '进入组网',
        editGroup: '编辑组网'
    },
    tipInfo: {
        placeholder: '请输入设备名称\xa0/\xa0地址',
        placeholder1: '请输入协同组名称',
        errorMessage: '组网名称为空！',
        errorMessage1: '未选择设备！',
        successMessage: '组网编辑成功！',
        successMessage1: '组网创建成功！',
        placeholder2: '该删除无法恢复，您确定删除该组网信息？',
        tip: '提示'
    },
    group: {
        name: '协同组名称：',
        select: '已选择',
        deviceGroup: "个设备组网",
        normal: '正常',
        delete: '删除',
        on_line: '设备在线',
        out_line: '设备离线',
        noconnect: '无人连接',
        connect: '有人连接'
    },
    table: {
        name: '协同组名称',
        time: '协同组创建时间',
        num: '设备数量',
        online: '当前在线设备',
        state: "当前状态",
        option: '操作',
        choose: '选择',
        onState: '在线状态',
        photo: '设备图片',
        name1: '设备名称',
        type: '设备类型',
        sn_id: '设备编号',
        address: '设备部署地址',

    },
    device: {
        title: '设备列表',
        device: '套设备',
        toTask: '一键下发任务',
        previous: '上一页',
        next: '下一页',
    },
    video: {
        videoLoading: '视频加载中',
        uavVideo: '无人机视频',
        inVideo: '舱内监控',
        outVideo: '舱外监控',
        errorVideo: '视频加载失败',
        reconnect: '重新连接'
    },
    deviceList: {
        title: '组内设备列表',
        bind: '已绑定任务',
        unbind: '未绑定任务',
        noData: '暂无绑定任务',
        waitBind: '待选任务列表',
        type: '任务类型：',
        rename: '重命名',
        del: '删除',
        unbind: "解除绑定",
        binds: '绑定任务',
        edit: '编辑',
        nodata1: '暂无可待选任务',
        taskRename: '任务重命名',
        taskName: '任务名称',
        nameMessage: '请输入任务名称',
        save: '保\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0存',
        cancel: "取\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0消",
        bindtip: '是否解除任务绑定？',
        tips: '提示',
        sure: '确定',
        cancel1: '取消',
        messageInfo: '已解除任务绑定！',
        messageInfo1: '已取消解除任务绑定！',
        delMessage: '此操作无法恢复，您确定删除该任务信息？',
        delSuccess: '删除成功',
        delInfo: '已取消删除',
        renameSuccess: "重命名成功！",
        renameInfo: '已取消重命名。'

    },
    route: {
        placeholder: '航点不能超出围栏范围！',
        placeholder1: '航线超出围栏范围！',
        placeholder2: '相邻航点有效距离不能超过2千米！',
        placeholder3: '您确定清除该航线的所有信息？',
        messageInfo: '清除成功!',
        messageInfo1: '已取消清除',
        tip: '提示',
        saveBtn: '确定',
        cancelBtn: '取消',
    },
    routeLoading: "航点加载中",
    placeholder10: '已撤回至最初状态',



}
export default coordination