<template>
  <custom-dialog
    :title="title"
    :visible.sync="isShow"
    :submitLoading="saveLoading"
    @submit="saveFormInfo"
    @cancel="cancel"
    @close="shutDialog"
  >
    <template v-slot:main>
      <el-form label-width="120px" :model="form" :rules="rules" ref="dictForm">
        <el-form-item :label="formLabel.name" prop="name">
          <el-input
            :placeholder="placeholder.name"
            v-model="form.name"
          ></el-input>
        </el-form-item>
        <el-form-item :label="formLabel.name_en" prop="name_en">
          <el-input
            :placeholder="placeholder.name_en"
            v-model="form.name_en"
          ></el-input>
        </el-form-item>
        <el-form-item :label="formLabel.funId" prop="fun_id">
          <el-input
            :placeholder="placeholder.funID"
            v-model="form.fun_id"
          ></el-input>
        </el-form-item>
        <el-form-item :label="formLabel.description" prop="description">
          <el-input
            :placeholder="placeholder.description"
            v-model="form.description"
          />
        </el-form-item>
        <el-form-item :label="formLabel.state">
          <el-radio-group v-model="form.state">
            <el-radio
              v-for="item in funState"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label.replace("已", "") }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="formLabel.notes">
          <el-input
            :placeholder="placeholder.notes"
            v-model="form.notes"
          ></el-input>
        </el-form-item>
      </el-form>
    </template>
  </custom-dialog>
</template>

<script>
import request from "@/utils/api";
import customDialog from "@/components/customDialog/index.vue";
export default {
  components: {
    customDialog,
  },
  props: {
    languagePackage: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      isShow: false,
      form: {
        name: "",
        name_en: "",
        fun_id: "",
        description: "",
        state: 10,
        notes: "",
      },
      rules: {
        name: [{ required: true, message: "请输入功能名称", trigger: "blur" }],
        name_en: [
          { required: true, message: "请输入功能英文名称", trigger: "blur" },
        ],
        fun_id: [{ required: true, message: "请输入功能ID", trigger: "blur" }],
        // description: [
        //   { required: true, message: "请输入功能描述", trigger: "blur" },
        // ],
      },
      saveLoading: false,
      type: "add",
    };
  },
  computed: {
    funState() {
      return this.$store.state.dict.funState;
    },
    title() {
      return this.type === "add"
        ? this.languagePackage.addTitle
        : this.languagePackage.editTitle;
    },
    formLabel() {
      return this.languagePackage.addFun.label;
    },
    placeholder() {
      return this.languagePackage.addFun.placeholder;
    },
    formVerify() {
      return this.languagePackage.addFun.verify;
    },
  },
  created() {
    this.rules.name[0].message = this.formVerify.name[0];
    this.rules.name_en[0].message = this.formVerify.name_en[0];
    this.rules.fun_id[0].message = this.formVerify.funID[0];
    // this.rules.description[0].message = this.formVerify.description[0];
  },
  methods: {
    cancel: function (item) {},
    openDialog: function (item) {
      if (item) {
        this.form = Object.assign(this.form, item);
        // this.form.id = "123";
        this.form.state = Number(item.state);
        this.type = "edit";
      }
      this.isShow = true;
    },
    saveFormInfo: function () {
      this.saveLoading = true;
      this.$refs.dictForm.validate((volid) => {
        if (volid) {
          let url = this.type === "edit" ? "funEdit" : "funAdd";
          let params = Object.assign({}, this.form);
          params.pmd = params.fun_id + params.name + params.state;
          request(url, params)
            .then((res) => {
              this.$emit("refresh", res);
              this.shutDialog();
            })
            .catch(() => {
              this.saveLoading = false;
            });
        } else {
          this.saveLoading = false;
        }
      });
    },
    shutDialog: function () {
      this.saveLoading = false;
      this.isShow = false;
      this.form = {
        name: "",
        name_en: "",
        description: "",
        state: 10,
        remark: "",
      };
      this.type = "add";
    },
  },
};
</script>