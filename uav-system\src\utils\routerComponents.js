export default {
    routeplan: () =>
        import ( /* webpackChunkName: 'routeplan' */ '@/views/routeplan/index.vue'),
    equipment: () =>
        import ( /* webpackChunkName: 'equipment' */ `@/views/equipment/index.vue`),
    totalResultList: () =>
        import ( /* webpackChunkName: 'totalResultList' */ `@/views/achievementAdmin/resultTotalList.vue`),
    achievementAdmin: () =>
        import ( /* webpackChunkName: 'achievementAdmin' */ `@/views/achievementAdmin/index.vue`),
    taskManage: () =>
        import ( /* webpackChunkName: 'taskManage' */ `@/views/taskManage/index.vue`),
    firmware: () =>
        import ( /* webpackChunkName: 'firmware' */ `@/views/firmware/index.vue`),
    deviceType: () =>
        import ( /* webpackChunkName: 'deviceType' */ `@/views/deviceType/index.vue`),
    teachingVideos: () =>
        import ( /* webpackChunkName: 'teachingVideos' */ `@/views/teachingVideos/index.vue`),
    dictData: () =>
        import ( /* webpackChunkName: 'dictData' */ `@/views/dictData/index.vue`),
    userManage: () =>
        import ( /* webpackChunkName: 'userManage' */ `@/views/userManage/index.vue`),
    teamManage: () =>
        import ( /* webpackChunkName: 'teamManage' */ `@/views/teamManage/index.vue`),
    coordination: () =>
        import ( /* webpackChunkName: 'coordination' */ `@/views/coordination/coordination.vue`),
    networkingItem: () =>
        import ( /* webpackChunkName: 'networkingItem' */ `@/views/coordination/networkingItem.vue`),
    editWork: () =>
        import ( /* webpackChunkName: 'editWork' */ `@/views/coordination/editWork.vue`),
    navigationEnvironment: () =>
        import ( /* webpackChunkName: 'navigationEnvironment' */ `@/views/navigationEnvironment/index.vue`),
    outLineMap: () =>
        import ( /* webpackChunkName: 'outLineMap' */ `@/views/outLineMap.vue`),
    googleMap: () =>
        import ( /* webpackChunkName: 'googleMap' */ `@/views/routeplan/googleMap.vue`)
}