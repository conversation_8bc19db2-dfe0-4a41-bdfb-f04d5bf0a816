{"name": "uav-system", "version": "1.0.0", "description": "A Vue.js project", "author": "C_H-B <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --open --hot --host 0.0.0.0 --inline --progress --watch --colors --profile --config build/webpack.dev.conf.js", "start": "npm run dev", "test": "webpack-dev-server --open --hot --host 0.0.0.0 --inline --progress --config build/webpack.test.conf.js", "build": "node build/build.js"}, "dependencies": {"@mapbox/togeojson": "^0.16.0", "axios": "^0.26.1", "babel-plugin-dynamic-import-node": "^2.3.3", "coordtransform": "^2.1.2", "d3-dsv": "^3.0.1", "echarts": "^5.3.1", "element-ui": "^2.15.6", "es6-promise": "^4.2.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "leaflet": "^1.9.4", "less": "^3.9.0", "less-loader": "^5.0.0", "photo-sphere-viewer": "^4.7.3", "vue": "^2.5.2", "vue-jsonp": "^2.0.0", "vuex": "^2.0.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-component": "^1.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-resources-loader": "^2.2.5", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-particles": "^1.0.9", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}