<template>
  <transition name="el-fade-in">
    <div class="flight-step" v-show="isShow">
      <div class="flight-step-title" v-if="flightType == 2">
        {{ language.altnlandReason
        }}{{ language.stepError[altnlandReason == 3 ? 1 : altnlandReason] }}
      </div>
      <!-- tab步骤 -->
      <tab-scroll
        :clickCut="false"
        :list="stepList"
        v-model="stepIndex"
        :upDown="false"
        :scollCode="true"
        style="height: 60px; padding: 0 20px; width: calc(100% - 40px)"
      >
        <template v-slot:content="scope">
          <div class="ul-li-icon">
            <div
              class="icon-square"
              v-if="scope.index === 0"
              :style="getStepColor(scope.index, scope.row)"
            ></div>
            <div
              class="icon-top"
              :style="getStepColor(scope.index, scope.row)"
            ></div>
            <div
              class="icon-bot"
              :style="getStepColor(scope.index, scope.row)"
            ></div>
          </div>
          <div class="li-text">
            <div class="li-text-content">{{ scope.row.title }}</div>
          </div>
        </template>
      </tab-scroll>

      <!-- 显示内容 -->
      <div class="body-content">
        <div class="error-info" v-show="showError">
          {{ language.errorHint }}
        </div>
        <div class="error-info" v-show="showError">
          {{ language.errorInfoTitle }}
          {{ language.errorStep[startOneKeyError] }}
        </div>
        <div class="error-info" v-show="interruptCode">
          {{ language.interruptHint }}
        </div>

        <div
          class="content-step"
          v-show="
            !showError && !interruptCode && !(flightType == 2 && showErrorCode)
          "
        >
          <!--  -->
          <div
            class="hint-text"
            v-show="stepIndex !== 5 || flightType == 1 || flightType == 2"
          >
            <div class="" v-show="!isSuspend">
              <span class="ml8 el-icon-loading"></span>
              {{ stepList[stepIndex] ? stepList[stepIndex].hintText : "" }}
            </div>
            <div class="" v-show="isSuspend">
              {{ language.suspendHint }}
            </div>

            <div
              class=""
              v-show="
                isShowRtkWaitTime &&
                flightType != 1 &&
                stepIndex < 7 &&
                !isSuspend
              "
            >
              {{ language.countDown }}：
              <span style="font-size: 24px">
                {{ takeOffTime }}
              </span>
              S
            </div>
            <div
              class=""
              v-show="flightType == 2 && stepIndex == 2 && !isSuspend"
            >
              {{ language.countDown1 }}：
              <span style="font-size: 24px">
                {{ formatS(countdownLoad) }}
              </span>
            </div>
          </div>

          <!--  -->
          <div
            class="progress"
            v-show="stepIndex === 5 && this.flightType === 0"
          >
            <el-progress
              :text-inside="true"
              :stroke-width="26"
              :percentage="waypointPercent"
            />
          </div>
        </div>

        <audio ref="audioTime" autoplay muted="muted" id="audioTime">
          <source />
        </audio>
      </div>

      <!-- 底部操作 -->
      <div class="footer" v-if="flightType == 0">
        <el-button
          type="primary"
          v-if="!showError && !interruptCode"
          @click="suspendContinue"
        >
          {{ isSuspend ? language.continue : language.pause }}
        </el-button>

        <!-- 终止 -->
        <!-- <el-button type="primary" @click="interruptCourse">
          {{ language.interrupt }}
        </el-button> -->

        <el-button v-if="showError || interruptCode" @click="isShow = false">{{
          language.close
        }}</el-button>
        <!-- <el-button  @click="isShow = false">关 闭</el-button> -->
      </div>
      <div class="footer" v-if="flightType == 2 && showErrorCode">
        <el-button @click="isShow = false">{{ language.close }}</el-button>
      </div>
    </div>
  </transition>
</template>
    
<script>
import tabScroll from "@/components/tabScroll/index.vue";
import Audio from "@/utils/audio";
export default {
  components: {
    tabScroll,
  },
  data() {
    return {
      isShow: false,
      stepIndex: 0,
      stepList: [],

      takeOff: [
        {
          value: 9,
          title: "装载飞机电池",
          hintText: "正在装载电池...",
          play: false,
        },
        { value: 2, title: "机巢开舱", hintText: "机巢开舱中...", play: false },
        {
          value: 3,
          title: "平台升起",
          hintText: "机巢平台升起中...",
          play: false,
        },
        {
          value: 4,
          title: "脚架释放",
          hintText: "机巢脚架释放中...",
          play: false,
        },
        {
          value: 0,
          title: "无人机开机",
          hintText: "无人机正在开机...",
          play: false,
        },
        {
          value: 1,
          title: "航线上传",
          hintText: "航线正在上传...",
          play: false,
        },
        {
          value: 5,
          title: "无人机自检",
          hintText: "无人机自检中...",
          play: false,
        },
        {
          value: 6,
          title: "无人机解锁",
          hintText: "无人机解锁中...",
          play: false,
        },
        { value: 7, title: "起飞", hintText: "无人机起飞中...", play: false },
        { value: 8, title: "机巢关舱", hintText: "机巢关舱中...", play: false },
      ],

      landingStep: [
        { value: 0, title: "返航", hintText: "无人机正在返航...", play: false },
        { value: 1, title: "开舱", hintText: "机巢开舱中...", play: false },
        {
          value: 2,
          title: "平台升起",
          hintText: "机巢平台升起中...",
          play: false,
        },
        {
          value: 3,
          title: "脚架释放",
          hintText: "机巢脚架释放中...",
          play: false,
        },
        {
          value: 4,
          title: "无人机降落",
          hintText: "无人机降落中...",
          play: false,
        },
        {
          value: 5,
          title: "脚架校正",
          hintText: "机巢脚架校正中...",
          play: false,
        },
        {
          value: 6,
          title: "平台下降",
          hintText: "机巢平台下降中...",
          play: false,
        },
        { value: 7, title: "关舱", hintText: "机巢关舱中...", play: false },
        {
          value: 8,
          title: "卸下电池",
          hintText: "卸下无人机电池...",
          play: false,
        },
      ],
      alternateSteps: [
        {
          value: 0,
          title: "上传备降点",
          hintText: "备降点上传中...",
          play: false,
        },
        {
          value: 1,
          title: "备降动作执行",
          hintText: "备降动作执行中...",
          play: false,
        },
        {
          value: 2,
          title: "等待复降",
          hintText: "等待复降中...",
          play: false,
        },
        {
          value: 3,
          title: "上传复降点",
          hintText: "复降点上传中...",
          play: false,
        },
        {
          value: 4,
          title: "无人机自检",
          hintText: "无人机自检中...",
          play: false,
        },
        { value: 5, title: "开舱", hintText: "机巢开舱中...", play: false },
        {
          value: 6,
          title: "平台升起",
          hintText: "机巢平台升起中...",
          play: false,
        },
        {
          value: 7,
          title: "脚架释放",
          hintText: "机巢脚架释放中...",
          play: false,
        },
        {
          value: 8,
          title: "无人机解锁",
          hintText: "无人机解锁中...",
          play: false,
        },
        { value: 9, title: "起飞", hintText: "无人机起飞中...", play: false },
        {
          value: 10,
          title: "前往复降点",
          hintText: "无人机前往复降点中...",
          play: false,
        },
        {
          value: 11,
          title: "脚架校正",
          hintText: "机巢脚架校正中...",
          play: false,
        },
        {
          value: 12,
          title: "平台下降",
          hintText: "机巢平台下降中...",
          play: false,
        },
        { value: 13, title: "关舱", hintText: "机巢关舱中...", play: false },
      ],
      alternateSteps1: [
        {
          value: 0,
          title: "上传备降点",
          hintText: "备降点上传中...",
          play: false,
        },
        {
          value: 1,
          title: "备降动作执行",
          hintText: "备降动作执行中...",
          play: false,
        },
      ],

      // 成功-正在进行-待进行-失败
      colorList: ["#67C23A", "#409EFF", "#909399", "#F56C6C"],
      showError: false, // 显示错误信息
      isSuspend: false, // 执行状态
      flightType: -1, // 飞行类型 0==起飞，1==返航
      isLoad: false,
      isShowRtkWaitTime: false,
      audio: null,
      takeOffTime: 10,
      waypointPercent: 0, // 航线上传进度
      interruptCode: false,
      rtkPlay: false, // rtk语音播报

      countDown: null, // 倒计时方法
      countDownPlay: false, // 倒计时播报
      countdownLoad: 0, //复降倒计时
      countdownLoad: "",
      showErrorCode: false,
      againLoad: false,
      changeLinkTipCode: false,
    };
  },
  computed: {
    staveTwoData: function () {
      return this.$store.state.equipment.staveTwoData;
    },
    // 当前步骤
    startOneKeyStep() {
      return this.staveTwoData.start_one_key_step;
    },
    // 发生错误
    startOneKeyError() {
      return this.staveTwoData.start_one_key_error;
    },
    // 脚手架打开
    nestCorrectOpen() {
      return this.staveTwoData.nest_correct_open;
    },
    // 脚手架收起
    nestCorrectClose() {
      return this.staveTwoData.nest_correct_close;
    },
    // 平台升起
    nestPlatformUp() {
      return this.staveTwoData.nest_platform_up;
    },
    // 平台下降
    nestPlatformDown() {
      return this.staveTwoData.nest_platform_down;
    },

    // 舱门打开
    nestDoorOpen() {
      return this.staveTwoData.nest_door_open;
    },
    // 舱门关闭
    nestDoorClose() {
      return this.staveTwoData.nest_door_close;
    },
    //装载电池
    nest_get_battery() {
      return this.staveTwoData.nest_get_battery;
    },
    //卸下电池
    nest_put_battery() {
      return this.staveTwoData.nest_put_battery;
    },
    // 航线上传进度
    // waypointPercent() {
    //   return this.$store.state.equipment.staveThreeData.waypoint_percent || 0;
    // },
    // 飞行状态
    flightStatus() {
      return this.$store.state.equipment.staveThreeData.flight_status;
    },
    // 无人机
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS || {};
    },
    rtkWaitTime() {
      return this.$store.state.equipment.staveThreeData.rtk_wait_time;
    },
    //
    rtkStatus() {
      return this.$store.state.equipment.staveThreeData.rtk_status;
    },
    language() {
      return this.$languagePackage.navigation.filghtStep;
    },
    //备降原因
    altnlandReason() {
      return this.staveTwoData.altnland_reason;
    },
    //备降步骤
    altnlandStep() {
      // console.log("备降步骤", this.staveTwoData.altnland_step);
      return this.staveTwoData.altnland_step;
    },
    //备降模式
    altnlandMode() {
      return this.staveTwoData.altnland_mode;
    },
    //备降结果
    altnlandResult() {
      // console.log("结果", this.staveTwoData.altnland_result);
      return this.staveTwoData.altnland_result;
    },
    //备降尝试次数
    // altnlandRetry() {
    //   return this.staveTwoData.altnland_retry;
    // },
    //备降错误
    altnlandError() {
      return this.staveTwoData.altnland_error;
    },
    flight_mode() {
      return this.$store.state.equipment.staveThreeData.flight_mode;
    },
    height() {
      return this.$store.state.equipment.staveThreeData.relative_altitude;
    },
    from_cno() {
      return this.$store.state.equipment.from_cno;
    },
  },
  watch: {
    staveTwoData: {
      deep: true,
      handler: function (item) {
        // console.log("步骤值--------------->", item)
      },
    },

    // 当前步骤，执行任务使用
    startOneKeyStep: function (val) {
      if (this.flightType !== 0 || !this.isShow) {
        return false;
      }

      switch (val) {
        case 0:
          this.stepIndex = -1;
          break;
        case 1:
          this.audioPlay(4, "无人机开机");
          break;
        case 2:
          this.audioPlay(5, "航线传输中");
          break;
        case 4:
          this.audioPlay(6, "无人机自检");
          break;
        case 5:
          if (!this.rtkPlay) {
            this.rtkPlay = true;
            this.stepIndex = 6;
            // this.audio.handleSpeak("RTK等待中");
          }

          // 进入倒计时
          if (this.rtkStatus == 6) {
            this.isShowRtkWaitTime = true;
            this.startCountDown();
          }

          break;
        case 6:
          this.audioPlay(7, "无人机解锁");
          break;
        case 7:
          this.audioPlay(8, "无人机起飞");

          this.isShowRtkWaitTime = false;
          this.$emit("cutMagnify");
          // 设置开启任务状态
          this.$store.commit("setFlightTask", true);
          break;
        case 8:
          this.stepIndex = 9;
          setTimeout(() => {
            this.isShow = false;
          }, 500);
          break;
        default:
          console.log("状态改变，不需要特殊处理", val);
          break;
      }
    },

    startOneKeyError: function (val) {
      if (val !== 0) {
        if (this.altnlandReason) {
          this.open(2);
        } else {
          this.showError = true;
          this.stepList[this.stepIndex] &&
            this.$set(this.stepList[this.stepIndex], "error", true);
        }
      } else {
        this.showError = false;
      }
    },

    //备降步骤
    altnlandStep: function (val) {
      if (this.flightType !== 2 || !this.isShow || this.showErrorCode) {
        return false;
      }
      switch (val) {
        case 0:
          // this.stepIndex = -1;
          break;
        case 1:
        case 2:
          if (this.altnlandMode == 1) {
            this.audioPlay(0, "备降点上传");
          } else {
            this.audioPlay(3, "上传复降点");
          }
          break;
        case 3:
          if (this.altnlandMode == 1) {
            this.audioPlay(1, "备降动作执行中");
          }
          break;
        case 4:
          this.audioPlay(2, "等待复降");
          this.countdownStart();
          break;
        case 5:
          // this.stepIndex = 4;
          break;
        case 6:
        case 7:
          this.audioPlay(4, "无人机自检");
          break;
        case 8:
          this.audioPlay(8, "无人机解锁");
          break;
        case 9:
          this.audioPlay(9, "无人机起飞");
          break;
        case 10:
          break;
        case 11:
          this.audioPlay(10, "无人机前往复降点");
          break;
        default:
          break;
      }
    },

    // 平台升起
    nestPlatformUp: function (val) {
      if (val == 3 && this.flightType == 1) {
        this.stepIndex = 2;
      } else if (val == 3 && this.flightType == 2) {
        if (
          this.altnlandResult !== 0 &&
          this.altnlandStep == 5 &&
          !this.showErrorCode &&
          !this.againLoad
        ) {
          this.stepIndex = 6;
        }
      } else if (
        val == 3 &&
        this.flightType == 0 &&
        this.startOneKeyStep == 3
      ) {
        this.stepIndex = 2;
      }
    },

    // 脚手架打开
    nestCorrectOpen: function (val) {
      if (val == 3 && this.flightType == 0 && this.startOneKeyStep == 3) {
        this.stepIndex = 3;
      }
      if (val == 3 && this.flightType == 2) {
        if (
          this.altnlandResult !== 0 &&
          this.altnlandStep == 5 &&
          !this.showErrorCode &&
          !this.againLoad
        ) {
          this.stepIndex = 7;
        }
      }

      if (val == 3 && this.flightType == 1 && !this.isLoad) {
        this.isLoad = true;
        this.stepIndex = 3;
      }
    },
    flightStatus: function (val) {
      if (val == 3 && this.flightType == 1) {
        this.stepIndex = 4;
      }
    },

    // 机巢仓门关闭
    nestDoorClose: function (val) {
      if (val == 3) {
        if (this.flightType == 1) {
          this.stepIndex = 7;
        } else if (this.flightType == 0) {
          this.stepIndex = 8;
        } else if (this.flightType == 2) {
          if (
            this.altnlandResult !== 0 &&
            this.altnlandStep == 10 &&
            !this.showErrorCode &&
            !this.againLoad
          ) {
            this.stepIndex = 13;
          }
        }
      }
      // else if (val == 1) {
      //   // if (this.startOneKeyStep == 8 && this.type != 12) {
      //   //   this.isShow = false;
      //   // }
      // }
    },
    // 机巢开仓
    nestDoorOpen: function (val) {
      if (val == 3 && this.flightType == 1) {
        this.stepIndex = 1;
      } else if (val == 3 && this.flightType == 2) {
        if (
          this.altnlandResult !== 0 &&
          this.altnlandStep == 5 &&
          !this.showErrorCode &&
          !this.againLoad
        ) {
          this.stepIndex = 5;
        }
      } else if (
        val == 3 &&
        this.flightType == 0 &&
        this.startOneKeyStep == 3
      ) {
        this.stepIndex = 1;
      }
    },
    nestPlatformDown: function (val) {
      if (val == 3 && this.flightType == 1) {
        this.stepIndex = 6;
      }
      if (val == 3 && this.flightType == 2) {
        if (
          this.altnlandResult !== 0 &&
          this.altnlandStep == 10 &&
          !this.showErrorCode &&
          !this.againLoad
        ) {
          this.stepIndex = 12;
        }
      }
    },
    nestCorrectClose: function (val) {
      if (val == 3 && this.flightType == 1) {
        this.stepIndex = 5;
      }
      if (val == 3 && this.flightType == 2) {
        if (
          this.altnlandResult !== 0 &&
          this.altnlandStep == 10 &&
          !this.showErrorCode &&
          !this.againLoad
        ) {
          this.stepIndex = 11;
        }
      }
    },
    //装载电池
    nest_get_battery(val) {
      if (val == 3 && this.flightType == 0) {
        this.stepIndex = 0;
      }
    },
    //卸下电池
    nest_put_battery(val) {
      if (this.flightType == 1) {
        if (val == 3) {
          this.stepIndex = 8;
        } else if (val == 1) {
          this.isShow = false;
        }
      }
    },

    isShow: function (val) {
      let item = null;
      if (val) {
        item = this.stepList[this.stepIndex];
      }
      this.$store.commit("setLaterTook", item);
    },

    rtkStatus: function (val) {
      if (val == 6 && this.startOneKeyStep == 5) {
        this.isShowRtkWaitTime = true;
        this.startCountDown();
      }
    },

    rtkWaitTime: function (val) {
      // this.rtkStatus == 6 && this.audio.handleSpeak(val);
    },

    stepIndex: function (val) {
      // 如果飞行步骤结束，则不提示
      if (!this.isShow) {
        return false;
      }
      // 如果已经播放，则不做任何操作
      if (this.stepList[val].play) {
        return false;
      }
      this.$set(this.stepList[val], "play", true);

      if (this.flightType == 1) {
        // 返航操作
        let valList = [0, 1, 2, 3, 4, 5, 6, 7];
        let n = valList.indexOf(val);
        if (n !== -1) {
          this.audio.handleSpeak(this.stepList[val].voice);
        }
        // if (val === 0) {
        //   this.audio.handleSpeak("任务执行完毕，开始返航");
        // } else if (val === 1) {
        //   this.audio.handleSpeak("机巢开舱");
        // } else if (val === 2) {
        //   this.audio.handleSpeak("平台升起");
        // } else if (val === 3) {
        //   this.audio.handleSpeak("脚架释放");
        // } else if (val === 4) {
        //   this.audio.handleSpeak("无人机降落");
        // } else if (val === 5) {
        //   this.audio.handleSpeak("无人机校正");
        // } else if (val === 6) {
        //   this.audio.handleSpeak("平台下降");
        // } else if (val === 7) {
        //   this.audio.handleSpeak("机巢关舱");
        // }
      } else if (this.flightType == 2) {
        if (!this.showErrorCode) {
          let valList2 = [5, 6, 7, 11, 12, 13];
          let n2 = valList2.indexOf(val);
          if (n2 !== -1) {
            this.audio.handleSpeak(this.stepList[val].voice);
          }
          //   if (val === 5) {
          //     this.audio.handleSpeak("机巢开舱");
          //     // } else if (val === 6) {
          //     //   this.audio.handleSpeak("平台升起");
          //   } else if (val === 6) {
          //     this.audio.handleSpeak("脚架释放");
          //   } else if (val === 11) {
          //     this.audio.handleSpeak("机巢关舱");
          //     // } else if (val === 12) {
          //     //   this.audio.handleSpeak("平台下降");
          //   } else if (val === 10) {
          //     this.audio.handleSpeak("无人机校正");
          //   }
        }
      } else {
        let valList1 = [0, 1, 2, 3];
        let n1 = valList1.indexOf(val);
        if (n1 !== -1) {
          this.audio.handleSpeak(this.stepList[val].voice);
        }
        // if (val === 0) {
        //   this.audio.handleSpeak("装载电池");
        // } else if (val === 1) {
        //   this.audio.handleSpeak("机巢开舱");
        // } else if (val === 2) {
        //   this.audio.handleSpeak("平台升起");
        // } else if (val === 3) {
        //   this.audio.handleSpeak("脚架释放");
        // }
      }
    },
    altnlandResult: function (val) {
      if (val == 3) {
        this.countdownLoad = this.staveTwoData.altnland_countdown - 1;
        if (this.againLoad) {
          this.isShow = false;
          // this.$message.success("备降完成")
        }
      }
      if (val == 6) {
        this.isShow = false;
        // this.$message.success("复降完成")
      } else if (val == 7) {
        // this.$message.success("复降失败");
      } else if (val == 8) {
        if (this.altnlandStep == 1) {
          this.open(2, true);
        }
      }
    },
    altnlandError: function (val) {
      if (val !== 0) {
        this.showErrorCode = true;
        this.stepList[this.stepIndex] &&
          this.$set(this.stepList[this.stepIndex], "error", true);
      } else {
        this.showErrorCode = false;
      }
    },
  },
  created() {
    this.audio = new Audio();

    this.languageDispose();

    this.$store.commit("setWsMmessageFun", {
      key: "flightStep",
      message: this.disposeData,
    });
    // this.open(0);
    // let index = 0;
    // let time = setInterval(() => {
    //   this.testAudio();
    //   index++;
    //   if (index >= 5) {
    //     clearInterval(time);
    //   }
    // }, 5000);
  },
  methods: {
    disposeData: function (msg_id, data) {
      if (msg_id == 434) {
        this.waypointPercent = data.waypoint_percent;
      }
      if (msg_id == 473) {
        if (this.isShow) {
          switch (data.control_cmd) {
            case 1:
              this.isSuspend = true;

              break;
            case 2:
              this.isSuspend = false;
              this.$message.success(this.language.process);
              break;
            case 3:
              this.interruptCode = true;
              this.stepList[this.stepIndex] &&
                this.$set(this.stepList[this.stepIndex], "error", true);

              break;

            default:
              break;
          }
        } else {
          let msg = "";
          if (data.control_cmd == 3) {
            msg = this.language.endProcess;
          } else if (data.control_cmd == 2) {
            msg = this.language.process1;
          } else if (data.control_cmd == 1) {
            msg = this.language.pauseProcess;
          }
          if (msg) {
            this.$message.success(msg);
          }
        }
      }
    },
    // 语音步骤播放
    audioPlay: function (index, text) {
      if (!this.stepList[index].play) {
        this.$set(this.stepList[index], "play", true);
        this.stepIndex = index;
        this.audio.handleSpeak(this.stepList[index].voice);
      }
    },

    // 测试语音步骤使用，勿删
    testAudio: function () {
      let data = {
        rtk_status: 6,
      };
      this.$store.commit("setStaveThreeData", data);
      let index = 0;
      let time = setInterval(() => {
        index++;

        if (index == 6) {
          clearInterval(time);
          return false;
        }

        let param = {
          start_one_key_step: index,
        };
        if (index == 3) {
          param.nest_door_open = 3;
        }

        this.$store.commit("setStaveTwoData", param);
      }, 3000);
      // this.open(0);
    },

    // language
    languageDispose: function () {
      let takeOffStep = this.language.takeOffStep;
      for (let i = 0; i < this.takeOff.length; i++) {
        let item = this.takeOff[i];
        this.takeOff[i] = Object.assign(item, takeOffStep[item.value]);
      }

      let landingStep = this.language.landingStep;
      for (let j = 0; j < this.landingStep.length; j++) {
        let item = this.landingStep[j];
        this.landingStep[j] = Object.assign(item, landingStep[j]);
      }

      let alternateSteps = this.language.alternateSteps;
      for (let n = 0; n < this.alternateSteps.length; n++) {
        let item = this.alternateSteps[n];
        this.alternateSteps[n] = Object.assign(item, alternateSteps[n]);
      }
      let alternateSteps1 = this.language.alternateSteps1;
      for (let m = 0; m < this.alternateSteps1.length; m++) {
        let item = this.alternateSteps1[m];
        this.alternateSteps1[m] = Object.assign(item, alternateSteps1[m]);
      }
    },

    // 开启10秒倒计时
    startCountDown: function () {
      // 保证每一个任务只触发一次倒计时
      if (this.countDownPlay) {
        return false;
      }

      if (this.countDown && this.takeOffTime > 0) {
        return;
      }

      this.countDownPlay = true;
      this.takeOffTime = 10;
      // 第一次时需要清除前面播放的
      this.audio.handleSpeak(this.takeOffTime, { rate: 2, eliminate: true });

      this.countDown = setInterval(() => {
        this.takeOffTime--;
        if (this.takeOffTime == 0) {
          this.takeOffTime = 1;
          clearInterval(this.countDown);
          this.countDown = null;
          return;
        } else {
          this.audio.handleSpeak(this.takeOffTime, { rate: 2 });
        }
      }, 1000);
    },
    //复降倒计时
    countdownStart: function () {
      if (this.countdownLoop) {
        return false;
      }
      this.countdownLoop = setInterval(() => {
        this.countdownLoad--;
        if (this.countdownLoad == 0) {
          clearInterval(this.countdownLoop);
        }
      }, 1000);
    },
    formatS(num) {
      if (num > 0) {
        let min = parseInt(num / 60);
        let s = num - min * 60;
        return (min > 9 ? min : "0" + min) + ":" + (s > 9 ? s : "0" + s);
      } else {
        return "00:00";
      }
    },

    /**
     * 打开对话框，两种模式，开始、返航
     * @param type 0==开始，1==返航
     */
    open: function (type, code) {
      this.interruptCode = false;
      this.isShow = true;
      this.flightType = type;
      this.takeOffTime = 10;
      this.waypointPercent = 0;

      this.countDownPlay = false;
      this.rtkPlay = false;
      if (type == 1) {
        this.stepList = this.landingStep;
        this.stepIndex = 0;
      } else if (type == 2) {
        if (this.altnlandReason == 2 || code) {
          this.stepList = this.alternateSteps1;
          this.againLoad = true;
        } else {
          this.stepList = this.alternateSteps;
          this.againLoad = false;
        }
        this.stepIndex = 0;
        this.changeLinkTipCode = false;
      } else {
        this.stepList = this.takeOff;
      }

      // 设置语音播放状态为false
      this.stepList.forEach((element) => {
        element.play = false;
      });
      if (type == 2) {
        this.linkTipFun();
      }
    },
    linkTipFun() {
      console.log("执行中");
      if (
        !this.changeLinkTipCode &&
        this.from_cno &&
        this.from_cno.substr(1, 1) != 0
      ) {
        this.changeLinkTipCode = true;
        this.$confirm(
          this.language.changeLinkText,
          this.language.changeLinkTip,
          {
            confirmButtonText: this.language.changeLinkSubmit,
            type: "warning",
            showCancelButton: false,
          }
        ).then(() => {
          this.$store.commit("setFormCor", 10);
          this.equipmentWS.manualSend({}, 206);
        });
      }
    },
    getStepColor: function (index, item) {
      let color = "#909399";
      if (index < this.stepIndex) {
        color = "#67C23A";
      } else if (index == this.stepIndex) {
        color = item.error && this.showError ? "#F56C6C" : "#409EFF";
      }
      return {
        backgroundColor: color,
      };
    },
    // 暂停继续
    suspendContinue: function () {
      // this.isSuspend = !this.isSuspend;
      // let msg_id = this.isSuspend ? 470 : 471;
      this.equipmentWS &&
        this.equipmentWS.manualSend &&
        this.equipmentWS.manualSend(
          {
            control_cmd: !this.isSuspend ? 1 : 2,
          },
          473
        );
    },
    interruptCourse: function () {
      this.equipmentWS &&
        this.equipmentWS.manualSend &&
        this.equipmentWS.manualSend({ control_cmd: 3 }, 473);
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .flight-step {
    width: @radio * 1000px !important;
    top: @radio * 100px !important;
    margin-left: @radio * -500px !important;
    .body-content {
      padding: @radio * 20px !important;
      min-height: @radio * 50px !important;
      .error-info {
        font-size: @radio * 18px !important;
      }
      .content-step {
        .hint-text {
          font-size: 18px !important;
        }
      }
    }
    .body-ul {
      padding: 0 @radio * 20px !important;
      height: @radio * 60px !important;

      li {
        padding: 0 @radio * 10px !important;

        .li-text {
          font-size: @radio * 14px !important;
        }

        .ul-li-icon {
          .icon-square {
            left: @radio * -8px !important;
            height: @radio * 30px !important;
            width: @radio * 30px !important;
          }
          .icon-top,
          .icon-bot {
            height: @radio * 15px !important;
            width: @radio * 140px !important;
          }
        }
      }
    }

    .li-text {
      font-size: @radio * 14px !important;
    }

    .ul-li-icon {
      .icon-square {
        left: @radio * -8px !important;
        height: @radio * 30px !important;
        width: @radio * 30px !important;
      }
      .icon-top,
      .icon-bot {
        height: @radio * 15px !important;
        width: @radio * 140px !important;
      }
    }

    .footer {
      padding: 0 0 @radio * 20px 0 !important;
    }
  }
}

.flight-step {
  width: 1000px;
  position: fixed;
  top: 100px;
  left: 50%;
  margin-left: -500px;
  // background-color: rgba(0, 0, 0, 0.8);
  z-index: 200;
  .flight-step-title {
    margin: 10px 20px 0;
  }
  .body-content {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    .error-info {
      // color: red;
      font-size: 18px;
    }
    .content-step {
      width: 100%;
      .hint-text {
        width: 100%;
        font-size: 18px;
        text-align: center;
        // color: #409eff;
      }
    }
    .progress {
      width: 100%;
      .el-progress-bar__outer {
        // background-color: #ccc !important;
      }
    }
  }
  .body-ul {
    padding: 0 20px;
    margin: 0;
    height: 60px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    li {
      list-style: none;
      padding: 0 10px;
      position: relative;
      // height: 40px;
      .li-text {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: 0;
        top: 0;
        // font-weight: 700;
        font-size: 14px;
        // color: #409eff;
      }

      .ul-li-icon {
        width: 100%;
        position: relative;
        .icon-square {
          position: absolute;
          left: -8px;
          top: 0;
          height: 30px;
          width: 30px;
          // background-color: rgb(121, 187, 255);
        }
        .icon-top,
        .icon-bot {
          height: 15px;
          width: 140px;
          // background-color: rgb(121, 187, 255);
        }
        .icon-top {
          transform: skew(50deg);
        }
        .icon-bot {
          transform: skew(-50deg);
        }
      }
    }
  }

  .li-text {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    top: 0;
    // font-weight: 700;
    font-size: 14px;
    // color: #fff;
    .li-text-content {
      white-space: break-spaces;
      text-align: center;
    }
  }

  .ul-li-icon {
    width: 100%;
    position: relative;
    .icon-square {
      position: absolute;
      left: -8px;
      top: 0;
      height: 30px;
      width: 30px;
      // background-color: rgb(121, 187, 255);
    }
    .icon-top,
    .icon-bot {
      height: 15px;
      width: 140px;
      // background-color: rgb(121, 187, 255);
    }
    .icon-top {
      transform: skew(50deg);
    }
    .icon-bot {
      transform: skew(-50deg);
    }
  }

  .footer {
    padding: 0 0 20px 0;
    text-align: center;
  }
}
</style>