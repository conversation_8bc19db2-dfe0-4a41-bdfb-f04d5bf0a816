<!-- 无人机设备列表 -->
<template>
  <div id="uav-equipment-list">
    <bg-layout :title="language.title">
      <template v-slot:content>
        <div class="checkbox">
          <div
            class="checkbox-item"
            @click="cutType(item)"
            v-for="(item, index) in typeList"
            :key="index"
          >
            <div class="checkbox-item-main">
              <div class="item-radio">
                <!-- 选中 -->
                <div class="select-circle" v-if="params.type === item.value">
                  <div class="interior-circle"></div>
                </div>

                <!-- 未选中 -->
                <div class="circle" v-else></div>
              </div>
              <div
                class="item-label"
                :style="{
                  color:
                    params.type === item.value ? '#fff' : 'rgb(113, 117, 141)',
                  letterSpacing: titleLetterSpacing
                }"
              >
                {{ item.label }}
              </div>
            </div>
            <img
              src="../../../assets/img/home/<USER>"
              alt=""
              v-if="params.type === item.value"
            />
          </div>
        </div>

        <!-- 表格数据 deviceList -->
        <div class="table-data">
          <scroll-list
            ref="scrollList"
            class="table-tbody scrollbar-style"
            pmd="page,type"
            urlName="deviceList"
            :params="params"
            :pageSize="200"
            @requestSuccess="requestSuccess"
          >
            <template v-slot:content="scope">
              <div
                class="table-tbody-tr"
                :style="tableTrStyle(scope, index)"
                style="width: 100%; display: flex; margin-bottom: 5px"
                v-for="(item, index) in scope.data"
                :key="index"
                @click="cutEquipment(item, index)"
              >
                <div style="width: 28px" class="tbody-cell">
                  <div class="table-index">
                    {{ index + 1 }}
                  </div>
                </div>
                <div
                  style="
                    width: 60%;
                    color: rgb(36, 191, 223);
                    letter-spacing: 2px;
                  "
                  class="tbody-cell"
                >
                  {{ item.name }}
                </div>
                <div
                  class="tbody-cell"
                  style="width: 20%"
                  :style="{ color: iconList[item.stateCode].color }"
                >
                  {{ iconList[item.stateCode].title }}
                </div>
                <!-- <div style="width: 20%" class="tbody-cell">3个机巢</div>
                <div style="width: 10%" class="tbody-cell">
                  <i class="el-icon-arrow-up"></i>
                </div> -->
              </div>
            </template>
          </scroll-list>
        </div>
      </template>
    </bg-layout>
  </div>
</template>

<script>
import bgLayout from "../components/bgLayout.vue";
import scrollList from "@/components/scrollList/index.vue";
export default {
  components: {
    bgLayout,
    scrollList
  },
  data() {
    return {
      typeList: [
        { label: "自动机场", value: 0, key: "AutomaticAirport" },
        { label: "单兵无人机", value: 200, key: "solos" }
      ],
      params: {
        type: 0,
        baseUrl: true
      },
      isInit: false,
      iconList: {
        0: {
          icon: require("@/assets/img/outLineHome.png"),
          title: "离线",
          color: "red"
        },
        1: {
          icon: require("@/assets/img/inLineHome.png"),
          title: "在线",
          color: "rgb(255, 172, 77)"
        },
        2: {
          icon: require("@/assets/img/workingHome.png"),
          title: "有人连接",
          color: "rgb(48, 155, 42)"
        }
      }
    };
  },
  computed: {
    language() {
      return this.$languagePackage.home.uavList;
    },
    titleLetterSpacing() {
      let type = this.$language;
      return type == "chinese" ? "3px" : 0;
    }
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      let tab = this.language.tab;
      for (let i = 0; i < this.typeList.length; i++) {
        let k = this.typeList[i].key;
        this.typeList[i].label = tab[k];
      }

      let state = this.language.state;
      this.iconList[0].title = state[2];
      this.iconList[1].title = state[1];
      this.iconList[2].title = state[3];
    },
    cutType: function(item) {
      if (this.params.type == item.value) {
        return false;
      }
      this.params.type = item.value;
      this.isInit = true;
      this.$refs.scrollList && this.$refs.scrollList.refresh();
    },
    tableTrStyle: function(scope, index) {
      let item = scope.data[index];
      return {
        animationDelay:
          (scope.current !== 1 || this.isInit ? 0 : 1) + item.delayed + "s"
      };
    },
    cutEquipment: function(item, index) {
      if (this.params.type == 0) {
        this.$emit("cutEquipment", item, index);
      }
    },
    uavState: function(item) {
      let text = "";
      let color = "";
      let state = this.language.state;
      if (item.is_push_on) {
        text = state[1];
        color = "rgb(255, 172, 77)";
      } else {
        text = state[2];
        color = "red";
      }

      if (item.is_pull_on) {
        text = state[3];
        color = "rgb(48, 155, 42)";
      }

      return {
        text,
        color
      };
    },
    requestSuccess: function(data) {
      console.log("请求成功------->", data);
      // this.cutEquipment(data.list[0], 0);
      for (let i = 0; i < data.list.length; i++) {
        let item = data.list[i];
        item.stateCode = 0;
        this.$set(item, "stateCode", 0);
        if (item.is_pull_on) {
          item.stateCode = 2;
        } else if (item.is_push_on) {
          item.stateCode = 1;
        }
      }
    },
    refreshList() {
      this.$refs.scrollList && this.$refs.scrollList.refresh();
    }
  }
};
</script>

<style lang="less" scoped>
#uav-equipment-list {
  .checkbox {
    margin: 0 5px 10px 5px;
    display: flex;
    font-size: 12px;

    .checkbox-item {
      cursor: pointer;
      //   padding: 8px;
      position: relative;
      width: 50%;
      height: 50px;

      img {
        width: 100%;
        height: 100%;
      }

      .checkbox-item-main {
        width: 100%;
        height: 100%;
        padding: 0px 20px;
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        align-items: center;
      }
      .item-radio {
        margin-right: 8px;
        .circle {
          width: 13px;
          height: 13px;
          border-radius: 50%;
          // border: 2px solid rgb(44, 90, 145);
        }
        .select-circle {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          border: none;
          // background-color: rgb(0, 255, 0);
          display: flex;
          align-items: center;
          justify-content: center;
          .interior-circle {
            width: 7px;
            height: 7px;
            // background-color: rgb(34, 172, 56);
            border-radius: 50%;
          }
        }
        cursor: pointer;
      }
      .item-label {
        font-size: 14px;
        font-weight: 700;
        letter-spacing: 3px;
      }
    }
  }

  .table-data {
    height: calc(100% - 115px);
    position: relative;
    margin: 10px;
    .table-tbody {
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
    }
    .table-tbody-tr {
      // border: 1px solid #2f6099;
      border-radius: 5px;
      // background-image: linear-gradient(to bottom, #0c2b67, #0c1f3c, #0c203a);
      animation-name: tbodyTr;
      animation-duration: 0.4s;
      animation-fill-mode: forwards; // 保留动画最后的状态
      opacity: 0;
      transform: rotateX(90deg);
    }
    @keyframes tbodyTr {
      0% {
        transform: rotateX(90deg);
      }
      50% {
        opacity: 1;
      }
      100% {
        transform: rotateX(0deg);
        opacity: 1;
      }
    }

    .tbody-cell {
      padding: 8px;
      font-size: 12px;
      // color: #fff;
      align-items: center;
      .table-index {
        width: 20px;
        height: 20px;
        // border: 1px solid rgb(20, 57, 90);
        text-align: center;
        line-height: 20px;
        border-radius: 5px;
        // color: #fff;
      }
    }
  }
}
</style>
