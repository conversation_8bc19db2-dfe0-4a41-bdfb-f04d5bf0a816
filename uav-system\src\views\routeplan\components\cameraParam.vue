<template>
  <div class="cameraParam">
    <div class="headerParam">
      <div class="param-title1"></div>
      <div class="param-title">{{ cameraParams.width }}</div>
      <div class="param-title">{{ cameraParams.height }}</div>
    </div>
    <div class="contentParam" v-for="item in paramTable" :key="item.id">
      <div class="param-title1">{{ item.name }}</div>
      <div class="param-title">
        <el-input
          v-model="item.width"
          v-if="item.id != 'focalLength'"
          @input="valueFormat(item, 0)"
          @change="changValue(item, 0)"
        >
          <template slot="suffix" v-if="item.id != 'image'">mm </template>
          <template slot="suffix" v-if="item.id == 'image'">px </template>
        </el-input>
        <div class="param-title" v-if="item.id == 'focalLength'">
          {{ item.width }}
        </div>
      </div>
      <div class="param-title">
        <el-input
          v-model="item.height"
          @input="valueFormat(item, 1)"
          @change="changValue(item, 1)"
        >
          <template slot="suffix" v-if="item.id != 'image'">mm </template>
          <template slot="suffix" v-if="item.id == 'image'">px </template>
        </el-input>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "cameraParam",
  props: {
    cameraParamList: {
      type: [Array, String],
      default: () => [],
    },
    cameraParams: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      paramTable: [],
    };
  },
  mounted() {
    this.paramTable = this.cameraParamList;
  },
  methods: {
    valueFormat(val, code) {
      let temp = "";
      if (code == 0) {
        temp = val.width.toString();
      } else {
        temp = val.height.toString();
      }
      temp = temp.replace(/。/g, ".");
      temp = temp.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
      temp = temp.replace(/^\./g, ""); //验证第一个字符是数字
      temp = temp.replace(/\.{2,}/g, ""); //只保留第一个, 清除多余的
      temp = temp.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      temp = temp.replace(/^(\-)*(\d+)\.(\d\d).*$/, "$1$2.$3"); //只能输入两个小数

      let index = this.paramTable.findIndex((item) => {
        return item.id == val.id;
      });
      if (code == 0) {
        this.paramTable[index].width = temp;
      } else {
        this.paramTable[index].height = temp;
      }
    },
    changValue(val, code) {
      let index = this.paramTable.findIndex((item) => {
        return item.id == val.id;
      });
      if (code == 0) {
        if (!val.width) {
          val.width = 0;
        }
        this.paramTable[index].width = (
          parseFloat(val.width) + 0.000001
        ).toFixed(2);
      } else {
        if (!val.height) {
          val.height = 0;
        }
        this.paramTable[index].height = (
          parseFloat(val.height) + 0.000001
        ).toFixed(2);
      }
      this.$emit("cameraParams", this.paramTable);
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .cameraParam {
    .headerParam,
    .contentParam {
      font-size: @zoomIndex * 14px !important;
    }
  }
}
.cameraParam {
  width: 98%;
  margin-bottom: 2%;
  .headerParam,
  .contentParam {
    width: 100%;
    display: flex;
    font-size: 14px;
    .param-title1 {
      flex: 4;
    }
    .param-title {
      flex: 3;
      text-align: center;
      .el-input {
        width: 80%;
      }
    }
  }
}
</style>
<style lang="less">
.cameraParam {
  .contentParam {
    margin-bottom: 2px;
    .param-title {
      .el-input {
        .el-input__inner {
          height: auto !important;
          line-height: 0 !important;
          padding: 1px !important;
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .cameraParam {
    .contentParam {
      margin-bottom: @zoomIndex * 2px !important;
      .param-title {
        .el-input {
          .el-input__inner {
            padding: @zoomIndex * 1px !important;
          }
        }
      }
    }
  }
}
</style>