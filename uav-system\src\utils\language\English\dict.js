const dict = {
    funState: {
        10: "Normal",
        20: "Deleted"
    },
    alarmLevel: {
        30: "Tall",
        20: "Centre",
        10: "Low"
    },
    processState: {
        10: "Pending",
        20: "Processed"
    },

    checkingType: {
        10: "Time",
        20: "Mileage",
        30: "Results"
    },

    achievementType: {
        10: "Image",
        20: "Video",
        15: "Panorama"
    },

    //
    defoggingType: {
        0: "Closed",
        1: "Common",
        2: "Auto"
    },

    // 网格类型
    gridType: {
        1: "Grid line",
        2: "Centre",
        3: "The grid center"
    },

    // 飞行模式
    flightModeList: {
        // 2: "Pose mode",
        2: "Model error",
        3: "Automatic mode",
        4: "Follow mode",
        5: "GPS mode",
        6: "Return mode",
        9: "Landing mode",
        13: "Sport mode",
    },

    // rtk状态
    rtkStatus: {
        0: "Not connected",
        1: "Not located",
        2: "Single point positioning",
        3: 'Floating solution',
        4: "Fixed solution"
    },

    // 拍照模式
    pictureMode: {
        1: "Delay",
        2: "Fps",
        3: "Selfie",
        4: "Delay"
    },

    // 语言
    languageList: {
        chinese: "Chinese",
        english: "English"
    },

    // 主题
    themeList: {
        default: "Default",
        concise: "Concise"
    },

    careraPcolor: {
        0: "White glow",
        1: "Lava",
        2: "Rouge",
        3: "Hot red",
        4: "Amber 1",
        5: "Amber 2",
        6: "RYB",
        7: "Black red",
        8: "Red 1",
        9: "Red 2",
    },

    whiteBalance: {
        0: "Automatic",
        1: "Candlelight",
        2: "Tungsten lamp",
        3: "Fluorescent lamp",
        4: "Sunlight",
        5: "Cloudy",
        6: "Overcast",
        7: "Azure",
        8: "Smoke",
    }

}

export default dict;