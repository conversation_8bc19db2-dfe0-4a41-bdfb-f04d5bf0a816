const camers = {
    state: {
        showComponents: '', // 默认显示第一个
        hierarchy: 1, // 默认显示第一级
        moreInsideType: '', // 更多内页显示类型
        openCameraState: false, // 打开相机设置状态
        formatLoading: false,

        defoggingType: '', // 去雾类型
        gridTypeVal: '3', // 网格类型值
        flickerVal: '', // 抗闪烁值

        sdcardTotalsize: "", // sd卡总容量
        cameraIso: "", // 相机iso值
        cameraShutter: "", // 相机快门值
        cameraExpMode: "", // 相机曝光模式
        cameraAwb: "", // 相机白平衡
        cameraExpVaule: "", // 相机曝光值
    },
    mutations: {
        setShowComponents(state, val) {
            state.showComponents = val;
        },
        setHierarchy(state, val) {
            state.hierarchy = val;
        },
        setMoreInsideType(state, val) {
            state.moreInsideType = val;
        },
        setDefoggingType(state, val) {
            state.defoggingType = val;
        },
        setFlickerVal(state, val) {
            state.flickerVal = val;
        },
        setGridTypeVal(state, val) {
            state.gridTypeVal = val;
        },
        setOpenCameraState(state, val) {
            state.openCameraState = val;
        },
        // 设置相机默认参数
        setCameraDefault(state, val) {
            state.sdcardTotalsize = val.sdcard_totalsize; // sd卡总容量
            state.cameraIso = val.camera_iso; // 相机iso值
            state.cameraShutter = val.camera_shutter; // 相机快门值
            state.cameraExpMode = val.camera_exp_mode; // 相机曝光模式
            state.cameraExpVaule = val.camera_exp_value, // 相机曝光值
                state.cameraAwb = val.camera_awb; // 相机白平衡
        },
        setFormatLoading(state, val) {
            state.formatLoading = val
        }
    },
    actions: {}
}

export default camers;