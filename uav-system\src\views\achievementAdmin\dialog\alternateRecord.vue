<template>
  <el-dialog
    :append-to-body="true"
    :visible.sync="isShow"
    width="1100px"
    :show-close="true"
    :destroy-on-close="true"
    :title="label.title"
    :center="true"
    custom-class="alternate-record"
  >
    <custom-table
      :column="recordList"
      :data="tableTable"
      :isShowPage="false"
      size="mini"
      ref="table"
    >
    <template v-slot:reason="scope">
        {{altnReason[scope.row.reason]}}
    </template>
    <template v-slot:solution="scope">
        {{altnSolution[scope.row.solution]}}
    </template>
    <template v-slot:timestamp="scope">
        {{formatTime(scope.row.timestamp)}}
    </template>
    <template v-slot:drone_int="scope">
        <div class="record-item">
            {{label.lon}}{{scope.row.drone_lon_int/1e7}}
        </div>
        <div class="record-item">
           {{label.lat}}{{scope.row.drone_lat_int/1e7}}
        </div>
    </template>
    <template v-slot:altn_int="scope">
        <div class="record-item">
            {{label.lon}}{{scope.row.altn_lon_int/1e7}}
        </div>
        <div class="record-item">
            {{label.lat}}{{scope.row.altn_lat_int/1e7}}
        </div>
    </template>
    <template v-slot:place_int="scope">
        <div class="record-item">
            {{label.lon}}{{scope.row.place_lon_int/1e7}}
        </div>
        <div class="record-item">
           {{label.lat}}{{scope.row.place_lat_int/1e7}}
        </div>
    </template>
    </custom-table>
  </el-dialog>
</template>
<script>
import customTable from "@/components/customTable/index.vue";
export default {
    components:{
        customTable
    },
  data() {
    return {
      isShow: false,
      tableTable:[],
    };
  },
  computed: {
    recordList() {
      return this.$languagePackage.achievement.flightLog.recordList;
    },
    altnReason() {
      return this.$languagePackage.achievement.flightLog.altnReason;
    },
    altnSolution() {
      return this.$languagePackage.achievement.flightLog.altnSolution;
    },
    label() {
      return this.$languagePackage.achievement.flightLog.label;
    },
  },
  methods: {
    open(e) {
        this.tableTable=e
      this.isShow = true;
    },
    formatTime(time){ 
      let date=new Date(time)
      let y=date.getFullYear() + '-';
      let month=(date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
      let day=(date.getDate()<10?'0'+date.getDate():date.getDate()) + ' ';
      let h = (date.getHours()<10?'0'+date.getHours():date.getHours())+ ':';
      let m = (date.getMinutes()<10?'0'+date.getMinutes():date.getMinutes()) + ':';
      let s = (date.getSeconds()<10?'0'+date.getSeconds():date.getSeconds()); 
      return y+month+day+h+m+s
    }
  },
};
</script>
<style lang="less">
.alternate-record {
    .el-table{
       height: 500px !important;
        th,td{
            border-bottom: 1px solid rgb(233, 233, 233) !important;

        }
       .record-item{
        text-align: left;
       }
    }
  .el-dialog__header {
    border-bottom: 1px solid rgba(40, 54, 179, 0.774);
  }
}
</style>
