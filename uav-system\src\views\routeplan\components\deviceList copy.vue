<template>
  <div class="deviceList">
    <el-dialog
      title="选择执行的任务的设备"
      :visible.sync="chooseDevice"
      :close-on-click-modal="true"
      :show-close="false"
      width="50%"
      @close="closeEvent"
    >
      <div class="contentDiv">
        <div class="check-group">
          <el-button
            v-for="item in deviceList"
            :key="item.sn_id"
            @click="changeDevice(item)"
            :class="checked == item.sn_id ? 'active' : ''"
          >
            <el-image :src="imgSrc[item.type]"></el-image>
            <div class="nameDiv">{{ item.name }}</div>
            <div class="idDiv">ID : {{ item.sn_id }}</div>
          </el-button>
        </div>
        <div class="entryDevice" v-if="deviceList.length > 0 ? false : true">
          暂无在线设备
        </div>
      </div>
      <div class="pageDiv">
        <el-pagination
          background
          layout="prev, pager, next"
          :pager-count="5"
          :page-size="6"
          :total="total_page"
          prev-text="上一页"
          next-text="下一页"
          @current-change="changePage"
          v-show="deviceList.length > 0 ? true : false"
        >
        </el-pagination>
      </div>
      <div class="btnDiv">
        <el-button class="sureBtn" @click="sureToplan">确定</el-button>
        <el-button class="closeBtn" @click="closeToplan">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "../../../utils/api";
export default {
  name: "deviceList",
  props: {
    planCode: {
      type: [Number, String],
      default: "",
    },
  },
  data() {
    return {
      chooseDevice: false,
      deviceList_total: [],
      deviceList: [],
      total_page: 0,
      closeCode: false,
      clickCode: true,
      websocket: "",
      deviceItem: "",
      fenceItem: "",
      checked: "",
      options: [
        {
          label: "悬停",
          value: 10,
        },
        {
          label: "返航",
          value: 20,
        },
        {
          label: "自动降落",
          value: 30,
        },
        {
          label: "回到第一个航点",
          value: 40,
        },
        {
          label: "盘旋",
          value: 50,
        },
      ],
      names: [
        { name: "无人机固定机巢", sn_id: "WK—GD0001" },
        { name: "移动车载机巢", sn_id: "WK—YD0001" },
        { name: "消防无人机指挥车 ", sn_id: "WK—XF0001" },
        { name: "无人机R1000", sn_id: "ID—R0001" },
      ],
      // imgSrc: [
      //   require("../../../assets/img/equipment/AC50.jpg"),
      //   require("../../../assets/img/equipment/move.png"),
      //   require("../../../assets/img/equipment/fireControl.png"),
      //   require("../../../assets/img/equipment/plane.png"),
      // ],
      imgSrc: {
        10: require("../../../assets/img/equipment/AC50.jpg"),
        12: require("../../../assets/img/equipment/AC100.jpg"),
        50: require("../../../assets/img/equipment/AC50MINI.jpg"),
        100: require("../../../assets/img/equipment/move.jpg"),
        500: require("../../../assets/img/equipment/R500.jpg"),
        1000: require("../../../assets/img/equipment/R1000.jpg"),
        1800: require("../../../assets/img/equipment/1800.jpg"),
        1900: require("../../../assets/img/equipment/1900.jpg"),
        2000: require("../../../assets/img/equipment/1900P.jpg"),
      },
    };
  },
  methods: {
    //获取在线设备列表数据
    // getDeviceData() {
    //   requestHttp("deviceOnline").then((res) => {
    //     this.deviceList_total = res.data.list;
    //     this.total_page=this.deviceList_total.length
    //     this.pageData(1)
    //   });
    // },
    //获取设备列表
    getDeviceData() {
      let data = {
        page: 0,
        size: 200,
        type: 0,
      };
      data.pmd = data.page.toString() + data.type.toString();
      requestHttp("deviceList", data).then((res) => {
        let deviceList_total = res.data.list ? res.data.list : [];
        this.deviceList_total = [];
        console.log("---->", deviceList_total);
        for (let index = 0; index < deviceList_total.length; index++) {
          if (
            deviceList_total[index].is_push_on &&
            !deviceList_total[index].is_pull_on
          ) {
            this.deviceList_total.push(deviceList_total[index]);
          }
          // this.deviceList_total.push(deviceList_total[index]);
        }
        this.total_page = this.deviceList_total.length;
        this.pageData(1);
      });
    },
    //点击选择设备
    changeDevice(item) {
      this.checked = item.sn_id;
    },
    //打开选择
    openChoose() {
      this.chooseDevice = true;
      this.deviceItem = "";
      this.getDeviceData();
    },
    //关闭选择
    closeEvent() {
      this.checked = [];
      if (!this.closeCode) {
        this.$message.info("已取消执行任务！");
      }
      this.closeCode = false;
      this.$emit("update:planCode", 0);
    },
    //取消按钮
    closeToplan() {
      this.chooseDevice = false;
      this.closeCode = true;
      this.checked = "";
      this.$message.info("已取消执行任务！");
      this.$emit("update:planCode", 0);
    },
    //确定按钮
    sureToplan() {
      if (this.clickCode) {
        this.clickCode = false;
        if (this.checked) {
          this.deviceItem = "";
          for (let index = 0; index < this.deviceList.length; index++) {
            if (this.deviceList[index].sn_id == this.checked) {
              this.deviceItem = this.deviceList[index];
            }
          }
          let fence = this.$store.state.route.fence;
          let f_id = this.$store.state.route.routeItem.f_id;
          this.fenceItem = "";
          for (let index = 0; index < fence.length; index++) {
            if (fence[index].f_id == f_id) {
              this.fenceItem = fence[index];
            }
          }
          // this.websocket.manualClone();
          this.chooseDevice = false;
          this.closeCode = true;
          this.checked = "";
          this.$emit("update:planCode", 0);
          this.toNext();
        } else {
          this.$message.warning("未选择执行任务的设备");
        }
        setTimeout(() => {
          this.clickCode = true;
        }, 400);
      }
    },
    //上传航线后跳转
    toNext() {
      let f_id = this.$store.state.route.routeItem.f_id;
      let m_id = this.$store.state.route.routeItem.m_id;
      let flightParam = sessionStorage.getItem("flightParam");
      if (flightParam) {
        flightParam = JSON.parse(flightParam);
        let i = flightParam.fence.findIndex((item) => item.f_id == f_id);
        if (i > -1) {
          flightParam.fence[i] = this.fenceItem;
        } else {
          flightParam.fence.push(this.fenceItem);
        }
        let j = flightParam.route.findIndex((item) => item.m_id == m_id);
        if (j > -1) {
          flightParam.route[j] = this.$store.state.route.routeItem;
        } else {
          flightParam.route.push(this.$store.state.route.routeItem);
        }
        let n = 0;
        for (let key in flightParam.point) {
          if (key == this.deviceItem.sn_id) {
            flightParam.point[key] = [
              (this.deviceItem.lon_int / 1e7).toFixed(7),
              (this.deviceItem.lat_int / 1e7).toFixed(7),
            ];
            n = 1;
          }
        }
        if (n == 0) {
          let sn_id = this.deviceItem.sn_id;
          flightParam.point[sn_id] = [
            this.deviceItem.lon_int / 1e7,
            this.deviceItem.lat_int / 1e7,
          ];
        }
      } else {
        let sn_id = this.deviceItem.sn_id;
        let point = {};
        point[sn_id] = [
          this.deviceItem.lon_int / 1e7,
          this.deviceItem.lat_int / 1e7,
        ];
        flightParam = {
          fence: [this.fenceItem],
          route: [this.$store.state.route.routeItem],
          point: point,
        };
      }
      flightParam.uav_id = this.deviceItem.uav_sn;
      sessionStorage.setItem("flightParam", JSON.stringify(flightParam));
      setTimeout(() => {
        const newwin = this.$router.resolve({
          path: `/navigationEnvironment`,
          query: {
            sn_id: this.deviceItem.sn_id,
            type: this.deviceItem.type,
            f_id: f_id,
            m_id: m_id,
            state: 1,
          },
        });
        window.open(newwin.href, "_blank");
      }, 301);
    },
    //页码切换
    changePage(e) {
      this.pageData(e);
    },
    //分页数据
    pageData(e) {
      this.deviceList = [];
      let num =
        (e - 1) * 6 + 6 > this.deviceList_total.length
          ? this.deviceList_total.length
          : (e - 1) * 6 + 6;
      for (let index = (e - 1) * 6; index < num; index++) {
        this.deviceList.push(this.deviceList_total[index]);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.deviceList {
  .el-dialog {
    .contentDiv {
      width: 100%;
      height: 80%;
      overflow: auto;
      .check-group {
        .el-button {
          height: auto;
          width: 28%;
          margin: 1% 2%;
          margin-bottom: 3%;
          padding: 1%;
          text-align: center;
          background-color: #ffffff;
          border-radius: 14px;
          border: 6px solid rgba(112, 112, 112, 1);
          &.active {
            border: 6px solid rgba(11, 89, 222, 0.8);
          }
          .el-image {
            width: 90%;
          }
          .nameDiv {
            font-size: 16px;
            color: #040404;
            margin: 2%;
          }
          .idDiv {
            // margin-top: 1%;
            color: #707070;
            font-size: 12px;
          }
        }
      }
      .entryDevice {
        color: white;
        text-align: center;
        padding-top: 20%;
        letter-spacing: 4px;
        font-size: 24px;
      }
    }
    .pageDiv {
      width: 94%;
      height: 5%;
      text-align: right;
    }
    .btnDiv {
      width: 95.5%;
      height: 8%;
      text-align: right;
      margin-top: 2%;
      .sureBtn {
        background-color: #0b58de;
        color: white;
        border: none;
        width: 15%;
        font-size: large;
        letter-spacing: 5px;
        margin-right: 5%;
      }
      .closeBtn {
        background-color: #123571;
        color: white;
        border: none;
        width: 15%;
        font-size: large;
        letter-spacing: 5px;
        margin-right: 2%;
      }
    }
  }
}
</style>
<style lang="less">
.deviceList {
  .el-dialog {
    height: 70% !important;
    background-color: rgba(8, 16, 39, 0.9) !important;
    top: 0 !important;
    margin-top: 15vh !important;
    border-radius: 8px !important;
    .el-dialog__header {
      text-align: left !important;
      height: 6% !important;
      padding: 2% !important;
      padding-bottom: 0 !important;
      .el-dialog__title {
        color: white !important;
      }
    }
    .el-dialog__body {
      height: 88% !important;
      padding: 1% 2% !important;
    }
    .contentDiv {
      // scrollbar-width: thin !important;
      // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      // -ms-overflow-style: none !important;
      // scrollbar-color: #777777 #ccc;
      &::-webkit-scrollbar {
        width: 3px;
      }
      &::-webkit-scrollbar-track {
        background-color: #ccc;
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #777777;
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }
    }
    .pageDiv {
      .el-pagination {
        .btn-prev,
        .btn-next {
          background-color: transparent !important;
          color: white !important;
          border: 1px solid white !important;
          padding-right: 6px !important;
          padding-left: 6px !important;
        }
        .el-pager {
          li {
            background-color: transparent !important;
            border: 1px solid #ffffff !important;
            color: white !important;
          }
          li:not(.disabled).active {
            background-color: #409eff !important;
            color: white !important;
          }
        }
      }
    }
  }
}
</style>