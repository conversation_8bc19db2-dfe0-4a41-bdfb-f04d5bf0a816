import Vue from 'vue'
import layerButton from "./index.vue"
let layerButtonVue = Vue.extend(layerButton)
export default class layerButtonGroup {
  constructor(val) {
    this.viewer = val.viewer
    this.layerIndex = val.layerIndex
    this.sceneModeButton = val.sceneModeButton
    this.layerButton = val.layerButton
    this.sceneMode3D = val.sceneMode3D
    this.layerButtonList = new layerButtonVue({
      propsData: {
        layerIndex: this.layerIndex,
        layerButton: this.layerButton,
        sceneModeButton: this.sceneModeButton,
        sceneMode3D: this.sceneMode3D
      },
      methods: {
        layerChange(e) {
          this.layerIndex = e
          val.returnFun(e)
        },
        sceneModeChange() {
          this.sceneMode3D = !this.sceneMode3D
          val.sceneModeChangeFun(this.sceneMode3D)

        }
      }
    }).$mount()
    val.viewer.cesiumWidget.container.appendChild(this.layerButtonList.$el);
  }
}
