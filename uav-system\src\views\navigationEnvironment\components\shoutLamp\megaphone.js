import OpusMediaRecorder from 'opus-media-recorder'
import Encoder<PERSON>orker from 'opus-media-recorder/encoderWorker.js?worker'

export default {
    data() {
        return {
            socket: null,
            recorder: null,
            audioCtx: null,
            sourceNode: null,
            processor: null,
        };
    },
    async mounted() {
        // 请求麦克风权限
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        // 设置 16kHz AudioContext
        this.audioCtx = new(window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 });
        this.sourceNode = this.audioCtx.createMediaStreamSource(stream);

        // 使用 ScriptProcessorNode 进行重采样和 PCM 提取
        const bufferSize = 4096;
        this.processor = this.audioCtx.createScriptProcessor(bufferSize, 1, 1);
        this.sourceNode.connect(this.processor);
        this.processor.connect(this.audioCtx.destination);

        // 创建 WebSocket
        this.socket = new WebSocket('wss://your-backend.com/audio');
        this.socket.onopen = () => console.log('WebSocket 已连接');

        // 初始化 Opus 编码器
        await OpusMediaRecorder.requireOpusEncoder(); // 加载 WASM
        const mimeType = 'audio/ogg'; // 或 'audio/webm'
        // const workerOpts = {
        //     encoderWorkerFactory: () => new Worker('opus-media-recorder/encoderWorker.js'),
        //     OggOpusEncoderWasmPath: 'opus-media-recorder/OggOpusEncoder.wasm',
        // };
        window.MediaRecorder = OpusMediaRecorder;
        this.recorder = new MediaRecorder(stream, { mimeType });

        // 接收编码块
        this.recorder.ondataavailable = (e) => {
            if (e.data.size > 0 && this.socket.readyState === WebSocket.OPEN) {
                this.socket.send(e.data);
            }
        };

        this.recorder.start(100); // 每100ms生成一块 Opus 数据

        // 如果音频采样率不是 16kHz，手动 downsample
        this.processor.onaudioprocess = (e) => {
            const inBuf = e.inputBuffer.getChannelData(0);
            // 简单线性重采样
            const outLen = Math.floor(inBuf.length * this.audioCtx.sampleRate / stream.getAudioTracks()[0].getSettings().sampleRate);
            const outBuf = new Float32Array(outLen);
            for (let i = 0; i < outLen; i++) {}
        }
    }
}



export function startAudio() {
    navigator.mediaDevices.getUserMedia({ audio: true }).then(stream => {
        // 设置 16kHz AudioContext
        const audioCtx = new(window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 });
        const sourceNode = audioCtx.createMediaStreamSource(stream);

        // 使用 ScriptProcessorNode 进行重采样和 PCM 提取
        const bufferSize = 4096;
        const processor = audioCtx.createScriptProcessor(bufferSize, 1, 1);
        sourceNode.connect(processor);
        processor.connect(audioCtx.destination);
        // 加载 WASM 编码器
        // OpusMediaRecorder.requireOpusEncoder().then(() => {
        //     const mimeType = 'audio/ogg; codecs=opus';
        //     const workerOpts = {
        //         encoderWorkerFactory: () => new EncoderWorker(),
        //         OggOpusEncoderWasmPath: 'opus-media-recorder/OggOpusEncoder.wasm',
        //     };
        //     window.MediaRecorder = OpusMediaRecorder;
        //     const recorder = new MediaRecorder(stream, { mimeType }, workerOpts);

        //     // 5. Opus 数据回调
        //     recorder.ondataavailable = (e) => {
        //         if (e.data.size > 0) {
        //             console.log("数据", e.data)
        //         }
        //         // if (e.data.size > 0 && this.socket.readyState === WebSocket.OPEN) {
        //         //     this.socket.send(e.data); // 发送 Ogg/Opus 数据
        //         // }
        //     };

        //     this.recorder.start(40); // 每 100ms 生成一个 Opus 块

        // });
        processor.onaudioprocess = e => {
            console.log(e)
        }
    });


}