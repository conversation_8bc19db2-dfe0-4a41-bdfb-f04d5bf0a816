/**
 * 数据大屏
 */
const home = {
    index: {
        state: {
            1: "在线",
            2: "离线",
            3: "有人连接"
        }
    },

    // 无人机设备列表
    uavList: {
        title: "无人机设备列表",
        tab: {
            AutomaticAirport: "自动机场",
            solos: "单兵无人机"
        },
        state: {
            1: "在线",
            2: "离线",
            3: "有人连接"
        }
    },

    // 巡检记录
    inspectionRecord: {
        title: "巡检记录",
        type: {
            total: "总巡检次数",
            day: "今日巡检次数"
        },
        prpo: {
            flightLog: "飞行日志回放",
            achievement: "成果展示"
        },
        time: {
            start: "开始日期",
            end: "结束日期"
        }
    },

    // 数据总览
    dataOverview: {
        time: {
            title: "总巡检次数"
        },
        mileage: {
            title: "总巡检里程(KM)"
        },
        achievement: {
            title: "巡检成果数"
        },
        lastMonthTotal: "本月总数"
    },

    // 巡检排行榜
    rankingList: {
        title: "巡检排行榜",
        tab: {
            10: "按出勤次数",
            20: "按巡检里程",
            30: "按成果数+"
        },

    },
    //定时任务
    taskList: {
        title: '定时任务列表',
        taskType: [
            { label: '待执行任务', value: 1 },
            { label: '执行成功任务', value: 10 },

        ],
        column: [
            { label: "航线名称", prop: "m_title" },
            { label: "设备ID", prop: "sn_id" },
            { label: "执行时间", prop: "task_tms", width: "100" }
        ],
    },

    // 飞行数据
    flightData: {
        title: "飞行数据",
        pie: {
            1: "总警告数",
            2: "待处理告警",
            3: "已处理告警"
        },
        list: {
            time: "时间",
            level: "告警级别",
            stave: "处理情况"
        }
    }
}

export default home;