<!-- 账户设置 -->
<template>
  <custom-dialog
    :title="userLanguage.title"
    :visible.sync="isShow"
    ref="userLayout"
    @submit="saveUserInfo"
  >
    <template v-slot:main>
      <el-form
        label-width="120px"
        :model="form"
        :rules="rules"
        ref="accountSetForm"
      >
        <el-form-item :label="formLabel.nick" prop="nick">
          <el-input :placeholder="formPlaceholder.nick" v-model="form.nick" />
        </el-form-item>
        <el-form-item :label="formLabel.account" prop="account">
          <el-input
            :placeholder="formPlaceholder.account"
            v-model="form.account"
          />
        </el-form-item>
        <el-form-item :label="formLabel.in_com" v-if="form.in_com">
          <!-- {<el-input
            :placeholder="formPlaceholder.startTime"
            readonly
            v-model="form.start_tms"
          />} -->
          <div style="color: #606266">{{ form.in_com.name }}</div>
        </el-form-item>
        <el-form-item :label="formLabel.startTime">
          <!-- {<el-input
            :placeholder="formPlaceholder.startTime"
            readonly
            v-model="form.start_tms"
          />} -->
          <div style="color: #606266">{{ form.start_tms }}</div>
        </el-form-item>
        <el-form-item :label="formLabel.endTime">
          <!-- <el-input
            :placeholder="formPlaceholder.endTime"
            readonly
            v-model="form.end_tms"
          /> -->
          <div style="color: #606266">{{ form.end_tms }}</div>
        </el-form-item>
      </el-form>
    </template>
  </custom-dialog>
</template>

<script>
import customDialog from "@/components/customDialog/index.vue";
import request from "@/utils/api.js";
import { getLocalStorage, setLocalStorage } from "@/utils/storage";
import { nowDate } from "@/utils/date";
export default {
  name: "accountSet",
  components: {
    customDialog,
  },

  data() {
    return {
      form: {
        nick: "",
        account: "",
        start_tms: "",
        end_tms: "",
      },
      rules: {
        nick: [{ required: true, message: "请输入账户昵称", trigger: "blur" }],
        account: [
          {
            required: true,
            message: "请输入用户手机号 / 邮箱",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value === "") {
                callback(new Error(this.formVerify.account[0]));
              } else if (
                !/^1(3|4|5|6|7|8|9)\d{9}$/.test(value) &&
                !/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/.test(
                  value
                )
              ) {
                callback(new Error(this.formVerify.account[1]));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      isSave: false,
      isShow: false,
      userInfo: {},
    };
  },
  computed: {
    userLanguage() {
      return this.$languagePackage.user.accountSet;
    },
    formLabel() {
      return this.userLanguage.label;
    },
    formPlaceholder() {
      return this.userLanguage.placeholder;
    },
    formVerify() {
      return this.userLanguage.verify;
    },
  },
  created() {
    this.rules.nick[0].message = this.formVerify.nick[0];
    this.rules.account[0].message = this.formVerify.account[0];
  },
  methods: {
    saveUserInfo: function () {
      this.isSave = true;
      this.$refs.accountSetForm.validate((volid) => {
        if (volid) {
          let pmd =
            "u_id,state,start_time,end_time,phone,email,password,in_com_id,nick,fun_json";
          let item = this.userInfo;
          let params = {
            u_id: item.u_id,
            state: 10,
            start_time: item.start_time || 0,
            end_time: item.end_time || "",
            nick: item.nick,
            phone: "",
            email: "",
            password: "",
            team: item.team,
            notes: item.notes,
            pmd: "",
            in_com_id: item.in_com.id,
            os_timestampCode: true,
          };

          params.fun_json = JSON.stringify(this.userInfo.fun_list);
          if (this.isEmail(this.form.account)) {
            params.email = this.form.account;
          } else {
            params.phone = this.form.account;
          }
          params.nick = this.form.nick;

          let list = pmd.split(",");
          for (let i = 0; i < list.length; i++) {
            params.pmd += params[list[i]];
          }
          request("userEdit", params)
            .then((res) => {
              setLocalStorage("userInfo", res.data);
              this.$store.commit("setUserInfo", res.data);
              this.$message({
                type: "success",
                message: this.userLanguage.hint.success,
              });
              this.isSave = false;
              this.shut();
            })
            .catch(() => {
              this.isSave = false;
            });

          return false;
        }
        this.isSave = false;
      });
    },
    open: function () {
      this.isShow = true;
      let user = getLocalStorage("userInfo");
      this.form.nick = user.nick;
      this.form.account = user.phone || user.email;
      this.form.in_com = user.in_com;
      this.userInfo = user;
      // this.$refs.userLayout.show();
      this.form.start_tms = nowDate(user.start_time).dateTime;
      this.form.end_tms =
        user.end_time == **********
          ? this.userLanguage.validity
          : nowDate(user.end_time).dateTime;
      console.log(this.form);
    },
    shut: function () {
      this.isShow = false;
      // this.$refs.userLayout.shut();
      this.$refs.accountSetForm.resetFields();
    },
    isEmail: function (email) {
      return /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(email);
    },
  },
};
</script>