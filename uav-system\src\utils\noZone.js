import { getCode } from './rsa'
import baseUrl from './global'
import "@/assets/js/qs.min.js"
import axios from 'axios'
import { Message } from 'element-ui'
let source = '';

function publicParameter() {
    var time = new Date().getTime()
    var app_ids = 'walkera.login.cn'
    var session_id = (Math.random().toFixed(8)) * 1e8.toString()
    var m = md5("3" + app_ids + session_id + time + "cBinL" + "0")
    var app_licence = getCode(m, 1)
    var datas = {
        "app_type": "3",
        "app_ids": app_ids,
        "app_code": "0",
        "app_flavor": "cBinL",
        "os_timestamp": time,
        "app_licence": app_licence,
        "app_session_id": session_id,
    }
    return datas
}
//取消上次请求
function cancelRequest() {
    if (typeof source === 'function') {
        source('终止请求')
    }
}
export function noZone(data) {
    return new Promise((resovle, reject) => {
        let datas = publicParameter()
        datas = Object.assign(datas, data)
        datas.pmd = md5(datas.app_licence + datas.pmd)
        let url = baseUrl.BASE_URL + baseUrl.ZONE_PROT + "/fence/app/get"
        cancelRequest()
        axios({ url: url, method: "post", data: Qs.stringify(datas), cancelToken: new axios.CancelToken(function executor(c) { source = c }) }).then(res => {
            source = ''
            if (res.data.code == 2000) {
                return resovle(res.data.data.list)
            } else {
                if (res.data.code != 44020) {
                    Message({
                        type: "error",
                        message: res.data.msg
                    })
                }
            }
        })
    })
}