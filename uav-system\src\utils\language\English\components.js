/**
 * 组件相应
 */
const components = {
    customDialog: {
        confirm: "Confirm",
        cancel: "Cancel"
    },
    customTable: {
        noData: "No query data available",
        index: "Index",
        loading: "Loading"
    },
    infoTip: {
        confirm: "Confirm",
        cancel: "Cancel"
    },
    pages: {
        prevText: "PgUp",
        nextText: "PgDn"
    },

    scrollConfirm: {
        showText: "Swipe right to confirm to continue"
    },

    scrollList: {
        loadFail: "Failed to load",
        refresh: "Refresh",
        loading: "Loading",
        returnTop: "Back to top",
        noDataText: "No query data available",
        reload: "Reload"
    },

    twiceDatePicker: {
        start: "Dtart date",
        end: "End date",
        cancelText: "Cancel"
    },

    video: {
        errorInfo: {
            1: "Waiting for the connection",
            2: "Connecting",
            3: "Network connection error",
            4: "The address does not exist"
        },
        reconnection: "Reconnection",
        videoDialog: {
            error: 'Your browser does not support the video TAB',
            errorList: {
                1: 'Failed to load, video link is unavailable'
            }
        }
    },
    dialogInfo: {
        equipName: 'Device Name:',
        equipSn: 'Equipment Number:'
    }
}

export default components;