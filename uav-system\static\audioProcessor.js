class PCMProcessor extends AudioWorkletProcessor {
    constructor(options) {
        super();
        this.frameSize = options.processorOptions.frameSize;
        this.buffer = new Float32Array();
    }

    process(inputs) {
        const input = inputs[0];
        if (input.length === 0) return true;

        // 将输入数据添加到缓冲区
        const newBuffer = new Float32Array(this.buffer.length + input[0].length);
        newBuffer.set(this.buffer, 0);
        newBuffer.set(input[0], this.buffer.length);
        this.buffer = newBuffer;

        // 处理完整帧
        while (this.buffer.length >= this.frameSize) {
            const frame = this.buffer.slice(0, this.frameSize);
            this.buffer = this.buffer.slice(this.frameSize);

            // 将帧发送到主线程
            this.port.postMessage(frame, [frame.buffer]);
        }

        return true;
    }
}

registerProcessor('pcm-processor', PCMProcessor);