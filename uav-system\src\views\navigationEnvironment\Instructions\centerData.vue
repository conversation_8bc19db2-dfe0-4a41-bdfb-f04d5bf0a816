<!-- 中间数据 -->
<template>
  <layouts
    :title="centerData.title"
    :direction="{
      bottom: '100%',
      left: 0,
      marginBootom: '20px',
      display: 'flex',
      width: '100%',
      alignItems: 'center'
    }"
  >
    <template v-slot:content>
      <div class="ml10" style="font-size: 12px; color: #fff">
        {{centerData.centent}}
      </div>
    </template>
  </layouts>
</template>

<script>
import layouts from "./layout.vue";
export default {
  components: {
    layouts,
  },
  data() {
    return {};
  },
  computed:{
    centerData(){
      return this.$languagePackage.navigation.instructions.centerData
    }
  }
};
</script>