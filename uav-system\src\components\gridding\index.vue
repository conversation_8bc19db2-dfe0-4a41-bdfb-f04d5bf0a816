<!-- 使用canvas绘制网格线 -->
<template>
  <div
    id="canvas-gridding"
    style="width: 100%; height: 100%"
    ref="griddingMain"
  >
    <canvas :id="id" :show="show"></canvas>
  </div>
</template>

<script>
export default {
  props: {
    type: [String, Number], // 网格类型，参考dict视频网格类型
    id: {
      type: String,
      default: "my-canvas",
    },
  },
  data() {
    return {
      show: true,
    };
  },
  watch: {
    type: function () {
      this.partialRenewal();
    },
  },
  beforeDestory() {
    window.removeEventListener("resize", this.resizeChange);
  },
  mounted() {
    this.$nextTick(() => {
      this.init();
      window.addEventListener("resize", this.resizeChange);
    });
  },
  methods: {
    resizeChange: function () {
      let time;
      clearTimeout(time);
      
      time = setTimeout(() => {
        this.partialRenewal();
      }, 30);
    },
    // 局部更新
    partialRenewal: function () {
      this.show = false;
      setTimeout(() => {
        this.show = true;
        this.$nextTick(() => {
          this.init();
        });
      }, 0);
    },

    init: function () {
      let canvas = document.getElementById(this.id);
      let griddingMain = this.$refs.griddingMain;

      let width = griddingMain.offsetWidth;
      let height = griddingMain.offsetHeight;
      canvas.width = width;
      canvas.height = height;
      var ctx = canvas.getContext("2d");
      // 中心点
      if (this.type == 2) {
        // ctx.beginPath();
        // ctx.arc(width / 2, height / 2, 15, 0, Math.PI * 2, true);
        // ctx.closePath();
        // 实心圆
        // ctx.fillStyle = "#ffffff";
        let iconWidth=width>300?80:40
        var img=new Image();
        img.onload=function () {
            ctx.drawImage(img,(width-iconWidth)/2,(height-iconWidth)/2,iconWidth,iconWidth);
        }
        img.src=require("@/assets/img/gridding.png");
        // ctx.fill();
        return false;
      }

      ctx.lineWidth = 1;
      ctx.strokeStyle = "#FFFFFF";
      ctx.beginPath();
      let wRatio = height / 3;
      let hRatio = width / 3;

      // 横线
      for (var i = 1; i < 3; i++) {
        ctx.moveTo(0, i * wRatio); // 开始
        ctx.lineTo(width, i * wRatio); // 结束
        ctx.stroke();
      }

      // 竖线
      for (let j = 1; j < 3; j++) {
        ctx.moveTo(j * hRatio, 0); // 开始
        ctx.lineTo(j * hRatio, height); // 结束
        ctx.stroke();
      }

      if (this.type == 3) {
        ctx.moveTo(0, 0); // 开始
        ctx.lineTo(width, height); // 结束
        ctx.moveTo(width, 0); // 开始
        ctx.lineTo(0, height); // 结束
        ctx.stroke();
      }
    },
  },
};
</script>