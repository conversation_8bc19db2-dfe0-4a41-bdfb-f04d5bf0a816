<template>
  <div class="batteryBox" :style="style">
    <div class="battery-back">
        <el-button icon="el-icon-arrow-right" @click="showInfo"></el-button>
    </div>
    <div class="battery-content">
      <div class="battery-header">{{language.title}}</div>
      <div class="battery-main">
        <div class="battery-item" v-for="item in batteryInfo" :key="item.id">
          <div class="item-label">{{ item.label }}</div>
          <div class="item-value">{{ format(item) }}{{ item.unit }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      batteryInfo: [
        { id: "voltage", label: "电压", unit: "V", accuracy: 2 },
        { id: "current", label: "电流", unit: "A", accuracy: 2 },
        { id: "battery_remaining", label: "电量", unit: "%", accuracy: 0 },
        { id: "battery_temperature", label: "温度", unit: "℃", accuracy: 0 },
      ],
      msgDta: {},
      show:false,
      style:{
        width: "0"
      }
    };
  },
  computed:{
    language() {
      return this.$languagePackage.navigation.batteryBox;
    },
  },
  created(){
    let battery=this.language.batteryInfo
    for (let index = 0; index < this.batteryInfo.length; index++) {
        let item=this.batteryInfo[index]
        this.batteryInfo[index].label=battery[item.id]
    }
  },
  methods: {
    getMessage(msg_id, data) {
      if (msg_id == 432) {
        this.msgDta = data;
      }
    },
    format(item) {
      return this.msgDta[item.id]?this.msgDta[item.id].toFixed(item.accuracy):this.msgDta[item.id];
    },
    showInfo(){
        if(this.show){
            this.style={
                width:0,
                transition: 'all 0.5s'
            }
            
        }else{
            this.style={
                width:"360px",
                transition: 'all 0.5s'
            }

        }
        this.show=!this.show

    }
  },
};
</script>
<style lang="less" scoped>
.batteryBox {
  position: absolute;
  top: 50px;
  right: 0;
  height: 100%;
  z-index: 210;
  color: #fff;
  display: flex;
  overflow: hidden;
  .battery-back {
    display: flex;
    align-items: center;
    .el-button{
        background-color: #000;
    color: #1be50d;
    border: none;
    border-top-right-radius: 0;
    border-end-end-radius: 0;
    padding: 5px;
    font-size: 20px;
    }


  }
  .battery-content {
    flex: 1;
    background-color: rgba(0, 0, 0, 0.774);
    .battery-header {
      padding: 10px;
      font-size: 16px;
      color: aqua;
    }
    .battery-main {
      padding: 10px;
      .battery-item {
        padding: 5px 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
}
</style>