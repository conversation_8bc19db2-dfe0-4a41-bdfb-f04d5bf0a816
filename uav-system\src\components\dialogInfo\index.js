import Vue from "vue";
import Label from "./index.vue";
let windowInfo = Vue.extend(Label);
export default class dialogInfo {
    constructor(val) {
            this.viewer = val.viewer
            let list = val.list
            this.position = val.list[0].position
            this.info = new windowInfo({
                propsData: {
                    list,
                    close: () => { this.windowClose(this) },
                    clickItem: (e) => { this.clickItem(e, this) }
                },

            }).$mount()
            val.viewer.cesiumWidget.container.appendChild(this.info.$el);
            this.addPostRender()
        }
        //添加场景事件
    addPostRender() {
        this.viewer.scene.postRender.addEventListener(this.postRenderEvent, this);
    }
    postRenderEvent() {
            if (!this.info.$el || !this.info.$el.style) return;
            const canvasHeight = this.viewer.scene.canvas.height;
            const windowPosition = new Cesium.Cartesian2();
            Cesium.SceneTransforms.wgs84ToWindowCoordinates(
                this.viewer.scene,
                this.position,
                windowPosition
            );
            const elHeight = this.info.$el.offsetHeight;
            this.info.$el.style.bottom =
                canvasHeight - windowPosition.y + elHeight + 40 + "px";
            const elWidth = this.info.$el.offsetWidth;
            this.info.$el.style.left = windowPosition.x - elWidth / 2 + 60 + "px";

            const camerPosition = this.viewer.camera.position;
            let height = this.viewer.scene.globe.ellipsoid.cartesianToCartographic(camerPosition).height;
            height += this.viewer.scene.globe.ellipsoid.maximumRadius;
            if ((!(Cesium.Cartesian3.distance(camerPosition, this.position) > height)) && this.viewer.camera.positionCartographic.height < 50000000) {
                this.info.$el.style.display = "block";
            } else {
                this.info.$el.style.display = "none";
            }

        }
        //关闭 
    windowClose(self) {
            self = self ? self : this
            if (self.info) {
                self.info.$el.remove();
                self.info.$destroy();
            }
            //this.vmInstance.$el.style.display = "none"; //删除dom
            self.viewer.scene.postRender.removeEventListener(self.postRenderEvent, self); //移除事件监听
        }
        //内容点击事件
    clickItem(item, self) {
        this.windowClose()
        self.viewer.scene.camera.flyTo({
            destination: item.position,
            duration: 1,
            maximumHeight: 2000,
            pitchAdjustHeight: 20,
            orientation: {
                heading: Cesium.Math.toRadians(0),
                pitch: Cesium.Math.toRadians(-90),
                roll: Cesium.Math.toRadians(0)
            }
        });

    }
}