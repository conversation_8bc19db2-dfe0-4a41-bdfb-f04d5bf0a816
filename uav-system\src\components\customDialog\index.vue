<!-- 自定义对话框 -->
<template>
  <transition name="el-fade-in-linear">
    <div class="dialog custom-components-dialog" v-show="isShow" @click="shut">
      <div class="dialog-body" @click.stop>
        <!-- 头部 -->
        <slot name="header">
          <div class="body-header">
            <div class="header-main" :style="headerMainStyle">
              {{ title }}
              <div class="header-footer-line"></div>
            </div>
            <div class="header-shut" @click="shut">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </slot>

        <!-- 内容 -->
        <div
          class=""
          style="padding: 80px 230px 0px 230px; margin-bottom: 94px"
        >
          <slot name="main"></slot>
        </div>

        <!-- 底部 -->
        <slot name="footer">
          <div
            class="dialog-footer"
            :style="{ paddingLeft: `calc(230px + ${labelWidth})` }"
          >
            <el-button
              type="primary"
              @click="notarize"
              :loading="submitLoading"
              :style="buttonStyle"
            >
              {{ language.confirm }}
            </el-button>
            <el-button @click="cancel" :style="buttonStyle">
              {{ language.cancel }}
            </el-button>
          </div>
        </slot>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: "Dialog",
  props: {
    title: String, // 标题
    visible: Boolean, // 是否显示
    submitLoading: Boolean, // 提交loading
    labelWidth: {
      type: String,
      default: "120px",
    },
  },
  data() {
    return {
      isShow: false,
      time: null,
    };
  },
  watch: {
    visible: function (val) {
      this.isShow = val;
    },
    isShow: function (val) {
      this.$emit("update:visible", val);
    },
  },
  computed: {
    headerMainStyle() {
      return {
        "letter-spacing": this.$language == "chinese" ? "14px" : 0,
      };
    },
    buttonStyle() {
      console.log("-this.$longuage>", this.$language);
      return {
        "letter-spacing": this.$language == "chinese" ? "20px" : undefined,
        "padding-right": this.$language == "chinese" ? "10px" : undefined
      };
    },
    language() {
      return this.$languagePackage.components.customDialog;
    },
  },
  methods: {
    show: function () {
      this.isShow = true;
    },
    shut: function () {
      this.isShow = false;
      this.$emit("close", "关闭");
    },
    cancel: function () {
      this.$emit("cancel", "取消");
      this.isShow = false;
    },
    notarize: function () {
      clearTimeout(this.time);
      this.time = setTimeout(() => {
        this.$emit("submit", "提交");
      }, 50);
    },
  },
};
</script>
<style lang="less">
// .dialog-body input::-webkit-input-placeholder {
//   color: #cccccc !important;
// }
// .dialog-body input::-moz-input-placeholder {
//   color: #cccccc !important;
// }
// .dialog-body input::-ms-input-placeholder {
//   color: #cccccc !important;
// }
</style>
<style lang="less" scoped>
.dialog {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  // background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  .dialog-body {
    width: 1000px;
    min-height: 600px;
    // background-color: #fff;
    border-radius: 8px;
    position: relative;
    .body-header {
      height: 128px;
      padding: 0 200px;
      position: relative;
      .header-main {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        // border-bottom: 4px solid #127ED7;
        // color: #464c57;
        font-size: 30px;
        font-weight: 900;
        letter-spacing: 14px;
        position: relative;
        .header-footer-line {
          height: 4px;
          width: 100%;
          // background-color: #127ed7;
          border-radius: 15px;
          position: absolute;
          left: 0;
          bottom: 0;
        }
      }
      .header-shut {
        position: absolute;
        right: 20px;
        top: 20px;
        font-size: 30px;
        font-weight: 900;
        &:hover {
          // color: #127ed7;
        }
      }
    }

    .dialog-footer {
      padding: 0px 230px 0 230px;
      position: absolute;
      left: 0;
      bottom: 0;
      // bottom: 50px;
      display: flex;
      align-items: center;
      height: 94px;
      .el-button {
        font-size: large;
        padding: 12px 30px;
        border-radius: 8px;
      }
      .el-button--default {
        // color: #0b58de;
      }
      .el-button--primary {
        // background-color: #0b58de;
        margin-right: 15%;
      }
    }
  }
}
</style>