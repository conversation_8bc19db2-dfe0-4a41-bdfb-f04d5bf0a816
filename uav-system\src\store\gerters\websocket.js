import {
    Websockets,
    userWebsocket
} from "@/utils/websocketUntil";
import baseUrl from "@/utils/global";

const ws = {
    state: {
        ws: null,
        wsMessageEvent: null,
        wsError: {},
        like: false,
        multiMessage: {},
        isCertificate: false, // 是否认证
        closeEquipWs: false,
        worker: null
            // wsInstance: null
    },
    mutations: {
        //关闭设备websocket连接状态
        setCloseEquipWs(state, val) {
            state.closeEquipWs = val
        },
        // 设置单个接受ws推送message
        wsMessage(state, val) {
            state.wsMessageEvent = val;
        },
        // 设置多个接收ws推送message
        setMultiMessage(state, val) {
            let {
                key,
                message,
                error
            } = val || {};
            state.multiMessage[key] = message;
            if (error) {
                state.wsError[key] = error
            }

        },
        // 卸载多个接收ws推送message
        unintallMultiMessage(state, val) {
            state.multiMessage = val || {};
        },
        // 多个message事件下发给各个页面
        issueMultiMessage(state, val) {
            let {
                msg_id,
                data
            } = val || {};
            for (let k in state.multiMessage) {
                state.multiMessage[k](msg_id, data);
            }
        },
        // 单个message事件下发给各个页面
        issueMessage(state, {
            msg_id = null,
            data = null
        }) {
            state.wsMessageEvent && state.wsMessageEvent(msg_id, data);
        },
        issueError(state, val) {
            for (let k in state.wsError) {
                state.wsError[k](val);
            }
        },
        // 设置ws
        setWS(state, val) {
            console.log("被赋值------>", val);
            state.ws = val;
        },
        // 设置连接状态
        setLikeState(state, val) {
            state.like = val;
        },
        //设置是否有worker
        setWorker(state, val) {
            state.worker = val
        }
    },
    actions: {
        // 创建web
        createWS(content) {
            content.commit("setLikeState", true);

            return new Promise((resolve, reject) => {
                let ws = new Websockets(baseUrl.WS_URL, {
                    heartbeat: 20000, // 开启心跳认证，每10秒发起一次
                    userVerify: true, // 开启用户认证
                    // 接收到推送
                    message: (res) => {
                        let msg_id = res.msg_id;
                        let data = res.data;
                        let params = {
                                msg_id: msg_id,
                                data: data
                            }
                            // 单个
                        content.commit("issueMessage", params)
                            // 多个
                        content.commit("issueMultiMessage", params)
                    },
                    success: (res) => {
                        console.log("连接成功------->", res);
                    },
                    fail: (err) => {
                        console.log("连接失败")
                        content.commit("issueError", err)
                        if (err.msg_id == 101 && err.code == 46010) { // 设备未认证
                            content.commit("setLikeState", false);
                            ws.manualClone();
                        }
                    },
                    close: () => {
                        console.log("连接被断开------------------------")
                            // 断开连接时，更改状态
                        content.commit("setLikeState", false);
                        content.commit("setWS", null);
                        // content.dispatch("createWS");
                    }
                });

                content.commit("setWS", ws);
                resolve(ws);
            })
        }
    }
}

export default ws;