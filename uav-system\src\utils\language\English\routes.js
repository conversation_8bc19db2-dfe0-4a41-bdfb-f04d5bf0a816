const routes = {
    language: 'en-US',
    tool: {
        placeholder: "Please enter search address",
        limitFly: 'Limited flight',
        noflyzone: 'No fly zone',
        limitHeight: 'Height limit zone',
        airportBuffer: 'Airport buffer zone',
        temporaryNofly: 'Temporary no fly zone',
        weatherLayer: 'Weather layer',
        RealTimeWeather: "Real-time weather"
    },
    toolMenu: ['location', 'distance', "area", "circular", "azimuth", "remove"],
    fence: {
        title: 'Flight Operation List',
        addFence: "Add Work Fence",
        edit: "Edit",
        del: "Delete",
        editTitle: "Edit Work Fence",
        name: "Name of working fence",
        basicSet: "Basic Setup",
        maxHeight: "Maximum height of fence",
        pointTitle: 'Fence boundary point',
        placeholder: 'Please enter a keyword search',
        placeholder1: "Please enter the fence name",
        strip: ' routes',
        do: 'Do',
        noSeachMessage: 'No matching job fence was found!',
        entryReturn: 'If the input is empty, return to the original list',
        delMessage: 'This operation cannot be resumed. Are you sure to delete the job fence information?',
        delTip: 'Tips',
        delSuccess: "The fence has been deleted successfully!",
        cancelDel: "Delete cancelled!",
        save: "Save",
        bindSn: 'Bind device SN',
        binSnTip: 'Please choose to bind device SN',
        deviceErrorTip: 'The device name is not within the fence range',
        equipTitle: 'Equip:'
    },
    routeLine: {
        addtask: 'Add Tasks',
        performTask: "Perform Task",
        issued: "Distribute tasks",
        set: 'Set',
        noData: "no data",
        rename: "Rename",
        del: "Delete",
        export: "Export",
        ET: "Route Estimated Time",
        planeET: 'Estimated flight time',
        distance: "Distance",
        waypointCount: "Waypoint Count",
        area: 'Area',
        photoNumber: "Photo Number",
        addTitle: "Add tasks",
        editTitle: "Edit Tasks",
        taskName: 'Task name',
        fenceName: "Belonging fence",
        taskType: 'Type',
        basicSet: "Basic Setup",
        heightType: 'Height type',
        autoSpeed: "Automatic cruising speed(m/s)",
        maxSpeed: "Maximum flight speed(m/s)",
        defaultHeight: "Default altitude for all routes(m)",
        returnHeight: "Turn back height",
        taskAction: "Action after completion of the task",
        waypoint: "Waypoint",
        save: 'Save',
        cancel: 'Cancel',
        routeLoading: "Waypoint loading",
        placeholder: 'Please enter the task name',
        placeholder1: 'Please select the action after the task is completed',
        placeholder2: 'Please enter a keyword search ',
        placeholder5: 'Select height type',
        placeholder4: 'Select route execution type',
        taskRename: "Task rename",
        cancelRename: 'Rename cancelled!',
        renameSuccess: "Rename succeeded!",
        uploading: "Uploading the route, please complete the operation!",
        delMessage: "This operation cannot be resumed. Are you sure you want to delete the task information?",
        delTip: 'Tips',
        delSuccess: "Deletion succeeded!",
        cancelDel: "Deletion cancelled!",
        setNewTask: 'New task',
        cameraType: "camera type",
        placeholder3: "Please select a camera type",
        customCamera: 'Custom camera',
        cameraType1: "6K camera",
        cameraType2: 'Dual light camera',
        sensor: 'Sensor',
        image: 'Image',
        focalLength: 'Focal length',
        course: 'Course overlap',
        lateral: 'Lateral overlap',
        wheelDist: 'Turning distance',
        angle: 'Angle',
        toPlaneType: 'Route execution type',
        timeNode: "Execution time node",
        intervalHeight: "Interval calculation height(m)",
        isPlaneHight: "Calculate elevation data",
        photoType: 'Photo type',
        placeholder6: 'Select photo type'
    },
    waypoint: {
        title: ' Waypoint',
        title1: ' Boundary point',
        lat: 'latitude',
        lng: 'longitude',
        height: "height(m)",
        waypointAction: "Waypoint Action",
        actionOptions: [{
                label: "Yaw Angle of UAV",
                value: "uav_yaw",
            },
            {
                label: "Hover",
                value: "hover",
            },
            {
                label: "Taking pictures",
                value: "takephoto",
            },
            {
                label: "Gimbal Angle control",
                value: "gimbal_ctrl",
            },
            {
                label: "Set UAV cruising speed",
                value: "speed",
            },
            {
                label: "Camera trigger range",
                value: "cam_trig_dist",
            },
            // {
            //     label: "Panoramic photography",
            //     value: "panorama_takephoto",
            // },
        ],
        uav_yaw: 'UAV yaw angle(range [0,360], unit:°):',
        gimbal_yaw: 'PTZ yaw angle(range [-180,180], unit:°):',
        gimbal_pitch: 'PTZ pitch angle(range [-180,180], unit:°) :',
        cam_trig_dist: 'Photo distance (unit:m):',
        speed: 'Cruising speed of UAV (unit:m/s):',
        hoverTime: 'Hover time (range [0255], unit: s):',
        placeholder: 'Please select waypoint action'
    },
    operationBtn: {
        drawPoint: "point",
        delete: "delete",
        remove: "remove",
        recall: "recall",
        import: "import",
        export: "export"
    },
    dialogType: {
        title: "Select task type",
        undeveloped: "This task type is still in development, updates will come in the near future!"
    },
    dialogEquip: {
        title: "Select a device to perform the task",
        noInEquip: 'No online equipment',
        previous: 'PgUp',
        next: 'PgDn',
        save: "Confirm",
        cancel: 'Cancel',
        cancelMessage: "Task execution has been canceled!",
        cancelMessage1: "The task has been canceled!",
        noSelectMessage: 'No device selected to perform task',
        noSelectMessage1: 'No device selected for task distribution',
        sendSuccessMsg: 'The scheduled route task has been successfully issued!',
        flightTime: 'Estimated Flight Time:',
    },
    weather: {
        windDirection: 'Wind Direction:',
        level: '',
        humidity: 'Humidity'

    },
    cameraParams: {
        width: 'width',
        height: 'height'

    },
    options: [{
            label: "hover",
            value: 10,
        },
        {
            label: "return",
            value: 20,
        },
        {
            label: "automatic landing",
            value: 30,
        },
        {
            label: "return to first waypoint",
            value: 40,
        },
    ],
    options1: [{
            label: "Relative height",
            value: 0,
        },
        {
            label: "Altitude",
            value: 1,
        },
    ],
    photoTypeList: [
        { label: 'Infrared', value: 1 },
        { label: 'Visible light', value: 2 },
        { label: 'Infrared and visible light', value: 3 },
    ],
    placeholder: 'Unable to obtain current specific location!',
    placeholder1: 'The route information is not saved. Are you sure you would like to return?',
    placeholder2: 'The fence information is not saved. Are you sure you would like to return?',
    placeholder3: 'Loading waypoint information, temporarily unable to operate!',
    placeholder4: 'The waypoint cannot exceed the fence!',
    placeholder5: 'The route is beyond the fence!',
    placeholder6: 'The effective distance between adjacent waypoints cannot exceed 2 kilometers!',
    placeholder7: 'The boundary line is beyond the fence!',
    placeholder8: 'Are you sure to clear all information of this route?',
    placeholder9: 'Are you sure to clear all information of this fence?',
    placeholder10: 'Withdrawn to original state',
    placeholder11: 'This operation will clear the drawn route. Do you want to continue?',
    placeholder12: 'This operation will empty the drawn fence. Do you want to continue?',
    placeholder13: 'Export route data to KML file?',
    placeholder14: 'Export fence data to KML file?',
    messageInfo: 'Cleared successfully!',
    messageInfo1: 'Cancel cleanup',
    messageInfo2: 'reminder: no crossing of fences',
    messageInfo3: 'no crossing!',
    messageInfo4: 'The fence is incomplete!',
    messageInfo5: 'The mission route is incomplete!',
    successMessage: 'Editing fence success!',
    successMessage1: 'Fence added successfully!',
    successMessage2: 'Edit task success!',
    successMessage3: 'New task success!',
    errorMessage: 'Cannot be 0',
    errorMessage1: "The selected file format is incorrect. Please select '.KML/.json/.plan' file",
    errorMessage2: 'The imported KML format is incorrect!',
    errorMessage3: 'The selected KML file task type does not match the selected task type!',
    errorMessage4: "The selected KML file is not a complete fence!",
    // errorMessage5: 'and',
    // errorMessage6: 'The connecting line between waypoint 1 and waypoint 2 exceeds the fence range',
    errorMessage7: 'The route is not within the fence!',
    errorMessage8: 'The effective distance between adjacent waypoints cannot exceed 2 kilometers!',
    errorMessage9: 'The distance between adjacent routes is too large',
    errorMessage10: "The time interval of the scheduled task cannot be less than 30 minutes.",
    errorMessage11: "In the calculation of elevation data, please wait for the calculation to be completed before saving",
    elevation: 'Elevation:',
    cancelexport: 'Export cancelled!',
    tip: 'Tips',
    saveBtn: 'Confirm',
    cancelBtn: 'Cancel',
    inLine: 'Online',
    outLine: 'Offline',
    toolBar: {
        azimuth: 'azimuth:',
        circular: 'circular area',
        area: 'total area:',
        meter: 'square meter',
    },
    getHeightLoading: 'Getting elevation...',
    heightLabel: 'Regional peak altitude:',
    layerType: {
        standard: "TileLayer",
        satellite: "Satellite"
    },
    mapModeTip: "The current map is in 3D mode, there may be deviations from the drawing point, it is recommended to switch to 2D mode.",
    photoActionError: "The waypoint [name] has already been selected for photography/panoramic photography, and panoramic photography cannot be selected again",
    photoActionError1: "The waypoint [name] has already been selected for panoramic photography and cannot be selected for photography again."
}
export default routes