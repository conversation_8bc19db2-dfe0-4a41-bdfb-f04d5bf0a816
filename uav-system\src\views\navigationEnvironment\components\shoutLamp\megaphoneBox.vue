<template>
  <div class="megaphone-box">
    <div class="item-box">
      <div class="megaphone-item-box" @click="showVolumeSlider">
        <el-image
          :src="volumeImgSrc"
          style="height: 40px"
          fit="contain"
        ></el-image>
        <div class="megaphone-value">
          {{ volumeValue }}
        </div>
      </div>
      <div class="megaphone-item-border"></div>
      <set-voice ref="setVoice" :sendWs="sendWs" @openSet="openSet"></set-voice>
    </div>
    <div class="item-box volume-slider">
      <div class="volume-slider-item" v-show="volumeSlider">
        <el-slider
          v-model="volumeValue"
          :show-tooltip="false"
          @change="changeVolumeSlider"
        ></el-slider>
      </div>
    </div>
    <div class="item-box">
      <el-button
        class="btn-shout"
        @click="startShoutEvent"
        v-show="!startShout"
        >{{ language.realTimeShout }}</el-button
      >
      <div class="shout-start-box" v-show="startShout">
        <div class="shout-start-item">
          <animation-bar :value="volume"></animation-bar>
          <!-- <div class="shout-start-item-animation"></div> -->
        </div>
        <div class="shout-start-icon" @click="stopShoutEvent">
          <div class="shout-start-icon-item"></div>
        </div>
      </div>
    </div>
    <div class="item-box">
      <div
        class="btn-megaphone"
        v-for="item in operateList"
        :key="item.id"
        @click="item.fun(item)"
        :class="{ active: item.classAble }"
      >
        <el-image
          :src="item.active ? item.iconSrc_active : item.iconSrc"
          :style="{ width: item.width }"
          fit="contain"
        ></el-image>
      </div>
    </div>
    <shout-file
      ref="shoutFile"
      @close="closeFile"
      :sendWs="sendWs"
    ></shout-file>
    <!-- <input type="file" accept=".opus" @change="handleFileUpload" /> -->
  </div>
</template>
<script>
export default {
  props: {
    sendWs: {
      type: Function,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      volumeValue: 0,
      volumeValue_1: 0,
      volumeImg_0: require("@/assets/img/volume_0.png"),
      volumeImg_1: require("@/assets/img/volume_1.png"),
      volumeImg_2: require("@/assets/img/volume_2.png"),
      startShout: false,
      operateList: [
        {
          id: 1,
          iconSrc: require("@/assets/img/start.png"),
          classAble: false,
          width: "52.8%",
          fun: this.stopPlay,
        },
        {
          id: 2,
          iconSrc: require("@/assets/img/replay.png"),
          iconSrc_active: require("@/assets/img/replay_1.png"),
          active: false,
          width: "50.8%",
          fun: this.cyclePlay,
        },
        {
          id: 3,
          iconSrc: require("@/assets/img/file.png"),
          iconSrc_active: require("@/assets/img/file_1.png"),
          active: false,
          width: "34.1%",
          fun: this.openFile,
        },
      ],
      volumeSlider: false,
      mediaStream: null,
      requestAnimationId: null,
      volume: 0,
      lock: true,
      buffer: new Int16Array(0),
      opusEncoder: "",
      FRAME_SIZE: (16000 * 40) / 1000,
      audioContext: null,
      sourceNode: null,
      workletNode: null,
      pcmChunks: [],
    };
  },
  components: {
    SetVoice: () => import("./setVoice.vue"),
    ShoutFile: () => import("./shoutFile.vue"),
    AnimationBar: () => import("./animationBar.vue"),
  },
  computed: {
    volumeImgSrc() {
      return this.volumeValue == 0
        ? this.volumeImg_0
        : this.volumeValue < 50
        ? this.volumeImg_1
        : this.volumeImg_2;
    },
    language() {
      return this.$languagePackage.navigation.shout;
    },
  },
  watch: {
    volumeValue_1: {
      handler(val) {
        this.volumeValue = val;
      },
      immediate: true,
    },
  },
  methods: {
    //获取websocket数据
    disposeData: function (msg_id, data) {
      this.$refs.shoutFile && this.$refs.shoutFile.getDataObj(msg_id, data);
      if (msg_id === 701 && data.model_connect) {
        this.volumeValue_1 = data.volume;
        this.operateList[1].active = data.loop ? true : false;
        this.$refs.setVoice && this.$refs.setVoice.getDataObj(data);
      }
      if (msg_id === 705) {
        if (!this.lock) {
          this.lock = true;
        }
      }
    },
    //显示音量调节
    showVolumeSlider: function () {
      this.volumeSlider = !this.volumeSlider;
    },
    //开始喊话
    startShoutEvent: function () {
      this.startShout = !this.startShout;
      this.getAudioData();
      this.pcmChunks = [];
      // this.getAudioData1();
    },

    //获取音频数据opus
    getAudioData: async function () {
      if (!this.checkSupport()) {
        this.$message.error(this.language.checkError);
        return false;
      }
      if (!this.mediaStream) {
        this.lock = false;
        this.sendWs({ action: 10 });
        // // 获取麦克风流
        this.mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16000,
            channelCount: 1,
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
          },
        });
        //设置采样码率
        this.audioContext = new (window.AudioContext ||
          window.webkitAudioContext)({
          sampleRate: 16000,
        });
        this.sourceNode = this.audioContext.createMediaStreamSource(
          this.mediaStream
        );
        // 注册并添加AudioWorklet处理器
        await this.audioContext.audioWorklet.addModule(
          "/static/audioProcessor.js"
        );
        this.workletNode = new AudioWorkletNode(
          this.audioContext,
          "pcm-processor",
          {
            processorOptions: {
              frameSize: this.FRAME_SIZE,
            },
          }
        );
        this.sourceNode.connect(this.workletNode);
        this.workletNode.connect(this.audioContext.destination);
        this.workletNode.port.onmessage = this.handlePCMData;
        this.getRealTimeVolume();
      } else {
        this.startShout = !this.startShout;
      }
    },
    handlePCMData: function (e) {
      // 接收到的PCM数据(Float32)
      const float32Data = e.data;
      // 转换为Int16 (Opus编码需要)
      const int16Data = this.float32ToInt16(float32Data);
      // this.pcmChunks.push(int16Data);
      // 添加到缓冲区
      this.appendToBuffer(int16Data);
      // 处理完整帧
      while (this.buffer.length >= this.FRAME_SIZE) {
        const frame = this.buffer.slice(0, this.FRAME_SIZE);
        this.buffer = this.buffer.slice(this.FRAME_SIZE);
        // const buffer_1 = this.opusEncoder.encode(frame);
        this.changeBase64(frame);
      }
    },
    float32ToInt16(float32Array) {
      const int16Array = new Int16Array(float32Array.length);
      for (let i = 0; i < float32Array.length; i++) {
        const s = Math.max(-1, Math.min(1, float32Array[i]));
        int16Array[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
      }
      return int16Array;
    },
    appendToBuffer(newData) {
      const newBuffer = new Int16Array(this.buffer.length + newData.length);
      newBuffer.set(this.buffer, 0);
      newBuffer.set(newData, this.buffer.length);
      this.buffer = newBuffer;
    },

    //转换成base64
    changeBase64: function (int16Array, code) {
      const blob = new Blob([int16Array]);
      console.log(int16Array);
      // 2. 使用FileReader读取Blob
      const reader = new FileReader();
      reader.onload = () => {
        // 3. 从Data URL中提取Base64部分
        const base64 = reader.result.split(",")[1];
        this.sendWs({ action: 12, value: base64 });
      };
      reader.readAsDataURL(blob);
    },
    //实时获取音量
    getRealTimeVolume: function () {
      const analyser = this.audioContext.createAnalyser();
      analyser.fftSize = 256;
      this.sourceNode.connect(analyser);
      const dataArray = new Uint8Array(analyser.fftSize);
      //获取音量数据
      const getVolumeData = () => {
        analyser.getByteTimeDomainData(dataArray);
        // 计算RMS音量
        let sum = 0;
        for (let i = 0; i < dataArray.length; i++) {
          let val = (dataArray[i] - 128) / 128;
          sum += val * val;
        }
        let rms = Math.sqrt(sum / dataArray.length);
        this.volume = rms * 100; // 0~100
        this.requestAnimationId = requestAnimationFrame(getVolumeData);
      };
      getVolumeData();
    },
    //检查浏览器是否支持
    checkSupport() {
      // 1. 检查getUserMedia
      const hasGetUserMedia = !!(
        navigator.mediaDevices && navigator.mediaDevices.getUserMedia
      );
      if (hasGetUserMedia) {
        return true;
      }
    },
    //停止喊话
    stopShoutEvent: function () {
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach((track) => track.stop());
        this.mediaStream = null;
      }
      this.workletNode && this.workletNode.disconnect();
      this.sourceNode && this.sourceNode.disconnect();
      if (this.requestAnimationId) {
        cancelAnimationFrame(this.requestAnimationId);
        this.requestAnimationId = null;
      }
      if (this.audioContext) {
        this.audioContext.close().then(() => {
          console.log("音频上下文已关闭");
        });
      }
      this.buffer = new Int16Array(0);
      this.startShout = !this.startShout;
      this.sendWs({ action: 14 });

      // const totalLength = this.pcmChunks.reduce(
      //   (acc, chunk) => acc + chunk.length,
      //   0
      // );
      // const pcmData = new Int16Array(totalLength);
      // let offset = 0;
      // this.pcmChunks.forEach((chunk) => {
      //   pcmData.set(chunk, offset);
      //   offset += chunk.length;
      // });

      // // 生成WAV文件
      // const wavBlob = this.createWavFile(pcmData);
      // const audioUrl = URL.createObjectURL(wavBlob);
      // // 或提供下载链接
      // const downloadLink = document.createElement("a");
      // downloadLink.href = audioUrl;
      // downloadLink.download = "recording.wav";
      // downloadLink.textContent = "下载录音";
      // document.body.appendChild(downloadLink);
      // downloadLink.click();

      // // 清理数据
      // this.pcmChunks = [];
      // this.sendWs({ action: 40 });
    },
    createWavFile: function (pcmData) {
      const sampleRate = 16000;
      const bytesPerSample = 2; // 16位 = 2字节
      const channelCount = 1; // 单声道

      // 计算数据大小
      const dataSize = pcmData.length * bytesPerSample;
      const buffer = new ArrayBuffer(44 + dataSize);
      const view = new DataView(buffer);

      // 写入WAV头部
      const writeString = (offset, str) => {
        for (let i = 0; i < str.length; i++) {
          view.setUint8(offset + i, str.charCodeAt(i));
        }
      };

      // RIFF头
      writeString(0, "RIFF");
      view.setUint32(4, 36 + dataSize, true); // 文件总长度-8
      writeString(8, "WAVE");

      // fmt区块
      writeString(12, "fmt ");
      view.setUint32(16, 16, true); // fmt区块长度
      view.setUint16(20, 1, true); // PCM格式
      view.setUint16(22, channelCount, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * channelCount * bytesPerSample, true); // 字节率
      view.setUint16(32, channelCount * bytesPerSample, true); // 块对齐
      view.setUint16(34, 16, true); // 位深度

      // data区块
      writeString(36, "data");
      view.setUint32(40, dataSize, true); // 数据大小

      // 写入PCM数据
      const pcmBytes = new Uint8Array(buffer, 44);
      for (let i = 0; i < pcmData.length; i++) {
        const sample = pcmData[i];
        // 16位小端序写入
        pcmBytes[i * 2] = sample & 0xff; // 低位字节
        pcmBytes[i * 2 + 1] = (sample >> 8) & 0xff; // 高位字节
      }

      return new Blob([buffer], { type: "audio/wav" });
    },
    //改变音量
    changeVolumeSlider: function (e) {
      this.sendWs({ action: 24, value: e });
    },
    //打开文件
    openFile: function (item) {
      item.active = !item.active;
      this.$refs.shoutFile && this.$refs.shoutFile.open(item.active);
    },
    closeFile: function () {
      this.operateList[2].active = false;
    },
    openSet: function (width) {
      this.$refs.shoutFile && this.$refs.shoutFile.changeLeft(width);
    },
    cyclePlay: function (item) {
      item.active = !item.active;
      this.sendWs({ action: 26, value: item.active ? 1 : 0 });
    },
    stopPlay: function (item) {
      if (item && item.classAble) {
        return false;
      }
      if (item) {
        item.classAble = true;
        setTimeout(() => {
          item.classAble = false;
        }, 200);
      }
      this.sendWs({ action: 40 });
      this.sendWs({ action: 14 });
      this.$refs.shoutFile && this.$refs.shoutFile.clearPlay();
    },
  },
};
</script>
<style lang="less" scoped>
@keyframes animator {
  0% {
    box-shadow: 9px 0 #ffde09, 27px 0 #ffffff, 45px 0 #ffffff, 63px 0 #ffffff,
      81px 0 #ffffff;
  }
  25% {
    box-shadow: 9px 0 #ffde09, 27px 0 #ffde09, 45px 0 #ffffff, 63px 0 #ffffff,
      81px 0 #ffffff;
  }
  50% {
    box-shadow: 9px 0 #ffde09, 27px 0 #ffde09, 45px 0 #ffde09, 63px 0 #ffffff,
      81px 0 #ffffff;
  }
  75% {
    box-shadow: 9px 0 #ffde09, 27px 0 #ffde09, 45px 0 #ffde09, 63px 0 #ffde09,
      81px 0 #ffffff;
  }
  100% {
    box-shadow: 9px 0 #ffde09, 27px 0 #ffde09, 45px 0 #ffde09, 63px 0 #ffde09,
      81px 0 #ffde09;
  }
}
@keyframes animator_1 {
  to {
    opacity: 0;
  }
  from {
    opacity: 1;
  }
}
.megaphone-box {
  background-color: rgba(71, 71, 71, 0.5);
  border-radius: 8px;
  padding: 28px 23px;
  position: relative;
  .item-box {
    margin-bottom: 18px;
    display: flex;
    align-items: center;
    &:last-child {
      margin-bottom: 0;
    }
    .megaphone-item-box {
      flex: 1;
      padding: 0 5px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      cursor: pointer;
      .megaphone-value {
        font-size: 30px;
        color: #ffffff;
      }
    }
    .megaphone-item-border {
      width: 1px;
      height: 39px;
      background-color: #cfcfcf;
    }
    .btn-shout {
      font-size: 30px;
      padding: 16.5px 0;
      width: 100%;
      border-radius: 5px;
      background-color: #24272b;

      color: #fff;
      border: none;
    }
    .shout-start-box {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10.5px 32px;
      width: 100%;
      background-color: #24272b;
      overflow: hidden;
      .shout-start-item {
        height: 36px;
        flex-grow: 1;
        overflow: hidden;
        .shout-start-item-animation {
          height: 36px;
          width: 9px;
          border-radius: 6px;
          box-shadow: 9px 0 #ffffff, 27px 0 #ffffff, 45px 0 #ffffff,
            63px 0 #ffffff, 81px 0 #ffffff;
          -webkit-animation: animator 1s linear infinite alternate;
          animation: animator 1s linear infinite alternate;
        }
      }
      .shout-start-icon {
        flex-shrink: 0;
        height: 36px;
        width: 36px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-sizing: border-box;
        cursor: pointer;
        margin-left: 10px;
        .shout-start-icon-item {
          width: 16px;
          height: 16px;
          border-radius: 2px;
          background-color: #fc1717;
          margin: 8px;
          animation: animator_1 2s ease-in-out infinite alternate;
        }
      }
    }
    .btn-megaphone {
      flex: 1;
      height: 75px;
      box-sizing: border-box;
      background-color: #24272b;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 5px;
      margin-right: 7px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
      &.active {
        background-color: #0092f8;
      }
    }
    &.volume-slider {
      height: 36px;
      .volume-slider-item {
        width: 100%;
        background-color: #24272b;
        padding: 0 9px;
        border-radius: 5px;
        .el-slider {
          width: 100%;
        }
      }
    }
  }
}
</style>
<style lang="less">
.megaphone-box {
  .volume-slider {
    .volume-slider-item {
      .el-slider {
        .el-slider__runway {
          height: 3px !important;
          .el-slider__bar {
            height: 3px !important;
          }
          .el-slider__button {
            width: 12px !important;
            height: 12px !important;
            border-width: 1px !important;
          }
        }
      }
    }
  }
}
</style>
