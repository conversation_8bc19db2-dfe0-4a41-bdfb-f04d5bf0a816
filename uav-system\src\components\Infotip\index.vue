<!-- 信息提示、确认框 -->
<template>
  <transition name="el-fade-in-linear">
    <div class="info-tip custom-info-tip" v-show="isShow">
      <div class="info-tip-body" @click.stop>
        <!-- 关闭按钮 -->
        <div class="shut-icon" @click="shut">
          <i class="el-icon-close"></i>
        </div>

        <!-- 头部 -->
        <div class="info-tip-header">
          {{ title }}
        </div>

        <!-- 提示内容 -->
        <div class="info-tip-main" v-html="message"></div>

        <!-- 滚动确认条 -->
        <div class="info-tip-footer" v-if="type == 'scroll'">
          <scroll-confirm @change="confirmChange"></scroll-confirm>
        </div>

        <!-- 按钮确认 -->
        <div class="mt10" style="text-align: center" v-if="type == 'button'">
          <el-button @click="confirmChange(true)" size="mini" type="primary">
            {{$languagePackage.components.infoTip.confirm}}
          </el-button>
          <el-button @click="confirmChange(false)" size="mini">
            {{$languagePackage.components.infoTip.cancel}}
          </el-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import scrollConfirm from "../scrollConfirm/index.vue";
export default {
  name: "info-tips",
  components: {
    scrollConfirm,
  },
  props: {},
  data() {
    return {
      isShow: false,
    };
  },
  methods: {
    show: function () {
      this.isShow = true;
    },
    shut: function () {
      this.isShow = false;
      this.destroy();
    },
    confirmChange: function (state) {
      if (state) {
        // 关闭并销毁
        this.autoShut && this.shut();
        this.submitCallback && this.submitCallback(state);
      } else {
        this.type == "button" && this.shut();
        this.cancelCallback && this.cancelCallback(state);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.info-tip {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  // background-color: rgba(0, 0, 0, 0.2);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  .info-tip-body {
    width: 400px;
    min-height: 120px;
    background-color: rgba(0, 0, 0, 0.9);
    position: relative;
    color: #fff;
    border-radius: 8px;
    .shut-icon {
      position: absolute;
      right: 10px;
      top: 7px;
      border: 1px solid #ccc;
      width: 14px;
      height: 14px;
      cursor: pointer;
      border-radius: 2px;
      color: #fff;
      font-weight: 700;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }
    .info-tip-header {
      padding: 14px 34px 7px 20px;
      font-weight: 400;
    }
    .info-tip-main {
      text-align: center;
      font-size: 14px;
      line-height: 2em;
    }
    .info-tip-footer {
      padding: 16px 30px;
      // margin-top: 10px;
    }
  }
}
</style>