<template>
  <el-date-picker
    @focus="dateFocus"
    ref="datePicker"
    :popper-class="popperClass"
    :value-format="valueFormat"
    v-model="time"
    @change="timeChange"
    prefix-icon=""
    :type="type"
    range-separator=""
    :start-placeholder="startTimePlaceholder"
    :end-placeholder="endTimePlaceholder"
  >
    <!-- :picker-options="pickerOptions" -->
  </el-date-picker>
</template>

<script>
// import { getPickerOptions } from "@/utils/date";
export default {
  props: {
    startPlaceholder: String,
    endPlaceholder: String,
    startTime: [String, Number], // 开始时间
    endTime: [String, Number], // 结束时间
    value: {
      type: Array,
      default: () => {
        return [];
      },
    },
    valueFormat: {
      type: String,
      default: "yyyy-MM-dd hh:mm:ss",
    },
    type: {
      type: String,
      default: "datetimerange", // daterange,datetimerange
    },
    isTimestamp: <PERSON><PERSON><PERSON>,
    isNativeFooter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      time: this.value,
      isGetFocus: false,
      popperClass: "date-poper",
      startTimePlaceholder: this.startPlaceholder,
      endTimePlaceholder: this.endPlaceholder
    };
  },
  created() {
    let item = this.$languagePackage.components.twiceDatePicker;
    if (!this.startPlaceholder) {
      this.startTimePlaceholder = item.start;
    }
    if (!this.endPlaceholder) {
      this.endTimePlaceholder = item.end;
    }
  },
  mounted() {},
  methods: {
    dateFocus: function () {
      if (!this.isNativeFooter) {
        return false;
      }
      this.$nextTick(() => {
        if (!this.isGetFocus) {
          this.isGetFocus = true;
          let el = document.querySelectorAll(".date-poper");
          if (el.length <= 1) {
            el = el[0];
          } else {
            let index = el.length;
            let className = `custom-date-${index}`;
            el[index - 1].classList.add(className);

            let elClass = "." + className;
            el = document.querySelector(elClass);
          }

          if (!el) {
            return false;
          }

          // 获取底部，并设置样式
          let footer = el.querySelector(".el-picker-panel__footer");
          footer.style.display = "flex";
          footer.style.justifyContent = "flex-end";

          // 隐藏第一个按钮
          let button = footer.querySelectorAll("button");
          button[0].style.display = "none";

          let divElm = document.createElement("div");
          
          let text = this.$languagePackage.components.twiceDatePicker.cancelText
          divElm.innerHTML = `<div class="el-button--custom">${text}</div>`;
          footer.appendChild(divElm);

          this.$nextTick(() => {
            let buttonCustom = footer.querySelector(".el-button--custom");
            buttonCustom.onclick = () => {
              this.$refs.datePicker.handleClose();
            };
          });
        }
      });
    },

    // 时间发生变化时
    timeChange: function (time) {
      this.$emit("input", time);
      let start = "",
        end = "";
      if (time) {
        if (this.isTimestamp) {
          start = new Date(time[0]).getTime();
          end = new Date(time[1]).getTime();
        } else {
          start = time[0];
          end = time[1];
        }
      }
      this.$emit("update:startTime", start);
      this.$emit("update:endTime", end);

      this.$emit("change", time);
    },
  },
};
</script>

