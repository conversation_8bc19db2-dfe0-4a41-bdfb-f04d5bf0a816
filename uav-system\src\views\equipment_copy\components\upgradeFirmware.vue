<template>
  <div class="upgradeFirmware">
    <el-dialog
      :title="language.dialogTitle"
      :visible.sync="show"
      :center="true"
      custom-class="upgrade-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :destroy-on-close="true"
      width="650px"
    >
      <el-form label-width="120px" :model="form" :rules="rules" ref="form">
        <el-form-item :label="language.fileTitle" prop="files">
          <el-upload
            class="upload-demo"
            action=""
            :on-change="changeUpload"
            :before-remove="beforeRemove"
            accept=".wkimg"
            :auto-upload="false"
          >
            <div class="">
              <el-button size="small" type="primary">{{
                language.clickChoose
              }}</el-button>
              <span
                class="el-upload__tip"
                style="color: #ccc; font-size: 14px; margin-left: 20px"
              >
                {{ language.uploadTip }}
              </span>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="固件类型" prop="type">
          <el-input v-model="form.type" placeholder="选择文件自动识别" readonly style="width:90%"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <slot name="footer">
          <el-button type="primary" @click="submit">{{
            language.submit
          }}</el-button>
          <el-button @click="closeDialog">{{ language.cancel }}</el-button>
        </slot>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      show: false,
      form: {
        files: "",
        type:''
      },
      rules: {
        files: [
          { required: true, trigger: "change", message: "请上传固件文件" },
        ],
        type: [
          { required: true, trigger: "blur", message: "请输入对应的固件类型" },
        ],
      },
      websocket: "",
      uploadLoading: "",
    };
  },
  computed: {
    language() {
      return this.$languagePackage.equipment.upgradeFirmware;
    },
  },
  created() {
    this.rules.files[0].message = this.language.errorTip;
  },
  methods: {
    open(websocket) {
      this.websocket = websocket;
      this.show = true;
    },
    changeUpload(file, fileList) {
      if (fileList.length > 1) {
        fileList.shift();
      }
      this.form.files = file;
      let reader = new FileReader();
      reader.readAsArrayBuffer(file.raw);
      let self = this;
      reader.onload = (e) => {
        let enc = new TextDecoder("utf-8");
        let uint8_msg = new Uint8Array(e.target.result);
        let lenList = uint8_msg.slice(53, 57);
        let len =
          lenList[0] +
          lenList[1] * 256 +
          lenList[2] * 256 * 256 +
          lenList[3] * 256 * 256 +
          57;
        if (len - 57 <= 0 || len > 1024 * 1024 * 10) {
          self.$message.error({
            message: "文件选择错误，请重新选择",
          });
          return reject(false);
        }
        let decodedString = enc.decode(uint8_msg.slice(57, len));
        let data = JSON.parse(decodedString);
        self.form.type=data.deviceId
        console.log(123,data)
      }
    },
    beforeRemove(file){
      this.form={
        files:'',
        type:''
      }
      console.log(file)

    },
    submit() {
      let data={
        file_url:'https://fly.walkera.cn/ff-bate/rom/download?name=ac1a37849097f5f873ffd8efa8bda8ca.wkimg'
      }
      this.websocket && this.websocket.manualSend(data, 408);
      // this.$refs.form.validate((valid) => {
      //   if (valid) {
      //     this.uploadLoading = this.$loading({
      //       text: this.language.loadingText,
      //       spinner: "el-icon-loading",
      //       background: "rgba(0, 0, 0, 0.7)",
      //     });
      //     let raw = this.form.files.raw;
      //     if (this.websocket) {
      //       this.websocket.ws && this.websocket.ws.send(raw);
      //     }
      //     //   this.websocket && this.websocket.manualSend(data, 406);
      //   }
      // });
    },
    closeDialog() {
      this.form = {
        files: "",
        type:''
      };
      this.websocket = "";
      this.show = false;
      if (this.uploadLoading) {
        this.uploadLoading.close();
        this.uploadLoading = "";
      }
      this.$emit("refresh", "");
    },
  },
};
</script>
<style lang="less">
.upgradeFirmware {
  .upgrade-dialog {
    .el-dialog__header {
      border-bottom: 1px solid #0c31ac;
      .el-dialog__title {
        font-size: 24px !important;
        font-weight: 550;
      }
    }
    .el-dialog__body {
      padding-bottom: 0;
      .el-upload-list__item {
        transition-duration: 0.1s;
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .upgradeFirmware {
    .upgrade-dialog {
      .el-dialog__header {
        .el-dialog__title {
          font-size: @zoomIndex * 24px !important;
        }
      }
    }
  }
}
</style>