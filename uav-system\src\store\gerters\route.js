const routeData = {
    state: {
        fence: {},
        fenceId: "",
        code: 0,
        operateFItem: '',
        typeList: '',
        checkType: '',
        routeItem: '',
        estTime: 0,
        firstPoint: [],
        lastPoint: []
    },
    mutations: {
        setFence(state, val) {
            state.fence = val
        },
        fenceItemId(state, val) {
            state.fenceId = val
        },
        changeCode(state, val) {
            state.code = val
        },
        operateFItem(state, val) {
            state.operateFItem = val
        },
        typeList(state, val) {
            state.typeList = val
        },
        checkType(state, val) {
            state.checkType = val
        },
        routeItem(state, val) {
            state.routeItem = val
        },
        setEstTime(state, val) {
            state.estTime = val
        },
        setFirstPoint(state, val) {
            state.firstPoint = val
        },
        setLastPoint(state, val) {
            state.lastPoint = val
        }
    },
    actions: {

    }
}
export default routeData