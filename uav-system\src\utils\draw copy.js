import initMaps from "./maps";
import { renderPolyline } from './orthophoto'
import { orthoPhotoComputer } from './orthoPhotoComputer'
import { gcj02_to_wgs84, wgs84_to_gcj02 } from './wgs84_to_gcj02'
import { computedMethod, computeCenter, computedDistance } from './computedMap'
export default {
    methods: {
        //航线:绘制点
        drawMarker(text, center) {
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
                zIndex: 55,
            };
            let marker = initMaps.drawMarker(text, center, "marker-edit", params);
            marker.setMap(this.map);
            marker.id = text;
            this.markers.push(marker);
            this.markerClick(marker);
            // if (this.noChange) {
            //     this.allActionList.push({ actionList: [] });
            //     this.heights.push(this.routeForm.default_height);
            // }
            if (!this.changeCode) {
                if (!this.importCode) {
                    this.routeForm.point_json.push({
                        lng: marker.getPosition().KL,
                        lat: marker.getPosition().kT,
                    });
                }
                marker.on("dragstart", this.markerDragStart);
                marker.on("dragging", this.markerDrag);
                marker.on("dragend", this.markerDragEnd);
                marker.on("click", this.markerClick);
            }
            if (this.markers.length > 1) {
                this.distance += computedMethod(1, {
                    point1: this.markers[this.markers.length - 1].getPosition(),
                    point2: this.markers[this.markers.length - 2].getPosition(),
                });
                this.estTime = this.distance / this.routeForm.auto_speed + this.markers.length;
            }
        },
        //航线、围栏：点点击事件
        markerClick(e) {
            if (this.drawend) {
                if (e.target) {
                    this.clickId = e.target.id;
                } else {
                    this.clickId = e.id;
                }
                // let back = "";
                if (this.code == 2) {
                    for (let index = 0; index < this.markers.length; index++) {
                        let content = this.markers[index].getContent();
                        if (this.clickId == this.markers[index].id) {
                            if (content.indexOf("active") == -1) {
                                let str = content.split("marker-o-edit");
                                content = str[0] + "marker-o-edit active" + str[1];
                            }
                        } else {
                            content = content.replace(" active", "");
                        }
                        this.markers[index].setContent(content);
                    }
                } else {
                    for (let index = 0; index < this.markers.length; index++) {
                        let content = this.markers[index].getContent();
                        if (this.clickId == this.markers[index].id) {
                            if (content.indexOf("active") == -1) {
                                let str = content.split("marker-edit");
                                content = str[0] + "marker-edit active" + str[1];
                            }
                        } else {
                            content = content.replace(" active", "");
                        }
                        this.markers[index].setContent(content);
                    }
                }
            }
            // else {
            //   // for (let index = 0; index < this.markers.length; index++) {
            //   //   let style = this.markers[index]._opts.style;
            //   //   style["background-color"] = "#FFFFFF";
            //   //   style["color"] = "#000000";
            //   //   this.markers[index].setStyle(style);
            //   // }
            // }
        },
        //航点拖拽开始
        markerDragStart(e) {
            this.routeFormCode = true;
            let index = this.markers.findIndex((item) => {
                return item.id == e.target.id;
            });
            this.startPoints = {
                lng: this.routeForm.point_json[index].lng,
                lat: this.routeForm.point_json[index].lat,
            };
            if (this.markers.length > 5) {
                if (this.line) {
                    this.map.remove(this.line);
                    this.line = "";
                }
                if (this.line1) {
                    this.map.remove(this.line1);
                    this.line1 = "";
                }
                if (this.line2) {
                    this.map.remove(this.line2);
                    this.line2 = "";
                }
                if (this.line3) {
                    this.map.remove(this.line3);
                    this.line3 = "";
                }
                if (index == 0 || index == 1) {
                    let paths = [];
                    for (let i = index + 1; i < this.markers.length; i++) {
                        paths.push(this.markers[i].getPosition());
                    }
                    this.line1 = initMaps.drawPolyline(paths);
                    this.line1.setMap(this.map);
                } else if (
                    index == this.markers.length - 1 ||
                    index == this.markers.length - 2
                ) {
                    let paths = [];
                    for (let i = 0; i < index; i++) {
                        paths.push(this.markers[i].getPosition());
                    }
                    this.line1 = initMaps.drawPolyline(paths);
                    this.line1.setMap(this.map);
                } else {
                    let paths = [];
                    for (let i = 0; i < index; i++) {
                        paths.push(this.markers[i].getPosition());
                    }
                    this.line1 = initMaps.drawPolyline(paths);
                    this.line1.setMap(this.map);
                    let paths1 = [];
                    for (let i = index + 1; i < this.markers.length; i++) {
                        paths1.push(this.markers[i].getPosition());
                    }
                    this.line2 = initMaps.drawPolyline(paths1);
                    this.line2.setMap(this.map);
                }
                this.editLine(index);
            }
        },
        //航点拖拽结束
        markerDragEnd(e) {
            let index = this.markers.findIndex((item) => {
                return item.id == e.target.id;
            });
            if (this.line1) {
                this.map.remove(this.line1);
                this.line1 = "";
            }
            if (this.line2) {
                this.map.remove(this.line2);
                this.line2 = "";
            }
            if (this.line3) {
                this.map.remove(this.line3);
                this.line3 = "";
            }
            let a = computedMethod(2, {
                point1: e.lnglat,
                fence: this.fenceItem.paths,
            });
            if (!a) {
                this.$message.error(this.routeLanguage.placeholder4);
                this.markers[index].setPosition(
                    new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
                );
                this.routeForm.point_json[index].lng = this.startPoints.lng;
                this.routeForm.point_json[index].lat = this.startPoints.lat;
                this.drawline();
                this.editCenterpoint(index);
                this.computedData();
                return false;
            }
            if (this.routeForm.point_json.length > 1) {
                let b = "";
                let isCreate = false;
                if (index == 0) {
                    b = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index + 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    isCreate = computedDistance(
                        e.lnglat,
                        this.markers[index + 1].getPosition()
                    );
                } else if (index == this.routeForm.point_json.length - 1) {
                    b = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    isCreate = computedDistance(
                        e.lnglat,
                        this.markers[index - 1].getPosition()
                    );
                } else {
                    let b1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index + 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let b2 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    b = b1 || b2;
                    let isCreate1 = computedDistance(
                        e.lnglat,
                        this.markers[index + 1].getPosition()
                    );
                    let isCreate2 = computedDistance(
                        e.lnglat,
                        this.markers[index - 1].getPosition()
                    );
                    isCreate = isCreate1 && isCreate2;
                }
                if (b) {
                    this.$message.error(this.routeLanguage.placeholder5);
                    this.markers[index].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.routeForm.point_json[index] = this.startPoints;
                    this.drawline();
                    this.editCenterpoint(index);
                    this.computedData();
                    return false;
                }
                if (!isCreate) {
                    this.$message.warning(this.routeLanguage.errorMessage8);
                    this.markers[index].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.routeForm.point_json[index] = this.startPoints;
                    this.drawline();
                    this.editCenterpoint(index);
                    this.computedData();
                    return false;
                }
            }
            if (this.markers.length > 5) {
                let paths = [];
                for (let i = 0; i < this.markers.length; i++) {
                    paths.push(this.markers[i].getPosition());
                }
                this.line = initMaps.drawPolyline(paths);
                this.line.setMap(this.map);
            }
            this.computedData();
            this.deepCopy();
        },
        //航线:点拖拽事件
        async markerDrag(e) {
            let index = this.markers.findIndex((item) => {
                return item.id == e.target.id;
            });
            let b = this.pointCodes.indexOf(e.target.id);
            this.markerClick(e);
            if (b != -1) {
                this.pointCodes.splice(b, 1);
                this.pointCodes.push(e.target.id);
            }
            this.markers[index].setPosition(e.lnglat);
            this.routeForm.point_json[index].lat = e.lnglat.kT;
            this.routeForm.point_json[index].lng = e.lnglat.KL;
            if (this.markers.length > 1) {
                if (this.markers.length > 5) {
                    this.editLine(index);
                    this.editCenterpoint(index);
                    // this.computedData();
                    return false;
                }
                this.drawline();
                this.editCenterpoint(index);
                // this.computedData();
            }
        },
        //拖拽编辑线段
        editLine(index) {
            if (this.line3) {
                this.map.remove(this.line3);
                this.line3 = "";
            }
            let paths = [];
            if (index == 0) {
                paths = [this.markers[0].getPosition(), this.markers[1].getPosition()];
            } else if (index == this.markers.length - 1) {
                paths = [
                    this.markers[this.markers.length - 2].getPosition(),
                    this.markers[this.markers.length - 1].getPosition(),
                ];
            } else {
                paths = [
                    this.markers[index - 1].getPosition(),
                    this.markers[index].getPosition(),
                    this.markers[index + 1].getPosition(),
                ];
            }
            this.line3 = initMaps.drawPolyline(paths);
            this.line3.setMap(this.map);
        },
        //航线:绘制线段
        drawline() {
            if (this.line) {
                // this.line.setPath(this.routeForm.point_json)
                this.map.remove(this.line);
                this.line = "";
            }
            let paths = [];
            for (let index = 0; index < this.markers.length; index++) {
                paths.push(this.markers[index].getPosition());
            }
            this.line = initMaps.drawPolyline(paths);
            this.line.setMap(this.map);
            // var polyEditor = new AMap.PolylineEditor(this.map, this.line);
        },
        //航线:设置两个点的中心点
        addCenterpoint(index) {
            let lat =
                (parseFloat(this.markers[index - 1].getPosition().lat) +
                    parseFloat(this.markers[index - 2].getPosition().lat)) /
                2;
            let lng =
                (parseFloat(this.markers[index - 1].getPosition().lng) +
                    parseFloat(this.markers[index - 2].getPosition().lng)) /
                2;
            let center = [lng, lat];
            let params = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker("+", center, "marker-edit-i", params);
            addMarker.setMap(this.map);
            addMarker.id = index - 1;
            this.addMarkers.push(addMarker);
            addMarker.on("click", this.clickAdd);
        },
        //航线:修改两点中的中心点
        editCenterpoint(index) {
            if (index == 0) {
                let lat =
                    (parseFloat(this.markers[index + 1].getPosition().lat) +
                        parseFloat(this.markers[index].getPosition().lat)) /
                    2;
                let lng =
                    (parseFloat(this.markers[index + 1].getPosition().lng) +
                        parseFloat(this.markers[index].getPosition().lng)) /
                    2;
                let center = [lng, lat];
                this.addMarkers[index].setPosition(center);
            } else {
                let lat =
                    (parseFloat(this.markers[index].getPosition().lat) +
                        parseFloat(this.markers[index - 1].getPosition().lat)) /
                    2;
                let lng =
                    (parseFloat(this.markers[index].getPosition().lng) +
                        parseFloat(this.markers[index - 1].getPosition().lng)) /
                    2;
                let center = [lng, lat];
                this.addMarkers[index - 1].setPosition(center);
                if (index < this.markers.length - 1) {
                    let lat =
                        (parseFloat(this.markers[index + 1].getPosition().lat) +
                            parseFloat(this.markers[index].getPosition().lat)) /
                        2;
                    let lng =
                        (parseFloat(this.markers[index + 1].getPosition().lng) +
                            parseFloat(this.markers[index].getPosition().lng)) /
                        2;
                    let center = [lng, lat];
                    this.addMarkers[index].setPosition(center);
                }
            }
            // if(index<this.markers.length-1)
        },
        //航线:点击+触发添加事件
        clickAdd(e) {
            this.num++;
            let index = e.target.id;
            this.map.remove(this.addMarkers[index - 1]);
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
                zIndex: 55,
            };
            let marker = initMaps.drawMarker(
                index + 1,
                e.lnglat,
                "marker-edit",
                params
            );
            marker.id = index + 1;
            marker.setMap(this.map);
            marker.on("dragstart", this.markerDragStart);
            marker.on("dragging", this.markerDrag);
            marker.on("dragend", this.markerDragEnd);
            this.markers.splice(index, 0, marker);
            this.routeForm.point_json.splice(index, 0, marker.getPosition());
            if (this.routeItem) {
                this.heights.push(this.routeForm.default_height);
                this.allActionList.push({ actionList: [] });
                this.pointCodes.push(index);
                this.pointCodes.pop();
            }
            for (let i = index + 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                    "<div class='marker-edit'><span class='text'>" +
                    (i + 1) +
                    "</span></div>";
                this.markers[i].setContent(content);
            }
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            this.drawline();
            for (let n = index; n < this.addMarkers.length; n++) {
                this.addMarkers[n].id = this.addMarkers[n].id + 1;
            }
            let lat =
                (parseFloat(this.markers[index - 1].getPosition().lat) +
                    parseFloat(this.markers[index].getPosition().lat)) /
                2;
            let lng =
                (parseFloat(this.markers[index - 1].getPosition().lng) +
                    parseFloat(this.markers[index].getPosition().lng)) /
                2;
            let center = [lng, lat];
            let params1 = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-edit-i",
                params1
            );
            addMarker.setMap(this.map);
            addMarker.id = index;
            this.addMarkers.splice(index - 1, 1, addMarker);
            addMarker.on("click", this.clickAdd);
            let lat1 =
                (parseFloat(this.markers[index].getPosition().lat) +
                    parseFloat(this.markers[index + 1].getPosition().lat)) /
                2;
            let lng1 =
                (parseFloat(this.markers[index].getPosition().lng) +
                    parseFloat(this.markers[index + 1].getPosition().lng)) /
                2;
            let center1 = [lng1, lat1];
            let addMarker1 = initMaps.drawMarker(
                "+",
                center1,
                "marker-edit-i",
                params1
            );
            addMarker1.setMap(this.map);
            addMarker1.id = index + 1;
            this.addMarkers.splice(index, 0, addMarker1);
            addMarker1.on("click", this.clickAdd);
            this.deepCopy();
        },
        //围栏：绘制标记点
        drawPoint(index, e) {
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
            };
            let marker = initMaps.drawMarker(index, e, "marker-o-edit", params);
            if (this.markers.length > 2) {
                let a = this.isLineCross(
                    this.markers[0].getPosition(),
                    marker.getPosition(),
                    0
                );
                let b = this.isLineCross(
                    this.markers[this.markers.length - 1].getPosition(),
                    marker.getPosition(),
                    this.markers.length - 1
                );
                if (a || b) {
                    this.$message.warning({
                        message: this.routeLanguage.messageInfo2,
                        duration: 1000,
                    });
                    this.num--;
                    return;
                }
            }
            marker.id = index;
            this.map.add(marker);
            this.markers.push(marker);
            this.fenceForm.point_json.push(marker.getPosition());
            marker.on("dragstart", this.markerDragStart1);
            marker.on("dragging", this.markerDrag1);
            marker.on("dragend", this.markerDragEnd1);
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            if (this.markers.length > 1) {
                this.drawAddPoint();
            }
        },
        //围栏：绘制多边形
        drawPolypon() {
            if (this.polypon) {
                this.map.remove(this.polypon);
            }
            this.polypon = initMaps.drawPolypon(this.fenceForm.point_json);
            this.polypon.setMap(this.map);
            // this.map.add(this.polypon)
        },
        //围栏：拖拽点开始
        markerDragStart1(e) {
            this.routeFormCode = true;
            let index = e.target.id - 1;
            this.startPoints = this.fenceForm.point_json[index];
            this.markerClick(e);
        },
        //围栏：拖拽点拖拽中触发事件
        markerDrag1(e) {
            let b = this.pointCodes.indexOf(e.target.id);
            if (b != -1) {
                let a = document
                    .getElementsByClassName("pointsClass")[0]
                    .getElementsByClassName("el-collapse-item")[e.target.id - 1];
                a.setAttribute("class", "el-collapse-item");
                this.pointCodes.splice(b, 1);
            }
            let index = e.target.id;
            this.position = this.markers[index - 1].getPosition();
            this.markers[index - 1].setPosition(e.lnglat);
            this.fenceForm.point_json[index - 1] = e.lnglat;
            this.drawPolypon();
            if (b != -1) {
                this.pointCodes.push(e.target.id);
                let a = document
                    .getElementsByClassName("pointsClass")[0]
                    .getElementsByClassName("el-collapse-item")[e.target.id - 1];
                a.setAttribute("class", "el-collapse-item is-active");
            }
            if (this.fenceForm.point_json.length > 1) {
                this.drawEditPoint(index);
            }
        },
        //围栏：拖拽点结束
        markerDragEnd1(e) {
            if (this.markers.length > 3) {
                let index = e.target.id;
                let a, b;
                if (index == 1) {
                    a = this.isLineCross1(
                        this.markers[0].getPosition(),
                        this.markers[index].getPosition(),
                        0
                    );
                    b = this.isLineCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[0].getPosition(),
                        0,
                        1
                    );
                } else if (index == this.markers.length) {
                    a = this.isLineCross1(
                        this.markers[0].getPosition(),
                        this.markers[this.markers.length - 1].getPosition(),
                        0,
                        1
                    );
                    b = this.isLineCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        this.markers.length - 2
                    );
                } else {
                    a = this.isLineCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        index - 2
                    );
                    b = this.isLineCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index].getPosition(),
                        index - 1
                    );
                }
                if (a || b) {
                    this.markers[index - 1].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.fenceForm.point_json[index - 1] = this.startPoints;
                    this.drawPolypon();
                    this.drawEditPoint(index);
                    this.$message.warning({
                        message: this.routeLanguage.messageInfo2,
                        duration: 1000,
                    });
                    return false;
                }
            }
            this.deepCopy();
        },
        //围栏：添加中点，方便直接添加
        drawAddPoint() {
            let center = computeCenter([
                this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
                this.fenceForm.point_json[this.fenceForm.point_json.length - 2],
            ]);
            let params = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-o-edit-i",
                params
            );
            addMarker.id = this.fenceForm.point_json.length - 1;
            addMarker.setMap(this.map);
            this.addMarkers.push(addMarker);
            addMarker.on("click", this.addClick);
            let labelMarker = this.fenceListPoint([
                this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
                this.fenceForm.point_json[this.fenceForm.point_json.length - 2],
            ]);
            labelMarker.setMap(this.map);
            this.distancePoint.push(labelMarker);
            if (this.fenceForm.point_json.length > 2) {
                let center1 = computeCenter([
                    this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
                    this.fenceForm.point_json[0],
                ]);
                if (this.addMarkers.length == this.fenceForm.point_json.length) {
                    this.addMarkers[this.addMarkers.length - 2].setPosition(center1);
                    this.addMarkers[this.addMarkers.length - 2].id =
                        this.fenceForm.point_json.length;
                    let temp = this.addMarkers[this.addMarkers.length - 1];
                    this.addMarkers[this.addMarkers.length - 1] =
                        this.addMarkers[this.addMarkers.length - 2];
                    this.addMarkers[this.addMarkers.length - 2] = temp;
                } else {
                    let addMarker1 = initMaps.drawMarker(
                        "+",
                        center1,
                        "marker-o-edit-i",
                        params
                    );
                    addMarker1.id = this.fenceForm.point_json.length;
                    addMarker1.setMap(this.map);
                    this.addMarkers.push(addMarker1);
                    addMarker1.on("click", this.addClick);
                }
                if (this.distancePoint.length > 3) {
                    this.map.remove(this.distancePoint[this.distancePoint.length - 2]);
                    this.distancePoint.splice(this.distancePoint.length - 2, 1);
                }
                let labelMarker = this.fenceListPoint([
                    this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
                    this.fenceForm.point_json[0],
                ]);
                labelMarker.setMap(this.map);
                this.distancePoint.push(labelMarker);
            }
        },
        //围栏：修改中点
        drawEditPoint(index) {
            if (this.fenceForm.point_json.length < 3) {
                let center = computeCenter([
                    this.fenceForm.point_json[0],
                    this.fenceForm.point_json[1],
                ]);
                this.addMarkers[0].setPosition(center);
                this.distancePoint[0].setPosition(center);
                let labelMarker = this.fenceListPoint([
                    this.fenceForm.point_json[0],
                    this.fenceForm.point_json[1],
                ]);
                this.distancePoint[0].setContent(labelMarker._opts.content);
                this.distancePoint[0].setStyle(labelMarker._opts.style);
                this.distancePoint[0].setOffset(labelMarker.getOffset());
            } else {
                if (index == 1) {
                    let center = computeCenter([
                        this.fenceForm.point_json[index],
                        this.fenceForm.point_json[index - 1],
                    ]);
                    this.addMarkers[index - 1].setPosition(center);
                    this.distancePoint[index - 1].setPosition(center);
                    let labelMarker = this.fenceListPoint([
                        this.fenceForm.point_json[index],
                        this.fenceForm.point_json[index - 1],
                    ]);
                    this.distancePoint[index - 1].setContent(labelMarker._opts.content);
                    this.distancePoint[index - 1].setStyle(labelMarker._opts.style);
                    this.distancePoint[index - 1].setOffset(labelMarker.getOffset());
                    let center1 = computeCenter([
                        this.fenceForm.point_json[0],
                        this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
                    ]);
                    this.addMarkers[this.addMarkers.length - 1].setPosition(center1);
                    this.distancePoint[this.distancePoint.length - 1].setPosition(
                        center1
                    );
                    let labelMarker1 = this.fenceListPoint([
                        this.fenceForm.point_json[0],
                        this.fenceForm.point_json[this.fenceForm.point_json.length - 1],
                    ]);
                    this.distancePoint[this.distancePoint.length - 1].setContent(
                        labelMarker1._opts.content
                    );
                    this.distancePoint[this.distancePoint.length - 1].setStyle(
                        labelMarker1._opts.style
                    );
                    this.distancePoint[this.distancePoint.length - 1].setOffset(
                        labelMarker1.getOffset()
                    );
                } else if (index > 1) {
                    let center = computeCenter([
                        this.fenceForm.point_json[index - 1],
                        this.fenceForm.point_json[index - 2],
                    ]);
                    this.addMarkers[index - 2].setPosition(center);
                    this.distancePoint[index - 2].setPosition(center);
                    let labelMarker = this.fenceListPoint([
                        this.fenceForm.point_json[index - 1],
                        this.fenceForm.point_json[index - 2],
                    ]);
                    this.distancePoint[index - 2].setContent(labelMarker._opts.content);
                    this.distancePoint[index - 2].setStyle(labelMarker._opts.style);
                    this.distancePoint[index - 2].setOffset(labelMarker.getOffset());
                    if (index == this.fenceForm.point_json.length) {
                        let center = computeCenter([
                            this.fenceForm.point_json[index - 1],
                            this.fenceForm.point_json[0],
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                        this.distancePoint[index - 1].setPosition(center);
                        let labelMarker = this.fenceListPoint([
                            this.fenceForm.point_json[index - 1],
                            this.fenceForm.point_json[0],
                        ]);
                        this.distancePoint[index - 1].setContent(labelMarker._opts.content);
                        this.distancePoint[index - 1].setStyle(labelMarker._opts.style);
                        this.distancePoint[index - 1].setOffset(labelMarker.getOffset());
                    } else {
                        let center = computeCenter([
                            this.fenceForm.point_json[index],
                            this.fenceForm.point_json[index - 1],
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                        this.distancePoint[index - 1].setPosition(center);
                        let labelMarker = this.fenceListPoint([
                            this.fenceForm.point_json[index],
                            this.fenceForm.point_json[index - 1],
                        ]);
                        this.distancePoint[index - 1].setContent(labelMarker._opts.content);
                        this.distancePoint[index - 1].setStyle(labelMarker._opts.style);
                        this.distancePoint[index - 1].setOffset(labelMarker.getOffset());
                    }
                }
            }
        },
        //围栏：点击触发添加事件
        addClick(e) {
            this.num++;
            let index = e.target.id;
            this.map.remove(this.addMarkers[index - 1]);
            this.map.remove(this.distancePoint[index - 1]);
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
            };
            let marker = initMaps.drawMarker(
                index + 1,
                e.lnglat,
                "marker-o-edit",
                params
            );
            marker.id = index + 1;
            marker.setMap(this.map);
            marker.on("dragstart", this.markerDragStart1);
            marker.on("dragging", this.markerDrag1);
            marker.on("dragend", this.markerDragEnd1);
            this.markers.splice(index, 0, marker);
            this.fenceForm.point_json.splice(index, 0, marker.getPosition());
            if (this.operateFItem) {
                this.pointCodes.push(index);
                this.pointCodes.pop();
            }
            for (let i = index + 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                    "<div class='marker-o-edit'><span class='text'>" +
                    (i + 1) +
                    "</span></div>";
                this.markers[i].setContent(content);
            }
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            this.drawPolypon();
            for (let n = index; n < this.addMarkers.length; n++) {
                this.addMarkers[n].id = this.addMarkers[n].id + 1;
            }
            let lat =
                (parseFloat(this.fenceForm.point_json[index - 1].lat) +
                    parseFloat(this.fenceForm.point_json[index].lat)) /
                2;
            let lng =
                (parseFloat(this.fenceForm.point_json[index - 1].lng) +
                    parseFloat(this.fenceForm.point_json[index].lng)) /
                2;
            let center = [lng, lat];
            let params1 = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-o-edit-i",
                params1
            );
            addMarker.setMap(this.map);
            addMarker.id = index;
            this.addMarkers.splice(index - 1, 1, addMarker);
            addMarker.on("click", this.addClick);
            let labelMarker = this.fenceListPoint([
                this.fenceForm.point_json[index - 1],
                this.fenceForm.point_json[index],
            ]);
            labelMarker.setMap(this.map);
            this.distancePoint.splice(index - 1, 1, labelMarker);
            if (index == this.addMarkers.length) {
                let lat1 =
                    (parseFloat(this.fenceForm.point_json[index].lat) +
                        parseFloat(this.fenceForm.point_json[0].lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.fenceForm.point_json[index].lng) +
                        parseFloat(this.fenceForm.point_json[0].lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-o-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addClick);
                let labelMarker = this.fenceListPoint([
                    this.fenceForm.point_json[index],
                    this.fenceForm.point_json[0],
                ]);
                labelMarker.setMap(this.map);
                this.distancePoint.splice(index, 0, labelMarker);
            } else {
                let lat1 =
                    (parseFloat(this.fenceForm.point_json[index].lat) +
                        parseFloat(this.fenceForm.point_json[index + 1].lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.fenceForm.point_json[index].lng) +
                        parseFloat(this.fenceForm.point_json[index + 1].lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-o-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addClick);
                let labelMarker = this.fenceListPoint([
                    this.fenceForm.point_json[index],
                    this.fenceForm.point_json[index + 1],
                ]);
                labelMarker.setMap(this.map);
                this.distancePoint.splice(index, 0, labelMarker);
            }
            this.deepCopy();
        },
        //围栏：判断是否出现交叉
        isLineCross(marker1, marker2, index) {
            let arr1 = this.fenceForm.point_json.slice(0, index);
            let arr2 = this.fenceForm.point_json.slice(
                index + 1,
                this.fenceForm.point_json.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //围栏：拖拽判断是否出现交叉
        isLineCross1(marker1, marker2, index, num) {
            let arr1 = this.fenceForm.point_json.slice(0, index);
            let arr2 = this.fenceForm.point_json.slice(
                num ? index + 1 : index + 2,
                num ?
                this.fenceForm.point_json.length - 1 :
                this.fenceForm.point_json.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //正射影像绘制点
        drawOrthoPoint(text, center, pushCode) {
            if (this.markers.length > 2) {
                let a = this.isLineOrthoCross(
                    this.markers[0].getPosition(),
                    center,
                    0
                );
                let b = this.isLineOrthoCross(
                    this.markers[this.markers.length - 1].getPosition(),
                    center,
                    this.markers.length - 1
                );
                if (a || b) {
                    this.$message.warning({
                        message: this.routeLanguage.messageInfo3,
                        duration: 1000,
                    });
                    this.num--;
                    return false;
                }
            }
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
                zIndex: 55,
            };
            let marker = initMaps.drawMarker(text, center, "marker-edit", params);
            marker.setMap(this.map);
            marker.id = text;
            this.markers.push(marker);
            this.markerClick(marker);
            if (!pushCode) {
                this.orthoForm.point_json.push({
                    lng: marker.getPosition().KL,
                    lat: marker.getPosition().kT,
                });
            }
            marker.on("dragstart", this.markerDragStart2)
            marker.on("dragging", this.markerDrag2);
            marker.on("dragend", this.markerDragEnd2);
            marker.on("click", this.markerClick);
            if (this.markers.length > 1) {
                this.drawOrthoCenter();
            }
            return true
        },
        //正射影像绘制边框图形
        drawOrthoPolypon() {
            let paths = []
            for (let index = 0; index < this.markers.length; index++) {
                paths.push(this.markers[index].getPosition())
            }
            if (this.polypon) {
                this.polypon.setPath(paths)
            } else {
                this.polypon = initMaps.drawPolypon(paths)
                this.polypon.setMap(this.map);
            }
            this.area = computedMethod(6, { fence: paths }).toFixed(2)
            this.drawOrthoInPolypon()
        },
        //绘制正射影像内图形
        drawOrthoInPolypon() {
            this.distance = 0
            if (this.markers.length > 2) {
                let paths = []
                for (let index = 0; index < this.markers.length; index++) {
                    paths.push(this.markers[index].getPosition())
                }
                let params = {
                    default_height: this.orthoForm.default_height,
                    cameraParamList: this.cameraParamList,
                    course: this.orthoForm.course,
                    lateral: this.orthoForm.lateral,
                    angle: this.orthoForm.angle,
                    wheelDist: this.orthoForm.wheelDist,
                    paths: paths
                }
                let { points, triggerDist } = orthoPhotoComputer(params, this.map, 1)
                this.routeSpotCount = points.length
                if (points.length > 0) {
                    this.messageCodeWarning = false
                    if (this.line) {
                        this.line.setPath(points)

                    } else {
                        this.line = initMaps.drawPolyline(points, { strokeColor: "#07ff0e" })
                        this.line.setMap(this.map)
                    }
                    if (this.startMarker) {
                        this.startMarker.setPosition(points[0])
                        this.endMarker.setPosition(points[points.length - 1])
                    } else {
                        let params = {
                            offset: -9,
                            clickable: false,
                            draggable: false,
                            zIndex: 100
                        }
                        this.startMarker = initMaps.drawMarker("S", points[0], "startend-marker", params)
                        this.startMarker.setMap(this.map)
                        this.endMarker = initMaps.drawMarker("E", points[points.length - 1], "startend-marker", params)
                        this.endMarker.setMap(this.map)
                    }
                } else {
                    if (this.line) {
                        this.map.remove(this.line)
                        this.line = ''
                    }
                    if (!this.messageCodeWarning) {
                        this.$message.warning(this.routeLanguage.errorMessage9)
                        this.messageCodeWarning = true
                    }
                    if (this.startMarker) {
                        this.map.remove(this.startMarker)
                        this.map.remove(this.endMarker)
                        this.startMarker = ''
                        this.endMarker = ''
                    }
                }
                for (let index = 1; index < points.length; index++) {
                    this.distance += computedMethod(1, { point1: points[index - 1], point2: points[index] })
                }
                this.estTime = this.distance / this.orthoForm.auto_speed + points.length
                this.photoCount = parseInt(this.distance / triggerDist) + points.length

            } else {
                if (this.line) {
                    this.map.remove(this.line)
                    this.line = ''
                }
            }

        },
        //正射影像拖拽开始
        markerDragStart2(e) {
            let index = e.target.id
            this.startPoints = {
                lng: this.orthoForm.point_json[index - 1].lng,
                lat: this.orthoForm.point_json[index - 1].lat
            }
            this.markerClick(e);
        },
        //正射影像拖拽中
        markerDrag2(e) {
            let index = e.target.id
            this.markers[index - 1].setPosition(e.lnglat)
            this.orthoForm.point_json[index - 1].lat = e.lnglat.kT
            this.orthoForm.point_json[index - 1].lng = e.lnglat.KL
            if (this.markers.length > 1) {
                this.drawOrthoPolypon()
                this.editOrthoCenter(index);
            }
        },
        //正射影像拖拽结束
        markerDragEnd2(e) {
            let index = e.target.id
            let a = computedMethod(2, {
                point1: e.lnglat,
                fence: this.fenceItem.paths,
            });
            let num = this.pointCodes.indexOf(index)
            if (!a) {
                this.$message.error(this.routeLanguage.placeholder4);
                this.markers[index - 1].setPosition(
                    new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
                );
                this.orthoForm.point_json[index - 1] = this.startPoints;
                if (this.markers.length > 1) {
                    this.drawOrthoPolypon()
                    this.editOrthoCenter(index);
                }
                if (num !== -1) {
                    this.pointCodes.splice(num, 1)
                    this.pointCodes.push(index)
                }
                return false;
            }
            if (this.markers.length > 1) {
                let d = "";
                if (index == 1) {
                    let ds = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let de = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[this.markers.length - 1].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    d = ds || de
                } else if (index == this.markers.length) {
                    let ds1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 2].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let de1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[0].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    d = ds1 || de1
                } else {
                    let b1 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    let b2 = computedMethod(4, {
                        point1: e.lnglat,
                        point2: this.markers[index - 2].getPosition(),
                        fence: this.fenceItem.paths,
                    });
                    d = b1 || b2;
                }
                if (d) {
                    this.$message.error(this.routeLanguage.placeholder5);
                    this.markers[index - 1].setPosition([this.startPoints.lng, this.startPoints.lat]);
                    this.orthoForm.point_json[index - 1] = this.startPoints;
                    this.drawOrthoPolypon();
                    this.editOrthoCenter(index);
                    if (num !== -1) {
                        this.pointCodes.splice(num, 1)
                        this.pointCodes.push(index)
                    }
                    return false;
                }
            }
            if (this.markers.length > 2) {
                let c, b;
                if (index == 1) {
                    c = this.isLineOrthoCross1(
                        this.markers[0].getPosition(),
                        this.markers[index].getPosition(),
                        0
                    );
                    b = this.isLineOrthoCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[0].getPosition(),
                        0,
                        1
                    );
                } else if (index == this.markers.length) {
                    c = this.isLineOrthoCross1(
                        this.markers[0].getPosition(),
                        this.markers[this.markers.length - 1].getPosition(),
                        0,
                        1
                    );
                    b = this.isLineOrthoCross1(
                        this.markers[this.markers.length - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        this.markers.length - 2
                    );
                } else {
                    c = this.isLineOrthoCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index - 2].getPosition(),
                        index - 2
                    );
                    b = this.isLineOrthoCross1(
                        this.markers[index - 1].getPosition(),
                        this.markers[index].getPosition(),
                        index - 1
                    );
                }
                if (c || b) {
                    this.markers[index - 1].setPosition(
                        new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
                    );
                    this.orthoForm.point_json[index - 1] = this.startPoints;

                    this.$message.warning({
                        message: this.routeLanguage.messageInfo3,
                        duration: 1000,
                    });
                    this.drawOrthoPolypon();
                    this.editOrthoCenter(index);
                    if (num !== -1) {
                        this.pointCodes.splice(num, 1)
                        this.pointCodes.push(index)
                    }
                    return false;
                }
            }
            this.deepCopy();
        },
        //绘制中心点
        drawOrthoCenter() {
            let center = computeCenter([
                this.orthoForm.point_json[this.orthoForm.point_json.length - 1],
                this.orthoForm.point_json[this.orthoForm.point_json.length - 2],
            ]);
            let params = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-edit-i",
                params
            );
            addMarker.id = this.orthoForm.point_json.length - 1;
            addMarker.setMap(this.map);
            this.addMarkers.push(addMarker);
            addMarker.on("click", this.addOrthoClick);
            if (this.orthoForm.point_json.length > 2) {
                let center1 = computeCenter([
                    this.orthoForm.point_json[this.orthoForm.point_json.length - 1],
                    this.orthoForm.point_json[0],
                ]);
                if (this.addMarkers.length == this.orthoForm.point_json.length) {
                    this.addMarkers[this.addMarkers.length - 2].setPosition(center1);
                    this.addMarkers[this.addMarkers.length - 2].id =
                        this.orthoForm.point_json.length;
                    let temp = this.addMarkers[this.addMarkers.length - 1];
                    this.addMarkers[this.addMarkers.length - 1] =
                        this.addMarkers[this.addMarkers.length - 2];
                    this.addMarkers[this.addMarkers.length - 2] = temp;
                } else {
                    let addMarker1 = initMaps.drawMarker(
                        "+",
                        center1,
                        "marker-edit-i",
                        params
                    );
                    addMarker1.id = this.orthoForm.point_json.length;
                    addMarker1.setMap(this.map);
                    this.addMarkers.push(addMarker1);
                    addMarker1.on("click", this.addOrthoClick);
                }
            }

        },
        //修改中心点坐标
        editOrthoCenter(index) {
            if (this.orthoForm.point_json.length < 3) {
                let center = computeCenter([
                    this.orthoForm.point_json[0],
                    this.orthoForm.point_json[1],
                ]);
                this.addMarkers[0].setPosition(center);
            } else {
                if (index == 1) {
                    let center = computeCenter([
                        this.orthoForm.point_json[index],
                        this.orthoForm.point_json[index - 1],
                    ]);
                    this.addMarkers[index - 1].setPosition(center);
                    let center1 = computeCenter([
                        this.orthoForm.point_json[0],
                        this.orthoForm.point_json[this.orthoForm.point_json.length - 1],
                    ]);
                    this.addMarkers[this.addMarkers.length - 1].setPosition(center1);
                } else if (index > 1) {
                    let center = computeCenter([
                        this.orthoForm.point_json[index - 1],
                        this.orthoForm.point_json[index - 2],
                    ]);
                    this.addMarkers[index - 2].setPosition(center);
                    if (index == this.orthoForm.point_json.length) {
                        let center = computeCenter([
                            this.orthoForm.point_json[index - 1],
                            this.orthoForm.point_json[0],
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                    } else {
                        let center = computeCenter([
                            this.orthoForm.point_json[index],
                            this.orthoForm.point_json[index - 1],
                        ]);
                        this.addMarkers[index - 1].setPosition(center);
                    }
                }
            }
        },
        //点击添加事件
        addOrthoClick(e) {
            this.num++;
            let index = e.target.id;
            this.map.remove(this.addMarkers[index - 1]);
            let params = {
                offset: -17,
                clickable: true,
                draggable: true,
            };
            let marker = initMaps.drawMarker(index + 1, e.lnglat, "marker-edit", params);
            marker.setMap(this.map);
            marker.id = index + 1;
            this.markers.splice(index, 0, marker);
            marker.on("dragstart", this.markerDragStart2)
            marker.on("dragging", this.markerDrag2);
            marker.on("dragend", this.markerDragEnd2);
            let paths = {
                lng: marker.getPosition().KL,
                lat: marker.getPosition().kT,
            }
            this.orthoForm.point_json.splice(index, 0, paths);

            for (let i = index + 1; i < this.markers.length; i++) {
                this.markers[i].id = i + 1;
                let content =
                    "<div class='marker-edit'><span class='text'>" +
                    (i + 1) +
                    "</span></div>";
                this.markers[i].setContent(content);
            }
            marker.on("click", this.markerClick);
            this.markerClick(marker);
            this.drawOrthoPolypon();
            for (let n = index; n < this.addMarkers.length; n++) {
                this.addMarkers[n].id = this.addMarkers[n].id + 1;
            }
            let lat =
                (parseFloat(this.orthoForm.point_json[index - 1].lat) +
                    parseFloat(this.orthoForm.point_json[index].lat)) /
                2;
            let lng =
                (parseFloat(this.orthoForm.point_json[index - 1].lng) +
                    parseFloat(this.orthoForm.point_json[index].lng)) /
                2;
            let center = [lng, lat];
            let params1 = {
                offset: -11,
                clickable: true,
                draggable: false,
            };
            let addMarker = initMaps.drawMarker(
                "+",
                center,
                "marker-edit-i",
                params1
            );
            addMarker.setMap(this.map);
            addMarker.id = index;
            this.addMarkers.splice(index - 1, 1, addMarker);
            addMarker.on("click", this.addOrthoClick);

            if (index == this.addMarkers.length) {
                let lat1 =
                    (parseFloat(this.orthoForm.point_json[index].lat) +
                        parseFloat(this.orthoForm.point_json[0].lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.orthoForm.point_json[index].lng) +
                        parseFloat(this.orthoForm.point_json[0].lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addOrthoClick);
            } else {
                let lat1 =
                    (parseFloat(this.orthoForm.point_json[index].lat) +
                        parseFloat(this.orthoForm.point_json[index + 1].lat)) /
                    2;
                let lng1 =
                    (parseFloat(this.orthoForm.point_json[index].lng) +
                        parseFloat(this.orthoForm.point_json[index + 1].lng)) /
                    2;
                let center1 = [lng1, lat1];
                let addMarker1 = initMaps.drawMarker(
                    "+",
                    center1,
                    "marker-edit-i",
                    params1
                );
                addMarker1.setMap(this.map);
                addMarker1.id = index + 1;
                this.addMarkers.splice(index, 0, addMarker1);
                addMarker1.on("click", this.addOrthoClick);
            }
            this.deepCopy();
        },
        //正射影像：判断是否出现交叉
        isLineOrthoCross(marker1, marker2, index) {
            let arr1 = this.orthoForm.point_json.slice(0, index);
            let arr2 = this.orthoForm.point_json.slice(
                index + 1,
                this.orthoForm.point_json.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //正射影像：拖拽判断是否出现交叉
        isLineOrthoCross1(marker1, marker2, index, num) {
            let arr1 = this.orthoForm.point_json.slice(0, index);
            let arr2 = this.orthoForm.point_json.slice(
                num ? index + 1 : index + 2,
                num ?
                this.orthoForm.point_json.length - 1 :
                this.orthoForm.point_json.length
            );
            let arr = arr2.concat(arr1);
            return computedMethod(5, {
                point1: marker1,
                point2: marker2,
                fence: arr,
            });
        },
        //正射影像修改
        changeOrtho() {
            let paths = []
            for (let index = 0; index < this.markers.length; index++) {
                paths.push(this.markers[index].getPosition())
            }
            if (this.markers.length > 2) {
                let points = renderPolyline({ paths: paths, stepRotate: this.orthoForm.rotate, spaceInp: this.orthoForm.space, amap: this.map })
                if (this.line) {
                    this.line.setPath(points)
                } else {
                    this.line = initMaps.drawPolyline(points, { strokeColor: "#07ff0e" })
                    this.line.setMap(this.map)
                }
                if (this.startMarker) {
                    this.startMarker.setPosition(points[0])
                    this.endMarker.setPosition(points[points.length - 1])
                }
            }
            if (this.orthoFormCode) {
                this.deepCopy()
            }
        },
        //设置数组深拷贝
        deepCopy() {
            let paths = {
                markers: [],
                addMarkers: [],
                num: this.num,
                clickId: this.clickId,
            };
            if (this.code == 1) {
                paths.line = this.line;
                paths.distance = this.distance;
                paths.estTime = this.estTime;
                paths.heights = this.heights.concat();
                paths.allActionList = JSON.parse(JSON.stringify(this.allActionList));
                paths.point_json = [];
                for (let i = 0; i < this.routeForm.point_json.length; i++) {
                    paths.point_json.push(
                        JSON.parse(JSON.stringify(this.routeForm.point_json[i]))
                    );
                }
            } else if (this.code == 2) {
                paths.polypon = this.polypon;
                paths.distancePoint = [];
                for (let index = 0; index < this.distancePoint.length; index++) {
                    paths.distancePoint.push({
                        text: this.distancePoint[index].getText(),
                        rotate: this.distancePoint[index].getExtData().rotate,
                        paths: this.distancePoint[index].getPosition(),
                    });
                }
            } else if (this.code == 3) {
                paths.line = this.line;
                paths.polypon = this.polypon;
            }
            for (let index = 0; index < this.markers.length; index++) {
                let path = JSON.parse(
                    JSON.stringify(this.markers[index].getPosition())
                );
                paths.markers.push({
                    id: this.markers[index].id,
                    content: this.markers[index].getContent(),
                    paths: new AMap.LngLat(path[0], path[1]),
                });
            }
            for (let index = 0; index < this.addMarkers.length; index++) {
                paths.addMarkers.push({
                    id: this.addMarkers[index].id,
                    paths: this.addMarkers[index].getPosition(),
                });
            }
            this.cacheData.push(paths);
            // let paths=JSON.parse(JSON.stringify(this.markers))
            // let path=[]
            // for (let index = 0; index < paths.length; index++) {
            //   path.push(new AMap.LngLat(paths[index][0],paths[index][1]))
            // }
        },
        //点击航线绘制绘制正射影像
        clickOrthoRoute() {
            let { points, triggerDist } = orthoPhotoComputer(this.routeItem, this.map)
            this.routeSpotCount = points.length
            if (this.layer && this.routeItem.point_list[0].type == 20) {
                for (let i = 0; i < points.length; i++) {
                    let a = gcj02_to_wgs84(points[i].KL, points[i].kT)
                    points[i].setLng(a[0])
                    points[i].setLat(a[1])
                }
            } else if (!this.layer && this.routeItem.point_list[0].type == 10) {
                for (let i = 0; i < points.length; i++) {
                    let a = wgs84_to_gcj02(points[i].KL, points[i].kT)
                    points[i].setLng(a[0])
                    points[i].setLat(a[1])
                }
            }
            this.clickRoute = initMaps.drawPolyline(points, { strokeColor: "#07ff0e" })
            if (this.startMarker) {
                this.startMarker.setPosition(points[0])
                this.endMarker.setPosition(points[points.length - 1])
            } else {
                let params = {
                    offset: -9,
                    clickable: false,
                    draggable: false,
                    zIndex: 100
                }
                this.startMarker = initMaps.drawMarker("S", points[0], "startend-marker", params)
                this.startMarker.setMap(this.map)
                this.endMarker = initMaps.drawMarker("E", points[points.length - 1], "startend-marker", params)
                this.endMarker.setMap(this.map)
            }
            this.clickRoute.setMap(this.map)
            for (let index = 1; index < points.length; index++) {
                this.distance += computedMethod(1, { point1: points[index - 1], point2: points[index] })
            }
            this.estTime = this.distance / (this.routeItem.auto_speed / 100) + points.length
            this.photoCount = parseInt(this.distance / triggerDist) + points.length
        },
    }

}