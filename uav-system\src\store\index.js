import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

import user from "./gerters/user" // 用户相关信息
import menus from "./gerters/menus" // 用户相关信息
import dict from "./gerters/dict" // 用户相关信息
import route from "./gerters/route"
import equipment from "./gerters/equipment" // 设备信息
import map from "./gerters/map" // 设备信息
import websocket from "./gerters/websocket" // ws信息
import camera from "./gerters/cameraSet" // 遥感相机信息
import deviceType from "./gerters/deviceType";
const store = new Vuex.Store({
  state: {},
  mutations: {},
  actions: {},
  modules: {
    user,
    menus,
    dict,
    route,
    equipment,
    map,
    websocket,
    camera,
    deviceType
  },
  getters: {
    // 把数据字典格式转换成map格式，即{value:label}
    getDictDataKeyValue: (state) => (key) => {
      let list = dict.state[key];
      let obj = {}
      list.forEach((item) => {
        obj[item.value] = item.label
      })
      return obj
    },
    /**
     * 把list转换成map
     * @param {*} state 
     * @param 
     * @returns 
     */
    formatConversionListMap: (state) => (list = [], val = "value", label = "label") => {
      let maps = {};
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        maps[item[val]] = item[label];
      }
      return maps;
    }
  }
})

export default store;
