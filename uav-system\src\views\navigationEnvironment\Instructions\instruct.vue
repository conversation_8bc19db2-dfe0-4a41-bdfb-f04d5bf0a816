<!-- 指令云台 -->
<template>
    <layouts :title="instruct.title" direction="leftTop">
        <template v-slot:content>
            <div class="" style="font-size: 12px; color: #fff;">
                <div class="mb8">{{instruct.content[0]}}</div>
                <div class="mb5">{{instruct.content[1]}}</div>
                <div class="mb5">{{instruct.content[2]}}</div>
            </div>
        </template>
    </layouts>
</template>

<script>
import layouts from "./layout.vue"
export default {
    components: {
        layouts
    },
    data(){
        return {}
    },
    computed: {
        instruct(){
            return this.$languagePackage.navigation.instructions.instruct
        }
    }
}
</script>