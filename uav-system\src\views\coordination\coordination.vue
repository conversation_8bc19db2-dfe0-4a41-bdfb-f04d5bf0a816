<template>
  <div class="coordination">
    <div class="searchDiv">
      <el-button
        v-if="editCode"
        icon="el-icon-arrow-left"
        @click="backEvent"
        class="backBtn"
      >
      </el-button>
      <el-input
        v-model="searchValue"
        :placeholder="
          clickCode
            ? groupLanguage.tipInfo.placeholder
            : groupLanguage.tipInfo.placeholder1
        "
        clearable
        @keydown.enter.native="search"
      ></el-input>
      <el-button
        class="searchBut"
        @click="searchCode ? '' : search()"
        :class="searchCode ? 'active' : ''"
        :disabled="searchCode1 || searchValue ? false : true"
        >{{ groupLanguage.allBtn.search }}</el-button
      >
      <div class="tip">
        <span v-if="clickCode"
          >{{ groupLanguage.group.select
          }}<span style="color: #2980d0">{{ deviceList.length }}</span
          >{{ groupLanguage.group.deviceGroup
          }}<span style="margin-left: 20px;"
            >{{ groupLanguage.group.name
            }}<el-input
              v-model="nameValue"
              :placeholder="groupLanguage.tipInfo.placeholder1"
            ></el-input></span
        ></span>
        <el-button
          icon="el-icon-plus"
          @click="addGroup"
          :class="clickCode ? 'active' : ''"
          v-if="!clickCode"
        >
          {{ groupLanguage.allBtn.newSet }}
        </el-button>
        <el-button
          @click="sureGroup"
          v-if="clickCode"
          :class="clickCode ? 'active' : ''"
          >{{ groupLanguage.allBtn.sure }}</el-button
        >
      </div>
    </div>
    <custom-table
      :column="column"
      v-if="!editCode"
      :data="tableData"
      :params="params"
      pmd="page,type"
    >
      <template v-slot:plan="scope">
        <el-button
          class="planBtn"
          :class="toCode == scope.row.id ? 'active' : ''"
          @click="toEvent(scope.row)"
          ><el-image
            :src="toCode == scope.row.id ? toImg : toImg1"
            fit="contain"
          ></el-image>
          <div>{{groupLanguage.allBtn.toGroup}}</div></el-button
        >
        <el-button
          class="planBtn"
          :class="editCode1 == scope.row.id ? 'active' : ''"
          @click="editEvent(scope.row)"
          ><el-image
            :src="editCode1 == scope.row.id ? editImg : editImg1"
            fit="contain"
          ></el-image>
          <div>{{groupLanguage.allBtn.editGroup}}</div></el-button
        >
      </template>
      <!-- <template v-slot:state="scope">
        <el-tag
          :class="scope.row.is_push_on ? 'firstDiv' : 'noneDiv noneDiv1'"
          >{{ scope.row.is_push_on ? "设备在线" : "设备离线" }}</el-tag
        >
        <el-tag :class="scope.row.is_pull_on ? 'secondDiv' : 'noneDiv'">{{
          scope.row.is_pull_on ? "有人连接" : "无人连接"
        }}</el-tag>
      </template> -->
      <template v-slot:start_time="scope">
        <div>{{ scope.row.start_time | timeFormat1 }}</div>
      </template>
      <template v-slot:stated="scope">
        <div>
          {{
            scope.row.stated == 0
              ? groupLanguage.group.normal
              : groupLanguage.group.delete
          }}
        </div>
      </template>
      <template v-slot:online="scope">
        <div>{{ scope.row.deviceList | onLineNum }}</div>
      </template>
      <template v-slot:option="scope">
        <el-button
          :class="scope.row.id == setCode ? 'active' : ''"
          @click="editCoordination(scope.row)"
          ><el-image
            :src="scope.row.id == setCode ? setImg1 : setImg"
            fit="contain"
          ></el-image
          >{{ groupLanguage.allBtn.set }}</el-button
        >
        <el-button
          :class="scope.row.id == delCode ? 'active' : ''"
          @click="delCoordination(scope.row)"
          ><el-image
            :src="scope.row.id == delCode ? delImg1 : delImg"
            fit="contain"
          ></el-image
          >{{ groupLanguage.allBtn.del }}</el-button
        >
      </template>
    </custom-table>
    <custom-table
      :column="addColumn"
      v-if="editCode"
      :isShowIndex="false"
      urlName="deviceList"
      :params="params"
      pmd="page,type"
      ref="addGroup"
    >
      <template v-slot:choose="scope">
        <el-checkbox
          v-model="scope.choose"
          @change="checkChange(scope)"
          :checked="scope.row.sn_id | ischeck(deviceList)"
        ></el-checkbox>
      </template>
      <template v-slot:state="scope">
        <el-tag
          :class="scope.row.is_push_on ? 'firstDiv' : 'noneDiv noneDiv1'"
          >{{
            scope.row.is_push_on
              ? groupLanguage.group.on_line
              : groupLanguage.group.out_line
          }}</el-tag
        >
        <el-tag :class="scope.row.is_pull_on ? 'secondDiv' : 'noneDiv'">{{
          scope.row.is_pull_on
            ? groupLanguage.group.connect
            : groupLanguage.group.noconnect
        }}</el-tag>
      </template>
      <template v-slot:photo="scope">
        <el-image :src="imgSrc[scope.row.type]" fit="contain"></el-image>
      </template>
      <template v-slot:type="scope">
        {{ scope.row.type | deviceTypeFormat }}
      </template>
    </custom-table>
  </div>
</template>
<script>
import customTable from "../../components/customTable/index";
export default {
  name: "coordination",
  data() {
    return {
      searchValue: "",
      searchCode: false,
      searchCode1: false,
      clickCode: false,
      editCode: false,
      toCode: "",
      editCode1: "",
      delCode: "",
      setCode: "",
      nameValue: "",
      deviceList: [],
      tableData: [],
      item: "",
      column: [],
      addColumn: [],
      params: {
        page: 0,
        size: 10,
        type: 0,
      },
      imgSrc: {
        10: require("../../assets/img/equipment/AC50.jpg"),
        12: require("../../assets/img/equipment/AC100.jpg"),
        50: require("../../assets/img/equipment/AC50MINI.jpg"),
        100: require("../../assets/img/equipment/move.jpg"),
        500: require("../../assets/img/equipment/R500.jpg"),
        1000: require("../../assets/img/equipment/R1000.jpg"),
        1800: require("../../assets/img/equipment/1800.jpg"),
        1900: require("../../assets/img/equipment/1900.jpg"),
        2000: require("../../assets/img/equipment/1900P.jpg"),
      },
      delImg: require("../../assets/img/del.png"),
      delImg1: require("../../assets/img/deled.png"),
      setImg: require("../../assets/img/setting.png"),
      setImg1: require("../../assets/img/seted.png"),
      editImg: require("../../assets/img/coordination/edit.png"),
      editImg1: require("../../assets/img/coordination/edit_1.png"),
      toImg: require("../../assets/img/coordination/to.png"),
      toImg1: require("../../assets/img/coordination/to_1.png"),
    };
  },
  components: {
    customTable,
  },
  created() {
    this.column = [
      {
        label: "",
        prop: "plan",
      },
      // {
      //   label: "在线状态",
      //   prop: "state",
      // },
      {
        label: this.groupLanguage.table.name,
        prop: "name",
      },
      {
        label: this.groupLanguage.table.time,
        prop: "start_time",
      },
      {
        label: this.groupLanguage.table.num,
        prop: "num",
      },
      {
        label: this.groupLanguage.table.online,
        prop: "online",
      },
      {
        label: this.groupLanguage.table.state,
        prop: "stated",
      },
      {
        label: this.groupLanguage.table.option,
        prop: "option",
      },
    ];
    this.addColumn = [
      {
        label: this.groupLanguage.table.choose,
        prop: "choose",
        width: "50",
      },
      {
        label: this.groupLanguage.table.onState,
        prop: "state",
      },
      {
        label: this.groupLanguage.table.photo,
        prop: "photo",
      },
      {
        label: this.groupLanguage.table.name1,
        prop: "name",
      },
      {
        label: this.groupLanguage.table.type,
        prop: "type",
      },
      {
        label: this.groupLanguage.table.sn_id,
        prop: "sn_id",
      },
      {
        label: this.groupLanguage.table.address,
        prop: "address",
        width: "400",
      },
    ];
  },
  computed: {
    groupLanguage() {
      return this.$languagePackage.coordination;
    },
  },
  mounted() {
    this.getCoordination();
  },
  methods: {
    //获取协同组列表
    getCoordination() {
      this.tableData = [];
      let listArray = localStorage.getItem("coordinate");
      if (listArray) {
        this.tableData = JSON.parse(listArray);
      }
    },
    //搜索功能
    search() {
      // this.searchCode = true;
      // this.params.search = this.searchValue;
      // this.searchCode1 = true;
      // if (!this.searchValue) {
      //   delete this.params.search;
      //   this.searchCode1 = false;
      // }
      // this.$refs.addGroup.refresh();
      // setTimeout(() => {
      //   this.searchCode = false;
      // }, 200);
    },
    //新建协同组
    addGroup() {
      this.clickCode = true;
      this.nameValue = "";
      setTimeout(() => {
        this.editCode = true;
        setTimeout(() => {
          this.$refs.addGroup.getTableData();
        }, 100);
      }, 200);
    },
    //确定组网
    sureGroup() {
      if (!this.nameValue) {
        this.$message.error(this.groupLanguage.tipInfo.errorMessage);
      } else if (this.deviceList.length == 0) {
        this.$message.error(this.groupLanguage.tipInfo.errorMessage1);
      } else {
        if (this.item) {
          let params = {
            name: this.nameValue,
            start_time: this.item.start_time,
            num: this.deviceList.length,
            // online: 1,
            stated: 0,
            // is_pull_on: true,
            // is_push_on: false,
            deviceList: this.deviceList,
            id: this.item.id,
          };
          let listArray = localStorage.getItem("coordinate");
          let tableData = JSON.parse(listArray);
          let a = tableData.findIndex((n) => {
            return n.id == this.item.id;
          });
          tableData[a] = params;
          localStorage.setItem("coordinate", JSON.stringify(tableData));
          this.$message.success(this.groupLanguage.tipInfo.successMessage);
        } else {
          let params = {
            name: this.nameValue,
            start_time: new Date().getTime(),
            num: this.deviceList.length,
            // online: 1,
            stated: 0,
            // is_pull_on: true,
            // is_push_on: false,
            deviceList: this.deviceList,
            id: "wk" + parseInt(Math.random() * 1000),
          };
          let listArray = localStorage.getItem("coordinate");
          if (listArray) {
            let tableData = JSON.parse(listArray);
            tableData.push(params);
            localStorage.setItem("coordinate", JSON.stringify(tableData));
          } else {
            localStorage.setItem("coordinate", JSON.stringify([params]));
          }
          this.$message.success(this.groupLanguage.tipInfo.successMessage1);
        }

        setTimeout(() => {
          this.clickCode = false;
          this.editCode = false;
          this.deviceList = [];
          this.item = "";
          this.getCoordination();
        }, 200);
      }
    },
    //返回组网首页
    backEvent() {
      this.clickCode = false;
      this.editCode = false;
      this.getCoordination();
    },
    //点击进入组网
    toEvent(item) {
      this.toCode = item.id;
      this.$router.push({
        path: "/networkingItem",
        query: {
          id: item.id,
        },
      });
    },
    //任务编辑
    editEvent(item) {
      this.editCode1 = item.id;
      this.$router.push({
        path: "/editWork",
        query: {
          id: item.id,
        },
      });
    },
    //协同组设置
    editCoordination(item) {
      this.item = item;
      this.setCode = item.id;
      this.nameValue = item.name;
      this.deviceList = item.deviceList;
      setTimeout(() => {
        this.clickCode = true;
        this.editCode = true;
        this.setCode = "";
        setTimeout(() => {
          this.$refs.addGroup.getTableData();
        }, 100);
      }, 200);
    },
    //协同组删除
    delCoordination(item) {
      this.delCode = item.id;
      this.$confirm(
        this.groupLanguage.tipInfo.placeholder2,
        this.groupLanguage.tipInfo.tip,
        {
          confirmButtonText: this.groupLanguage.allBtn.sure,
          cancelButtonText: this.groupLanguage.allBtn.cancel,
          type: "warning",
        }
      )
        .then(() => {
          let list = JSON.parse(localStorage.getItem("coordinate"));
          let index = list.findIndex((n) => {
            return n.id == item.id;
          });
          list.splice(index, 1);
          localStorage.setItem("coordinate", JSON.stringify(list));
        })
        .catch(() => {})
        .finally(() => {
          this.delCode = "";
          this.getCoordination();
        });
    },
    //多选
    checkChange(e) {
      // console.log(e)
      if (e.choose) {
        this.deviceList.push(e.row);
      } else {
        let i = this.deviceList.findIndex((item) => {
          return item.sn_id == e.row.sn_id;
        });
        this.deviceList.splice(i, 1);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.coordination {
  width: 100%;
  height: 100%;
  .searchDiv {
    padding: 1% 2%;
    text-align: left;
    width: 96%;
    .el-input {
      width: 20%;
      margin-right: 1%;
    }
    .tip {
      float: right;
      letter-spacing: 2px;
      .el-input {
        width: 30%;
        margin-right: 0;
      }
    }
    .el-button {
      border-radius: 6px;
      font-size: 16px;
    }
    .backBtn {
      font-size: xx-large;
      padding: 0;
      vertical-align: middle;
    }
  }
  .custom-table {
    width: 96%;
    height: 84%;
    margin: 0.5% 2%;
    .el-tag {
      padding: 6px;
      font-size: 12px;
      height: auto;
      min-width: 30%;
      width: auto;
      margin: 0 20%;
      min-width: 60px;
      line-height: 16px;
    }
    .firstDiv {
      margin-bottom: 8px;
    }
    
    
    .noneDiv1 {
      margin-bottom: 8px;
    }
    .el-image {
      width: 50%;
    }
    .el-button {
      
      padding: 0;
      margin-left: 10%;
     

      
      .el-image {
        width: 30%;
        margin-right: 10%;
      }
    }
    .planBtn {
      font-size: 12px;
      margin-left: 10%;
      width: 30%;
      .el-image {
        width: 50%;
        margin-bottom: 10%;
      }
    }
  }
}
</style>
<style lang="less">
.coordination {
  .searchDiv {
    i[class*=" el-icon-"],
    i[class^="el-icon-"] {
      font-weight: 1000 !important;
    }
  }
  .custom-table {
    .el-checkbox__inner {
      width: 24px !important;
      height: 24px !important;
    }
    
    
    .el-checkbox__inner::after {
      left: 6px !important;
      height: 12px !important;
      width: 6px !important;
      border-width: 2px !important;
    }
  }
}
</style>