// 数据大屏相关样式
.home {
  #home {
    .home-map {
      background-color: #000000;
    }
  }

  .renderMarker {
    border: 1px solid #c84141;

    span {
      background-color: #c84141;
    }
  }

  // 飞行数据
  .flight-data {

    .flight-data-top {
      background-color: rgba(59, 159, 233, 0.1);
    }

    .flight-data-title {
      color: #5dbdff;
      background-image: linear-gradient(to right,
          rgba(47, 83, 174, 0.5),
          rgba(26, 50, 112, 0.5),
          rgba(19, 40, 85, 0.5));
    }

    .pie-round {
      color: #fff;
      background-image: linear-gradient(to right,
          rgba(26, 50, 112, 0.2),
          rgba(19, 40, 85, 0.2));
    }

    .table-data {

      .thead-cell {
        background-color: #1d2088;
        color: #fff;
      }

      .tbody-cell {
        color: #fff;
        background-color: rgba(8, 17, 49, 1);
        border-bottom: 1px solid rgb(47, 96, 153);
      }
    }
  }

  // 数据总览
  .data-screening {
    .data-screening-main {
      .main-setting {
        .setting-footer {
          .line-item {
            background-image: linear-gradient(to right,
                rgba(119, 161, 246, 0.1),
                rgba(59, 117, 235, 0.4),
                rgba(255, 255, 255, 0.4),
                rgba(59, 117, 235, 0.4),
                rgba(119, 161, 246, 0.1));
          }
        }
      }
    }



    .main-content {

      .row-explain {
        .explain-title {
          color: rgba(0, 204, 255, 1);
        }

        .explain-num {
          color: rgba(1, 229, 105, 1);
        }
      }

      .content-cell {
        .cell-title {
          color: rgba(89, 163, 215, 1);
        }

        .cell-main {
          .row-total {
            color: rgba(0, 204, 255, 1);
            text-shadow: 5px 0 4px #069f4c;
          }
        }
      }
    }
  }

  // 巡检排行榜
  .lnspection-leaderboard {
    .tab-nav {
      color: #fff;
    }

    .table-data {
      .tbody-cell {
        color: #fff;
      }
    }
  }

  // 定时任务列表
  .task-list-data {
    .tab-nav {
      color: #fff;
    }

    .table-data {
      .tbody-cell {
        color: #fff;
      }
    }
  }

  // 巡检记录
  .inspection-record {
    .date {
      color: #fff;
      background-color: rgba(18, 15, 79, 0.7);
    }

    .data-preview {
      color: #fff;
      background-color: rgba(13, 11, 51, 0.7);

      .view-row {
        .view-icon {
          color: rgb(2, 224, 110);
        }

        .view-content {
          .title {
            color: rgb(106, 112, 169);
          }

          .total {
            color: rgb(2, 224, 110);
          }
        }
      }
    }

    .table-data {
      .tbody-cell {
        color: #fff;
        background-color: rgba(25, 56, 171, 0.4);
      }
    }

    .el-input__inner {
      background-color: rgba(15, 12, 68, 1);
      color: #fff;
    }

    input::-webkit-input-placeholder {
      color: rgb(106, 112, 169) !important;
    }

    input::-moz-input-placeholder {
      color: rgb(106, 112, 169) !important;
    }

    input::-ms-input-placeholder {
      color: rgb(106, 112, 169) !important;
    }
  }

  // 设备列表
  #uav-equipment-list {
    .checkbox {
      .checkbox-item {
        .item-radio {
          .circle {
            border: 2px solid rgb(44, 90, 145);
          }

          .select-circle {
            background-color: rgb(0, 255, 0);

            .interior-circle {
              background-color: rgb(34, 172, 56);
            }
          }
        }
      }
    }

    .table-data {
      .table-tbody-tr {
        border: 1px solid #2f6099;
        background-image: linear-gradient(to bottom, #0c2b67, #0c1f3c, #0c203a);
      }


      .tbody-cell {
        color: #fff;

        .table-index {
          border: 1px solid rgb(20, 57, 90);
          color: #fff;
        }
      }
    }
  }


  // 布局
  .bg-layout {
    background-color: rgba(0, 0, 0, 0.5);

    .bg-layout-main {
      border: 2px solid rgba(59, 159, 233, 0.4);
    }

    .marquee {
      .marquee-cell {
        background-image: linear-gradient(to right,
            rgba(119, 161, 246, 0.1),
            rgba(59, 117, 235, 0.4),
            rgba(255, 255, 255, 1),
            rgba(59, 117, 235, 0.4),
            rgba(119, 161, 246, 0.1));
      }
    }

    .layout-header {
      .header-title {
        color: #5dbdff;
      }
    }

    .quadrangle {

      border: 2px solid rgba(70, 178, 247, 0.5);
    }
  }
}
