export default {
    methods: {
        //分页，页码切换
        changePage(e) {
            this.params.page = e - 1;
            this.getMentData();
            this.checkOpen = "";
        },
        //设置点击页码前后按钮变化
        prevClick(e) {
            this.className = "prev-class";
            setTimeout(() => {
                this.className = "";
            }, 200);
        },
        //设置点击页码前后按钮变化
        nextClick(e) {
            this.className = "next-class";
            setTimeout(() => {
                this.className = "";
            }, 200);
        },
        //打开传感器校准
        openCalibration() {
            let websocket = this.$refs.contentInfo[0].websocket;
            this.getComponents("calibrationListDialog").then((component) => {
                this.componentDialog = component;
                this.$nextTick(() => {
                    this.$refs.componentDialog.open(websocket);
                });
            });
        },
        //升级固件
        upgradeFirmware() {
            if (this.uavItemList) {
                this.getHeaderItem().upgradeCode = "";
                if (!(
                        this.uavItemList.flight_status === 0 ||
                        this.uavItemList.flight_status === 4
                    )) {
                    this.$message.error(this.equipLanguage.upgradeMsg.noUpgrade1);
                    this.getHeaderItem().upgradeCode = "";
                    return false;
                }
            }
            let websocket = this.$refs.contentInfo[0].websocket;
            if (this.modules.firmwareOperate) {
                this.componentDialog = this.modules.firmwareOperate;
                this.$nextTick(() => {
                    this.$refs.componentDialog.open("", websocket);
                });
                return false;
            }
            import ("../firmware/components/firmwareOperate.vue").then((modules) => {
                this.modules.firmwareOperate = modules.default;
                this.componentDialog = modules.default;
                this.componentProps = { code: true };
                this.$nextTick(() => {
                    this.$refs.componentDialog.open("", websocket);
                });
            });
        },
        //获取目前打开的设备头部
        getHeaderItem() {
            let headerList = this.$refs.collapseItemHeader;
            let headerItem = null;
            headerList.forEach((item) => {
                if (item.item.sn_id == this.checkOpen) {
                    headerItem = item;
                }
            });
            return headerItem;
        },
        //升级固件返回
        dialogRefresh() {
            this.getHeaderItem().upgradeCode = "";
        },
        //固件更新进度
        updateVersion() {
            this.getComponents("versionRate").then((component) => {
                this.componentDialog = component;
                this.componentProps = {
                    versionList: this.versionList,
                    // deviceItemList: this.deviceItemList,
                };
                this.$nextTick(() => {
                    console.log(this.$refs.componentDialog);
                    this.$refs.componentDialog.open();
                });
            });
        },
        //进度完成返回
        reStart() {
            this.$nextTick(() => {
                this.countDownFun();
                this.$confirm(
                    this.equipLanguage.upgradeMsg.reStart,
                    this.equipLanguage.upgradeMsg.reStartTip, {
                        confirmButtonText: `${this.equipLanguage.upgradeMsg.submit}（${this.takeOffTime}）`,
                        showClose: false,
                        showCancelButton: false,
                        confirmButtonClass: "restartConfirmBtn",
                        type: "warning",
                    }
                ).then(() => {
                    let box = document.getElementsByClassName("restartConfirmBtn")[0];
                    this.takeOffTime--;
                    box.innerHTML = `<span>${this.equipLanguage.upgradeMsg.submit}</span>`;
                    if (this.countDown) {
                        this.takeOffTime = 10;
                        clearInterval(this.countDown);
                        this.countDown = null;
                    }
                    let data = {
                        nest_action_cmd: 125,
                    };
                    let websocket = this.$refs.contentInfo[0].websocket;
                    websocket && websocket.manualSend(data, 403);
                });
            });
        },

    },
}