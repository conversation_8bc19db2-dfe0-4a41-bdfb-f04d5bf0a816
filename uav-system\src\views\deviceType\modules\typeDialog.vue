<template>
  <div class="">
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :destroy-on-close="true"
      center
      class="typeDialog"
      width="700px"
    >
      <el-form label-width="120px" :model="form" :rules="rules" ref="form">
        <el-form-item :label="formLable.cla_type" prop="cla_type">
          <el-radio-group
            v-model="form.cla_type"
            :disabled="form.id ? true : false"
          >
            <el-radio
              v-for="item in $dict.devictType"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="formLable.cla_name" prop="cla_name">
          <el-input
            v-model="form.cla_name"
            :placeholder="placeholder.cla_name"
          ></el-input>
        </el-form-item>
        <el-form-item :label="formLable.value" prop="value">
          <el-input
            v-model="form.value"
            :placeholder="placeholder.value"
            keyup="value=value.replace(/[^\d]/g,'')"
            :disabled="form.id ? true : false"
          ></el-input>
        </el-form-item>
        <el-form-item :label="formLable.stream" prop="stream">
          <!-- <el-input
            v-model="form.stream"
            :placeholder="placeholder.stream"
            onkeyup="value=value.replace(/[^\d]/g,'')"
          ></el-input> -->
          <el-select v-model="form.stream" :placeholder="placeholder.stream">
            <el-option
              v-for="item in 3"
              :key="item"
              :value="item"
              :label="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="formLable.state" prop="state" v-if="form.id">
          <el-radio-group v-model="form.state">
            <el-radio
              v-for="item in $dict.deviceTypeState"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="formLable.notes" prop="notes">
          <el-input
            v-model="form.notes"
            :placeholder="placeholder.notes"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">确 定</el-button>
        <el-button @click="close">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
export default {
  data() {
    return {
      title: "",
      visible: false,
      form: {
        cla_type: "stable",
        cla_name: "",
        value: "",
        stream: "",
        notes: "",
        state: "",
      },
      rules: {
        cla_type: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        cla_name: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        value: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        stream: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
          { validator: this.streamMax, trigger: "change" },
        ],
      },
      loading: "",
    };
  },
  computed: {
    language() {
      return this.$languagePackage.deviceType;
    },
    formLable() {
      return this.language.formLable;
    },
    placeholder() {
      return this.language.placeholder;
    },
  },
  created() {
    for (const key in this.rules) {
      this.rules[key][0].message = this.placeholder[key];
    }
  },
  methods: {
    open(row) {
      this.visible = true;
      this.title = this.language.addTitle;
      this.form = {
        cla_type: "stable",
        cla_name: "",
        value: "",
        stream: "",
        notes: "",
      };
      if (row) {
        this.title = this.language.editTitle;
        this.form = Object.assign({}, row);
        delete this.form.create_time;
        delete this.form.modified_time;
        console.log(row);
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    close() {
      this.visible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = this.$loading({
            text: this.language.loading,
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let data = Object.assign({}, this.form);
          data.pmd =
            data.cla_type.toString() +
            data.cla_name.toString() +
            data.value.toString() +
            data.stream.toString();
          let url = "deviceTypeAdd";
          let tip = this.language.successTipAdd;
          if (this.form.id) {
            url = "deviceTypeEdit";
            data.pmd =
              data.cla_type.toString() +
              data.cla_name.toString() +
              data.value.toString() +
              data.stream.toString() +
              data.id.toString() +
              data.state.toString();
            tip = this.language.successTipEdit;
            if (this.form.state == 20) {
              tip = this.language.successTipDel;
              this.$confirm(
                this.language.disabledContent,
                this.language.disabledTip,
                {
                  confirmButtonText: this.language.confirm,
                  cancelButtonText: this.language.cancel,
                  type: "warning",
                }
              )
                .then(() => {
                  this.requestFun(url, data, tip);
                })
                .catch(() => {
                  if (this.loading) {
                    this.loading.close();
                    this.loading = "";
                  }
                });
              return false;
            }
            if (this.form.state == 30) {
              this.$confirm(this.language.deleteContent, this.language.delTip, {
                confirmButtonText: this.language.confirm,
                cancelButtonText: this.language.cancel,
                type: "warning",
              })
                .then(() => {
                  this.requestFun(url, data, tip);
                })
                .catch(() => {
                  if (this.loading) {
                    this.loading.close();
                    this.loading = "";
                  }
                });

              return false;
            }
          }
          this.requestFun(url, data, tip);
        }
      });
    },
    requestFun: function (url, data, tip) {
      requestHttp(url, data)
        .then((res) => {
          this.$message.success(tip);
          this.visible = false;
          this.$emit("refresh", "");
        })
        .finally(() => {
          if (this.loading) {
            this.loading.close();
            this.loading = "";
          }
        });
    },
    streamMax(rule, value, callback) {
      if (value > 3 || value < 0) {
        callback(new Error(this.placeholder.stream_1));
      } else {
        callback();
      }
    },
  },
};
</script>
<style lang="less" scoped>
.typeDialog {
  .el-form {
    .el-form-item {
      .el-input,
      .el-select {
        width: 90%;
      }
    }
  }
}
</style>
<style lang="less">
.typeDialog {
  .el-dialog__header {
    border-bottom: 1px solid #0c31ac;
  }
  input::-webkit-input-placeholder {
    color: #cfcfcf !important;
  }

  input::-moz-input-placeholder {
    color: #cfcfcf !important;
  }

  input::-ms-input-placeholder {
    color: #cfcfcf !important;
  }
}
</style>