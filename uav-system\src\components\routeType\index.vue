<template>
  <div class="routeType">
    <el-button
      v-for="(item, index) in typeList"
      :key="index"
      @click="clickType(item)"
      :class="typeCode == item.value ? 'active' : ''"
    >
      <el-image
        :src="typeCode == item.value ? item.type_img : item.type_img_1"
      ></el-image>
      <div class="typeTitle" :class="$loadingEnUI ? 'typeTitle-en' : ''">
        {{ $loadingEnUI ? item.name_en : item.name_cn }}
      </div>
    </el-button>
  </div>
</template>
<script>
import requestHttp from "../../utils/api";
export default {
  name: "routeType",
  props: {
    typeCode: {
      type: [Number, String],
    },
  },
  data() {
    return {
      typeList: "",
    };
  },
  created() {
    this.getType();
  },
  methods: {
    //获取类型
    async getType() {
      this.typeList = [];
      await requestHttp("missionType").then((res) => {
        // this.typeList = res.data;
        let typeList = res.data;
        for (let index = 0; index < typeList.length; index++) {
          const item = typeList[index];
          switch (item.value) {
            case 10:
              item.type_img = require("../../assets/img/routeplan/type_1-1.png");
              item.type_img_1 = require("../../assets/img/routeplan/type_1.png");
              item.type_img_2 = require("../../assets/img/routeplan/type_1-2.png");
              item.type_img_3 = require("../../assets/img/routeplan/type_1-3.png");
              break;
            case 20:
              item.type_img = require("../../assets/img/routeplan/type_2-1.png");
              item.type_img_1 = require("../../assets/img/routeplan/type_2.png");
              item.type_img_2 = require("../../assets/img/routeplan/type_2-2.png");
              item.type_img_3 = require("../../assets/img/routeplan/type_2-3.png");
              this.typeList.push(item);
              break;
            case 30:
              item.type_img = require("../../assets/img/routeplan/type_4-1.png");
              item.type_img_1 = require("../../assets/img/routeplan/type_4.png");
              item.type_img_2 = require("../../assets/img/routeplan/type_4-2.png");
              item.type_img_3 = require("../../assets/img/routeplan/type_4-3.png");
              break;
            case 40:
              item.type_img = require("../../assets/img/routeplan/type_5-1.png");
              item.type_img_1 = require("../../assets/img/routeplan/type_5.png");
              item.type_img_2 = require("../../assets/img/routeplan/type_5-2.png");
              item.type_img_3 = require("../../assets/img/routeplan/type_5-3.png");
              break;
            case 50:
              item.type_img = require("../../assets/img/routeplan/type_6-1.png");
              item.type_img_1 = require("../../assets/img/routeplan/type_6.png");
              item.type_img_2 = require("../../assets/img/routeplan/type_6-2.png");
              item.type_img_3 = require("../../assets/img/routeplan/type_6-3.png");
              this.typeList.push(item);
              break;
            default:
              break;
          }
        }
        // [typeList[0], typeList[1]] = [typeList[1], typeList[0]];
        // let temp = this.typeList[0];
        // this.typeList[0] = this.typeList[1];
        // this.typeList[1] = temp;
        // this.typeList = typeList;
      });
      this.$store.commit("typeList", this.typeList);
    },
    //点击类型
    clickType(item) {
      this.$emit("clickType", item);
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .routeType {
    .el-button {
      font-size: @zoomIndex * 12px !important;
      .typeTitle {
        margin-top: @zoomIndex * 7px !important;
        font-size: @zoomIndex * 12px !important;
        letter-spacing: @zoomIndex * 2px !important;
        &.typeTitle-en {
          letter-spacing: 0 !important;
        }
      }
    }
  }
}
.routeType {
  .el-button {
    width: 18%;
    margin: 0;
    margin-right: 2%;
    font-size: 12px;
    padding: 0;
    color: white;
    padding: 0;
    background-color: transparent;
    border: none;
    &.active {
      color: #0b58de;
    }
    .typeTitle {
      margin-top: 7px;
      font-size: 12px;
      letter-spacing: 2px;
      &.typeTitle-en {
        letter-spacing: 0;
        white-space: normal;
        word-break: break-all;
        text-align: left;
      }
    }
  }
}
</style>