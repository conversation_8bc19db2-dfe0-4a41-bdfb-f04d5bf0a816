<template>
  <div class="lamp-box">
    <div class="item-box">
      <div class="lamp-item-box" @click="showBrightnessSlider">
        <el-image
          :src="led_mo ? brightnessImg_1 : brightnessImg"
          style="height: 40px"
          fit="contain"
        ></el-image>
        <div
          class="brightness-value"
          :style="{ color: led_mo ? '#ffffff' : '#707070' }"
        >
          {{ led_lb }}
        </div>
      </div>
      <div class="lamp-item-border"></div>
      <color-box :sendWs="sendWs" ref="colorBox" v-show="led_pse"></color-box>
      <div
        class="lamp-item-box"
        v-show="lfCode"
        @click="() => (changeLfCode = !changeLfCode)"
      >
        <el-image
          :src="frequency"
          style="height: 36px"
          fit="contain"
        ></el-image>
        <div class="brightness-value" :style="{ color: '#ffffff' }">
          {{ led_lf }}
        </div>
      </div>
    </div>
    <div class="item-box brightness-slider">
      <div class="brightness-slider-item" v-show="brightnessSlider">
        <el-slider
          v-model="led_lb"
          :show-tooltip="false"
          @change="changeBrightnessSlider"
        ></el-slider>
      </div>
    </div>
    <div class="item-box">
      <div
        class="btn-lamp"
        v-for="item in flickerList"
        :key="item.id"
        :class="{ active: led_pse && item.id == led_psm }"
        @click="changeFlicker(item.id, 'led_psm')"
      >
        <el-image
          :src="item.iconSrc"
          style="height: 53.33%"
          fit="contain"
        ></el-image>
      </div>
    </div>
    <div class="item-box">
      <div
        class="btn-lamp"
        v-for="item in brightList"
        :key="item.id"
        :class="{ active: item.id == led_mo }"
        @click="changeFlicker(item.id, 'led_mo')"
      >
        <el-image
          :src="item.iconSrc"
          style="width: 36%"
          fit="contain"
        ></el-image>
      </div>
    </div>
    <div class="item-box">
      <el-button
        class="btn-close"
        :class="{ 'close-able': led_pse || led_mo }"
        @click="closeLamp"
        >{{ language.close }}</el-button
      >
    </div>
    <div class="lf_box" v-if="lfCode && changeLfCode">
      <el-slider
        vertical
        v-model="led_lf"
        :show-tooltip="false"
        @change="changeLfSlider"
        height="200px"
      ></el-slider>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    sendWs: {
      type: [Function, String],
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      led_lb: 0,
      led_lb_1: 0,
      led_lf: 0,
      led_lf_1: 0,
      led_pse: 0,
      led_psm: null,
      flickerList: [
        { id: 1, iconSrc: require("@/assets/img/lamp_3.png") },
        { id: 0, iconSrc: require("@/assets/img/lamp_4.png") },
        { id: 2, iconSrc: require("@/assets/img/lamp_5.png") },
      ],
      led_mo: "",
      brightList: [
        { id: 2, iconSrc: require("@/assets/img/lamp_1.png") },
        { id: 1, iconSrc: require("@/assets/img/lamp_2.png") },
      ],
      brightnessImg: require("@/assets/img/brightness.png"),
      brightnessImg_1: require("@/assets/img/brightness_1.png"),
      frequency: require("@/assets/img/frequency.png"),
      brightnessSlider: false,
      changeLfCode: false,
      noClickCode: false,
    };
  },
  components: {
    ColorBox: () => import("./colorBox.vue"),
  },
  computed: {
    language() {
      return this.$languagePackage.navigation.shout;
    },
    lfCode() {
      return this.led_mo == 1 && !this.led_pse;
    },
  },
  watch: {
    led_lb_1: {
      handler(val) {
        this.led_lb = val;
      },
      immediate: true,
    },
    led_lf_1: {
      handler(val) {
        this.led_lf = val;
      },
      immediate: true,
    },
  },
  methods: {
    disposeData: function (msg_id, data) {
      if (msg_id == 701) {
        if (!this.noClickCode) {
          if (data.led_mo || data.led_mo == 0) {
            let colorBox = this.$refs.colorBox;
            colorBox && colorBox.disposeData(data);
            this.led_mo = data.led_mo;
            this.led_pse = data.led_pse;
            this.led_psm = data.led_psm;
            this.led_lb_1 = data.led_lb;
            this.led_lf_1 = data.led_lf;
          }
        }
      }
    },
    changeFlicker: function (id, name) {
      if (this.noClickCode) {
        return false;
      }
      setTimeout(() => {
        this.noClickCode = false;
      }, 600);
      if (name == "led_mo") {
        if (this.led_mo == id) {
          this.sendWs({ action: 62, value: 0 });
          return false;
        }
        this[name] = id;
        this.sendWs({ action: 62, value: id });
        if (this.led_lb == 0 && id === 2) {
          this.changeBrightnessSlider(20);
        }
        if (this.led_lf == 0 && id === 1) {
          this.changeLfSlider(20);
        }
      }
      if (name == "led_psm") {
        if (this.led_pse && this.led_psm == id) {
          this.sendWs({ action: 64, value: `0#${this.led_psm}` });
          return false;
        }
        this[name] = id;
        this.led_pse = 1;
        this.sendWs({ action: 64, value: `1#${id}` });
      }
      this.$message({
        message: this.language.ledMessage[name + id],
        customClass: "led_message",
      });
    },
    closeLamp: function () {
      if (!this.led_pse && !this.led_mo) return;
      if (this.led_pse) {
        this.sendWs({ action: 64, value: `0#${this.led_psm}` });
        this.led_psm = null;
        this.led_pse = 0;
      }
      if (this.led_mo) {
        this.led_mo = "";
        this.sendWs({ action: 62, value: 0 });
      }
      this.brightnessSlider = false;
    },
    showBrightnessSlider: function () {
      if (!this.led_mo) return;
      this.brightnessSlider = !this.brightnessSlider;
    },
    changeBrightnessSlider: function (e) {
      this.sendWs({ action: 63, value: e });
    },
    changeLfSlider: function (e) {
      this.sendWs({ action: 61, value: e });
    },
  },
};
</script>
<style lang="less" scoped>
.lamp-box {
  background-color: rgba(71, 71, 71, 0.5);
  border-radius: 8px;
  padding: 28px 23px;
  position: relative;
  .item-box {
    margin-bottom: 18px;
    display: flex;
    align-items: center;
    &:last-child {
      margin-bottom: 0;
    }
    .btn-close {
      font-size: 30px;
      padding: 16.5px 0;
      width: 100%;
      border-radius: 5px;
      background-color: #24272b;

      color: #fff;
      border: none;
      &.close-able {
        background-color: #409eff;
      }
    }
    .btn-lamp {
      flex: 1;
      height: 75px;
      box-sizing: border-box;
      background-color: #24272b;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 5px;
      margin-right: 7px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
      &.active {
        background-color: #409eff;
      }
    }
    .lamp-item-box {
      width: 48%;
      flex-shrink: 0;
      padding: 0 5px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      cursor: pointer;
      .brightness-value {
        font-size: 30px;
      }
    }
    .lamp-item-border {
      flex-shrink: 0;
      width: 1px;
      height: 39px;
      background-color: #cfcfcf;
    }
    &.brightness-slider {
      height: 36px;
      .brightness-slider-item {
        width: 100%;
        background-color: #24272b;
        padding: 0 9px;
        border-radius: 5px;
        .el-slider {
          width: 100%;
        }
      }
    }
  }
  .lf_box {
    position: absolute;
    top: 0;
    left: calc(100% + 18px);
    background-color: rgba(71, 71, 71, 0.5);
    padding: 10px;
    border-radius: 8px;
  }
}
</style>
<style lang="less">
.lamp-box {
  .brightness-slider {
    .brightness-slider-item {
      .el-slider {
        .el-slider__runway {
          height: 3px !important;
          .el-slider__bar {
            height: 3px !important;
          }
          .el-slider__button {
            width: 12px !important;
            height: 12px !important;
            border-width: 1px !important;
          }
        }
      }
    }
  }
}
.led_message {
  background-color: #2f3337;
  border: none;
  padding: 18px 40px;
  min-width: 0;
  i {
    display: none !important;
  }
  .el-message__content {
    color: #fff;
    font-size: 30px;
  }
}
</style>