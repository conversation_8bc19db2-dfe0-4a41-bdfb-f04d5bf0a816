/**
 * 字典数据
 * 参数
 */
const dictData = {
    state: {
        // 功能状态
        funState: [{
                label: "正常",
                value: 10
            },
            {
                label: "已删除",
                value: 20
            }
        ],

        // 告警级别
        alarmLevel: [{
                label: "高",
                value: 30
            },
            {
                label: "中",
                value: 20
            },
            {
                label: "低",
                value: 10
            }
        ],
        // 处理状态
        processState: [{
                label: "待处理",
                value: 10
            },
            {
                label: "已处理",
                value: 20
            }
        ],
        // 巡检类型
        checkingType: [{
                label: "按出勤次数",
                value: 10
            },
            {
                label: "按巡检里程",
                value: 20
            },
            {
                label: "按成果数",
                value: 30
            }
        ],
        // 成果类型
        achievementType: [{
                label: "图片",
                value: 10
            },
            {
                label: "视频",
                value: 20
            },
            {
                label: "全景图",
                value: 15
            }
        ],
        // 去雾类型
        defoggingType: [{
                label: "关 闭",
                value: "0"
            },
            {
                label: "普 通",
                value: "1"
            },
            {
                label: "自 动",
                value: "2"
            }
        ],
        // 网格类型
        gridType: [{
                label: "网格线",
                value: "1"
            },
            {
                label: "中心点",
                value: "2"
            },
            {
                label: "网格线和中心点",
                value: "3"
            }
        ],
        // 录像预览分辨率
        videoPreviewDpi: [{
                label: "720p",
                value: 1
            },
            {
                label: "1080p",
                value: 0
            }
        ],
        // 录像预览码率
        videoPreviewKpbs: [{
                label: "1M",
                value: 0
            },
            {
                label: "2M",
                value: 1
            },
            {
                label: "4M",
                value: 2
            }
        ],
        // 快门
        shutterList: [{
                label: "2.0",
                value: 1,
                index: 2000000
            },
            {
                label: "1.0",
                value: 2,
                index: 1000000
            },
            {
                label: "1/2",
                value: 3,
                index: 500000
            },
            {
                label: "1/5",
                value: 4,
                index: 200000
            },
            {
                label: "1/10",
                value: 5,
                index: 100000
            },
            {
                label: "1/13",
                value: 6,
                index: 76923
            },
            {
                label: "1/15",
                value: 7,
                index: 66666
            },
            {
                label: "1/20",
                value: 8,
                index: 50000
            },
            {
                label: "1/25",
                value: 9,
                index: 40000
            },
            {
                label: "1/30",
                value: 10,
                index: 33333
            },
            {
                label: "1/50",
                value: 11,
                index: 20000
            },
            {
                label: "1/100",
                value: 12,
                index: 10000
            },
            {
                label: "1/125",
                value: 13,
                index: 8000
            },
            {
                label: "1/200",
                value: 14,
                index: 5000
            },
            {
                label: "1/250",
                value: 15,
                index: 4000
            },
            {
                label: "1/500",
                value: 16,
                index: 2000
            },
            {
                label: "1/1000",
                value: 17,
                index: 1000
            },
            {
                label: "1/1200",
                value: 18,
                index: 833
            },
            {
                label: "1/1500",
                value: 19,
                index: 666
            },
            {
                label: "1/1800",
                value: 20,
                index: 555
            },
            {
                label: "1/2000",
                value: 21,
                index: 500
            },
            {
                label: "1/2500",
                value: 22,
                index: 399
            },
            {
                label: "1/3000",
                value: 23,
                index: 333
            },
            {
                label: "1/5000",
                value: 24,
                index: 199
            },
            {
                label: "1/10000",
                value: 25,
                index: 99
            }
        ],
        // 0,0,0,0,0,106,104,102,100,98,96,94,92,90,88,86,84,82,80,78,76,74,72,70,68,66,64,62,60,58,56
        //     ,60,64,68,72,76,80,84,88,92,96,100,108,112,116,124,128,132,136,140,144,152,156,160,164,168
        //     ,172,176

        // 曝光补偿
        exposureNegList: [{
                label: "0",
                value: 0
            },
            {
                label: "0",
                value: 1
            },
            {
                label: "0",
                value: 2
            },
            {
                label: "0",
                value: 3
            },
            {
                label: "0",
                value: 4
            },
            {
                label: "106",
                value: 0
            },
            {
                label: "104",
                value: 0
            },
            {
                label: "102",
                value: 0
            },
            {
                label: "100",
                value: 0
            },
            {
                label: "98",
                value: 0
            },
            {
                label: "96",
                value: 0
            },
            {
                label: "94",
                value: 0
            },
            {
                label: "92",
                value: 0
            },
            {
                label: "90",
                value: 0
            },
            {
                label: "88",
                value: 0
            },
            {
                label: "86",
                value: 0
            },
            {
                label: "84",
                value: 0
            },
            {
                label: "82",
                value: 0
            },
            {
                label: "80",
                value: 0
            },
            {
                label: "78",
                value: 0
            },
            {
                label: "76",
                value: 0
            },
            {
                label: "74",
                value: 0
            },
            {
                label: "72",
                value: 0
            },
            {
                label: "70",
                value: 0
            },
            {
                label: "68",
                value: 0
            },
            {
                label: "66",
                value: 0
            },
            {
                label: "64",
                value: 0
            },
            {
                label: "62",
                value: 0
            },
            {
                label: "60",
                value: 0
            },
            {
                label: "72",
                value: 0
            },
        ],
        // exposureNegList: [
        //     { label: "-5.0", value: 0 },
        //     { label: "-4.5", value: 1 },
        //     { label: "-4.0", value: 2 },
        //     { label: "-3.5", value: 3 },
        //     { label: "-3.0", value: 4 },
        //     { label: "-2.5", value: 5 },
        //     { label: "-2.0", value: 6 },
        //     { label: "-1.5", value: 7 },
        //     { label: "-1.0", value: 8 },
        //     { label: "-0.5", value: 9 },
        //     { label: "0.0", value: 10 },
        //     { label: "0.5", value: 11 },
        //     { label: "1.0", value: 12 },
        //     { label: "1.5", value: 13 },
        //     { label: "2.0", value: 14 },
        //     { label: "2.5", value: 15 },
        //     { label: "3.0", value: 16 },
        //     { label: "3.5", value: 17 },
        //     { label: "4.0", value: 18 },
        //     { label: "4.5", value: 19 },
        //     { label: "5.0", value: 20 }
        // ],
        // 相机iso
        isoList: [{
                label: "100",
                value: 100
            },
            {
                label: "200",
                value: 200
            },
            {
                label: "300",
                value: 300
            },
            {
                label: "400",
                value: 400
            },
            {
                label: "500",
                value: 500
            },
            {
                label: "600",
                value: 600
            },
            {
                label: "700",
                value: 700
            },
            {
                label: "800",
                value: 800
            },
            {
                label: "900",
                value: 900
            },
            {
                label: "1000",
                value: 1000
            },
            {
                label: "1100",
                value: 1100
            },
            {
                label: "1200",
                value: 1200
            },
            {
                label: "1300",
                value: 1300
            },
            {
                label: "1400",
                value: 1400
            },
            {
                label: "1500",
                value: 1500
            },
            {
                label: "1600",
                value: 1600
            },
            {
                label: "1700",
                value: 1700
            },
            {
                label: "1800",
                value: 1800
            },
            {
                label: "1900",
                value: 1900
            },
            {
                label: "2000",
                value: 2000
            },
            {
                label: "2100",
                value: 2100
            },
            {
                label: "2200",
                value: 2200
            },
            {
                label: "2300",
                value: 2300
            },
            {
                label: "2400",
                value: 2400
            },
            {
                label: "2500",
                value: 2500
            },
            {
                label: "2600",
                value: 2600
            },
            {
                label: "2700",
                value: 2700
            },
            {
                label: "2800",
                value: 2800
            },
            {
                label: "2900",
                value: 2900
            },
            {
                label: "3000",
                value: 3000
            },
            {
                label: "3100",
                value: 3100
            },
            {
                label: "3200",
                value: 3200
            },
            {
                label: "3300",
                value: 3300
            },
            {
                label: "3400",
                value: 3400
            },
            {
                label: "3500",
                value: 3500
            },
            {
                label: "3600",
                value: 3600
            },
            {
                label: "3700",
                value: 3700
            },
            {
                label: "3800",
                value: 3800
            },
            {
                label: "3900",
                value: 3900
            },
            {
                label: "4000",
                value: 4000
            },
            {
                label: "4100",
                value: 4100
            },
            {
                label: "4200",
                value: 4200
            },
            {
                label: "4300",
                value: 4300
            },
            {
                label: "4400",
                value: 4400
            },
            {
                label: "4500",
                value: 4500
            },
            {
                label: "4600",
                value: 4600
            },
            {
                label: "4700",
                value: 4700
            },
            {
                label: "4800",
                value: 4800
            },
        ],
        // 白平衡
        // '自动', '烛光', '钨丝灯', '荧光灯', '日光', '多云', '阴天', '天蓝', '烟雾'
        whiteBalance: [{
                label: "自动",
                value: 0
            },
            {
                label: "烛光",
                value: 1
            },
            {
                label: "钨丝灯",
                value: 2
            },
            {
                label: "荧光灯",
                value: 3
            },
            {
                label: "日光",
                value: 4
            },
            {
                label: "多云",
                value: 5
            },
            {
                label: "阴天",
                value: 6
            },
            {
                label: "天蓝",
                value: 7
            },
            {
                label: "烟雾",
                value: 8
            },
        ],

        // 飞行模式
        flightModeList: {
            2: "姿态模式",
            // 2: "模式错误",   
            3: "自动模式",
            4: "跟随模式",
            5: "GPS模式",
            6: "返航模式",
            9: "降落飞行模式",
            13: "运动模式"
        },

        // 英语支持设置列表
        languageList: [{
                label: "中文",
                value: "chinese"
            },
            {
                label: "英文",
                value: "english"
            },
        ],

        // 主题列表
        themeList: [
            { label: "默认", value: "default" },
            { label: "简洁", value: "concise" }
        ],

        // 相机双光伪彩模式
        careraPcolor: [
            { label: "白热", value: 0 },
            { label: "熔岩", value: 1 },
            { label: "铁红", value: 2 },
            { label: "热红", value: 3 },
            { label: "琥珀1", value: 4 },
            { label: "琥珀2", value: 5 },
            { label: "蓝红黄", value: 6 },
            { label: "黑红", value: 7 },
            { label: "红1", value: 8 },
            { label: "红2", value: 9 },
            // { label: "空", value: 10 },
        ]
    },
    mutations: {},
    actions: {}
}

import language from "@/utils/language/index.js";
let dict = language.dict;
let state = dictData.state;
for (let k in dict) {
    if (state[k]) {
        for (let i = 0; i < state[k].length; i++) {
            state[k][i].label = dict[k][state[k][i].value];
        }
    }
}

export default dictData;