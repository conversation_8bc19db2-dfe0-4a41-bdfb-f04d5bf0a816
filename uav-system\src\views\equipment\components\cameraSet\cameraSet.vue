<template>
  <div class="cameraSet">
    <div class="camera-item">
      <span class="camera-item-title">{{
        equipLanguage.cameraParams.ISO
      }}</span>
      <div class="camera-item-1">
        <el-button
          icon="el-icon-minus"
          type="text"
          @click="change('iso', 0)"
          :disabled="listValue.camera_iso == 100"
        ></el-button>
        <span class="content">{{ iso_value }}</span>
        <el-button
          icon="el-icon-plus"
          type="text"
          @click="change('iso', 1)"
          :disabled="listValue.camera_iso == 4800"
        ></el-button>
      </div>
    </div>
    <div class="camera-item">
      <span class="camera-item-title">{{
        equipLanguage.cameraParams.shutter
      }}</span>
      <div class="camera-item-1">
        <el-button
          icon="el-icon-minus"
          type="text"
          @click="change('shutter', 0)"
        ></el-button>
        <span class="content">{{ shutter_value }}</span>
        <el-button
          icon="el-icon-plus"
          type="text"
          @click="change('shutter', 1)"
        ></el-button>
      </div>
    </div>
    <div class="camera-item">
      <span class="camera-item-title">{{
        equipLanguage.cameraParams.expModel
      }}</span>
      <el-button
        class="modelBtn"
        type="text"
        :class="deviceItemList.camera_exp_mode != 1 ? 'active' : ''"
        @click="change('expMode', 0)"
        >AUTO</el-button
      ><el-button
        class="modelBtn"
        type="text"
        :class="deviceItemList.camera_exp_mode == 1 ? 'active' : ''"
        @click="change('expMode', 1)"
        >M</el-button
      >
    </div>
    <div class="camera-item">
      <span class="camera-item-title">{{
        equipLanguage.cameraParams.expValue
      }}</span>
      <div class="camera-item-1">
        <el-button
          icon="el-icon-minus"
          type="text"
          @click="change('expValue', 0)"
        ></el-button>
        <span class="content">{{ camera_exp_value }}</span>
        <el-button
          icon="el-icon-plus"
          type="text"
          @click="change('expValue', 1)"
        ></el-button>
      </div>
    </div>
    <div class="camera-item" v-for="item in selsectList" :key="item.id">
      <span class="camera-item-title">{{ item.title }}</span>
      <el-select
        v-model="item.value"
        :placeholder="item.placeholder"
        popper-class="selectMode"
        @change="change(item.id)"
      >
        <el-option
          v-for="item1 in item.options"
          :key="item1.value"
          :label="item1.label"
          :value="item1.value"
        >
        </el-option>
      </el-select>
      <div
        class="camera-item-1 camera-continues"
        v-if="item.id == 'photoMode' && item.value == 2"
      >
        <el-button
          icon="el-icon-minus"
          type="text"
          @click="change('capContinues', 0)"
        ></el-button>
        <span class="content">{{ cap_continues }}</span>
        <el-button
          icon="el-icon-plus"
          type="text"
          @click="change('capContinues', 1)"
        ></el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "cameraSet",
  props: {
    deviceItemList: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    websocket1: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    equipLanguage: {
      type: [Object, String],
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      listValue: "",
      camera_shutter: {
        99: "1/10000",
        199: "1/5000",
        333: "1/3000",
        399: "1/2500",
        500: "1/2000",
        555: "1/1800",
        666: "1/1500",
        833: "1/1200",
        1000: "1/1000",
        2000: "1/500",
        4000: "1/250",
        5000: "1/200",
        8000: "1/125",
        10000: "1/100",
        20000: "1/500",
        33333: "1/300",
        40000: "1/25",
        50000: "1/20",
        66666: "1/15",
        76923: "1/13",
        100000: "1/10",
        200000: "1/5",
        500000: "1/2",
        1000000: "1.0",
        2000000: "2.0",
      },
      selsectList: [],
      cap_continues: 1,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.selsectList = this.equipLanguage.selsectList;
    });
  },
  computed: {
    camera_exp_value() {
      if (this.deviceItemList) {
        let value = (86 - this.deviceItemList.camera_exp_value) / 10;
        return value;
      } else {
        return 0;
      }
    },
    iso_value() {
      if (this.deviceItemList) {
        return this.deviceItemList.camera_iso;
      } else {
        return 100;
      }
    },
    shutter_value() {
      if (this.deviceItemList) {
        return camera_shutter[this.deviceItemList.camera_shutter]
          ? camera_shutter[this.deviceItemList.camera_shutter]
          : camera_shutter[0];
      } else {
        return camera_shutter[99];
      }
    },
  },
  methods: {
    change(id, index) {
      let num = 0;
      let value = "";
      switch (id) {
        case "iso":
          value = this.listValue.camera_iso;
          if (index == 0) {
            if (value > 100) {
              value = value - 100;
            }
          } else if (index == 1) {
            if (value < 4800) {
              value = value + 100;
            }
          }
          this.websocket1.manualSend({ iso: value }, 409);
          break;
        case "shutter":
          num = this.deviceItemList.camera_shutter || 99;
          let keys = Object.keys(this.camera_shutter);
          let a = keys.indexOf(num.toString());
          if (index == 0) {
            if (a > 0) {
              num = keys[a + 1];
            }
          } else if (index == 1) {
            if (a < keys.length - 1) {
              num = keys[a + 1];
            }
          }
          value = this.camera_shutter[num];
          this.websocket1.manualSend({ shutter: num }, 410);
          break;
        case "expMode":
          if (index == 0) {
            this.websocket1.manualSend({ exposure_mode: 0 }, 412);
          } else if (index == 1) {
            this.websocket1.manualSend({ exposure_mode: 1 }, 412);
          }
          break;
        case "expValue":
          if (index == 0) {
            value = this.deviceItemList.camera_exp_value + 2;
          } else if (index == 1) {
            value = this.deviceItemList.camera_exp_value - 2;
          }
          this.websocket1.manualSend({ exposure_value: value }, 413);
          break;
        case "photoMode":
        case "photoFormat":
        case "photoSize":
        case "capContinues":
          let e = this.selsectList.findIndex((item) => {
            return item.id == "photoMode";
          });
          let b = this.selsectList.findIndex((item) => {
            return item.id == "photoFormat";
          });
          let c = this.selsectList.findIndex((item) => {
            return item.id == "photoSize";
          });
          if (id == "capContinues") {
            if (index == 0) {
              if (this.cap_continues > 0) {
                this.cap_continues -= 1;
              }
            } else {
              this.cap_continues += 1;
            }
          }

          let param = {
            cap_mode: this.selsectList[e].value,
            cap_continues: this.cap_continues,
            cap_save_format: this.selsectList[b].value,
            cap_resolution: this.selsectList[c].value,
          };
          this.websocket1.manualSend(param, 428);
          break;
        case "resolving":
        case "rateCode":
          let f = this.selsectList.findIndex((item) => {
            return item.id == "resolving";
          });
          let g = this.selsectList.findIndex((item) => {
            return item.id == "rateCode";
          });
          let params = {
            preview_bitrate: this.selsectList[g].value,
            preview_resolution: this.selsectList[f].value,
          };
          this.websocket1.manualSend(params, 406);
          break;
        case "videotapeResolving":
        case "videotapeRateCode":
          let v = this.selsectList.findIndex((item) => {
            return item.id == "videotapeResolving";
          });
          let vc = this.selsectList.findIndex((item) => {
            return item.id == "videotapeRateCode";
          });
          let params1 = {
            record_bitrate: this.selsectList[vc].value,
            record_resolution: this.selsectList[v].value,
          };
          this.websocket1.manualSend(params1, 407);
          break;
        case "awb":
          let awb = this.selsectList.findIndex((item) => {
            return item.id == "awb";
          });
          this.websocket1.manualSend({ awb: this.selsectList[awb].value }, 411);
          break;
        default:
          break;
      }
    },
  },

  watch: {
    deviceItemList: {
      handler(val) {
        this.listValue = val;
        let index = this.selsectList.findIndex((item) => {
          return item.id == "awb";
        });
        this.selsectList[index].value = this.listValue.camera_awb;
        let index1 = this.selsectList.findIndex((item) => {
          return item.id == "resolving";
        });
        this.selsectList[index1].value = this.listValue.preview_resolution;
        let index2 = this.selsectList.findIndex((item) => {
          return item.id == "rateCode";
        });
        this.selsectList[index2].value = this.listValue.preview_bitrate;
        let index3 = this.selsectList.findIndex((item) => {
          return item.id == "videotapeResolving";
        });
        this.selsectList[index3].value = this.listValue.record_resolution;
        let index4 = this.selsectList.findIndex((item) => {
          return item.id == "videotapeRateCode";
        });
        this.selsectList[index4].value = this.listValue.record_bitrate;
      },
      deep: true,
    },
  },
};
</script>
<style lang="less" scoped>
.cameraSet {
  width: 100%;
  .camera-item {
    margin: 8px 20px;
    color: white;
    font-size: 16px;
    .camera-item-title {
      width: 80px;
      display: inline-block;
    }
    .camera-item-1 {
      display: inline-block;
      border: 1px solid #eee;
      border-radius: 8px;
      .el-button {
        padding: 5px;
        font-size: 16px;
      }
      .content {
        width: 80px;
        display: inline-block;
        text-align: center;
      }
      &.camera-continues {
        margin-left: 81px;
        margin-top: 2px;
      }
    }
    .modelBtn {
      margin-right: 30px;
      color: white;
      padding: 5px;
      &.active {
        color: rgb(54, 103, 210);
        border: 1px solid #eee;
      }
    }
    .el-select {
      width: 130px;
      margin-right: 5px;
    }
  }
}
</style>
<style lang="less">
.cameraSet {
  .camera-item {
    .el-select {
      .el-input__inner {
        background-color: rgb(56, 56, 56);
        color: white;
        border-radius: 8px;
      }
    }
  }
}
.selectMode {
  &.el-select-dropdown {
    background-color: rgb(16, 28, 67) !important;
    border: none !important;
    color: white;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: rgb(14, 6, 174);
  }
  .el-select-dropdown__item.select {
    background-color: rgb(14, 6, 174);
  }
  .el-select-dropdown__item {
    color: white;
  }
  &.el-popper[x-placement^="bottom"] .popper__arrow,
  &.el-popper[x-placement^="bottom"] .popper__arrow::after {
    border-bottom-color: rgb(16, 28, 67) !important;
  }
  &.el-popper[x-placement^="top"] .popper__arrow,
  &.el-popper[x-placement^="top"] .popper__arrow::after {
    border-top-color: rgb(16, 28, 67) !important;
  }
}
</style>