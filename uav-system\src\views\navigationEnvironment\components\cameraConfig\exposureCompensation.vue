<!-- 曝光补偿 -->
<template>
  <div class="exposure-compensation">
    <div class="left" @click="subtract">
      <i class="el-icon-remove-outline"></i>
    </div>

    <div class="center" ref="scrollBar" style="width: 100%">
      <!-- 前面 -->
      <div
        class=""
        style="display: flex"
        v-for="(item, index) in insertionNum"
        :key="index + 'start'"
        ref="gratingCell"
        @click="cutSlide(index)"
      >
        <div
          class="center-item"
          :style="{ width: slideW + 'px' }"
          :class="selectIndex == index ? 'select-item' : ''"
        >
          <div class="item-top"></div>
          <div class="item-bot"></div>
        </div>
      </div>

      <!-- 中间内容 -->
      <div
        class=""
        style="display: flex"
        v-for="(item, index) in total"
        :key="index"
      >
        <div
          class="center-item"
          v-for="(cell, lens) in partitionNum"
          :key="item + '-' + cell"
          v-show="total !== index + 1"
          :class="
            selectIndex == index * partitionNum + lens + insertionNum
              ? 'select-item'
              : ''
          "
          :style="{ width: slideW + 'px' }"
          @click="cutSlide(index * partitionNum + lens + insertionNum)"
        >
          <div class="item-top" v-if="lens === 0">{{ index - start }}</div>
          <div
            class="item-bot"
            :style="{ height: lens === 0 ? '8px' : '4px' }"
          ></div>
        </div>

        <div
          class="center-item"
          v-show="total == index + 1"
          :style="{ width: slideW + 'px' }"
          :class="
            selectIndex == index * partitionNum + insertionNum
              ? 'select-item'
              : ''
          "
          @click="cutSlide(index * partitionNum + insertionNum)"
        >
          <div class="item-top">{{ index - start }}</div>
          <div class="item-bot" style="height: 8px"></div>
        </div>
      </div>

      <!-- 后面 -->
      <div
        class=""
        style="display: flex"
        v-for="(item, index) in insertionNum"
        :key="index + 'end'"
        :class="
          selectIndex == (total - 1) * partitionNum + index + 2
            ? 'select-item'
            : ''
        "
        @click="cutSlide((total - 1) * partitionNum + index + 2)"
      >
        <div class="center-item" :style="{ width: slideW + 'px' }">
          <div class="item-top"></div>
          <div class="item-bot"></div>
        </div>
      </div>

      <!-- 滑动图标 -->
      <div
        class="select-icon"
        @mousedown="slideIconDown"
        ref="slideIcon"
        :style="slideIconStyle"
      ></div>
    </div>

    <div class="right" @click="plus">
      <i class="el-icon-circle-plus-outline"></i>
    </div>

    <div class="forbid" v-if="readonly"></div>
  </div>
</template>

<script>
export default {
  props: {
    value: [Number, String],
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      start: 3, // 开始值
      total: 7, // 总值
      partitionNum: 5, // 每一个数字分成多少份
      insertionNum: 0, // 前后插入值

      selectIndex: this.value || 0,
      gratingW: 15,
      startX: 0, // 鼠标按下位置
      slideIconX: 0, // 滑块初始位置
      isDown: false, // 是否按下
      scrollBarW: 0, // 滚动条宽度
      slideTotal: 0,
      slideW: 11,
      isChangeEvent: false,
    };
  },
  computed: {
    slideIconStyle() {
      let width = `${this.slideW / 2}px`;
      return {
        "border-left-width": width,
        "border-right-width": width,
        "border-bottom-width": width,
      };
    },
  },
  watch: {
    value: function () {
      if (!this.isDown) {
        this.isChangeEvent = true;
        this.setSelectIndex();
        // this.selectIndex = this.value;

        this.cutSlide(this.value, true);
      }
    },
    selectIndex: function (val) {},
  },
  created() {
    this.slideTotal =
      (this.total - 1) * this.partitionNum + this.insertionNum * 2 + 1;
  },
  mounted() {
    this.$nextTick(() => {
      this.cutSlide(this.selectIndex, true);
      this.gratingW = this.$refs.scrollBar.offsetWidth / this.slideTotal;
      this.slideW = this.gratingW;
      if (this.value) {
        this.setSelectIndex();
      }
    });
  },
  methods: {
    subtract: function () {
      if (this.selectIndex > 0) {
        this.selectIndex--;
        this.cutSlide(this.selectIndex);
      }
    },
    plus: function () {
      let max = this.insertionNum * 2 + (this.total - 1) * this.partitionNum;
      if (this.selectIndex < max) {
        this.selectIndex++;
        this.cutSlide(this.selectIndex);
      }
    },
    cutSlide: function (index, isChange) {
      this.selectIndex = index;
      let gratingW = this.gratingW;
      this.$refs.slideIcon.style.left = this.selectIndex * gratingW + "px";
      let val = this.getScrollVal();
      !isChange && this.$emit("onChange", val, this.selectIndex);
    },

    slideIconDown: function (event) {
      if (this.readonly) {
        console.log("禁止操作------->");
        return;
      }
      this.gratingW = this.$refs.scrollBar.offsetWidth / this.slideTotal;
      this.startX = event.pageX;
      this.slideIconX = this.$refs.slideIcon.offsetLeft;
      this.isDown = true;
      this.scrollBarW = this.$refs.scrollBar.offsetWidth;

      window.addEventListener("mousemove", this.mousemoveEvent);
      window.addEventListener("mouseup", this.mouseupEvent);
    },
    mousemoveEvent: function (event) {
      if (this.isDown) {
        let x = event.pageX - this.startX;
        let left = this.slideIconX + x;
        if (left <= 0) {
          this.$refs.slideIcon.style.left = 0;
        } else if (left + this.gratingW >= this.scrollBarW) {
          this.$refs.slideIcon.style.left =
            this.scrollBarW - this.gratingW + "px";
        } else {
          this.$refs.slideIcon.style.left = left + "px";
        }

        let slideLeft = this.$refs.slideIcon.offsetLeft;
        // 向上取整
        let index = Math.ceil((slideLeft + this.gratingW / 2) / this.gratingW);
        if (index <= 0) {
          this.selectIndex = 0;
        } else {
          this.selectIndex = index - 1;
        }

        let val = this.getScrollVal();

        this.$emit("input", val);
      }
    },
    mouseupEvent: function () {
      if (this.isDown) {
        let slideLeft = this.$refs.slideIcon.offsetLeft;
        // 向上取整
        let index = Math.ceil((slideLeft + this.gratingW / 2) / this.gratingW);
        if (index <= 0) {
          this.selectIndex = 0;
        } else {
          this.selectIndex = index - 1;
        }
        this.$refs.slideIcon.style.left =
          this.selectIndex * this.gratingW + "px";

        window.removeEventListener("mousemove", this.mousemoveEvent);
        window.removeEventListener("mouseup", this.mouseupEvent);

        let val = this.getScrollVal();
        this.$emit("onChange", val, this.selectIndex);
      }
    },
    // 获取当前滚动的值
    getScrollVal: function () {
      let ratio = 1 / this.partitionNum; // 获取每一格的比例
      // 当前值
      let value =
        this.selectIndex * ratio * 100 -
        this.start * 100 -
        this.insertionNum * ratio * 100;
      return value / 100;
    },
    // 回显选中的值
    setSelectIndex: function () {
      // let ratio = 1 / this.partitionNum;
      // let val = this.insertionNum * ratio + this.value + this.start;
      // this.selectIndex = Math.ceil(val / ratio);
      // this.cutSlide(this.selectIndex);
    },
  },
};
</script>

<style lang="less" scoped>
.exposure-compensation {
  display: flex;
  justify-content: space-between;
  width: 100%;
  // color: #fff;
  position: relative;
  .forbid {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 30;
  }
  .left,
  .right {
    display: flex;
    align-items: flex-end;
    font-size: 14px;
    &:hover {
      // color: #398bcc;
    }
  }
  .center {
    position: relative;
    display: flex;
    .select-item {
      .item-top {
        // color: #398bcc !important;
      }
      .item-bot {
        // background-color: #398bcc !important;
      }
    }

    .center-item {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      width: 3px;
      align-items: flex-end;
      &:hover .item-top {
        // color: #398bcc;
      }
      &:hover .item-bot {
        // background-color: #398bcc;
      }

      .item-top {
        font-size: 12px;
        // color: #fff;
        transform: scale(0.8);
        width: 100%;
        text-align: center;
      }
      .item-bot {
        width: 1px;
        height: 4px;
        // background-color: #fff;
      }
    }

    .select-icon {
      width: 0;
      height: 0;
      position: absolute;
      // border-left: 5px solid transparent;
      // border-right: 5px solid transparent;
      // border-bottom: 5px solid #398bcc;
      bottom: -7px;
      left: 0;
      // &::after {
      //   content: "";
      //   width: 0;
      //   height: 0;
      //   position: absolute;
      //   border-left: 5px solid transparent;
      //   border-right: 5px solid transparent;
      //   border-bottom: 5px solid #398bcc;
      //   bottom: -7px;
      //   left: 0;
      // }
    }
  }
}
</style>