<!-- 相机基本配置 -->
<template>
  <div class="camera-basic">
    <div class="basic-item">
      <div class="item-title">{{ language.grid }}</div>
      <div class="item-main" @click="openInside('gridding')">
        <div class="main-left">{{ gridTypeText }}</div>
        <div class="main-right">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>

    <div class="basic-item">
      <div class="item-title">{{ language.defogging }}</div>
      <div class="item-main" @click="openInside('defogging')">
        <div class="main-left">{{ defoggingText }}</div>
        <div class="main-right">
          <!-- <i class="el-icon-arrow-right"></i> -->
          <div class="right-main" :class="defoggingClass">
            <div class="circle"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="basic-item">
      <div class="item-title">{{ language.flicker }}</div>
      <div class="item-main" @click="openInside('flicker')">
        <div class="main-left" :style="resistanceBlink">
          {{ flickerValue || 0 }}
        </div>
        <div class="main-right">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>

    <div class="basic-item">
      <div class="item-title">{{ language.cardCapacity }}</div>
      <div class="item-main" style="display: flex; justify-content: center">
        <div class="main-left" :style="TFCardCapacity" v-if="sdcard_state==2">
          {{ language.noSD }}
        </div>
        <div class="main-left tf-card" v-else>{{ sdcardTotalsize }}</div>
      </div>
    </div>

    <div class="basic-item">
      <div class="item-title">{{ language.formatting1 }}</div>
      <div class="item-main" @click="openInside('formatting')">
        <div class="main-left">{{ language.formatting }}</div>
        <div class="main-right">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>

    <div class="basic-item">
      <div class="item-title">{{ language.capacity }}</div>
      <div class="item-main" style="display: flex; justify-content: center">
        <div
          class="main-left"
          :style="TFCardCapacity"
          v-if="sdcard_state==2"
        >
          {{ language.noSD }}
        </div>

        <div class="main-left tf-card" v-else>{{ sdcardAvailableSize }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      flickerValue: null,
      defoggingType: null,
      gridTypeValue: null,
    };
  },
  computed: {
    sdcard_state(){
      return this.$store.state.equipment.staveTwoData.sdcard_state
    },
    sdcardTotalsize() {
      let size = this.$store.state.equipment.staveTwoData.sdcard_totalsize;
      if (!size) {
        return "";
      }
      let num = (Number(size) / 1024).toFixed(2);
      return `${num}G`;
    },
    sdcardAvailableSize() {
      let size = this.$store.state.equipment.staveTwoData.sdcard_available_size;
      if (!size) {
        return "";
      }

      let num = (Number(size) / 1024).toFixed(2);
      return `${num}G`;
    },

    // 卡容量
    TFCardCapacity() {
      return {
        color: "red",
        "text-align": "center",
      };
    },
    // 抗闪烁
    resistanceBlink() {
      return {
        color: "#008EFF",
      };
    },
    // 去雾类
    defoggingClass() {
      return this.defoggingType && this.defoggingType != "0"
        ? "select-right-style"
        : "";
    },
    defoggingText() {
      let list = this.$store.state.dict.defoggingType;
      let item = list.filter((row) => {
        return row.value == this.defoggingType;
      })[0];
      return item ? item.label : this.language.shut;
    },
    gridTypeText() {
      let list = this.$store.state.dict.gridType;
      let item = list.filter((row) => {
        return row.value == this.gridTypeValue;
      })[0];
      return item ? item.label : this.language.shut;
    },
    language() {
      return this.$languagePackage.navigation.cameraConfig.more;
    },
  },
  created() {
    this.flickerValue = this.$store.state.camera.flickerVal;
    this.defoggingType = this.$store.state.camera.defoggingType;
    this.gridTypeValue = this.$store.state.camera.gridTypeVal;
  },
  methods: {
    openInside: function (key) {
      this.$store.commit("setShowComponents", "moreInside");
      this.$store.commit("setHierarchy", 2);
      this.$store.commit("setMoreInsideType", key);
    },
  },
};
</script>

<style lang="less" scoped>
.camera-basic {
  padding: 0 50px 20px 50px;
  display: flex;
  flex-wrap: wrap;
  .basic-item {
    width: 50%;
    // color: #fff;
    .tf-card {
      // color: #398bcc;
    }
    .item-title {
      margin: 28px 0 12px 0;
      font-size: 12px;
    }
    .item-main {
      width: 114px;
      height: 22px;
      padding: 0 38px;
      // border: 1px solid #c2c3c3;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      &:hover .main-right {
        color: #398bcc;
      }

      .right-main {
        width: 32px;
        height: 16px;
        // border: 1px solid #fff;
        border-radius: 8px;
        display: flex;
        align-items: center;
        .circle {
          width: 16px;
          height: 16px;
          // background-color: #fff;
          border-radius: 50%;
        }
      }
    }
    .select-right-style {
      // border-color: #398bcc !important;
      justify-content: flex-end;
      .circle {
        // background-color: #398bcc !important;
      }
    }
  }
}
</style>