<template>
  <div class="firmwareOperate">
    <el-dialog
      :title="
        form.id
          ? language.dialogTitle2
          : code
          ? language.dialogTitle
          : language.dialogTitle1
      "
      :visible.sync="show"
      :center="true"
      custom-class="upgrade-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :destroy-on-close="true"
      width="650px"
    >
      <div class="changeBtn" v-if="code">
        <el-button icon="el-icon-sort" type="primary" @click="changeChoose">{{
          uploadCode ? language.reupgrade : language.upgraded
        }}</el-button>
      </div>
      <el-form
        label-width="120px"
        :model="form"
        :rules="rules"
        ref="form"
        v-if="!uploadCode"
      >
        <el-form-item :label="language.fileTitle" prop="files" v-if="!form.id">
          <el-upload
            class="upload-demo"
            action=""
            :on-change="changeUpload"
            :before-remove="beforeRemove"
            accept=".wkimg"
            :auto-upload="false"
          >
            <div class="">
              <el-button size="small" type="primary">{{
                language.clickChoose
              }}</el-button>
              <span
                class="el-upload__tip"
                style="color: #ccc; font-size: 14px; margin-left: 20px"
              >
                {{ language.uploadTip }}
              </span>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item :label="language.type" prop="type">
          <el-input
            v-model="form.type"
            :placeholder="language.placeholder"
            readonly
            style="width: 90%"
          ></el-input>
        </el-form-item>
        <el-form-item :label="language.vCode" prop="type">
          <el-input
            v-model="form.version_code"
            :placeholder="language.placeholder"
            readonly
            style="width: 90%"
          ></el-input>
        </el-form-item>
        <el-form-item :label="language.vName" prop="type">
          <el-input
            v-model="form.version_name"
            :placeholder="language.placeholder"
            readonly
            style="width: 90%"
          ></el-input>
        </el-form-item>

        <el-form-item :label="language.desc">
          <el-input
            :placeholder="language.placeholder"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 10 }"
            v-model="form.description"
            style="width: 90%"
          />
        </el-form-item>
        <el-form-item :label="language.desc_en" prop="description_en">
          <el-input
            :placeholder="language.placeholder1"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 10 }"
            v-model="form.description_en"
            style="width: 90%"
          />
        </el-form-item>
      </el-form>
      <div class="table-list" v-if="uploadCode">
        <custom-table
          :column="column"
          urlName="firmwareList"
          ref="customTable"
          :border="true"
        >
          <template #state="scope">
            <!-- .replace(/\n|\r\n/g, '<br>')  -->
            {{ scope.row.state == 10 ? language1.normal : "" }}
          </template>
          <template #operation="scope">
            <!-- .replace(/\n|\r\n/g, '<br>')  -->
            <el-button type="text" @click="upgradate(scope.row)">{{
              language.upgradeText
            }}</el-button>
          </template>
        </custom-table>
      </div>

      <span slot="footer" class="dialog-footer">
        <slot name="footer">
          <el-button type="primary" @click="submit" v-if="!uploadCode">{{
            language.submit
          }}</el-button>
          <el-button @click="closeDialog(1)">{{ language.cancel }}</el-button>
        </slot>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
import customTable from "@/components/customTable/index.vue";
export default {
  props: {
    code: Boolean,
  },
  components: {
    customTable,
  },
  data() {
    return {
      show: false,
      uploadCode: false,
      form: {
        files: "",
        type: "",
        version_code: "",
        version_name: "",
        description: "",
        description_en: "",
      },
      column: [
        { label: "类型", prop: "type" },
        { label: "版本号", prop: "version_code" },
        { label: "版本名", prop: "version_name" },
        { label: "状态", prop: "state" },
        { label: "操作", prop: "operation" },
      ],
      rules: {
        files: [
          { required: true, trigger: "change", message: "请上传固件文件" },
        ],
        // description_en: [
        //   { validator: this.descEnFormat, trigger: ["blur", "change"] },
        // ],
      },
      uploadLoading: "",
      websocket: "",
    };
  },
  computed: {
    language() {
      return this.$languagePackage.firmware.upgradeFirmware;
    },
    language1() {
      return this.$languagePackage.firmware;
    },
  },
  created() {
    this.rules.files[0].message = this.language.errorTip;
    // console.log(md5('8c7a9ofe228ddc3aa163a2f2d7063a8a.wkimg'))
  },
  methods: {
    changeChoose() {
      this.uploadCode = !this.uploadCode;
      if (this.uploadCode) {
        for (let index = 0; index < this.column.length; index++) {
          this.column[index].label =
            this.language1.column[this.column[index].prop];
        }
      }
    },
    upgradate(row) {
      let self = this;
      this.$confirm(this.language.tipText, this.language.Tip, {
        confirmButtonText: this.language.submit,
        cancelButtonText: this.language.cancel,
        type: "warning",
      }).then(() => {
        // self.uploadLoading = this.$loading({
        //   text: self.form.id
        //     ? self.language.loadingText1
        //     : self.language.loadingText,
        //   spinner: "el-icon-loading",
        //   background: "rgba(0, 0, 0, 0.7)",
        // });
        const xhr = new XMLHttpRequest();
        xhr.open("GET", row.file_url, true);
        xhr.responseType = "arraybuffer";
        xhr.onload = () => {
          if (xhr.status === 200) {
            // resolve(xhr.response);
            let params = {
              file_url: row.file_url,
              filesize: row.size_kb,
              md5value: md5(xhr.response),
            };
            self.websocket && self.websocket.manualSend(params, 408);
            // setTimeout(() => {
              this.$message.success(this.language.successTip1);
              self.closeDialog(1);
               this.$emit("updateVersion", "");
            // }, 1000);
          }
        };
        xhr.send();
      });
    },
    descEnFormat(rule, value, callback) {
      if (value) {
        callback();
      } else {
        callback(new Error(this.language.placeholder1));
      }
    },
    open(item, websocket) {
      this.show = true;
      this.uploadCode = false;
      this.websocket = websocket;
      if (item) {
        this.form = {
          id: item.id,
          state: item.state,
          type: item.type,
          version_code: item.version_code,
          version_name: item.version_name,
          description: item.description,
          description_en: item.description_en,
        };
      }
    },
    changeUpload(file, fileList) {
      if (fileList.length > 1) {
        fileList.shift();
      }
      this.form.files = file.raw;
      let reader = new FileReader();
      reader.readAsArrayBuffer(file.raw);
      let self = this;
      reader.onload = (e) => {
        console.log(md5(e.target.result));
        let enc = new TextDecoder("utf-8");
        let uint8_msg = new Uint8Array(e.target.result);
        let lenList = uint8_msg.slice(53, 57);
        let len =
          lenList[0] +
          lenList[1] * 256 +
          lenList[2] * 256 * 256 +
          lenList[3] * 256 * 256 +
          57;
        if (len - 57 <= 0 || len > 1024 * 1024 * 10) {
          self.$message.error({
            message: self.language.errorFile,
          });
          return reject(false);
        }
        let decodedString = enc.decode(uint8_msg.slice(57, len));
        let data = JSON.parse(decodedString);
        self.form.type = data.deviceId;
        self.form.version_code = data.versionCode;
        self.form.version_name = data.versionName;
        self.form.description = data.description;
      };
    },
    beforeRemove(file) {
      this.form = {
        files: "",
        type: "",
        version_code: "",
        version_name: "",
        description: "",
        description_en: "",
      };
      this.$refs.form.clearValidate();
    },
    submit() {
      // let data = {
      //   file_url:
      //     "https://fly.walkera.cn/ff-bate/rom/download?name=ac1a37849097f5f873ffd8efa8bda8ca.wkimg",
      // };
      // this.websocket && this.websocket.manualSend(data, 408);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.uploadLoading = this.$loading({
            text: this.form.id
              ? this.language.loadingText1
              : this.language.loadingText,
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let item = Object.assign({}, this.form);
          if (item.id) {
            let { type, ...data } = item;
            data.pmd =
              data.id.toString() +
              data.state.toString() +
              data.version_code.toString() +
              data.version_name.toString() +
              data.description.toString() +
              data.description_en.toString();
            requestHttp("editFirmware", data)
              .then((res) => {
                this.$message.success(this.language.successTip);
                this.closeDialog();
                this.$emit("refresh", "");
              })
              .catch(() => {
                if (this.uploadLoading) {
                  this.uploadLoading.close();
                  this.uploadLoading = "";
                }
              });
          } else {
            let { files, ...data } = item;
            data.file = item.files;
            data.pmd =
              data.type.toString() +
              data.version_code.toString() +
              data.version_name.toString() +
              data.description.toString() +
              data.description_en.toString();
            requestHttp("uploadFirmware", data, "", true)
              .then((res) => {
                this.$message.success(this.language.successTip1);
                this.closeDialog();
                this.$emit("refresh", "");
                if (this.code) {
                  let self = this;
                  let reader = new FileReader();
                  reader.onload = function () {
                    //文件读取成功时触发
                    // resolve(reader.result)
                    let params = {
                      file_url: res.data.file_url,
                      filesize: res.data.size_kb,
                      md5value: md5(reader.result),
                    };
                    self.websocket && self.websocket.manualSend(params, 408);
                  };
                  reader.readAsArrayBuffer(data.file);
                  this.$emit("updateVersion", "");
                }
              })
              .catch(() => {
                if (this.uploadLoading) {
                  this.uploadLoading.close();
                  this.uploadLoading = "";
                }
              });
          }
        }
      });
    },
    closeDialog(num) {
      this.form = {
        files: "",
        type: "",
        version_code: "",
        version_name: "",
        description: "",
        description_en: "",
      };
      this.$refs.form && this.$refs.form.clearValidate();
      this.show = false;
      this.uploadCode = false;
      if (this.uploadLoading) {
        this.uploadLoading.close();
        this.uploadLoading = "";
      }
      if (num && this.code) {
        this.$emit("refresh", "");
      }
    },
  },
};
</script>
<style lang="less">
.firmwareOperate {
  .upgrade-dialog {
    .el-dialog__header {
      border-bottom: 1px solid #0c31ac;
      .el-dialog__title {
        font-size: 24px !important;
        font-weight: 550;
      }
    }
    .el-dialog__body {
      padding-bottom: 0 !important;
      .changeBtn {
        margin-top: -24px;
        text-align: right;
        .el-button {
          padding: 6px 12px;
          i {
            transform: rotate(90deg);
          }
        }
      }
      .table-list {
        margin-top: 10px;
        // height: 500px;
        .el-table {
          height: 400px !important;
        }
        .custom-table .custom-table-header,
        .custom-table .el-table__row {
          background-color: transparent !important;
        }
        .custom-table .el-table__cell {
          color: #000 !important;
        }
        .el-table td.el-table__cell,
        .el-table th.el-table__cell.is-leaf {
          border-bottom: 1px solid #ebeef5 !important;
          border-right: 1px solid #ebeef5 !important;
        }
        .el-pagination {
          .btn-prev,
          .btn-next,
          .el-pager li {
            color: rgb(0, 0, 0) !important;
            border-color: rgb(97, 97, 97) !important;
          }
          .el-pager li:not(.disabled).active {
            color: #fff !important;
          }
        }
      }
      .el-upload-list__item {
        transition-duration: 0.1s;
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .firmwareOperate {
    .upgrade-dialog {
      .el-dialog__header {
        .el-dialog__title {
          font-size: @zoomIndex * 24px !important;
        }
      }
      input::-webkit-input-placeholder {
        color: rgb(167, 167, 167) !important;
      }
      input::-moz-input-placeholder {
        color: rgb(167, 167, 167) !important;
      }
      input::-ms-input-placeholder {
        color: rgb(167, 167, 167) !important;
      }
    }
  }
}
</style>