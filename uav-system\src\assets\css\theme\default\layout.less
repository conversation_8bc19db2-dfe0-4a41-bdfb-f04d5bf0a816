// 页面布局样式
.layout {
  #layout {
    .default-map {
      background-color: #000;
    }
  }

  #layout-header {

    background-color: rgba(17, 27, 47, 1);

    .layout-header-main {
      .header-nav {
        .nav-title {
          color: #fff;

          .segmentation {
            .dynamic-light-waves {
              background-image: linear-gradient(to right,
                  rgba(119, 161, 246, 0.1),
                  rgba(59, 117, 235, 0.4),
                  rgba(255, 255, 255, 1),
                  rgba(59, 117, 235, 0.4),
                  rgba(119, 161, 246, 0.1));

            }
          }

          .header-nav-cell {
            color: rgb(96, 216, 252);
          }
        }

        .header-user {

          color: #fff;

          .message-text {
            color: rgb(96, 216, 252);

          }
        }
      }


    }
  }

  .header-user-popover {
    background-color: #13213d;

    .user-popover {
      .popover-item {
        color: #90969b;

        &:hover {
          color: #60d8fc;
          background-color: #0d1930;
        }
      }
    }
  }

  .layout-nav-poppers {
    background-color: #13213d;

    .foxbase-menus {
      color: #90969b;

      &:hover {
        color: #60d8fc;
        background-color: #0d1930;
      }
    }
  }
}
