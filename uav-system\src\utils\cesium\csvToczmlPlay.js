/**
 * csv文件解析成czml数据格式实现轨迹回放
 */

/**
 * 获取当前日期格式或cesium时钟格式
 * @param {*} time 
 * @returns 
 */
export function dateFormat(time) {
    let date = new Date();
    if (time || time === 0) {
        date = new Date(time);
    }
    let year = date.getFullYear(); // 年
    let month = date.getMonth() + 1; // 月
    month = String(month).padStart(2, "0")
    let day = date.getDate(); // 日
    day = String(day).padStart(2, "0")
    let hour = date.getHours(); // 时
    hour = String(hour).padStart(2, "0")
    let minute = date.getMinutes(); // 分
    minute = String(minute).padStart(2, "0")
    let second = date.getSeconds(); // 秒
    second = String(second).padStart(2, "0")
    let dateTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`
    let CZMLClock = `${year}-${month}-${day}T${hour}:${minute}:${second}Z`
    return {
        year,
        month,
        day,
        hour,
        minute,
        second,
        dateTime,
        CZMLClock
    }
}

/**
 *  解析csv文件并生成czml格式数据
 * @param {*} viewer cesium实例
 * @param {*} config csv文件路径
 */
import {
    wgs84_to_gcj02
} from "../wgs84_to_gcj02"
import {
    pointsConvert
} from "../coordinateConvert";
import axios from "axios";
const d3 = require("d3-dsv");
export function setCzmlData(viewer, config = {}) {
    let {
        url, // csv文件
        playEvene, // 播放事件，返回播放状态
        endPlay, // 结束事件，返回最后一个点
        playChange, // 播放改变时间，返回当前播放点，播放点对应的数据，时间
        color = [170, 255, 85, 255], // 线条颜色
        width = 6, // 线条大小
        outlineColor = [170, 255, 85, 255], // 边框颜色
        outlineWidth = 6, // 边框大小
        // modelUrl = process.env.VUE_APP_STATIC_URL + "/glbModel/Cesium_Air.glb", // 模型地址
        modelUrl = process.env.VUE_APP_STATIC_URL + "/glbModel/uav_3D.glb", // 模型地址
    } = config;
    return new Promise((resolve, reject) => {
        if (!url) {
            return reject("csv文件不能为空")
        }
        const czml = [{
                id: "document",
                name: "CZML",
                version: "1.0",
                clock: {
                    interval: "2022-08-04T10:00:00Z/2022-08-04T15:00:00Z", // 时间段
                    currentTime: "2022-08-04T10:00:00Z", // 开始时间
                    multiplier: 1, // 已多少倍速播放
                    range: "CLAMPED",
                },
            },
            {
                id: "path",
                name: "path with GPS flight data",
                description: "",
                availability: "2022-08-04T10:00:00Z/2022-08-04T15:00:00Z", // 时间段 
                path: {
                    material: {
                        polylineOutline: {
                            color: {
                                rgba: color,
                            },
                            outlineColor: {
                                rgba: outlineColor,
                            },
                            outlineWidth: outlineWidth,
                        },
                    },
                    width: width,
                    leadTime: 0, // 提前绘制时间
                },
                model: {
                    gltf: modelUrl,
                    scale: 0.1,
                    minimumPixelSize: 100,
                },
                position: {
                    epoch: "2022-08-04T10:00:00Z", // 该项开始绘制路径时间
                    cartographicDegrees: [],
                },
            },
        ];
        let entity = ''
        axios.get(url).then((res) => {
            let index = res.data.indexOf("End") + 4;
            let list = res.data.substr(index);
            let data = d3.csvParse(list);
            let returnData = generateData(data)
            let newData = [];
            let time = Number(data[0].tms);

            let start = new Date(Number(data[0].tms)).toJSON().trim()
            let end = new Date(Number(data[data.length - 1].tms)).toJSON().trim()

            czml[0].clock.currentTime = start;
            czml[0].clock.interval = `${start}/${end}`;
            czml[1].availability = `${start}/${end}`;
            czml[1].position.epoch = `${start}`;

            for (let i = 0; i < data.length; i++) {
                let item = data[i];
                let item1 = returnData.list[i]
                let tms = Number(item.tms) - time;
                newData.push(
                    tms / 1000, // 时间,秒
                    Number(item1.lng),
                    Number(item1.lat),
                    Number(item.height) / 100 // 高度
                );
            }

            czml[1].position.cartographicDegrees = newData;

            // resolve(czml);
            // 更新播放时间
            const updateCurrentTime = (index) => {
                    let time = new Date(Number(data[index].tms));
                    viewer.clockViewModel.currentTime = Cesium.JulianDate.fromDate(time);
                }
                // 变更播放状态
            const updatePlayStatus = (state) => {
                    viewer.clock.canAnimate = state;
                    viewer.clock.shouldAnimate = state;
                }
                //变更倍数
            const updateCurrentMultiplier = (multiplier) => {
                viewer.clockViewModel.multiplier = multiplier;
            }

            let params = {
                returnData,
                czml,
                updateCurrentTime,
                updatePlayStatus,
                updateCurrentMultiplier
            }

            // setTimeout(()=>{
            //     let time = data[0].tms;
            //     time = Number(time) + 60 * 1000
            //     updateCurrentTime(time)
            //     updatePlayStatus(true);
            // }, 5000)

            resolve(params);

            // 监听播放状态
            Cesium.knockout.getObservable(viewer.clockViewModel, 'shouldAnimate').subscribe(function(isAnimating) {
                if (isAnimating) {
                    console.log('Cesium clock is animating.');
                } else {
                    console.log('Cesium clock is paused.');
                }
                playEvene && playEvene(isAnimating)
            });
            // 检测实时播放的时间轴
            let playIndex = 1;
            Cesium.knockout.getObservable(viewer.clockViewModel, 'currentTime').subscribe(function(clockTime) {
                // 播放状态
                let shouldAnimate = viewer.clockViewModel.shouldAnimate;
                // 当前时间轴播放时间
                let currentTime = clockTime.toString();
                let tms = new Date(currentTime).getTime();

                // 播放结束
                if (parseInt(tms / 100) >= parseInt(Number(data[data.length - 1].tms) / 100)) {
                    endPlay && endPlay(data[data.length - 1], data.length - 1);
                    return false;
                }
                if (shouldAnimate) {
                    if (tms >= Number(data[playIndex].tms)) {
                        playIndex++;
                    }
                } else {
                    for (let i = 0; i < data.length; i++) {
                        if (Number(data[i].tms) >= tms) {
                            playIndex = i + 1;
                            break;
                        }
                    }
                }
                // 播放监听事件
                playChange && playChange(playIndex, returnData.list[playIndex], currentTime)

                //设置航向角，根据返回数据设置
                let cartesian3 = Cesium.Cartesian3.fromDegrees(
                    Number(returnData.list[playIndex].lng),
                    Number(returnData.list[playIndex].lat),
                    Number(returnData.list[playIndex].height) / 100 // 高度
                );
                let heading = Cesium.Math.toRadians((returnData.list[playIndex].yaw) - 90);
                let pitch = Cesium.Math.toRadians(returnData.list[playIndex].pitch);
                let roll = Cesium.Math.toRadians(returnData.list[playIndex].roll);
                let hpr = new Cesium.HeadingPitchRoll(heading, pitch, roll);
                entity.orientation = Cesium.Transforms.headingPitchRollQuaternion(
                    cartesian3,
                    hpr
                );
                // entity.orientation
            });

            // 设置播放状态
            // viewer.clockViewModel.shouldAnimate = true;
            viewer.dataSources
                .add(Cesium.CzmlDataSource.load(czml))
                .then(function(ds) {

                    let s = ds.entities.getById("path");

                    viewer.trackedEntity = s; // 设置相机跟随

                    entity = s; //记录实例，方便改变方向角
                    // 设置图标执行下个点
                    //   s.orientation = new Cesium.VelocityOrientationProperty(s.position);
                    //   s.billboard.alignedAxis = new Cesium.VelocityVectorProperty(
                    //     s.position,
                    //     true
                    //   );

                });
        });
    })
}

/**
 * 生成列表数据返回
 * @param {array} data 传入解析后数据
 * @returns {array} 返回列表数据
 */
function generateData(data) {
    let list = [];
    let hData = []; // 高度 
    let vsData = []; // 垂直速度
    let hsData = []; // 水平速度
    let xAxisData = []; // x轴文字
    let dataTime = [];
    let logLatList = [];
    let deWeight = [];
    let distance = "0.00"
    let rocker = {};

    for (let i = 0; i < data.length; i++) {
        let item = data[i];

        // let position = wgs84_to_gcj02(
        //   Number(item.longitudeInt) / 1e7,
        //   Number(item.latitudeInt) / 1e7
        // );
        let position = pointsConvert({
            point: [Number(item.longitudeInt) / 1e7,
                Number(item.latitudeInt) / 1e7
            ],
            type: 10
        });

        list.push({
            lng: position[0].toFixed(7),
            lat: position[1].toFixed(7),
            height: (Number(item.height) / 100).toFixed(2),
            yaw: Number(item.yaw),
            pitch: Number(item.pitch),
            roll: Number(item.roll),
            distance: (Number(item.distance) / 100).toFixed(2),
            vs: Number(item.verticalSpeed) / 100,
            hs: item.horizontalSpeed ? Number(item.horizontalSpeed) / 100 : 0,
            time: 0,
            mode: item.mode,
            rocker: {
                ch1: item.ch1,
                ch2: item.ch2,
                ch3: item.ch3,
                ch4: item.ch4,
                ch5: item.ch5,
                ch6: item.ch6,
                ch7: item.ch7,
                ch8: item.ch8,
                ch9: item.ch9,
                ch10: item.ch10,
                ch11: item.ch11,
                ch12: item.ch12,
                ch13: item.ch13,
                ch14: item.ch14,
                ch15: item.ch15,
                ch16: item.ch16
            }
        });
        if (Number(item.distance) / 100 !== 0) {
            distance = (Number(item.distance) / 100).toFixed(2)
        }

        let index = i + 1;
        xAxisData.push(index);
        hData.push(list[i].height);
        vsData.push(list[i].vs);
        hsData.push(list[i].hs);
        logLatList.push(position);

        if (i > 0) {
            let time = item.tms - data[0].tms;
            list[i].time = time;

            if (logLatList[i][0] == logLatList[i - 1][0] && logLatList[i][1] == logLatList[i - 1][1]) {
                let lens = deWeight.length - 1;
                deWeight[lens].duration += time;
            } else {
                deWeight.push({
                    duration: time,
                    position: position
                })
            }
        } else {
            deWeight.push({
                duration: 0,
                position: position
            })
        }

        if (data[index]) {
            let start = list[i].time;
            let end = data[index].tms - data[0].tms;
            let when = end - start; // 时间差
            dataTime.push({
                position: position,
                duration: when,
            });
        } else {
            dataTime.push({
                position: position,
                duration: 0,
            });
        }
    }
    return {
        list,
        hData,
        vsData,
        hsData,
        xAxisData,
        dataTime,
        logLatList,
        deWeight,
        distance
    }

}