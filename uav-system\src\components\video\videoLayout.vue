<!-- 直播视频 -->
<template>
  <div
    class="video-module"
    :class="isScreen ? 'video-module-screen' : ''"
    @click.stop="clickVideoEvent"
    @dblclick.stop="dblClickEvent"
  >
    <!-- 头部标题 -->
    <div class="header-title" v-if="title">{{ title }}</div>

    <!-- 视频-拉流 -->
    <slot name="video" v-if="isShowVideo"></slot>

    <!-- 底部操作 -->
    <div class="video-footer" v-show="showFooter">
      <div class="">
        <i
          @click.stop="playVideo"
          :class="isPlay ? 'el-icon-video-pause' : 'el-icon-video-play'"
        />
      </div>
      <div class="">
        <i class="el-icon-full-screen" @click.stop="fullScreen"></i>
      </div>
    </div>

    <!-- 错误显示 -->
    <slot name="error">
      <!-- 未连接时显示 -->
      <div class="no-link-show" v-show="!isNetworking">
        <!-- 等待连接 -->
        <div class="link-show-type" v-show="linkErrorType == 1">
          <img src="../../assets/icon/link.png" alt="" style="width: 23px" />
          <div class="">{{ language.errorInfo[1] }}</div>
        </div>

        <!-- 正在连接 -->
        <div class="" v-show="linkErrorType == 2">
          <div class="el-icon-loading"></div>
          <div class="">{{ language.errorInfo[2] }}</div>
        </div>

        <!-- 网络连接错误 -->
        <div class="link-show-type" v-show="linkErrorType == 3">
          <div class="el-icon-loading"></div>
          <div class="">
            {{ language.errorInfo[3] }}
            <el-link
              class="ml10"
              @click.stop="reconnection"
              type="primary"
              :underline="false"
            >
              {{ language.reconnection }}
            </el-link>
          </div>
        </div>

        <!-- url地址为空 -->
        <div class="link-show-type" v-show="linkErrorType == 4">
          <div class="">
            {{ language.errorInfo[4] }}
            <el-link
              class="ml10"
              @click.stop="reconnection"
              type="primary"
              :underline="false"
            >
              {{ language.reconnection }}
            </el-link>
          </div>
        </div>
      </div>
    </slot>

    <!-- 视频网格 -->
    <div class="video-gridding" v-if="griddingType">
      <gridding :id="videoId" :type="griddingType" ref="gridding"></gridding>
    </div>


    <slot name="temperature"></slot>

  </div>
</template>

<script>
import gridding from "../gridding/index.vue";
export default {
  components: {
    gridding,
  },
  props: {
    title: String, // 标题
    url: String, // 视频链接
    videoId: String, // 视频id，唯一
    griddingType: [String, Number],
    // 显示底部操作
    showFooter: {
      type: Boolean,
      default: true,
    },
    autoPlay: Boolean, // 是否自动播放
    isFill: Boolean, // 播放器是否充满
    // 视频是否连接成功
    isNetworking: {
      type: Boolean,
      default: false,
    },
    // 连接错误类型
    linkErrorType: {
      type: Number,
      default: 0,
    },
    // 是否播放
    isPlay: {
      type: Boolean,
      default: false,
    },
    // 全屏
    screen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isScreen: false, // 是否全屏
      isShowVideo: true, // 是否显示视频
      flvjsVideo: null, // flv实例
      clickTime: null,
      time: null,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.components.video;
    },
  },
  watch: {
    griddingType: function (val) {
      console.log("网格线------>", val);
    },
    linkErrorType: function (val){
      if(val == 3){
        this.reconnection();
      }
    }
  },
  methods: {
    clickVideoEvent: function () {
      clearTimeout(this.time);
      this.time = setTimeout(() => {
         
        this.$emit("clickVideo", this.isScreen);
      }, 200);
    },
    // 双击
    dblClickEvent: function () {
      clearTimeout(this.time);
      this.$emit("dblClickVideo", this.isScreen);
    },

    // 全屏事件
    fullScreen: function () {
      this.isScreen = !this.isScreen;
      this.$emit("update:screen", this.isScreen);
      this.$store.commit("setVideoScreen", this.isScreen);
    },
    // 播放/暂停
    playVideo: function () {
      let state = !this.isPlay;
      this.$emit("update:isPlay", state);
      this.$emit("playVideo", state);
    },

    // 获取视频播放中的某一个页面
    getVideoImg: function () {
      var canvas = document.createElement("canvas");
      let video = document.getElementById(this.videoId);
      if (!video) {
        return false;
      }
      canvas.width = video.clientWidth;
      canvas.height = video.clientHeight;
      canvas
        .getContext("2d")
        .drawImage(video, 0, 0, canvas.width, canvas.height);
      var dataURL = canvas.toDataURL("image/jpeg");
      this.imgUrl = dataURL;
    },
    // 重新连接/局部刷新
    reconnection: function () {
      this.$emit("playVideo", true);

      // ffmpeg -re -rtsp_transport tcp -i "rtsp://admin:hk123456@*************:554" -f flv -vcodec libx264 -vprofile baseline -acodec aac -ar 44100 -strict -2 -ac 1 -f flv -flvflags no_duration_filesize  -s 1280x720 -q 10 "rtmp://ab.walkera.cn/live/abc"
    },
  },
};
</script>

<style lang="less" scoped>
// 全屏样式
.video-module-screen {
  position: fixed !important;
  bottom: 0;
  left: 0;
  right: 0;
  top: 0;
  z-index: 2100 !important;
  height: 100vh !important;
}

.video-module {
  position: relative;
  width: 100%;
  // background-color: rgba(61, 67, 72, 1);
  overflow: hidden;
  &:hover .video-footer {
    bottom: 0;
  }
  .video {
    width: 100%;
    height: 100%;
  }

  .img-show {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 0;
  }

  .header-title {
    // background-color: rgba(1, 1, 1, 1);
    position: absolute;
    left: 0;
    top: 0;
    padding: 3px 10px;
    // color: #fff;
    font-size: 12px;
    z-index: 2;
  }

  .video-footer {
    height: 30px;
    transition: 0.5s;
    // width: 100%;
    display: none;
    // background-color: rgba(0, 0, 0, 0.3);
    position: absolute;
    bottom: -30px;
    left: 0;
    display: flex;
    justify-content: space-between;
    // color: #fff;
    padding: 0 10px;
    align-items: center;
    width: calc(100% - 20px);
    z-index: 11;
    i {
      cursor: pointer;
      font-size: 18px;
    }
  }

  .no-link-show {
    width: 100%;
    height: 100%;
    // background-color: rgba(61, 67, 72, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    // color: #fff;
    flex-direction: column;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    div {
      width: 100%;
      text-align: center;
    }
  }
}

.video-gridding {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
}
</style>