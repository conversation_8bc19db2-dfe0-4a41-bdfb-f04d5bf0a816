export class ShoutSend {
    constructor(websocket) {
        this.websocket = websocket
    }
    sendWs = (data) => {
        this.websocket.manualSend(data, 705)
    }
}
/**
 * data={action：24，value：50}，action主要为以下值
 * 10 :开启OPUS实时喊话
 * 12 :定长opus数据
 * 14 :结束OPUS实时喊话
 * 20 :设置文本音色帧
 * 22 :设置语速，设置文本的语速 1~100， 1为0.5倍速 50 常速 100 2倍速
 * 24 :调节音量帧
 * 26 :设置循环播放帧
 * 28 :设置循环间隔时间帧
 * 33 :开始传输文本帧
 * 35 :传输文本数据帧
 * 37 :结束文本数据帧并播放
 * 40 :停止播放帧，停止除了实时喊话外的所有音频播放
 * 60 :激光控制，0(关)/1(开)
 * 61 :爆闪频率，0x01~0x64(1~100)
 * 62 :灯光控制，0(关)/1(爆闪)/2(常亮)
 * 63 :灯光亮度，0x00~0x64(0~100亮度)
 * 64 :警灯模式，数值1：0(关)/1(开)；	数值2:：模式：0(交替快闪),1(交替满闪),2(交替齐闪)
 * 65 :警灯颜色，数值1：颜色1(0x01~0x05)对应 红、绿、蓝、黄、白；	数值2：颜色2(0x01~0x05)对应 红、绿、蓝、黄、白
 * 66 :云台俯仰，±0-1800
 * 67 :云台航向，±0-1800
 * 68 :加减云台俯仰微调值，±（默认为5，其他值视为与云台取值范围一致）
 * 69 :加减云台水平微调值，±（默认为5，其他值视为与云台取值范围一致）
 */