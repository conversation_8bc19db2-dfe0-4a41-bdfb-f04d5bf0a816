<!-- 文件播放视频 -->
<template>
  <video-layout
    ref="videoLayout"
    :videoId="videoId"
    :griddingType="griddingType"
    :title="title"
    :showFooter="showFooter"
    :autoPlay="autoPlay"
    :isPlay.sync="isPlay"
    :isNetworking="isNetworking"
    :linkErrorType="linkErrorType"
    @playVideo="playVideo"
    @clickVideo="clickVideoEvent"
    @dblClickVideo="dblClickVideo"
  >
    <template v-slot:video>
      <video
        class="video"
        ref="video"
        muted
        :style="videoStyle"
        loop="loop"
        style="border-radius: 5px"
        :controls="controls"
      >
        <source :src="url" type="video/mp4" />
      </video>

      <!-- 封面图 -->
      <div class="cover-picture" style="width: 100%" v-if="coverPicture">
        <img :src="coverPicture" alt="封面图" style="width: 100%" />
      </div>
    </template>

    <!-- 错误显示时 -->
    <template v-slot:error>
      <slot name="error"></slot>
    </template>
  </video-layout>
</template>

<script>
import videoLayout from "./videoLayout.vue";
export default {
  components: {
    videoLayout,
  },
  props: {
    title: String, // 标题
    url: String, // 视频链接
    videoId: String, // video标签id
    griddingType: [String, Number], // 视频网格类型
    // 显示底部操作
    showFooter: {
      type: Boolean,
      default: true,
    },
    autoPlay: Boolean, // 是否自动播放
    isFill: Boolean, // 播放器是否充满
    // video原生属性
    controls: {
      type: Boolean,
      default: false,
    },
    speed: {
      // 播放速度
      type: [Number, String],
      default: 1,
    },
    cover: String, // 封面图片
  },
  data() {
    return {
      isNetworking: false,
      linkErrorType: 1, // 连接错误类型
      isPlay: this.autoPlay, // 播放状态
      coverPicture: this.cover, // 封面图
    };
  },
  watch: {
    cover: function (val) {
      this.coverPicture = val;
    },
  },
  computed: {
    videoStyle() {
      return {
        "object-fit": this.isFill ? "fill" : "",
        // 'object-fit': 'fill',
      };
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.playVideo(this.isPlay);
      this.videoInit();
    });
  },
  methods: {
    // 更新网格
    updareGridding: function () {
      this.$refs.videoLayout.$refs.gridding.resizeChange();
    },
    
    videoInit: function () {
      let video = this.$refs.video;
      if(!video){
        return false;
      }
      // 取消播放
      video.oncanplay = (row) => {
        this.isNetworking = true;
      };
      // 播放卡顿时触发
      video.onwaiting = (row) => {
        this.isNetworking = false;
        this.linkErrorType = 2;
      };
      // 发生错误时触发
      video.onerror = (err) => {
        console.log("遇到错误了....", err);
        this.linkErrorType = 3;
      };
      // 数据不可用时触发
      video.onstalled = (row) => {};
      // 设置播放速度
      video.playbackRate = this.speed;
    },
    playVideo: function (state) {
      this.isPlay = state;
      if (state) {
        this.coverPicture = null;
        this.$nextTick(()=>{
          this.$refs.video.play();
        })
      } else {
        this.$refs.video && this.$refs.video.pause();
      }
    },
    clickVideoEvent: function (row) {
      this.$emit("clickVideo", row);
    },
    dblClickVideo: function (row) {
      this.$emit("dblClickVideo", row);
    },
  },
};
</script>

<style lang="less" scoped>
.cover-picture {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}
</style>