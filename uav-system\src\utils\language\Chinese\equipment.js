const equipment = {
    language: 'zh-CN',
    //设备信息
    equipInfo: {
        equipStateIn: '设备在线',
        equipStateOut: '设备离线',
        personalStatus: "有人连接",
        personalStatusNo: "无人连接",
        equipNum: "设备编号：",
        belonging: "所属：",
        equipName: "设备名：",
        address: "地址",
        equipStateOut1: '设\xa0备\xa0离\xa0线',
        taskExecuted: '待执行任务',
        unit: '条',

        popupLabel: {
            title: '连接用户',
            nick: '昵称：',
            account: '手机号/邮箱：'
        }

    },
    //输入框
    input: {
        placeholder: "请输入设备名称/地址"
    },
    //页面按钮
    button: {
        searchBtn: "搜\xa0\xa0\xa0\xa0索",
        addEquip: "新增设备",
        setEquip: "设\xa0\xa0置",
        delEquip: "删\xa0\xa0除",
        previous: "上一页",
        next: "下一页",
        firmwareVersion: '版本信息',
        Upgrade: '升级固件',
        calibration: '传感器',
        outCabinVCR: '舱外录像',
        nestLog: '上传机巢日志'
    },
    //设备天气状态
    equipStatus: {
        temperature: "温度",
        weather: "天气",
        rain: "雨量",
        humidity: "湿度",
        wind: "风力",
        windDirection: "风速"
    },
    //机巢状态
    airportStatus: {
        title: '机场状态',
        drone: "无人机",
    },
    operationList: [{
            title: "舱门",
            id: "Hatch",
            value: false,
            value1: 0,
        },
        {
            title: "升降台",
            id: "Lift",
            value: false,
            value1: 0,
        },
        {
            title: "归中机构",
            id: "center",
            value: false,
            value1: 0,
        },
        {
            title: "充电器",
            id: "charger",
            value: false,
            value1: 0,
        },
    ],
    //机巢控制按钮
    airportBtn: {
        onKeyState: '一键启动',
        starting: '正在启动',
        starting1: '正在打开',
        onKeyClose: '一键关闭',
        closing: "正在关闭",
        removeError: '清除错误',
        removeState: '清除状态',
        crashStop: "急停",
        stop: '暂停',
        continue: '继续',
        reset: '复位',
        fitOn: "装载飞机电池",
        fitOning: '正在装载',
        unfitOn: "卸下飞机电池",
        unfitOning: '正在卸下',
    },
    //机巢地图
    airportMap: {
        title: "机场位置",
        title1: "飞机位置",
        placeholder: '搜索位置',
        sure: '确定',
        cancel: '取消',
        reading: '读取中',
        readUav: '读取飞机位置',
        readAirport: '读取机巢位置',
        errorMessage: '暂无法获取机巢精准位置',
        errorMessage1: '暂无法获取无人机精准位置',
        errorMessage2: '无人机处于解锁状态，暂无法获取精准位置',
        errorMessage3: '无人机rtk未进入，暂无法获取精准位置',
        successMessage: '读取机巢精准位置成功',
        successMessage1: '读取飞机精准位置成功',



    },
    //飞机状态
    uavStatus: {
        title: "飞机状态",
        lng: "经度",
        lat: "纬度",
        beaconValue: '信标值',
        direction: '车载方向',
        rtkStatus: "RTK状态",
        satellitesNum: "卫星数",
        electricity: "电量",
        voltage: "电压",
        flightModel: "飞行模式",
        uavState: "飞机状态",
        height: "高度（m）",
        yaw: "航向",
        roll: "偏航角",
        pitch: "俯仰角",
        hSpeed: "水平速度（m/s）",
        vSpeed: "垂直速度（m/s）"
    },
    //云台操作
    holderOperation: {
        title: "云台操作",
        holder: "云台",
        zoom: "变焦",
        center: "回正",
        down: '垂直向下'

    },
    //相机参数
    cameraParams: {
        title: "相机参数",
        ISO: "ISO值",
        shutter: "快门",
        expModel: "曝光模式",
        expValue: "曝光值",
        awb: "白平衡",
        moreSet: '更多设置>>>',
        // pictureMode: "拍照模式",
        // photoFormat: "照片格式",
        // photoSize: "照片大小",
        // previewResolution: "预览分辨率",
        // PreviewBitrate: "预览码率",
        // videoResolution: "录像分辨率",
        // videoBitrate: "录像码率"
    },
    selsectList: [{
            title: "拍照模式",
            placeholder: "请选择拍照模式",
            value: 1,
            id: "photoMode",
            options: [{
                    label: "单拍",
                    value: 1,
                },
                {
                    label: "连拍",
                    value: 2,
                },
                {
                    label: "定时拍摄",
                    value: 3,
                },
                {
                    label: "延迟拍摄",
                    value: 4,
                },
            ],
        },
        {
            title: "照片格式",
            placeholder: "请选择照片格式",
            value: 1,
            id: "photoFormat",
            options: [{
                    label: "JPG",
                    value: 1,
                },
                {
                    label: "RAW",
                    value: 2,
                },
                {
                    label: "RAW&JPG",
                    value: 3,
                },
            ],
        },
        {
            title: "照片大小",
            placeholder: "请选择照片大小",
            value: 1,
            id: "photoSize",
            options: [{
                    label: "8M",
                    value: 1,
                },
                {
                    label: "12M",
                    value: 2,
                },
                {
                    label: "16M",
                    value: 3,
                },
                {
                    label: "21M",
                    value: 4,
                },
                {
                    label: "32M",
                    value: 5,
                },
                {
                    label: "41M",
                    value: 6,
                },
            ],
        },
        {
            title: "预览分辨率",
            placeholder: "请选择预览分辨率",
            value: 0,
            id: "resolving",
            options: [{
                    label: "720p",
                    value: 1,
                },
                {
                    label: "1080p",
                    value: 0,
                },
            ],
        },
        {
            title: "预览码率",
            placeholder: "请选择预览码率",
            value: 2,
            id: "rateCode",
            options: [{
                    label: "1M",
                    value: 0,
                },
                {
                    label: "2M",
                    value: 1,
                },
                {
                    label: "4M",
                    value: 2,
                }
            ],
        },
        {
            title: "录像分辨率",
            placeholder: "请选择录像分辨率",
            value: 3,
            id: "videotapeResolving",
            options: [{
                    label: "4K 25",
                    value: 0,
                },
                {
                    label: "4K 30",
                    value: 1,
                },
                {
                    label: "4K 60",
                    value: 2,
                },
                {
                    label: "6K 25",
                    value: 3,
                },

            ],
        },
        {
            title: "录像码率",
            placeholder: "请选择预览码率",
            value: 2,
            id: "videotapeRateCode",
            options: [{
                    label: "8M",
                    value: 0,
                },
                {
                    label: "16M",
                    value: 1,
                },
                {
                    label: "32M",
                    value: 2,
                },
                {
                    label: "64M",
                    value: 3,
                }
            ],
        },
        {
            title: "白平衡",
            placeholder: "请选择白平衡",
            value: 0,
            id: "awb",
            options: [{
                    label: "自动",
                    value: 0,
                },
                {
                    label: "烛光",
                    value: 1,
                },
                {
                    label: "钨丝灯",
                    value: 2,
                },
                {
                    label: "荧光灯",
                    value: 3,
                },
                {
                    label: "日光",
                    value: 4,
                },
                {
                    label: "多云",
                    value: 5,
                },
                {
                    label: "阴天",
                    value: 6,
                },
                {
                    label: "天蓝",
                    value: 7,
                },
                {
                    label: "烟雾",
                    value: 8,
                }
            ],
        },
    ],
    flightModeList: {
        2: "姿态模式",
        3: "自动模式",
        4: "跟随模式",
        5: "GPS模式",
        6: "返航模式",
        9: "降落飞行模式",
        13: "运动模式",
    },
    flightStateList: {
        0: "空闲",
        1: "起飞",
        2: "在空中",
        3: "降落",
        4: "在地面",
        8: "返航",
    },
    cellList: [{
        label: '电池A',
        seq: 1,
    }, {
        label: '电池B',
        seq: 2,
    }, {
        label: '电池C',
        seq: 3,
    }, {
        label: '电池D',
        seq: 4,
    }],
    rtkStatusList: ["未连接", "未定位", "单点定位", "浮动解", "固定解"],
    awb: ['自动', '烛光', '钨丝灯', '荧光灯', '日光', '多云', '阴天', '天蓝', '烟雾'],
    novideoTip: "该 通 道 视 频 流 无 效",
    inCabin: "舱内视频",
    outCabin: "舱外视频",
    uav: '无人机视频',
    addeditEquip: {
        addTitle: "新\xa0\xa0建\xa0\xa0设\xa0\xa0备",
        editTilte: "编\xa0\xa0辑\xa0\xa0设\xa0\xa0备",
        type: "设备类型",
        linkType: '链路类型',
        name: "自动机场名称",
        uavName: "无人机名称",
        num: '自动机场编号',
        address: '自动机场部署地址',
        uavModel: '无人机型号',
        uavNum: "无人机编号",
        desc: '描述',
        fixAirport: "固定式小机场",
        fixLargeAirport: "固定式大机场",
        MobileAirport: "移动式机场",
        uav: "单兵无人机",
        link2: '飞机端（直连）',
        link1: '机巢端',
        link3: '自动',
        azimuth: "自动机场方位角",
        placeholder: "请输入无人机名称",
        placeholder1: "请输入自动机场名称",
        placeholder2: "请输入自动机场SN码",
        placeholder3: '请选择机场位置',
        placeholder4: '请选择无人机型号',
        placeholder5: '请输入无人机SN码',
        placeholder6: "请选择设备类型",
        placeholder7: "请输入设备名称",
        placeholder8: "请输入设备SN码",
        save: '保\xa0\xa0\xa0\xa0\xa0\xa0存',
        cancel: '取\xa0\xa0\xa0\xa0\xa0\xa0消',
        use: "启\xa0\xa0\xa0\xa0\xa0\xa0用",
        disable: "禁\xa0\xa0\xa0\xa0\xa0\xa0用",
        addSuccess: '新增设备成功',
        noOption: '该设备正处于禁用状态，无法重复操作！',
        editSuccess: '编辑设备成功',
        equipNo: '设备已禁用',
        landPoint: '备降点',
        airportPoint: '机巢坐标点',
        lng: '经度',
        lat: '纬度',
        company: '选择所属公司/部门',
        placeholder9: "请选择用户所属公司/部门",
        sendDevice: '发送至设备',
        placeholderBeacon: '请点击设置信标参数',
        beacon: '信标参数',
        setBeacon: '设置信标参数',
        warnTip: '会影响降落准度问题，是否继续？',
        tipTitle: '提示',
        setDirectionIng: "正在设置方向角。。。",
        setBeaconing: '正在设置信标参数。。。',
        deviceInLine: '当前设备不在线',
        directionEmpty: '方位角无效，当前为-1',
        setTimeOut: '方位角设置超时！',
        setTimeOut1: '信标参数设置超时！',
        setBeaconSuccess: '信标参数设置成功！',
        setDirectionSuccess: "方位角设置成功！",
        noSetDirection: "方向角未设置，",
        noSetBeacon: "信标参数未设置，",

    },
    onKeyStartError: '一键启动失败！',
    onKeyCloseError: '一键关闭失败！',
    onKeyStartSuccess: '一键启动成功！',
    onKeyCloseSuccess: '一键关闭成功！',
    disconnect: "已断开连接！",
    searchTip: "未搜索到与之匹配的设备",
    searchReturn: '输入为空，返回起始列表',
    noset: '该设备已有人连接，暂无法设置！',
    delTip: '该删除无法恢复，您确定删除该设备信息？',
    tips: '提示',
    delSuccess: '删除成功！',
    cancelDel: '已取消删除!',
    noDel: '该设备已有人连接，暂无法删除！',
    noClose: '设备正在启动，暂无法关闭！',
    inClosing: '设备正处在关闭状态！',
    noOpen: '设备正在关闭，暂无法打开！',
    inOpening: '设备正处于开启状态！',
    onend: '无人机已装电池',
    unonend: '无人机未装电池',
    upgradeFirmware: {
        dialogTitle: '升级固件',
        fileTitle: '固件文件',
        clickChoose: '点击选择',
        uploadTip: '只能上传.wkimg文件',
        submit: '确\xa0定 ',
        cancel: '取\xa0消',
        errorTip: '请上传固件文件',
        loadingText: '正在上传固件。。。'
    },
    versionLabel: {
        nest_version: '机巢',
        nest_mcu_version: '机巢驱动',
        fcs_version: '飞控',
        camera_version: '相机',
        fcs_slave_version: '副IC',
        gimbal_version: '云台',
        drone_linux_version: '机载4G'
    },
    upgradeMsg: {
        noUpgrade: '当前设备不在线，暂无法升级！',
        noUpgrade1: '飞机正在执行任务，暂无法升级！',
        reStart: '升级完成，需重启才能生效，是否确定重启？',
        reStartTip: '重启提示',
        submit: '确\xa0定'

    },
    noVersionData: "暂无版本信息返回",
    rtkStateTitle: 'RTK状态：',
    calibration: {
        title: '无人机传感器设置',
        title1: "校准",
        calibrationTitle: '校 准',
        startTitle: '开始校准',
        nextStep: '下一步',
        list: {
            gyroscope: '陀螺仪',
            compass: '电子罗盘',
            acceleration: '加速度'
        },
        stateList: {
            0: '异常',
            1: '正常'
        },
        gyroscope: {
            1: "将飞行器水平垂直放置在平坦平台，保持飞行器静止状态",
            2: "将飞行器水平垂直放置在平坦平台，状态指示灯进入快闪，当指示灯为红绿彩灯时，表示校准成功。",
        },
        compass: {
            1: "首先，取下无人机桨叶",
            2: "将飞行器机头垂直向上，沿水平方向旋转720°。",
            3: "将飞行器放平，在沿水平方向旋转720度。",
        },
        acceleration: {
            1: "将飞行器水平垂直放置在平坦平台。",
            2: "将飞行器背面水平垂直放置在平坦平台。",
            3: "将飞行器左侧面水平垂直放置在平坦平台。",
            4: "将飞行器右侧面水平垂直放置在平坦平台。",
            5: "将飞行器摄像头朝下水平垂直放置在平坦平台。",
            6: "将飞行器摄像头朝上水平垂直放置在平坦平台。",
        },
        resultList: {
            success: "校准成功",
            error: "校准失败，请重新校准",
            reStart: "校准成功后请重启飞行器再开始使用",
        },
    },
    weatherState: {
        humidity: NaN,
        temperature: NaN,
        weather: "无",
        winddirection: "无",
        windpower: "NaN",
    },
}
export default equipment