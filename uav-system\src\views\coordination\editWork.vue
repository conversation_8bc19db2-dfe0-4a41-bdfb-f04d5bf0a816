<template>
  <div class="editWork">
    <div id="map"></div>
    <input
      type="file"
      style="display: none"
      ref="files"
      accept=".kml,.json"
      @change="chooseFileAfter"
    />
    <div class="deviceListDiv" v-if="awaitMap">
      <el-container>
        <el-header> {{ groupLanguage.deviceList.title }} </el-header>
        <el-button
          class="backBtn"
          icon="el-icon-arrow-left"
          @click="backEvent"
        ></el-button>
        <el-main>
          <el-row
            v-for="(item, index) in deviceList"
            :key="index"
            @click.native="clickShow(item)"
            :class="item.sn_id == idCode ? 'active' : ''"
          >
            <el-col :span="2" class="num">{{ index + 1 }}</el-col>
            <el-col :span="7" class="image"
              ><el-image :src="imgSrc[item.type]" fit="contain"></el-image
            ></el-col>
            <el-col :span="15" class="content">
              <div class="content-title">{{ item.name }}</div>
              <div class="isbind">
                <div
                  class="isbind-item-1"
                  :class="
                    item.routeList && item.routeList.length > 0 ? 'active' : ''
                  "
                ></div>
                <div
                  class="isbind-item-2"
                  :class="
                    item.routeList && item.routeList.length > 0 ? 'active' : ''
                  "
                >
                  {{
                    item.routeList && item.routeList.length > 0
                      ? groupLanguage.deviceList.bind
                      : groupLanguage.deviceList.unbind
                  }}
                </div>
              </div>
            </el-col>
          </el-row>
        </el-main>
      </el-container>
    </div>
    <task-list
      ref="taskList"
      v-if="listCode && editCode == 0"
      :listCode.sync="listCode"
      :fenceList="fenceList"
      :deviceItem="deviceItem"
      :editCode.sync="editCode"
      :checkType="checkType"
      @changeRoute="changeRoute"
      @clickType="clickType"
      @deviceChange="deviceChange"
    ></task-list>
    <route-edit
      v-if="editCode == 1"
      ref="routeEdit"
      :fenceItem="fenceItem"
      :checkType="checkType"
      :changePoint="changePoint"
      @changeMarker="changeMarker"
      @goBack="goBack"
      @deepCopy="deepCopy"
      v-loading="loading"
      :element-loading-text="routeLanguage.routeLine.routeLoading"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(10, 10, 10, 0.8)"
    ></route-edit>
    <ortho-edit
      v-if="editCode == 3"
      ref="orthoEdit"
      :fenceItem="fenceItem"
      :checkType="checkType"
      :changePoint="changePoint"
      @deepCopy="deepCopy"
      @refresh="refresh"
      @changeMarker="changeMarker1"
      @goBack="goBack"
      v-loading="loading"
        :element-loading-text="routeLanguage.routeLine.routeLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(10, 10, 10, 0.8)"
    ></ortho-edit>
    <div class="operateBar_1" v-if="editCode !== 0">
      <operate-bar
        :drawend="drawend"
        :removeClick="removeClick"
        :delClick="delClick"
        :recallClick="recallClick"
        :importClick="importClick"
        :exportClick="exportClick"
        @operateEvent="operateEvent"
      ></operate-bar>
    </div>
  </div>
</template>
<script>
import requestHttp from "../../utils/api";
import initMap from "../../utils/maps";
import taskList from "./components/taskList";
import { orthoPhotoComputer } from "../../utils/orthoPhotoComputer";
import operateBar from "../routeplan/components/operaterBar";
import routeEdit from "../routeplan/components/routeEdit";
import orthoEdit from "../routeplan/components/orthoEdit";
import {
  computedMethod,
  computeCenter,
  calcAngle,
  computedDistance,
} from "../../utils/computedMap";
import togeojson from "../../../node_modules/@mapbox/togeojson";
export default {
  name: "editWork",
  data() {
    return {
      coordinationData: "",
      map: "",
      awaitMap: false,
      imgSrc: {
        10: require("../../assets/img/equipment/AC50.jpg"),
        12: require("../../assets/img/equipment/AC100.jpg"),
        50: require("../../assets/img/equipment/AC50MINI.jpg"),
        100: require("../../assets/img/equipment/move.jpg"),
        500: require("../../assets/img/equipment/R500.jpg"),
        1000: require("../../assets/img/equipment/R1000.jpg"),
        1800: require("../../assets/img/equipment/1800.jpg"),
        1900: require("../../assets/img/equipment/1900.jpg"),
        2000: require("../../assets/img/equipment/1900P.jpg"),
      },
      deviceList: "",
      listCode: false,
      idCode: "",
      fenceList: "",
      deviceMarker: [],
      polyponList: [],
      markerPoint: [],
      clickRoute: "",
      clickRoute1: "",
      info: "",
      deviceItem: "",
      startMarker: "",
      endMarker: "",
      drawend: false,
      removeClick: false,
      delClick: false,
      recallClick: false,
      importClick: false,
      exportClick: false,
      editCode: 0,
      fenceItem: {},
      checkType: {},
      distancePoint: "",
      clickId: "",
      addMarkers: [],
      changePoint: {
        index: 0,
        lat: 0,
        lng: 0,
        type: "edit",
      },
      startPonit: {
        lng: 0,
        lat: 0,
      },
      num: 0,
      deepCode: false,
      cacheData: [],
      loading: false,
      importCode: false,
      backCode: false,
      messageCodeWarning: false,
      startPoints: "",
    };
  },
  components: {
    taskList,
    operateBar,
    routeEdit,
    orthoEdit,
  },
  computed: {
    groupLanguage() {
      return this.$languagePackage.coordination;
    },
    routeLanguage() {
      return this.$languagePackage.routes;
    },
    typeList() {
      return this.$store.state.route.typeList;
    },
  },
  async mounted() {
    await this.initMap();
    // this.map.on("zoomchange", this.changeZoom);
    await this.getDevice();
    await this.getFenceList();
  },
  methods: {
    //初始化地图
    async initMap() {
      await initMap
        .initMap("map", {
          mapStyle: "amap://styles/92907bd07b27bf2b8ca66585015fdc7a",
          showLayerType: true,
        })
        .then((res) => {
          this.map = res;
        });
      this.awaitMap = true;
    },
    //获取设备列表
    async getDevice() {
      // let data = {
      //   page: 0,
      //   size: 200,
      //   type: 0,
      // };
      // data.pmd = data.page.toString() + data.type.toString();
      // await requestHttp("deviceList", data).then((res) => {
      //   this.deviceList = res.data.list;
      // });
      // this.drawDevice();
      let list = JSON.parse(localStorage.getItem("coordinate"));
      let id = this.$route.query.id;
      let i = list.findIndex((item) => {
        return item.id == id;
      });
      this.coordinationData = list[i];
      this.deviceList = this.coordinationData.deviceList;
      this.drawDevice();
    },
    //绘制设备坐标
    // drawDevice() {
    //   for (let index = 0; index < this.deviceList.length; index++) {
    //     if (this.deviceList[index].type === 10) {
    //       let icon = new AMap.Icon({
    //         // 图标的取图地址
    //         image: require("../../assets/img/routeplan/home.png"),
    //         imageSize: new AMap.Size(50, 50),
    //       });
    //       let marker = new AMap.Marker({
    //         position: new AMap.LngLat(
    //           this.deviceList[index].lon_int / 1e7,
    //           this.deviceList[index].lat_int / 1e7
    //         ),
    //         title: this.deviceList[index].name,
    //         icon: icon,
    //         offset: new AMap.Pixel(-15, -30),
    //         extData: {
    //           id: this.deviceList[index].sn_id,
    //         },
    //       });
    //       this.map.add(marker);
    //       this.deviceMarker.push(marker);
    //     }
    //   }
    // },
    //绘制设备坐标
    drawDevice() {
      let points = [];
      for (let index = 0; index < this.deviceList.length; index++) {
        if (this.deviceList[index].type === 10) {
          points.push({
            lnglat: [
              this.deviceList[index].lon_int / 1e7,
              this.deviceList[index].lat_int / 1e7,
            ],
            id: this.deviceList[index].sn_id,
            name: this.deviceList[index].name,
            state: this.deviceList[index].is_push_on,
          });
        }
      }
      //点聚合
      let cluster = initMap.markerMerge(this.map, points);
      let self = this;
      cluster.on("click", function (e) {
        if (e.clusterData.length > 1) {
          self.info = initMap.setInfoWindow(e.clusterData);
          let point = e.marker.getPosition();
          self.info.open(self.map, point);
          self.info.on("mouseover", self.mouseoverEvent);
          self.info.on("mouseout", self.mouseoutEvent);
        }
      });
    },
    //鼠标移入窗口
    mouseoverEvent() {
      this.map.setStatus({ scrollWheel: false });
    },
    //鼠标移除窗口
    mouseoutEvent() {
      this.map.setStatus({ scrollWheel: true });
    },
    //地图缩放事件
    // changeZoom(e) {
    //   if (this.deviceMarker[0]) {
    //     let x, y;
    //     if (e.target.getZoom() > 9 && e.target.getZoom() < 17) {
    //       x = (50 * e.target.getZoom()) / 20;
    //       y = (50 * e.target.getZoom()) / 20;
    //     } else if (e.target.getZoom() > 17) {
    //       x = 50;
    //       y = 50;
    //     } else if (e.target.getZoom() < 9) {
    //       x = 0;
    //       y = 0;
    //     }
    //     let img = this.deviceMarker[0].getIcon().getImage();
    //     for (let index = 0; index < this.deviceMarker.length; index++) {
    //       let icon = new AMap.Icon({
    //         image: img,
    //         imageSize: new AMap.Size(x, y),
    //       });
    //       this.deviceMarker[index].setIcon(icon);
    //     }
    //   }
    // },
    //获取围栏信息
    async getFenceList() {
      let data = {
        page: 0,
        size: 200,
      };
      data.pmd = data.page.toString();
      await requestHttp("fenceList", data).then((res) => {
        this.fenceList = res.data.list?res.data.list:[];
      });
      this.drawFence();
    },
    //绘制围栏
    drawFence() {
      for (let index = 0; index < this.fenceList.length; index++) {
        let paths = [];
        for (let i = 0; i < this.fenceList[index].point_list.length; i++) {
          let path = new AMap.LngLat(
            this.fenceList[index].point_list[i].lon_int / 1e7,
            this.fenceList[index].point_list[i].lat_int / 1e7
          );
          paths.push(path);
        }
        let polypon = initMap.drawPolypon(paths);
        polypon.id = this.fenceList[index].f_id;
        this.polyponList.push(polypon);
        polypon.setMap(this.map);
        // this.map.setFitView(polypon,);
      }
    },
    //点击类型
    clickType(e) {
      this.checkType = e;
      if (this.clickRoute) {
        this.map.remove(this.clickRoute);
        this.clickRoute = "";
        this.map.remove(this.markerPoint);
        this.markerPoint = [];
      }
      if (this.clickRoute1) {
        this.map.remove(this.clickRoute1);
        this.clickRoute1 = "";
      }
      if (this.startMarker) {
        this.map.remove(this.startMarker);
        this.map.remove(this.endMarker);
        this.startMarker = "";
        this.endMarker = "";
      }
    },
    //点击切换航线
    changeRoute(item) {
      if (item == 0 && this.clickRoute) {
        this.map.remove(this.clickRoute);
        this.clickRoute = "";
        this.map.remove(this.markerPoint);
        this.markerPoint = [];
        return false;
      }
      if (this.clickRoute) {
        this.map.remove(this.clickRoute);
        this.clickRoute = "";
        this.map.remove(this.markerPoint);
        this.markerPoint = [];
      }
      if (this.clickRoute1) {
        this.map.remove(this.clickRoute1);
        this.clickRoute1 = "";
      }
      if (this.startMarker) {
        this.map.remove(this.startMarker);
        this.map.remove(this.endMarker);
        this.startMarker = "";
        this.endMarker = "";
      }
      if (item.type != 50) {
        this.clickRoute = initMap.drawPolyline(item.paths);
        this.clickRoute.setMap(this.map);
        this.map.setFitView(this.clickRoute, true, [200, 200, 500, 100], 30);
      } else {
        this.clickRoute1 = initMap.drawPolypon(item.paths);
        this.clickRoute1.setMap(this.map);
        this.map.setFitView(this.clickRoute1, true, [200, 200, 500, 100], 30);
        let { points, triggerDist } = orthoPhotoComputer(item, this.map);
        this.clickRoute = initMap.drawPolyline(points, {
          strokeColor: "#07ff0e",
        });
        this.clickRoute.setMap(this.map);
        if (this.startMarker) {
          this.startMarker.setPosition(points[0]);
          this.endMarker.setPosition(points[points.length - 1]);
        } else {
          let params = {
            offset: -9,
            clickable: false,
            draggable: false,
            zIndex: 100,
          };
          this.startMarker = initMap.drawMarker(
            "S",
            points[0],
            "startend-marker",
            params
          );
          this.startMarker.setMap(this.map);
          this.endMarker = initMap.drawMarker(
            "E",
            points[points.length - 1],
            "startend-marker",
            params
          );
          this.endMarker.setMap(this.map);
        }
        // console.log( this.clickRoute)
      }
      for (let index = 0; index < item.paths.length; index++) {
        let params = {
          offset: -17,
          clickable: false,
          draggable: false,
          zIndex: 55,
        };
        let marker = initMap.drawMarker(
          index + 1,
          item.paths[index],
          "marker-edit",
          params
        );
        marker.setMap(this.map);
        marker.id = index + 1;
        this.markerPoint.push(marker);
      }
      let polyponItem = "";
      for (let i = 0; i < this.polyponList.length; i++) {
        if (this.polyponList[i].id == item.f_id) {
          polyponItem = this.polyponList[i];
          this.polyponList[i].show();
        } else {
          this.polyponList[i].hide();
        }
      }
      for (let j = 0; j < this.fenceList.length; j++) {
        if (this.fenceList[j].f_id == item.f_id) {
          this.fenceItem.title = this.fenceList[j].title;
          this.fenceItem.id = this.fenceList[j].f_id;
          this.fenceItem.height = this.fenceList[j].height_limit / 100;
          this.fenceItem.paths = polyponItem.getPath();
          if (this.distancePoint) {
            this.map.remove(this.distancePoint);
          }
          this.distancePoint = [];
          for (let i = 0; i < this.fenceItem.paths.length - 1; i++) {
            let labelMarker = this.fenceListPoint([
              this.fenceItem.paths[i],
              this.fenceItem.paths[i + 1],
            ]);
            labelMarker.setMap(this.map);
            this.distancePoint.push(labelMarker);
          }
          let labelMarker = this.fenceListPoint([
            this.fenceItem.paths[this.fenceItem.paths.length - 1],
            this.fenceItem.paths[0],
          ]);
          labelMarker.setMap(this.map);
          this.distancePoint.push(labelMarker);
        }
      }
    },
    //返回事件
    backEvent() {
      this.$router.push("/coordination");
    },
    //点击显示切换设备
    async clickShow(item) {
      if (this.idCode != item.sn_id) {
        this.backCode = true;
        this.listCode = true;
        this.deviceItem = item;
        if (this.editCode == 1) {
          await this.$refs.routeEdit.goBack();
          this.$store.commit("routeItem", "");
          this.$refs.taskList.routId = "";
        } else if (this.editCode == 3) {
          await this.$refs.orthoEdit.goBack();
          this.$store.commit("routeItem", "");
          this.$refs.taskList.routId = "";
        } else {
          if (this.clickRoute) {
            this.map.remove(this.clickRoute);
            this.clickRoute = "";
            this.map.remove(this.markerPoint);
            this.markerPoint = [];
            this.$refs.taskList.routId = "";
          }
        }
        if (this.clickRoute1) {
          this.map.remove(this.clickRoute1);
          this.clickRoute1 = "";
        }
        if (this.startMarker) {
          this.map.remove(this.startMarker);
          this.map.remove(this.endMarker);
          this.startMarker = "";
          this.endMarker = "";
        }
        this.idCode = item.sn_id;
        if (item.lon_int && item.lat_int) {
          let center = [item.lon_int / 1e7, item.lat_int / 1e7];
          this.map.setZoom(this.map.getZoom() - 0.01);
          this.map.setCenter(center, true);
        }
        setTimeout(() => {
          this.$refs.taskList.clickType(this.typeList[0]);
          this.backCode = false;
        }, 100);
      }
    },
    //计算围栏中两点的距离并显示
    fenceListPoint(points) {
      let center = computeCenter(points);
      let str = computedMethod(1, {
        point1: points[0],
        point2: points[1],
      });
      str = str.toFixed(2) + "m";
      let rotate = calcAngle([points[0], points[1]], this.map);
      let labelMarker = initMap.textMarker(str, center, rotate);
      return labelMarker;
    },
    //改变设备绑定数据
    deviceChange(item) {
      let a = this.deviceList.findIndex((x) => {
        return x.sn_id == item.sn_id;
      });
      this.deviceList[a] = item;
      let list = JSON.parse(localStorage.getItem("coordinate"));
      let id = this.$route.query.id;
      let i = list.findIndex((item) => {
        return item.id == id;
      });
      list[i].deviceList = this.deviceList;
      localStorage.setItem("coordinate", JSON.stringify(list));
    },
    //操作栏点击之后参数回传
    operateEvent(e) {
      switch (e) {
        case "drawMark":
          this.drawMark();
          break;
        case "removeMarker":
          this.removeMarker();
          break;
        case "delMarker":
          this.delMarker();
          break;
        case "recallMarker":
          this.recallMarker();
          break;
        case "importEvent":
          this.importEvent();
          break;
        case "exportEvent":
          this.exportEvent();
          break;
        default:
          break;
      }
    },
    //点击标点
    drawMark() {
      if (!this.drawend) {
        this.drawend = true;
        this.map.setDefaultCursor("pointer");

        for (let index = 0; index < this.addMarkers.length; index++) {
          this.addMarkers[index].setMap(this.map);
        }
        for (let index = 0; index < this.markerPoint.length; index++) {
          this.markerPoint[index]._opts.clickable = true;
          this.markerPoint[index]._opts.draggable = true;
        }
        this.map.on("click", this.clickEvents);
        if (this.clickId) {
          this.markerClick(this.markerPoint[this.clickId - 1]);
        }
      } else {
        this.drawend = false;
        this.map.setDefaultCursor("");
        this.map.remove(this.addMarkers);
        for (let index = 0; index < this.markerPoint.length; index++) {
          this.markerPoint[index]._opts.clickable = false;
          this.markerPoint[index]._opts.draggable = false;
        }
        if (this.clickId > 0) {
          let str = this.markerPoint[this.clickId - 1]
            .getContent()
            .split(" active");
          let content = str[0] + str[1];
          this.markerPoint[this.clickId - 1].setContent(content);
        }
      }
    },
    //删除航点
    removeMarker() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (this.drawend) {
        if (!this.removeClick) {
          this.removeClick = true;
          if (this.markerPoint.length > 0) {
            this.map.remove(this.markerPoint[this.clickId - 1]);
            this.markerPoint.splice(this.clickId - 1, 1);
            if(this.editCode==1){
              for (let i = this.clickId - 1; i < this.markerPoint.length; i++) {
              this.markerPoint[i].id = i + 1;
              let content =
                "<div class='marker-edit'><span class='text'>" +
                (i + 1) +
                "</span></div>";
              this.markerPoint[i].setContent(content);
            }
            this.num--;
            this.changePoint = {
              index: this.clickId - 1,
              lat: 0,
              lng: 0,
              type: "remove",
            };
            if (this.num > 0) {
              if (this.clickId > 1) {
                this.map.remove(this.addMarkers[this.clickId - 2]);
                this.addMarkers.splice(this.clickId - 2, 1);
              } else {
                this.map.remove(this.addMarkers[this.clickId - 1]);
                this.addMarkers.splice(this.clickId - 1, 1);
              }
              for (
                let index =
                  this.clickId > 1 ? this.clickId - 2 : this.clickId - 1;
                index < this.addMarkers.length;
                index++
              ) {
                this.addMarkers[index].id = index + 1;
                this.editCenterpoint(this.clickId - 1);
              }
              this.changeLine();
            }
            if (this.markerPoint.length > 0) {
              this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
            }

            }else if(this.editCode==3){
              for (let i = this.clickId - 1; i < this.markerPoint.length; i++) {
                this.markerPoint[i].id = i + 1;
                let content =
                  "<div class='marker-edit'><span class='text'>" +
                  (i + 1) +
                  "</span></div>";
                this.markerPoint[i].setContent(content);
              }
              this.num--;
              this.changePoint = {
                index: this.clickId - 1,
                lat: 0,
                lng: 0,
                type: "remove",
              };
              if (this.num > 0) {
                if (this.num == 1 && this.clickId == 2) {
                  this.map.remove(this.addMarkers[this.clickId - 2]);
                  this.addMarkers.splice(this.clickId - 2, 1);
                } else {
                  this.map.remove(this.addMarkers[this.clickId - 1]);
                  this.addMarkers.splice(this.clickId - 1, 1);
                  // console.log(this.addMarkers.length)
                }
              }
              if (this.num > 2) {
                if (this.clickId == 1) {
                  this.editOrthoCenter(this.markerPoint.length);
                } else {
                  this.editOrthoCenter(this.clickId - 1);
                }
                for (
                  let i = this.clickId - 1;
                  i < this.addMarkers.length;
                  i++
                ) {
                  this.addMarkers[i].id = i + 1;
                }
              }
              if (this.num == 2) {
                if (this.clickId > 1) {
                  this.map.remove(this.addMarkers[this.clickId - 2]);
                  this.addMarkers.splice(this.clickId - 2, 1);
                } else {
                  this.map.remove(this.addMarkers[this.addMarkers.length - 1]);
                  this.addMarkers.pop();
                }
              }
              this.drawOrthoPolypon();
              if (this.markerPoint.length > 0) {
                this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
              }
            }
            
          }
        }
        this.timeOut = setTimeout(() => {
          this.removeClick = false;
        }, 200);
      }
    },
    //清除所有航点
    delMarker() {
      if (this.markerPoint.length > 0) {
        this.delClick = true;
        this.$confirm(
          this.groupLanguage.route.placeholder3,
          this.groupLanguage.route.tip,
          {
            confirmButtonText: this.groupLanguage.route.saveBtn,
            cancelButtonText: this.groupLanguage.route.cancelBtn,
            type: "warning",
            customClass: "messageTip",
          }
        )
          .then(() => {
            this.map.remove(this.markerPoint);
            this.map.remove(this.addMarkers);
            this.markerPoint = [];
            this.addMarkers = [];
            this.num = 0;
            this.clickId = 0;
            if(this.editCode==1){
              this.changePoint = {
              index: 0,
              lat: 0,
              lng: 0,
              type: "del",
            };
            this.map.remove(this.clickRoute);
            this.clickRoute = "";
            

            }else if (this.editCode == 3) {
              this.changePoint = {
                index: 0,
                lat: 0,
                lng: 0,
                type: "del",
              };
              if (this.clickRoute) {
                this.map.remove(this.clickRoute);
                this.clickRoute = "";
              }
              if (this.clickRoute1) {
                this.map.remove(this.clickRoute1);
                this.clickRoute1 = "";
              }
              if (this.startMarker) {
                this.map.remove(this.startMarker);
                this.map.remove(this.endMarker);
                this.startMarker = "";
                this.endMarker = "";
              }
            }
            this.$message({
              type: "success",
              message: this.groupLanguage.route.messageInfo,
            });
            
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: this.groupLanguage.route.messageInfo1,
            });
          })
          .finally(() => {
            this.delClick = false;
          });
      }
    },
    //点击撤回数据
    recallMarker() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (this.drawend) {
        if (!this.recallClick) {
          this.recallClick = true;
          if (this.cacheData.length > 1) {
            let oldArr = this.cacheData[this.cacheData.length - 2];
            this.clickId = oldArr.clickId;
            let paths = [];
            for (
              let index = 0;
              index < oldArr.routeForm.point_json.length;
              index++
            ) {
              paths.push([
                oldArr.routeForm.point_json[index].lng,
                oldArr.routeForm.point_json[index].lat,
              ]);
            }
            if (this.editCode == 1) {
              this.$refs.routeEdit.routeForm = JSON.parse(
                JSON.stringify(oldArr.routeForm)
              );
              if (this.markerPoint.length < paths.length) {
                for (let index = 0; index < this.markerPoint.length; index++) {
                  this.markerPoint[index].setPosition(paths[index]);
                }
                for (let i = this.markerPoint.length; i < paths.length; i++) {
                  this.drawMarker(i + 1, paths[i]);
                }
                for (let j = 1; j < this.addMarkers.length; j++) {
                  this.editCenterpoint(j);
                }
                for (
                  let i = this.addMarkers.length;
                  i < paths.length - 1;
                  i++
                ) {
                  this.addCenterpoint(i + 1);
                }
              } else {
                for (let index = 0; index < paths.length; index++) {
                  this.markerPoint[index].setPosition(paths[index]);
                }
                for (let i = paths.length; i < this.markerPoint.length; i++) {
                  this.map.remove(this.markerPoint[i]);
                }
                for (let j = 1; j < paths.length - 1; j++) {
                  this.editCenterpoint(j);
                }
                for (
                  let i = paths.length - 1;
                  i < this.addMarkers.length;
                  i++
                ) {
                  this.map.remove(this.addMarkers[i]);
                }
                this.markerPoint.splice(
                  paths.length,
                  this.markerPoint.length - paths.length
                );
                this.addMarkers.splice(
                  paths.length - 1,
                  this.addMarkers.length - paths.length + 1
                );
              }
              this.markerClick(this.markerPoint[this.clickId - 1]);
              this.changeLine();
              this.num = paths.length;
              this.cacheData.pop();
            } else if (this.editCode == 3) {
              this.$refs.orthoEdit.orthoForm = JSON.parse(
                JSON.stringify(oldArr.routeForm)
              );
              if (this.markerPoint.length < paths.length) {
                for (let index = 0; index < this.markerPoint.length; index++) {
                  this.markerPoint[index].setPosition(paths[index]);
                }
                for (let i = this.markerPoint.length; i < paths.length; i++) {
                  this.drawOrthoPoint(i + 1, paths[i]);
                }
                for (let j = 0; j < this.addMarkers.length; j++) {
                  this.editOrthoCenter(j + 1);
                }

              } else {
                for (let index = 0; index < paths.length; index++) {
                  this.markerPoint[index].setPosition(paths[index]);
                }
                for (let i = paths.length; i < this.markerPoint.length; i++) {
                  this.map.remove(this.markerPoint[i]);
                }
                this.markerPoint.splice(
                  paths.length,
                  this.markerPoint.length - paths.length
                );
                for (let j = 0; j < paths.length; j++) {
                  this.editOrthoCenter(j + 1);
                }
                for (let i = paths.length; i < this.addMarkers.length; i++) {
                  this.map.remove(this.addMarkers[i]);
                }
                this.addMarkers.splice(
                  paths.length,
                  this.addMarkers.length - paths.length
                );
              }
              this.markerClick(this.markerPoint[this.clickId - 1]);
              this.drawOrthoPolypon();
              this.num = paths.length;
              this.cacheData.pop();
            }
          } else {
            this.$message.warning(this.groupLanguage.placeholder10);
          }
        }
        setTimeout(() => {
          this.recallClick = false;
        }, 200);
      }
    },
    //地图点击事件
    clickEvents(e) {
      this.num++;
      if (this.editCode == 1) {
        let a = computedMethod(2, {
          point1: e.lnglat,
          fence: this.fenceItem.paths,
        });
        if (!a) {
          this.$message.error(this.groupLanguage.route.placeholder);
          this.num--;
          return false;
        }
        if (this.num > 1) {
          let b = computedMethod(4, {
            point1: this.markerPoint[this.num - 2].getPosition(),
            point2: e.lnglat,
            fence: this.fenceItem.paths,
          });
          if (b) {
            this.$message.error(this.groupLanguage.route.placeholder1);
            this.num--;
            return false;
          }
          let isCreate = computedDistance(
            e.lnglat,
            this.markerPoint[this.num - 2].getPosition()
          );
          if (!isCreate) {
            this.$message.warning(this.groupLanguage.route.placeholder2);
            this.num--;
            return false;
          }
          this.drawMarker(this.num, e.lnglat);
          this.changeLine();
          this.addCenterpoint(this.num - 1);
        } else {
          this.drawMarker(this.num, e.lnglat);
        }
      } else if (this.editCode == 3) {
        let a = computedMethod(2, {
          point1: e.lnglat,
          fence: this.fenceItem.paths,
        });
        if (!a) {
          this.$message.error(this.routeLanguage.placeholder4);
          this.num--;
          return false;
        }
        if (this.num > 1) {
          let b = computedMethod(4, {
            point1: this.markerPoint[this.num - 2].getPosition(),
            point2: e.lnglat,
            fence: this.fenceItem.paths,
          });
          let b1 = computedMethod(4, {
            point1: this.markerPoint[0].getPosition(),
            point2: e.lnglat,
            fence: this.fenceItem.paths,
          });
          if (b || b1) {
            this.$message.error(this.routeLanguage.placeholder7);
            this.num--;
            return false;
          }
          // let isCreate = computedDistance(
          //   e.lnglat,
          //   this.markers[this.num - 2].getPosition()
          // );
          // if (!isCreate) {
          //   this.$message.warning("相邻航点有效距离不能超过2千米！");
          //   this.num--;
          //   return false;
          // }
          this.drawOrthoPoint(this.num, e.lnglat);
          this.drawOrthoPolypon();
          // this.addCenterpoint(this.num);
        } else {
          this.drawOrthoPoint(this.num, e.lnglat);
        }
      }
    },
    //绘制航点
    drawMarker(text, center) {
      let params = {
        offset: -17,
        clickable: true,
        draggable: true,
        zIndex: 55,
      };
      let marker = initMap.drawMarker(text, center, "marker-edit", params);
      marker.setMap(this.map);
      marker.id = text;
      this.markerPoint.push(marker);
      this.markerClick(marker);
      marker.on("click", this.markerClick);
      marker.on("dragstart", this.markerDragStart);
      marker.on("dragging", this.markerDrag);
      marker.on("dragend", this.markerDragEnd);
      if (!this.recallClick && !this.importClick) {
        this.changePoint = {
          index: text - 1,
          lat: parseFloat(marker.getPosition().kT.toFixed(7)),
          lng: parseFloat(marker.getPosition().KL.toFixed(7)),
          type: "add",
        };
      }
    },
    //更新marker
    updateMarker() {
      for (let index = 0; index < this.markerPoint.length; index++) {
        // this.markerPoint[index].setDraggable(true);
        // this.markerPoint[index].setClickable(true);
        this.markerPoint[index].on("click", this.markerClick);
        this.markerPoint[index].on("dragstart", this.markerDragStart);
        this.markerPoint[index].on("dragging", this.markerDrag);
        this.markerPoint[index].on("dragend", this.markerDragEnd);
        this.addCenterpoint(index);
      }
      this.num = this.markerPoint.length;
    },
    //正射影像更新航点
    updateMarkerOrtho() {
      for (let index = 0; index < this.markerPoint.length; index++) {
        // this.markerPoint[index].setDraggable(true);
        // this.markerPoint[index].setClickable(true);
        this.markerPoint[index].on("click", this.markerClick);
        this.markerPoint[index].on("dragstart", this.markerDragStart2);
        this.markerPoint[index].on("dragging", this.markerDrag2);
        this.markerPoint[index].on("dragend", this.markerDragEnd2);
        this.drawOrthoCenter(index);
      }
      this.num = this.markerPoint.length;
    },
    //点击触发
    markerClick(e) {
      if (this.drawend) {
        if (e.target) {
          this.clickId = e.target.id;
        } else {
          this.clickId = e.id;
        }
        // let back = "";
        for (let index = 0; index < this.markerPoint.length; index++) {
          let content = this.markerPoint[index].getContent();
          if (this.clickId == this.markerPoint[index].id) {
            if (content.indexOf("active") == -1) {
              let str = content.split("marker-edit");
              content = str[0] + "marker-edit active" + str[1];
            }
          } else {
            content = content.replace(" active", "");
          }
          this.markerPoint[index].setContent(content);
        }
      }
    },
    //开始拖拽航点
    markerDragStart(e) {
      this.deepCode = true;
      let index = this.markerPoint.findIndex((item) => {
        return item.id == e.target.id;
      });
      this.startPonit = {
        lng: this.$refs.routeEdit.routeForm.point_json[index].lng,
        lat: this.$refs.routeEdit.routeForm.point_json[index].lat,
      };
    },
    //拖拽中
    markerDrag(e) {
      this.markerClick(e);
      let index = this.markerPoint.findIndex((item) => {
        return item.id == e.target.id;
      });
      this.markerPoint[index].setPosition(e.lnglat);
      this.changePoint = {
        index: index,
        lat: parseFloat(e.lnglat.kT.toFixed(7)),
        lng: parseFloat(e.lnglat.KL.toFixed(7)),
        type: "edit",
      };
      this.editCenterpoint(index);
      this.changeLine();
    },
    //拖拽结束
    markerDragEnd(e) {
      this.deepCode = false;
      let index = this.markerPoint.findIndex((item) => {
        return item.id == e.target.id;
      });
      let a = computedMethod(2, {
        point1: e.lnglat,
        fence: this.fenceItem.paths,
      });
      if (!a) {
        this.$message.error(this.groupLanguage.route.placeholder);
        this.markerPoint[index].setPosition([
          this.startPonit.lng,
          this.startPonit.lat,
        ]);
        this.changePoint = {
          index: index,
          lat: this.startPonit.lat,
          lng: this.startPonit.lng,
          type: "edit",
        };
        this.editCenterpoint(index);
        this.changeLine();
        return false;
      }
      let point_json = this.$refs.routeEdit.routeForm.point_json;
      if (point_json.length > 1) {
        let b = "";
        let isCreate = false;
        if (index == 0) {
          b = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index + 1].getPosition(),
            fence: this.fenceItem.paths,
          });
          isCreate = computedDistance(
            e.lnglat,
            this.markerPoint[index + 1].getPosition()
          );
        } else if (index == point_json.length - 1) {
          b = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index - 1].getPosition(),
            fence: this.fenceItem.paths,
          });
          isCreate = computedDistance(
            e.lnglat,
            this.markerPoint[index - 1].getPosition()
          );
        } else {
          let b1 = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index + 1].getPosition(),
            fence: this.fenceItem.paths,
          });
          let b2 = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index - 1].getPosition(),
            fence: this.fenceItem.paths,
          });
          b = b1 || b2;
          let isCreate1 = computedDistance(
            e.lnglat,
            this.markerPoint[index + 1].getPosition()
          );
          let isCreate2 = computedDistance(
            e.lnglat,
            this.markerPoint[index - 1].getPosition()
          );
          isCreate = isCreate1 && isCreate2;
        }
        if (b) {
          this.$message.error(this.groupLanguage.route.placeholder1);
          this.markerPoint[index].setPosition([
            this.startPonit.lng,
            this.startPonit.lat,
          ]);
          this.changePoint = {
            index: index,
            lat: this.startPonit.lat,
            lng: this.startPonit.lng,
            type: "edit",
          };
          this.editCenterpoint(index);
          this.changeLine();
          return false;
        }
        if (!isCreate) {
          this.$message.warning(this.groupLanguage.route.placeholder2);
          this.markerPoint[index].setPosition([
            this.startPonit.lng,
            this.startPonit.lat,
          ]);
          this.changePoint = {
            index: index,
            lat: this.startPonit.lat,
            lng: this.startPonit.lng,
            type: "edit",
          };
          this.editCenterpoint(index);
          this.changeLine();
          return false;
        }
      }
      this.deepCopy();
    },
    //设置中心点
    addCenterpoint(index) {
      if (index > 0) {
        let lat =
          (parseFloat(this.markerPoint[index - 1].getPosition().lat) +
            parseFloat(this.markerPoint[index].getPosition().lat)) /
          2;
        let lng =
          (parseFloat(this.markerPoint[index - 1].getPosition().lng) +
            parseFloat(this.markerPoint[index].getPosition().lng)) /
          2;
        let center = [lng, lat];
        let params = {
          offset: -11,
          clickable: true,
          draggable: false,
        };
        let addMarker = initMap.drawMarker(
          "+",
          center,
          "marker-edit-i",
          params
        );
        addMarker.setMap(this.map);
        addMarker.id = index;
        addMarker.on("click", this.clickAdd);
        this.addMarkers.push(addMarker);
      }
    },
    //修改中心点
    editCenterpoint(index) {
      if (index == 0) {
        let lat =
          (parseFloat(this.markerPoint[index + 1].getPosition().lat) +
            parseFloat(this.markerPoint[index].getPosition().lat)) /
          2;
        let lng =
          (parseFloat(this.markerPoint[index + 1].getPosition().lng) +
            parseFloat(this.markerPoint[index].getPosition().lng)) /
          2;
        this.addMarkers[index].setPosition([lng, lat]);
      } else {
        let lat =
          (parseFloat(this.markerPoint[index - 1].getPosition().lat) +
            parseFloat(this.markerPoint[index].getPosition().lat)) /
          2;
        let lng =
          (parseFloat(this.markerPoint[index - 1].getPosition().lng) +
            parseFloat(this.markerPoint[index].getPosition().lng)) /
          2;
        this.addMarkers[index - 1].setPosition([lng, lat]);
        if (index < this.markerPoint.length - 1) {
          let lat1 =
            (parseFloat(this.markerPoint[index + 1].getPosition().lat) +
              parseFloat(this.markerPoint[index].getPosition().lat)) /
            2;
          let lng1 =
            (parseFloat(this.markerPoint[index + 1].getPosition().lng) +
              parseFloat(this.markerPoint[index].getPosition().lng)) /
            2;
          this.addMarkers[index].setPosition([lng1, lat1]);
        }
      }
    },
    //中心点点击事件
    clickAdd(e) {
      this.deepCode = true;
      this.num++;
      let index = e.target.id;
      this.map.remove(this.addMarkers[index - 1]);
      let params = {
        offset: -17,
        clickable: true,
        draggable: true,
        zIndex: 55,
      };
      let marker = initMap.drawMarker(
        index + 1,
        e.lnglat,
        "marker-edit",
        params
      );
      marker.id = index + 1;
      marker.setMap(this.map);
      marker.on("dragstart", this.markerDragStart);
      marker.on("dragging", this.markerDrag);
      marker.on("dragend", this.markerDragEnd);
      this.markerPoint.splice(index, 0, marker);
      this.changePoint = {
        index: index,
        lat: marker.getPosition().kT.toFixed(7),
        lng: marker.getPosition().KL.toFixed(7),
        type: "editAdd",
      };
      for (let i = index + 1; i < this.markerPoint.length; i++) {
        this.markerPoint[i].id = i + 1;
        let content =
          "<div class='marker-edit'><span class='text'>" +
          (i + 1) +
          "</span></div>";
        this.markerPoint[i].setContent(content);
      }
      marker.on("click", this.markerClick);
      this.markerClick(marker);
      this.changeLine();
      // for (let n = index; n < this.addMarkers.length; n++) {
      //     this.addMarkers[n].id = this.addMarkers[n].id + 1;
      // }
      let lat =
        (parseFloat(this.markerPoint[index - 1].getPosition().lat) +
          parseFloat(this.markerPoint[index].getPosition().lat)) /
        2;
      let lng =
        (parseFloat(this.markerPoint[index - 1].getPosition().lng) +
          parseFloat(this.markerPoint[index].getPosition().lng)) /
        2;
      let center = [lng, lat];
      let params1 = {
        offset: -11,
        clickable: true,
        draggable: false,
      };
      let addMarker = initMap.drawMarker("+", center, "marker-edit-i", params1);
      addMarker.setMap(this.map);
      addMarker.id = index;
      this.addMarkers.splice(index - 1, 1, addMarker);
      addMarker.on("click", this.clickAdd);
      let lat1 =
        (parseFloat(this.markerPoint[index].getPosition().lat) +
          parseFloat(this.markerPoint[index + 1].getPosition().lat)) /
        2;
      let lng1 =
        (parseFloat(this.markerPoint[index].getPosition().lng) +
          parseFloat(this.markerPoint[index + 1].getPosition().lng)) /
        2;
      let center1 = [lng1, lat1];
      let addMarker1 = initMap.drawMarker(
        "+",
        center1,
        "marker-edit-i",
        params1
      );
      addMarker1.setMap(this.map);
      addMarker1.id = index + 1;
      this.addMarkers.splice(index, 0, addMarker1);
      addMarker1.on("click", this.clickAdd);
      setTimeout(() => {
        this.deepCode = false;
        this.deepCopy();
      });
    },
    //正射影像绘制点
    drawOrthoPoint(text, center) {
      if (this.markerPoint.length > 2) {
        let a = this.isLineOrthoCross(
          this.markerPoint[0].getPosition(),
          center,
          0
        );
        let b = this.isLineOrthoCross(
          this.markerPoint[this.markerPoint.length - 1].getPosition(),
          center,
          this.markerPoint.length - 1
        );
        if (a || b) {
          this.$message.warning({
            message: this.routeLanguage.messageInfo3,
            duration: 1000,
          });
          this.num--;
          return false;
        }
      }
      let params = {
        offset: -17,
        clickable: true,
        draggable: true,
        zIndex: 55,
      };
      let marker = initMap.drawMarker(text, center, "marker-edit", params);
      marker.setMap(this.map);
      marker.id = text;
      this.markerPoint.push(marker);
      this.markerClick(marker);
      this.changePoint = {
        index: text - 1,
        lat: parseFloat(marker.getPosition().kT.toFixed(7)),
        lng: parseFloat(marker.getPosition().KL.toFixed(7)),
        type: "add",
      };

      marker.on("dragstart", this.markerDragStart2);
      marker.on("dragging", this.markerDrag2);
      marker.on("dragend", this.markerDragEnd2);
      marker.on("click", this.markerClick);
      if (this.markerPoint.length > 1) {
        this.drawOrthoCenter(this.markerPoint.length-1);
      }
      return true;
    },
    //正射影像绘制边框图形
    drawOrthoPolypon() {
      let paths = [];
      for (let index = 0; index < this.markerPoint.length; index++) {
        paths.push(this.markerPoint[index].getPosition());
      }
      if (this.clickRoute1) {
        this.clickRoute1.setPath(paths);
      } else {
        this.clickRoute1 = initMap.drawPolypon(paths);
        this.clickRoute1.setMap(this.map);
      }
      this.drawOrthoInPolypon();
    },
    //绘制正射影像内图形
    drawOrthoInPolypon() {
      if (this.markerPoint.length > 2) {
        let paths = [];
        for (let index = 0; index < this.markerPoint.length; index++) {
          paths.push(this.markerPoint[index].getPosition());
        }
        let ortho = this.$refs.orthoEdit.orthoForm;
        let params = {
          default_height: ortho.default_height,
          cameraParamList: this.$refs.orthoEdit.cameraParamList,
          course: ortho.course,
          lateral: ortho.lateral,
          angle: ortho.angle,
          wheelDist: ortho.wheelDist,
          paths: paths,
        };
        let { points, triggerDist } = orthoPhotoComputer(params, this.map, 1);
        if (points.length > 0) {
          this.messageCodeWarning = false;
          if (this.clickRoute) {
            this.clickRoute.setPath(points);
          } else {
            this.clickRoute = initMap.drawPolyline(points, {
              strokeColor: "#07ff0e",
            });
            this.clickRoute.setMap(this.map);
          }
          if (this.startMarker) {
            this.startMarker.setPosition(points[0]);
            this.endMarker.setPosition(points[points.length - 1]);
          } else {
            let params = {
              offset: -9,
              clickable: false,
              draggable: false,
              zIndex: 100,
            };
            this.startMarker = initMap.drawMarker(
              "S",
              points[0],
              "startend-marker",
              params
            );
            this.startMarker.setMap(this.map);
            this.endMarker = initMap.drawMarker(
              "E",
              points[points.length - 1],
              "startend-marker",
              params
            );
            this.endMarker.setMap(this.map);
          }
        } else {
          if (this.clickRoute) {
            this.map.remove(this.clickRoute);
            this.clickRoute = "";
          }
          if (!this.messageCodeWarning) {
            this.$message.warning(this.routeLanguage.errorMessage9);
            this.messageCodeWarning = true;
          }
          if (this.startMarker) {
            this.map.remove(this.startMarker);
            this.map.remove(this.endMarker);
            this.startMarker = "";
            this.endMarker = "";
          }
        }
      } else {
        if (this.clickRoute) {
          this.map.remove(this.clickRoute);
          this.clickRoute = "";
        }
        if (this.startMarker) {
          this.map.remove(this.startMarker);
          this.map.remove(this.endMarker);
          this.startMarker = "";
          this.endMarker = "";
        }
      }
    },
    //正射影像：判断是否出现交叉
    isLineOrthoCross(marker1, marker2, index) {
      let ortho = this.$refs.orthoEdit.orthoForm.point_json;
      let arr1 = ortho.slice(0, index);
      let arr2 = ortho.slice(index + 1, this.markerPoint.length);
      let arr = arr2.concat(arr1);
      return computedMethod(5, {
        point1: marker1,
        point2: marker2,
        fence: arr,
      });
    },
    //正射影像：拖拽判断是否出现交叉
    isLineOrthoCross1(marker1, marker2, index, num) {
      let ortho = this.$refs.orthoEdit.orthoForm.point_json;
      let arr1 = ortho.slice(0, index);
      let arr2 = ortho.slice(
        num ? index + 1 : index + 2,
        num ? ortho.length - 1 : ortho.length
      );
      let arr = arr2.concat(arr1);
      return computedMethod(5, {
        point1: marker1,
        point2: marker2,
        fence: arr,
      });
    },
    //正射影像拖拽开始
    markerDragStart2(e) {
      this.deepCode = true;
      let index = e.target.id;
      let orthoForm = this.$refs.orthoEdit.orthoForm;
      this.startPoints = {
        lng: orthoForm.point_json[index - 1].lng,
        lat: orthoForm.point_json[index - 1].lat,
      };
      this.markerClick(e);
    },
    //正射影像拖拽中
    markerDrag2(e) {
      let index = e.target.id;
      this.markerPoint[index - 1].setPosition(e.lnglat);
      this.changePoint = {
        index: index - 1,
        lat: parseFloat(e.lnglat.kT.toFixed(7)),
        lng: parseFloat(e.lnglat.KL.toFixed(7)),
        type: "edit",
      };
      if (this.markerPoint.length > 1) {
        this.drawOrthoPolypon();
        this.editOrthoCenter(index);
      }
    },
    //正射影像拖拽结束
    markerDragEnd2(e) {
      let index = e.target.id;
      let a = computedMethod(2, {
        point1: e.lnglat,
        fence: this.fenceItem.paths,
      });
      if (!a) {
        this.$message.error(this.routeLanguage.placeholder4);
        this.markerPoint[index - 1].setPosition(
          new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
        );
        this.changePoint = {
          index: index - 1,
          lat: this.startPoints.lat,
          lng: this.startPoints.lng,
          type: "edit",
        };
        if (this.markerPoint.length > 1) {
          this.drawOrthoPolypon();
          this.editOrthoCenter(index);
        }
        return false;
      }
      if (this.markerPoint.length > 1) {
        let d = "";
        if (index == 1) {
          let ds = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index].getPosition(),
            fence: this.fenceItem.paths,
          });
          let de = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[this.markerPoint.length - 1].getPosition(),
            fence: this.fenceItem.paths,
          });
          d = ds || de;
        } else if (index == this.markerPoint.length) {
          let ds1 = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index - 2].getPosition(),
            fence: this.fenceItem.paths,
          });
          let de1 = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[0].getPosition(),
            fence: this.fenceItem.paths,
          });
          d = ds1 || de1;
        } else {
          let b1 = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index].getPosition(),
            fence: this.fenceItem.paths,
          });
          let b2 = computedMethod(4, {
            point1: e.lnglat,
            point2: this.markerPoint[index - 2].getPosition(),
            fence: this.fenceItem.paths,
          });
          d = b1 || b2;
        }
        if (d) {
          this.$message.error(this.routeLanguage.placeholder5);
          this.markerPoint[index - 1].setPosition([
            this.startPoints.lng,
            this.startPoints.lat,
          ]);
          this.changePoint = {
            index: index - 1,
            lat: this.startPoints.lat,
            lng: this.startPoints.lng,
            type: "edit",
          };
          this.drawOrthoPolypon();
          this.editOrthoCenter(index);
          return false;
        }
      }
      if (this.markerPoint.length > 2) {
        let c, b;
        if (index == 1) {
          c = this.isLineOrthoCross1(
            this.markerPoint[0].getPosition(),
            this.markerPoint[index].getPosition(),
            0
          );
          b = this.isLineOrthoCross1(
            this.markerPoint[this.markerPoint.length - 1].getPosition(),
            this.markerPoint[0].getPosition(),
            0,
            1
          );
        } else if (index == this.markerPoint.length) {
          c = this.isLineOrthoCross1(
            this.markerPoint[0].getPosition(),
            this.markerPoint[this.markerPoint.length - 1].getPosition(),
            0,
            1
          );
          b = this.isLineOrthoCross1(
            this.markerPoint[this.markerPoint.length - 1].getPosition(),
            this.markerPoint[index - 2].getPosition(),
            this.markerPoint.length - 2
          );
        } else {
          c = this.isLineOrthoCross1(
            this.markerPoint[index - 1].getPosition(),
            this.markerPoint[index - 2].getPosition(),
            index - 2
          );
          b = this.isLineOrthoCross1(
            this.markerPoint[index - 1].getPosition(),
            this.markerPoint[index].getPosition(),
            index - 1
          );
        }
        if (c || b) {
          this.markerPoint[index - 1].setPosition(
            new AMap.LngLat(this.startPoints.lng, this.startPoints.lat)
          );
          this.changePoint = {
            index: index - 1,
            lat: this.startPoints.lat,
            lng: this.startPoints.lng,
            type: "edit",
          };

          this.$message.warning({
            message: this.routeLanguage.messageInfo3,
            duration: 1000,
          });
          this.drawOrthoPolypon();
          this.editOrthoCenter(index);
          return false;
        }
      }
      this.deepCode = false;
      this.deepCopy();
    },
    //绘制正射影像中心点
    drawOrthoCenter(index) {
      if (index == 1) {
        let center = computeCenter([
          this.markerPoint[index].getPosition(),
          this.markerPoint[index - 1].getPosition(),
        ]);
        let params = {
          offset: -11,
          clickable: true,
          draggable: false,
        };
        let addMarker = initMap.drawMarker(
          "+",
          center,
          "marker-edit-i",
          params
        );
        addMarker.id = index;
        addMarker.setMap(this.map);
        addMarker.on("click", this.addOrthoClick);
        this.addMarkers.push(addMarker);
      } else if (index == 2) {
        let center = computeCenter([
          this.markerPoint[index].getPosition(),
          this.markerPoint[index - 1].getPosition(),
        ]);
        let params = {
          offset: -11,
          clickable: true,
          draggable: false,
        };
        let addMarker = initMap.drawMarker(
          "+",
          center,
          "marker-edit-i",
          params
        );
        addMarker.id = index;
        addMarker.setMap(this.map);
        addMarker.on("click", this.addOrthoClick);
        this.addMarkers.push(addMarker);
        let center1 = computeCenter([
          this.markerPoint[index].getPosition(),
          this.markerPoint[0].getPosition(),
        ]);
        let addMarker1 = initMap.drawMarker(
          "+",
          center1,
          "marker-edit-i",
          params
        );
        addMarker1.id = index + 1;
        addMarker1.setMap(this.map);
        addMarker1.on("click", this.addOrthoClick);
        this.addMarkers.push(addMarker1);
      } else if (index > 2) {
        let center = computeCenter([
          this.markerPoint[index].getPosition(),
          this.markerPoint[index - 1].getPosition(),
        ]);
        this.addMarkers[index - 1].setPosition(center);
        let center1 = computeCenter([
          this.markerPoint[index].getPosition(),
          this.markerPoint[0].getPosition(),
        ]);
        let params = {
          offset: -11,
          clickable: true,
          draggable: false,
        };
        let addMarker1 = initMap.drawMarker(
          "+",
          center1,
          "marker-edit-i",
          params
        );
        addMarker1.id = index + 1;
        addMarker1.setMap(this.map);
        addMarker1.on("click", this.addOrthoClick);
        this.addMarkers.push(addMarker1);
      }
      // let center = computeCenter([
      //   this.markerPoint[this.markerPoint.length - 1].getPosition(),
      //   this.markerPoint[this.markerPoint.length - 2].getPosition(),
      // ]);
      // let params = {
      //   offset: -11,
      //   clickable: true,
      //   draggable: false,
      // };
      // let addMarker = initMap.drawMarker("+", center, "marker-edit-i", params);
      // addMarker.id = this.markerPoint.length - 1;
      // addMarker.setMap(this.map);
      // this.addMarkers.push(addMarker);
      // addMarker.on("click", this.addOrthoClick);
      // if (this.markers.length > 2) {
      //   let center1 = computeCenter([
      //     this.markerPoint[this.markerPoint.length - 1].getPosition(),
      //     this.markerPoint[0].getPosition(),
      //   ]);
      //   if (this.addMarkers.length == this.markers.length) {
      //     this.addMarkers[this.addMarkers.length - 2].setPosition(center1);
      //     this.addMarkers[this.addMarkers.length - 2].id =
      //       this.markerPoint.length;
      //     let temp = this.addMarkers[this.addMarkers.length - 1];
      //     this.addMarkers[this.addMarkers.length - 1] =
      //       this.addMarkers[this.addMarkers.length - 2];
      //     this.addMarkers[this.addMarkers.length - 2] = temp;
      //   } else {
      //     let addMarker1 = initMap.drawMarker(
      //       "+",
      //       center1,
      //       "marker-edit-i",
      //       params
      //     );
      //     addMarker1.id = this.markers.length;
      //     addMarker1.setMap(this.map);
      //     this.addMarkers.push(addMarker1);
      //     addMarker1.on("click", this.addOrthoClick);
      //   }
      // }
    },
    //修改中心点坐标
    editOrthoCenter(index) {
      if (this.markerPoint.length < 3) {
        let center = computeCenter([
          this.markers[0].getPosition(),
          this.markers[1].getPosition(),
        ]);
        this.addMarkers[0].setPosition(center);
      } else {
        if (index == 1) {
          let center = computeCenter([
            this.markerPoint[index].getPosition(),
            this.markerPoint[index - 1].getPosition(),
          ]);
          this.addMarkers[index - 1].setPosition(center);
          let center1 = computeCenter([
            this.markerPoint[0].getPosition(),
            this.markerPoint[this.markerPoint.length - 1].getPosition(),
          ]);
          this.addMarkers[this.addMarkers.length - 1].setPosition(center1);
        } else if (index > 1) {
          let center = computeCenter([
            this.markerPoint[index - 1].getPosition(),
            this.markerPoint[index - 2].getPosition(),
          ]);
          this.addMarkers[index - 2].setPosition(center);
          if (index == this.markerPoint.length) {
            let center = computeCenter([
              this.markerPoint[index - 1].getPosition(),
              this.markerPoint[0].getPosition(),
            ]);
            this.addMarkers[index - 1].setPosition(center);
          } else {
            let center = computeCenter([
              this.markerPoint[index].getPosition(),
              this.markerPoint[index - 1].getPosition(),
            ]);
            this.addMarkers[index - 1].setPosition(center);
          }
        }
      }
    },
    addOrthoClick() {},
    //更改航线
    changeLine() {
      let paths = [];
      for (let index = 0; index < this.markerPoint.length; index++) {
        paths.push(this.markerPoint[index].getPosition());
      }
      if (this.clickRoute) {
        this.clickRoute.setPath(paths);
      } else {
        this.clickRoute = initMap.drawPolyline(paths);
        this.clickRoute.setMap(this.map);
      }
    },
    //返回修改marker
    changeMarker(e) {
      this.markerPoint[e.index].setPosition(new AMap.LngLat(e.lng, e.lat));
      if (this.markerPoint.length > 1) {
        this.changeLine();
        this.editCenterpoint(e.index);
      }
    },
    //正射影像返回修改marker
    changeMarker1(e) {
      this.markerPoint[e.index].setPosition(new AMap.LngLat(e.lng, e.lat));
      if (this.markerPoint.length > 1) {
        this.drawOrthoPolypon();
        this.editOrthoCenter(e.index + 1);
      }
    },
    //返回上一层
    goBack() {
      let code=0
      if(this.editCode==3){
        code=4
      }
      this.editCode = 0;
      if (this.drawend) {
        this.drawMark();
      }
      this.clickId = 0;
      this.cacheData=[]
      this.map.remove(this.markerPoint);
      this.map.remove(this.addMarkers);
      this.markerPoint = [];
      this.addMarkers = [];
      this.num = 0;
      this.map.remove(this.clickRoute);
      this.clickRoute = "";
      if(this.clickRoute1){
        this.map.remove(this.clickRoute1);
      this.clickRoute1 = "";
      }
      if(this.startMarker){
        this.map.remove(this.startMarker);
          this.map.remove(this.endMarker);
          this.startMarker = "";
          this.endMarker = "";

      }
      
      if (!this.backCode) {
        setTimeout(() => {
          this.$refs.taskList.clickType(this.typeList[code]);
          // this.$refs.taskList.typeCode=code
          this.$store.commit("routeItem", "");
        });
      }
    },
    //保存数据，方便撤回
    deepCopy() {
      if (this.editCode == 1) {
        if (!this.deepCode && !this.recallClick && !this.importCode) {
          let paths = {
            routeForm: JSON.parse(
              JSON.stringify(this.$refs.routeEdit.routeForm)
            ),
            clickId: this.clickId,
          };
          this.cacheData.push(paths);
          console.log(this.cacheData);
        }
      } else if (this.editCode == 3) {
        if (!this.deepCode && !this.recallClick && !this.importCode) {
          let paths = {
            routeForm: JSON.parse(
              JSON.stringify(this.$refs.orthoEdit.orthoForm)
            ),
            clickId: this.clickId,
          };
          this.cacheData.push(paths);
          console.log(this.cacheData);
        }
      }
    },
    //点击导入kml文件
    importEvent() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (!this.importClick) {
        this.importClick = true;
        if (this.markerPoint.length > 0) {
          this.$confirm(
            this.routeLanguage.placeholder11,
            this.routeLanguage.tip,
            {
              confirmButtonText: this.routeLanguage.saveBtn,
              cancelButtonText: this.routeLanguage.cancelBtn,
              type: "warning",
            }
          )
            .then(() => {
              this.$refs.files.click();
              this.map.remove(this.markerPoint);
              this.map.remove(this.addMarkers);
              this.markerPoint = [];
              this.addMarkers = [];
              this.num = 0;
              this.clickId = 0;
              if (this.editCode == 1) {
                this.changePoint = {
                  index: 0,
                  lat: 0,
                  lng: 0,
                  type: "del",
                };
                if (this.clickRoute) {
                  this.map.remove(this.clickRoute);
                  this.clickRoute = "";
                }
              } else if (this.editCode == 3) {
                this.changePoint = {
                  index: 0,
                  lat: 0,
                  lng: 0,
                  type: "del",
                };
                if (this.clickRoute) {
                  this.map.remove(this.clickRoute);
                  this.clickRoute = "";
                }
                if (this.clickRoute1) {
                  this.map.remove(this.clickRoute1);
                  this.clickRoute1 = "";
                }
                if (this.startMarker) {
                  this.map.remove(this.startMarker);
                  this.startMarker = "";
                  this.map.remove(this.endMarker);
                  this.endMarker = "";
                }
              }
            })
            .catch(() => {})
            .finally(() => {
              this.importClick = false;
            });
        } else {
          this.$refs.files.click();
          this.timeOut = setTimeout(() => {
            this.importClick = false;
          }, 300);
        }
      }
    },
    //解析上传的kml文件
    chooseFileAfter(e) {
      let file = e.target.files[0];
      let a = file.name.split(".");
      if (a[a.length - 1] == "kml") {
        let file_reader = new FileReader();
        file_reader.readAsText(file, "UTF-8");
        file_reader.onload = () => {
          const xml = new DOMParser().parseFromString(
            file_reader.result,
            "text/xml"
          );
          let kml = togeojson.kml(xml, {
            style: true,
          });
          this.kmlFormat(kml);
        };
        let obj = this.$refs.files;
        obj.value = "";
      } else if (a[a.length - 1] == "json") {
        let file_reader = new FileReader();
        file_reader.readAsText(file, "UTF-8");
        file_reader.onload = () => {
          this.exportPoint(JSON.parse(file_reader.result));
        };
        let obj = this.$refs.files;
        obj.value = "";
      } else {
        this.$message.error(this.routeLanguage.errorMessage1);
      }
    },
    //json文件解析的点
    exportPoint(files) {
      let paths = [];
      for (let index = 0; index < files.points.length; index++) {
        let lng = parseFloat(files.points[index].lng.toFixed(7));
        let lat = parseFloat(files.points[index].lat.toFixed(7));
        let height = files.points[index].height;
        let action = [];
        if (
          files.points[index].yawPitchArray &&
          files.points[index].yawPitchArray.length > 0
        ) {
          action.push({
            action_id: "gimbal_ctrl",
            param_list: [
              {
                param_id: "gimbal_yaw",
                value: files.points[index].yawPitchArray[0].aircraftYaw,
              },
              {
                param_id: "gimbal_pitch",
                value: files.points[index].yawPitchArray[0].gimbalPitch,
              },
            ],
          });
        }

        paths.push([lng, lat, height, action]);
      }
      this.importCode = true;
      this.loading = true;
      if (this.editCode == 1) {
        if (!this.$refs.routeEdit.routeForm.name) {
          this.$refs.routeEdit.routeForm.name = files.taskname;
        }
        this.$refs.routeEdit.routeForm.default_height = files.height;
        this.drawImport(paths);
      } else if (this.editCode == 3) {
        if (!this.$refs.orthoEdit.orthoForm.name) {
          this.$refs.orthoEdit.orthoForm.name = files.taskname;
        }
        this.$refs.orthoEdit.orthoForm.default_height = files.height;
        this.drawImportOrtho(paths);
      }
      // console.log(files)
    },
    //获取kml坐标数据
    getKmlData(kml) {
      let datas = "";
      if (kml.features.length > 1) {
        datas = [];
        for (let index = 0; index < kml.features.length; index++) {
          datas.push(kml.features[index].geometry.coordinates);
        }
      } else {
        let type = kml.features[0].geometry.type;
        switch (type) {
          case "LineString":
            datas = kml.features[0].geometry.coordinates;
            break;
          case "Polygon":
            datas = kml.features[0].geometry.coordinates[0];
            break;
          default:
            break;
        }
      }
      return datas;
    },
    //kml转换成坐标
    async kmlFormat(kml) {
      if (this.editCode == 1) {
        // if (kml.features[0].geometry.type == "LineString") {
        if (
          !kml.features[0].properties.type ||
          kml.features[0].properties.type == this.checkType.value
        ) {
          let datas = this.getKmlData(kml);
          if (!datas) {
            this.$message.error(this.routeLanguage.errorMessage2);
            return false;
          }
          if (!this.$refs.routeEdit.routeForm.name) {
            this.$refs.routeEdit.routeForm.name =
              kml.features[0].properties.name;
          }
          this.importCode = true;
          this.loading = true;
          // for (let index = 0; index < datas.length; index++) {
          //   let str = new AMap.LngLat(datas[index][0], datas[index][1]);
          //   pointstr.push(str);
          // }
          await this.drawImport(datas);
        } else {
          this.$message.error(this.routeLanguage.errorMessage3);
        }
        // } else {
        //   this.$message.error("选择的kml文件不是任务航线！");
        // }
      } else if (this.editCode == 3) {
        if (
          !kml.features[0].properties.type ||
          kml.features[0].properties.type == this.checkType.value
        ) {
          let datas = this.getKmlData(kml);
          if (!datas) {
            this.$message.error(this.routeLanguage.errorMessage2);
            return false;
          }
          if (!this.$refs.orthoEdit.orthoForm.name) {
            this.$refs.orthoEdit.orthoForm.name =
              kml.features[0].properties.name;
          }
          // this.orthoForm.lateral=kml.features[0].properties.lateral||this.orthoForm.lateral
          // this.orthoForm.course=kml.features[0].properties.course||this.orthoForm.course
          // this.orthoForm.wheelDist=kml.features[0].properties.wheelDist||this.orthoForm.wheelDist
          // this.orthoForm.angle=kml.features[0].properties.angle||this.orthoForm.angle
          this.importCode = true;
          this.loading = true;
          await this.drawImportOrtho(datas);
        } else {
          this.$message.error(this.routeLanguage.errorMessage3);
        }
      }
    },
    //绘制导入的航点
    async drawImport(pointstr) {
      if (this.num < pointstr.length) {
        let point = new AMap.LngLat(
          pointstr[this.num][0],
          pointstr[this.num][1]
        );
        let a = computedMethod(2, {
          point1: point,
          fence: this.fenceItem.paths,
        });
        if (a) {
          if (this.num > 0) {
            let b = computedMethod(4, {
              point1: this.markerPoint[this.num - 1].getPosition(),
              point2: point,
              fence: this.fenceItem.paths,
            });
            if (b) {
              if (this.routeLanguage.language == "en-US") {
                this.$message.error(
                  "The connection between waypoint" +
                    this.num +
                    "and waypoint" +
                    (this.num + 1) +
                    "exceeds the fence"
                );
              } else {
                this.$message.error(
                  this.num +
                    this.routeLanguage.errorMessage5 +
                    (this.num + 1) +
                    this.routeLanguage.errorMessage6
                );
              }
              this.loading = false;
              this.importCode = false;
              if (!this.drawend) {
                this.drawMark();
              }
              if (this.markerPoint.length > 0) {
                this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
              }
              return false;
            } else {
              this.changePoint = {
                index: 0,
                lng: pointstr[this.num][0],
                lat: pointstr[this.num][1],
                height: pointstr[this.num][2],
                action: pointstr[this.num][3],
                type: "import",
              };
              this.num++;
              await this.drawMarker(this.num, point);
              if (this.num > 1) {
                await this.changeLine();
                await this.addCenterpoint(this.num - 1);
              }
              setTimeout(() => {
                this.drawImport(pointstr);
              });
            }
          } else {
            this.changePoint = {
              index: 0,
              lng: pointstr[this.num][0],
              lat: pointstr[this.num][1],
              height: pointstr[this.num][2],
              action: pointstr[this.num][3],
              type: "import",
            };
            this.num++;
            await this.drawMarker(this.num, point);
            // if (this.num > 1) {
            //   await this.drawline();
            //   await this.addCenterpoint(this.num-1);
            // }
            setTimeout(() => {
              this.drawImport(pointstr);
            });
          }
        } else {
          if (this.routeLanguage.language == "en-US") {
            this.$message.error(
              this.routeLanguage.waypoint.title +
                this.num +
                1 +
                this.routeLanguage.errorMessage7
            );
          } else {
            this.$message.error(
              this.num + 1 + this.routeLanguage.errorMessage7
            );
          }
          this.importCode = false;
          this.loading = false;
          if (!this.drawend) {
            this.drawMark();
          }
          if (this.markerPoint.length > 0) {
            this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
          }
          return false;
        }
      } else {
        this.loading = false;
        this.importCode = false;
        if (!this.drawend) {
          this.drawMark();
        }
        this.map.setFitView(this.clickRoute, true, [100, 100, 150, 100], 30);
        this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
        this.deepCopy();
      }
    },
    //绘制正射影像导入的点
    async drawImportOrtho(points) {
      if (this.num < points.length) {
        let point = new AMap.LngLat(points[this.num][0], points[this.num][1]);
        let a = computedMethod(2, {
          point1: point,
          fence: this.fenceItem.paths,
        });
        if (a) {
          if (this.num > 0) {
            let b = computedMethod(4, {
              point1: this.markerPoint[this.num - 1].getPosition(),
              point2: point,
              fence: this.fenceItem.paths,
            });
            if (b) {
              if (this.routeLanguage.language == "en-US") {
                this.$message.error(
                  "The connection between waypoint" +
                    this.num +
                    "and waypoint" +
                    (this.num + 1) +
                    "exceeds the fence"
                );
              } else {
                this.$message.error(
                  this.num +
                    this.routeLanguage.errorMessage5 +
                    (this.num + 1) +
                    this.routeLanguage.errorMessage6
                );
              }
              this.loading = false;
              this.importCode = false;
              if (!this.drawend) {
                this.drawMark();
              }
              this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
              return false;
            } else {
              this.num++;
              let ab = await this.drawOrthoPoint(this.num, point, 1);
              if (!ab) {
                this.loading = false;
                this.importCode = false;
                if (!this.drawend) {
                  this.drawMark();
                }
                this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
                this.num = this.num - 1;
                return false;
              }
              this.changePoint = {
                index: 0,
                lng: points[this.num - 1][0],
                lat: points[this.num - 1][1],
                type: "import",
              };
              await this.drawOrthoPolypon();
              setTimeout(() => {
                this.drawImportOrtho(points);
              });
            }
          } else {
            this.num++;
            let ab = await this.drawOrthoPoint(this.num, point, 1);
            if (!ab) {
              this.loading = false;
              this.importCode = false;
              if (!this.drawend) {
                this.drawMark();
              }
              this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
              this.num = this.num - 1;
              return false;
            }
            this.changePoint = {
              index: 0,
              lng: points[this.num - 1][0],
              lat: points[this.num - 1][1],
              type: "import",
            };
            setTimeout(() => {
              this.drawImportOrtho(points);
            });
          }
        } else {
          if (this.routeLanguage.language == "en-US") {
            this.$message.error(
              this.routeLanguage.waypoint.title +
                this.num +
                1 +
                this.routeLanguage.errorMessage7
            );
          } else {
            this.$message.error(
              this.num + 1 + this.routeLanguage.errorMessage7
            );
          }
          this.importCode = false;
          this.loading = false;
          if (!this.drawend) {
            this.drawMark();
          }
          this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
          return false;
        }
      } else {
        this.loading = false;
        this.importCode = false;
        if (!this.drawend) {
          this.drawMark();
        }
        this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
        this.deepCopy();
      }
    },
    //点击导出kml文件
    exportEvent() {
      if (this.loading) {
        this.$message.warning(this.routeLanguage.placeholder3);
        return false;
      }
      if (!this.exportClick) {
        this.exportClick = true;
        this.$confirm(
          this.routeLanguage.placeholder13,
          this.routeLanguage.tip,
          {
            confirmButtonText: this.routeLanguage.saveBtn,
            cancelButtonText: this.routeLanguage.cancelBtn,
            type: "warning",
          }
        )
          .then(() => {
            if (this.editCode == 1) {
              let formList = this.$refs.routeEdit.routeForm;
              if (!formList.name) {
                this.$message.error(this.routeLanguage.routeLine.placeholder);
              } else {
                if (formList.point_json.length > 1) {
                  let str =
                    `<?xml version='1.0' encoding='utf-8'?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>
                <Placemark><name>` +
                    formList.name +
                    `</name><type></type><ExtendedData><Data name="type"><value>` +
                    this.checkType.value +
                    `</value></Data></ExtendedData><LineString><coordinates>`;
                  for (
                    let index = 0;
                    index < formList.point_json.length;
                    index++
                  ) {
                    str +=
                      formList.point_json[index].lng +
                      "," +
                      formList.point_json[index].lat +
                      "," +
                      formList.point_json[index].height +
                      " ";
                  }
                  str += `</coordinates></LineString><styleUrl>style-id</styleUrl></Placemark><Style id="style-id"><LineStyle><color>ffffffff</color><width>6</width></LineStyle></Style></Document></kml>`;
                  const domObj = document.createElement("a");
                  domObj.setAttribute(
                    "href",
                    "data:text/xml;charset=utf-8," + encodeURIComponent(str)
                  ); //注：如存储数组 or JSON需将其转换为JSON字符串
                  domObj.setAttribute(
                    "download",
                    formList.name + this.returnDate() + ".kml"
                  );
                  if (document.createEvent) {
                    const event = document.createEvent("MouseEvents");
                    event.initEvent("click", true, true);
                    domObj.dispatchEvent(event);
                  } else {
                    domObj.click();
                  }
                } else {
                  this.$message.error(this.routeLanguage.messageInfo5);
                }
              }
            } else if (this.editCode == 3) {
              let formList = this.$refs.orthoEdit.orthoForm;
              if (!formList.name) {
                this.$message.error(this.routeLanguage.routeLine.placeholder);
              } else {
                if (formList.point_json.length > 1) {
                  let str =
                    `<?xml version='1.0' encoding='utf-8'?><kml xmlns="http://www.opengis.net/kml/2.2"><Document>
                <Placemark><name>` +
                    formList.name +
                    `</name><type></type><ExtendedData><Data name="type"><value>` +
                    this.checkType.value +
                    // `</value></Data><Data name="lateral"><value>` +
                    // this.orthoForm.lateral +
                    // `</value></Data><Data name="course"><value>` +
                    // this.orthoForm.course +
                    // `</value></Data>
                    // <Data name="wheelDist"><value>` +
                    // this.orthoForm.wheelDist +
                    // `</value></Data>
                    // <Data name="angle"><value>` +
                    // this.orthoForm.angle +
                    `</value></Data></ExtendedData><Polygon><outerBoundaryIs><LinearRing><coordinates>`;
                  for (
                    let index = 0;
                    index < formList.point_json.length;
                    index++
                  ) {
                    str +=
                      formList.point_json[index].lng +
                      "," +
                      formList.point_json[index].lat +
                      "," +
                      formList.default_height +
                      " ";
                  }
                  str += `</coordinates></LinearRing></outerBoundaryIs></Polygon><styleUrl>style-id</styleUrl></Placemark><Style id="style-id"><LineStyle><color>ffffffff</color><width>6</width></LineStyle></Style></Document></kml>`;
                  const domObj = document.createElement("a");
                  domObj.setAttribute(
                    "href",
                    "data:text/xml;charset=utf-8," + encodeURIComponent(str)
                  ); //注：如存储数组 or JSON需将其转换为JSON字符串
                  domObj.setAttribute(
                    "download",
                    formList.name + this.returnDate() + ".kml"
                  );
                  if (document.createEvent) {
                    const event = document.createEvent("MouseEvents");
                    event.initEvent("click", true, true);
                    domObj.dispatchEvent(event);
                  } else {
                    domObj.click();
                  }
                } else {
                  this.$message.error(this.routeLanguage.messageInfo5);
                }
              }
            }
          })
          .catch(() => {
            this.$message.info(this.routeLanguage.cancelexport);
          })
          .finally(() => {
            this.exportClick = false;
          });
      }
    },
    //计算当前的日期yyyyMMddhhmmss
    returnDate() {
      let time = new Date();
      time =
        time.getFullYear() +
        (time.getMonth() + 1 > 9
          ? time.getMonth() + 1
          : "0" + (time.getMonth() + 1)) +
        (time.getDate() > 9 ? time.getDate() : "0" + time.getDate()) +
        (time.getHours() > 9 ? time.getHours() : "0" + time.getHours()) +
        (time.getMinutes() > 9 ? time.getMinutes() : "0" + time.getMinutes()) +
        (time.getSeconds() > 9 ? time.getSeconds() : "0" + time.getSeconds());
      return time;
    },
    //刷新正射影像
    refresh() {
      this.drawOrthoInPolypon();
    },
  },
  watch: {
    listCode(val) {
      if (!val) {
        this.idCode = "";
        if (this.clickRoute) {
          this.map.remove(this.clickRoute);
          this.clickRoute = "";
          this.map.remove(this.markerPoint);
          this.markerPoint = [];
        }
        if (this.clickRoute1) {
          this.map.remove(this.clickRoute1);
          this.clickRoute1 = "";
        }
        if (this.startMarker) {
          this.map.remove(this.startMarker);
          this.map.remove(this.endMarker);
          this.startMarker = "";
          this.endMarker = "";
        }
        for (let i = 0; i < this.polyponList.length; i++) {
          this.polyponList[i].show();
        }
        if (this.distancePoint) {
          this.map.remove(this.distancePoint);
        }
        this.distancePoint = [];
      }
    },
    editCode(val) {
      if (val) {
        if (val == 1) {
          this.updateMarker();
        } else if (val == 3) {
          this.updateMarkerOrtho();
        }
        this.drawMark();
        this.markerClick(this.markerPoint[this.markerPoint.length - 1]);
      }
    },
  },
  beforeDestroy() {
    this.map && this.map.destroy();
    this.map = "";
  },
};
</script>
<style lang="less" scoped>
.editWork {
  width: 100%;
  height: 100%;
  position: relative;
  #map {
    width: 100%;
    height: 100%;
  }
  .deviceListDiv {
    position: absolute;
    top: 0;
    left: 1%;
    width: 350px;
    height: 98%;
    border-radius: 6px;
    .el-container {
      width: 100%;
      height: 100%;
      .el-header {
        height: 6% !important;
        padding-top: 6%;
        text-align: center;
        color: white;
        font-size: 20px;
        letter-spacing: 1px;
      }
      .backBtn {
        font-size: xx-large;
        position: absolute;
        top: 2.5%;
        left: 2%;
        color: white;
        padding: 0;
      }
      .el-main {
        padding: 2%;
        padding-top: 4%;
        overflow-y: auto;
        overflow-x: hidden;
        .el-row {
          width: 100%;
          border-radius: 4px;
          padding: 4% 2%;
          padding-left: 0;
          margin-bottom: 2%;
          &.active {
            border: 1px solid #0b58de;
          }
          .num {
            padding-top: 5%;
            font-size: 12px;
            text-align: center;
          }
          .image {
            .el-image {
              width: 100%;
            }
          }
          .content {
            padding-left: 3%;
            .content-title {
              padding: 0 3%;

              border-radius: 6px;
              font-size: 14px;
              letter-spacing: 2px;
            }
            .isbind {
              position: absolute;
              bottom: 23%;
              left: 41%;
              display: flex;
              width: 58%;
              .isbind-item-1 {
                width: 12px;
                height: 12px;
                margin-top: 2%;
                border-radius: 2px;
              }
              .isbind-item-2 {
                font-size: 12px;
                margin-left: 5%;
              }
            }
          }
        }
      }
    }
  }
  .taskList {
    position: absolute;
    top: 7%;
    left: calc(1.5vw + 350px);
    width: 300px;
    height: 91%;
    border-radius: 6px;
  }
  .routeEdit,
  .orthoEdit {
    position: absolute;
    top: 7%;
    left: calc(1.5vw + 350px);
    width: 340px;
    height: 88%;
    border-radius: 6px;
    padding: 1% 0;
    padding-bottom: 1%;
    padding-left: 1%;
  }
  .operateBar_1 {
    position: absolute;
    bottom: 3%;
    right: 7%;
    height: auto;
    border-radius: 8px;
    font-size: 18px;
  }
}
</style>
<style lang="less">
.editWork {
  .deviceListDiv {
    .el-container {
      i[class*=" el-icon-"],
      i[class^="el-icon-"] {
        font-weight: 1000 !important;
      }
    }
  }
  .amap-logo,
  .amap-copyright {
    display: none !important;
  }
  #map {
    .amap-markers {
      .amap-marker {
        .marker-edit {
          position: relative;
          height: 30px;
          width: 30px;
          border-radius: 50%;
          text-align: center;
          .text {
            position: absolute;
            top: 50%;
            left: 50%;
            font-weight: 700;
            font-size: 20px;
            transform: translate(-50%, -50%);
          }
        }
        .marker-edit-i {
          height: 18px;
          width: 18px;
          border-radius: 50%;
          text-align: center;
          .text {
            position: absolute;
            top: 47%;
            left: 50%;
            font-weight: 800;
            font-size: 18px;
            vertical-align: middle;
            transform: translate(-50%, -50%);
          }
        }
        .renderMarker {
          border-radius: 4px;
          padding: 1px;
          span {
            display: inline-block;
            padding: 2px 4px;
            min-width: 32px;
            border-radius: 4px;
            text-align: center;
          }
        }
        .startend-marker {
          height: 14px;
          width: 14px;
          border-radius: 50%;
          text-align: center;
          .text {
            position: absolute;
            top: 50%;
            left: 50%;
            font-weight: 800;
            font-size: 14px;
            vertical-align: middle;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
    .amap-info {
      .amap-info-sharp {
        display: none !important;
      }
      .amap-info-content {
        padding: 0 !important;
        min-width: 300px !important;
        .divider {
          width: 100%;
          margin-top: 25px;
        }
        .info-item-content {
          margin: 1% 5%;
          padding: 1%;
          .imageStyle {
            width: 8%;
            margin-right: 5%;
          }
        }
      }
    }
  }
}
</style>