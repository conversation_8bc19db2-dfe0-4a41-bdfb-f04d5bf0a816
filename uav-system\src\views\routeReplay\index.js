import {
  csvTo<PERSON><PERSON>
} from "@/utils/csvJson.js";
import {
  wgs84_to_gcj02
} from "@/utils/wgs84_to_gcj02.js";
import {
  pointsConvert
} from "@/utils/coordinateConvert";
/**
 * 获取csv数据
 * @param {*} url
 */
export function getCsvData(url) {
  return new Promise((resolve, reject) => {
    csvToJson(url)
      .then((data) => {
        console.log("原始数据------------>", data);
        let list = [];
        let hData = []; // 高度 
        let vsData = []; // 垂直速度
        let hsData = []; // 水平速度
        let xAxisData = []; // x轴文字
        let dataTime = [];
        let logLatList = [];
        let deWeight = [];
        let distance = "0.00"
        let rocker = {};

        for (let i = 0; i < data.length; i++) {
          let item = data[i];

          //   let position = wgs84_to_gcj02(
          //     Number(item.longitudeInt) / 1e7,
          //     Number(item.latitudeInt) / 1e7
          //   );
          let position = pointsConvert({
            point: [Number(item.longitudeInt) / 1e7,
              Number(item.latitudeInt) / 1e7
            ],
            type: 10
          });

          list.push({
            lng: position[0].toFixed(7),
            lat: position[1].toFixed(7),
            height: (Number(item.height) / 100).toFixed(2),
            yaw: Number(item.yaw),
            pitch: Number(item.pitch),
            roll: Number(item.roll),
            distance: (Number(item.distance) / 100).toFixed(2),
            vs: Number(item.verticalSpeed) / 100,
            hs: item.horizontalSpeed ? Number(item.horizontalSpeed) / 100 : 0,
            time: 0,
            mode: item.mode,
            rocker: {
              ch1: item.ch1,
              ch2: item.ch2,
              ch3: item.ch3,
              ch4: item.ch4,
              ch5: item.ch5,
              ch6: item.ch6,
              ch7: item.ch7,
              ch8: item.ch8,
              ch9: item.ch9,
              ch10: item.ch10,
              ch11: item.ch11,
              ch12: item.ch12,
              ch13: item.ch13,
              ch14: item.ch14,
              ch15: item.ch15,
              ch16: item.ch16
            }
          });
          if (Number(item.distance) / 100 !== 0) {
            distance = (Number(item.distance) / 100).toFixed(2)
          }

          let index = i + 1;
          xAxisData.push(index);
          hData.push(list[i].height);
          vsData.push(list[i].vs);
          hsData.push(list[i].hs);
          logLatList.push(position);

          if (i > 0) {
            let time = item.tms - data[0].tms;
            list[i].time = time;

            if (logLatList[i][0] == logLatList[i - 1][0] && logLatList[i][1] == logLatList[i - 1][1]) {
              let lens = deWeight.length - 1;
              deWeight[lens].duration += time;
            } else {
              deWeight.push({
                duration: time,
                position: position
              })
            }
          } else {
            deWeight.push({
              duration: 0,
              position: position
            })
          }

          if (data[index]) {
            let start = list[i].time;
            let end = data[index].tms - data[0].tms;
            let when = end - start; // 时间差
            dataTime.push({
              position: position,
              duration: when,
            });
          } else {
            dataTime.push({
              position: position,
              duration: 0,
            });
          }
        }

        // console.log("logLatList---->", JSON.stringify(logLatList));
        // console.log("logLatList---->", logLatList);
        // console.log("deWeight------------>", deWeight);
        // console.log("dataTime------------>", dataTime);

        resolve({
          list,
          hData,
          vsData,
          hsData,
          xAxisData,
          dataTime,
          logLatList,
          deWeight,
          distance
        })
      })
      .catch((err) => {
        reject(err);
      })
  })
}

/**
 * 设置飞行信息折线图
 * @param {*} hData 高度数据
 * @param {*} vsData 垂直速度
 * @param {*} hsData 水平速度
 * @param {*} xAxisData x轴点列表
 * @returns 
 */
export function setFlightlineChart(hData, vsData, hsData, xAxisData, textList) {
  let option = {
    color: ["#F38E00", "#4CDC4C", "#2947B2"], //设置线条颜色
    tooltip: {
      trigger: 'axis',
      axisPointer: {}
    },
    toolbox: {
      show: false,
      feature: {
        saveAsImage: {}
      }
    },
    grid: {
      left: '20px',
      right: '20px',
      bottom: '20px',
      top: '20px',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      boundaryGap: false,
      data: xAxisData
    }],
    yAxis: [{
      type: 'value'
    }],
    series: [{
        name: textList[0] || '高度',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          normal: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                  offset: 1,
                  color: "rgba(138,84,10,0.34)" // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: "rgba(255,175,0, 1)" // 100% 处的颜色
                }
              ],
              globalCoord: false // 缺省为 false
            }
          }
        },
        emphasis: {
          focus: 'series'
        },
        symbol: "none",
        data: hData
      },
      {
        name: textList[1] || '垂直速度',
        type: 'line',
        stack: 'Total',
        symbol: "none",
        areaStyle: {
          normal: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                  offset: 1,
                  color: "rgba(83,249,0,0.3)" // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: "rgba(90,226,32, 1)" // 100% 处的颜色
                }
              ],
              globalCoord: false // 缺省为 false
            }
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: vsData
      },
      {
        name: textList[2] || '水平速度',
        type: 'line',
        stack: 'Total',
        symbol: "none",
        areaStyle: {
          normal: {
            color: {
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                  offset: 1,
                  color: "rgba(0,220,255,0.3)" // 0% 处的颜色
                },
                {
                  offset: 0,
                  color: "rgba(0,0,241, 1)" // 100% 处的颜色
                }
              ],
              globalCoord: false // 缺省为 false
            }
          }
        },
        emphasis: {
          focus: 'series'
        },
        data: hsData
      }
    ]
  };
  return option;
}
