<template>
  <div
    id="app"
    class="theme-default"
    :class="$loadingEnUI ? 'theme-default-en' : ''"
  >
    <router-view />
  </div>
</template>

<script>
export default {
  name: "App",
  created() {
    console.log(process.env.NODE_ENV);
    if (process.env.NODE_ENV === "production") {
      // 禁用右键菜单
      document.addEventListener("contextmenu", (event) =>
        event.preventDefault()
      );

      // 禁用F12、Ctrl+Shift+I等快捷键
      document.onkeydown = function (e) {
        if (
          e.key === "F12" ||
          (e.ctrlKey && e.shiftKey && e.key === "I") ||
          (e.ctrlKey && e.shiftKey && e.key === "J") ||
          (e.ctrlKey && e.key === "U")
        ) {
          return false;
        }
      };
    }
  },
  // computed: {
  //   worker() {
  //     return this.$store.state.websocket.worker;
  //   },
  // },
  // mounted() {
  //   window.addEventListener("beforeunload", this.handleBeforeUnload);
  // },
  // methods: {
  //   handleBeforeUnload() {
  //     if (this.worker && this.worker.port) {
  //       this.worker.port.postMessage({ type: "close" });
  //     }
  //   },
  // },
  // beforeDestroy() {
  //   window.removeEventListener("beforeunload", this.handleBeforeUnload);
  //   // 组件销毁时也可以通知
  //   if (this.worker && this.worker.port) {
  //     this.worker.port.postMessage({ type: "close" });
  //   }
  // },
};
</script>

<style lang="less">
@font-face {
  font-family: siyuanheitiCNRegular;
  src: url("../static/fontStyle/SourceHanSans-Regular.otf");
}
@import url("./assets/css/theme/index.less");
@import url("./assets/css/english/index.less");
#app {
  min-width: 1200px;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  font-family: siyuanheitiCNRegular;
  overflow: hidden;
}
html,
body,
#app {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

.v-modal {
  opacity: 0.8;
  background-color: black;
  /* background: url('./assets/img/mask.png') no-repeat; */
  /* background-position: center;
  background-size: 100% 100%; */
  /* background-color: transparent; */
}
input::-webkit-input-placeholder {
  color: #505050 !important;
}
input::-moz-input-placeholder {
  color: #505050 !important;
}
input::-ms-input-placeholder {
  color: #505050 !important;
}
input[aria-hidden="true"] {
  display: none !important;
}
.el-image-viewer__canvas {
  .el-image-viewer__img {
    max-height: 80% !important;
    max-width: 80% !important;
  }
}
</style>
