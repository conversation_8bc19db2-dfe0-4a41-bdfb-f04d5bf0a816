import {
    gcj02_to_wgs84
} from './wgs84_to_gcj02'
// import { orthoPhotoComputer } from './orthoPhotoComputer'
import {
    orthoPhotoComputer
} from "@/utils/cesium/orthoPhotoComputer";
import store from "@/store"
// export function getRouteParam(routeItem, fenceItem, amap, downPoints) {
export function getRouteParam(routeItem, fenceItem, amap, leafletMap) {
    let photo_type_json = routeItem.photo_type_json ?
        JSON.parse(routeItem.photo_type_json) : {};
    let data = {
        mission_id: routeItem.m_id,
        mission_name: routeItem.title,
        mission_type: routeItem.type,
        speed: routeItem.auto_speed,
        max_height: fenceItem.height_limit,
        min_height: 500,
        rtl_height: routeItem.return_height,
        // mission_type: routeItem.type,
        mission_end_action: routeItem.action_completed,
        altitude_type: 0,
        height_type: routeItem.height_type == 1 ? true : false,
        capture_mode: photo_type_json.capture_mode || 3
    };


    let points = []
    if (routeItem.type !== 50) {
        for (let index = 0; index < routeItem.point_list.length; index++) {
            let point = {}
            if (routeItem.point_list[index].type == 20) {
                let path = gcj02_to_wgs84((routeItem.point_list[index].lon_int) / 1e7, (routeItem.point_list[index].lat_int) / 1e7)
                point = {
                    // latitude: parseInt(path[1] * 1e7 - 100),
                    // longitude: parseInt(path[0] * 1e7 - 600),
                    latitude: parseInt(path[1] * 1e7),
                    longitude: parseInt(path[0] * 1e7),

                }
            } else {
                point = {
                    // latitude: parseInt(path[1] * 1e7 - 100),
                    // longitude: parseInt(path[0] * 1e7 - 600),
                    latitude: routeItem.point_list[index].lat_int,
                    longitude: routeItem.point_list[index].lon_int,
                }
            }
            // for (let index = 0; index < downPoints.length; index++) {
            //     point = {
            //         // latitude: parseInt(path[1] * 1e7 - 100),
            //         // longitude: parseInt(path[0] * 1e7 - 600),
            //         latitude: parseInt(path[1] * 1e7),
            //         longitude: parseInt(path[0] * 1e7),

            //     }

            // }
            point.altitude = routeItem.point_list[index].height
            point.id = routeItem.point_list[index].id
            if (routeItem.type == 20) {
                if (routeItem.point_list[index].action_json) {
                    point.action_list = JSON.parse(routeItem.point_list[index].action_json);
                    let num = []
                    for (let j = 0; j < point.action_list.length; j++) {
                        if (point.action_list[j].action_id == "takephoto") {
                            num.push(j)
                        }
                        if (point.action_list[j].param_list) {
                            let params_list = JSON.parse(point.action_list[j].param_list)
                            if (point.action_list[j].action_id == "speed") {
                                params_list[0].value = parseInt(params_list[0].value) * 100
                            }
                            if (point.action_list[j].action_id == "cam_trig_dist") {
                                params_list[0].value = parseInt(params_list[0].value) * 100
                            }
                            point.action_list[j].param_list = params_list;
                        }
                    }
                    if (num.length > 0) {
                        let action = {
                            action_id: "hover",
                            param_list: [{
                                param_id: "hovertime",
                                value: 2
                            }]
                        }
                        let action1 = {
                            action_id: "hover",
                            param_list: [{
                                param_id: "hovertime",
                                value: 1
                            }]
                        }
                        for (let m = 0; m < num.length; m++) {
                            if (m == 0) {
                                if (num[m] == 0) {
                                    point.action_list.splice(1, 0, action1)
                                    point.action_list.unshift(action)
                                } else if (num[m] == point.action_list.length - 1) {
                                    point.action_list.splice(point.action_list.length - 1, 0, action)
                                    point.action_list.push(action1)
                                } else {
                                    point.action_list.splice(num[m], 0, action)
                                    point.action_list.splice(num[m] + 2, 0, action1)
                                }
                            } else {
                                if (num[m] == 0) {
                                    point.action_list.splice(3, 0, action1)
                                    point.action_list.unshift(action)
                                } else if (num[m] == point.action_list.length - 1) {
                                    point.action_list.splice(point.action_list.length - 1, 0, action)
                                    point.action_list.push(action1)
                                } else {
                                    point.action_list.splice(num[m] + 2, 0, action)
                                    point.action_list.splice(num[m] + 4, 0, action1)
                                }
                            }
                        }
                    }
                    point.action_list.push({
                        action_id: "hover",
                        param_list: [{
                            param_id: "hovertime",
                            value: 1
                        }]
                    })
                } else {
                    point.action_list = [{
                        action_id: "hover",
                        param_list: [{
                            param_id: "hovertime",
                            value: 1
                        }]
                    }]

                }

            }

            points.push(point);
        }
    } else {
        let returnData = orthoPhotoComputer(routeItem, amap, leafletMap)
        let paths = returnData.points
        let triggerDist = returnData.triggerDist
        let type = routeItem.point_list[0].type
        for (let index = 0; index < paths.length; index++) {
            let path = ''
            if (type == 20) {
                path = gcj02_to_wgs84(paths[index].lng, paths[index].lat)
            } else {
                path = [paths[index].lng, paths[index].lat]
            }
            let point = {
                // latitude: parseInt(path[1] * 1e7 - 100),
                // longitude: parseInt(path[0] * 1e7 - 600),
                latitude: parseInt(path[1] * 1e7),
                longitude: parseInt(path[0] * 1e7),
                altitude: routeItem.default_height + (store.state.equipment.zoomMaxHeight * 100),
                id: index + 1,
                // action_list: [{
                //   action_id: 'takephoto',
                //   param_list: ""
                // }]
                action_list: [{
                    action_id: "gimbal_ctrl",
                    param_list: [{
                        param_id: "gimbal_yaw",
                        value: 0
                    }, {
                        param_id: "gimbal_pitch",
                        value: -90
                    }]
                }, {
                    action_id: "hover",
                    param_list: [{
                        param_id: "hovertime",
                        value: 2
                    }]
                }, {
                    action_id: "cam_trig_dist",
                    param_list: [{
                        param_id: "dist",
                        value: parseInt(triggerDist * 100),
                    }]
                }]
            };
            // if (index == 0) {
            // point.action_list.push({
            //         action_id: "cam_trig_dist",
            //         param_list: [{
            //             param_id: "dist",
            //             value: parseInt(triggerDist * 100),
            //         }]
            //     })
            // }
            // point.action_list.push({
            //     action_id: "hover",
            //     param_list: [{
            //         param_id: "hovertime",
            //         value: 1
            //     }]
            // })
            points.push(point);
        }
    }
    data.waypoints = points;
    console.log(data)
        // data.waypoints=downPoints.waypoints
    return data
}