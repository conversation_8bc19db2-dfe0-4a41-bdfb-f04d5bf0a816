*, html, body {
    margin:0;
    padding: 0;
}

.sliding-block-style(vertical, 0 4px, 100%, 4px, calc(100% - 32px), -100%, 12px, 32px, 80%, 2px);

.sliding-block-style(block);


.sliding-block-style(
    @className, // 类名称
    @mainPadding: 4px 0, // 内容内边距
    @slidingBlockHeight: 4px, // 进度条高度
    @slidingBlockWidth: 100%, // 进度条高度
    @mobileTop: -100%, // 滑块上边距
    @mobileLeft: 0, // 滑块左边距
    @blockCellWidth: 32px, // 滑块宽度
    @blockCellHeight: 12px, // 滑块高度
    @blockCellItemWidth: 2px, // 滑块里面分隔线宽度
    @blockCellItemHeight: 80%
, // 滑块里面分隔线高度
) {
  // 横屏样式
  .sliding-@{className} {
    padding: @mainPadding;
    .sliding-block-main {
      position: relative;
      height: @slidingBlockHeight;
      width: @slidingBlockWidth;
      background-color: #fff;
      border-radius: 8px;
      .block-mobile {
        position: absolute;
        left: 0;
        top: -100%;
      }

      .block-cell {
        height: @blockCellHeight;
        width: @blockCellWidth;
        display: flex;
        align-items: center;
        justify-content: center;

        background-color: #fff;
        border-radius: 8px;
        cursor: move;
        .cell-item {
          width: @blockCellItemWidth;
          height: @blockCellItemHeight;
          background-color: rgb(123, 123, 124);
          margin-right: 5px;
        }
      }
    }
  }
}