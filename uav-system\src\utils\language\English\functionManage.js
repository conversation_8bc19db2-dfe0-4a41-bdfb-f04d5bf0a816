/**
 * 功能管理
 */
const functionManage = {
    addTitle: "Add Functions",
    editTitle: "Edit Functions",


    tableOperate: {
        edit: "Edit",
        delete: "Delete"
    },


    table: {
        name: "Name",
        name_en: "English Name",
        fun_id: "ID",
        description: "Describe",
        state: "State",
        notes: "Notes",
        operation: "Operation"
    },
    delete: {
        content: "This operation will permanently delete the data. Do you want to continue?",
        title: "Hint",
        confirmText: "Confirm",
        cancelText: "Cancel",
        success: "Delete success!"
    },
    addFun: {
        placeholder: {
            name: "Please enter the name",
            name_en: "Please enter the English name",
            funID: "Please enter the ID",
            description: "Please enter the description",
            notes: "Please enter the notes"
        },
        label: {
            name: "Name",
            name_en: "English Name",
            funId: "ID",
            description: "Describe",
            state: "State",
            notes: "Notes"
        },
        verify: {
            name: ["Please enter the name"],
            name_en: ["Please enter the English name"],
            funID: ["Please enter the ID"],
            description: ["Please enter the description"]
        }
    }
}

export default functionManage;