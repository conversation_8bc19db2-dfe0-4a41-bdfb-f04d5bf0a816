
<!-- 操作说明模块 -->
<template>
  <transition name="el-fade-in-linear">
    <div class="instructions" v-show="show">
      <div class="header"></div>
      <div class="content">
        <div class="content-left" v-if="false">
          <!-- 左上 -->
          <div class="" style="height: 100%">
            <components
              class="left-top mb20"
              :is="leftTop.is"
              v-if="leftTop.isShow"
              style="height: 100%"
            />
          </div>

          <!-- 左下 -->
          <components
            :is="leftBot.is"
            :style="leftBot.style"
            v-if="leftBot.isShow"
            :type="type"
            :sortList="sortList"
          />
        </div>

        <div
          class="content-center"
        >
          <div class="center-top">
            <img :src="$loadingEnUI ? keyboardImg1 : keyboardImg" alt="" />
            <div class="shut-icon" @click="shut">
              <i class="el-icon-error"></i>
            </div>
          </div>

          <div class="" v-if="false" style="width: 600px; height: 87px">
            <components is="centerData"></components>
          </div>
        </div>

        <div class="content-right" v-if="false">
          <!-- 右上 -->
          <div style="height: 100%" class="right-top mb20">
            <components :is="rightTop.is" v-if="rightTop.isShow" />
          </div>

          <!-- 右下 -->
          <div class="right-bot" :style="rightBot.style">
            <components :is="rightBot.is" v-if="rightBot.isShow" />
          </div>
        </div>
      </div>

      <div class="shut-icon" @click="shut" v-if="false">
        <i class="el-icon-error"></i>
      </div>
    </div>
  </transition>
</template>

<script>
import warning from "./warning.vue";
import centerData from "./centerData.vue";
import videoMap from "./videoMap.vue";
import instruct from "./instruct.vue";
import achievement from "./achievement.vue";
export default {
  props: {
    show: Boolean,
    config: {
      type: Object,
      default: () => {
        return {};
      },
    },
    type: [Number, String],

    sortList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  components: {
    warning,
    centerData,
    videoMap,
    instruct,
    achievement,
  },
  data() {
    return {
      keyboardImg: require("@/assets/img/remote_sensing10.png"),
      keyboardImg1: require("@/assets/img/remote_sensing11.png"),
    };
  },
  computed: {
    leftTop() {
      return this.config.leftTop;
    },
    leftBot() {
      return this.config.leftBot;
    },
    rightTop() {
      return this.config.rightTop;
    },
    rightBot() {
      return this.config.rightBot;
    },
  },
  methods: {
    shut: function () {
      this.$emit("update:show", false);
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .instructions {
    .header {
      height: @radio * 50px !important;
    }
    .content {
      @hH: @radio * 90px;
      height: calc(100vh - @hH);
      padding: @radio * 20px !important;
      .content-left,
      .content-right {
        width: @radio * 282px !important;
        min-width: @radio * 282px !important;
      }

      .content-center {
        width: calc(100% - @radio * 700px) !important;
        position: fixed;
        left: @radio * 350px !important;
        // bottom:@radio * 20px !important;
        border-radius: @radio * 8px !important;
        .center-top {
          // padding-top: @radio * 59px !important;
          // padding-bottom: @radio * 24px !important;
          // top: @radio * -100px !important;
        }
      }
    }

    .shut-icon {
      right: @radio * 10px !important;
      top: @radio * 8px !important;
      font-size: @radio * 40px !important;
    }
  }
}

.instructions {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  // background-color: rgba(0, 0, 0, 0.1);
  .header {
    height: 50px;
    width: 100%;
  }
  .content {
    // width: 100%;
    height: calc(100vh - 90px);
    padding: 20px;
    display: flex;
    .content-left,
    .content-right {
      width: 282px;
      min-width: 282px;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .content-left {
      .left-bot {
        display: flex;
        flex-direction: column;
      }
    }

    .content-center {
      width: calc(100% - 700px);
      position: fixed;
      // top: 0px;
      left: 350px;
      bottom: 0px;
      right: 0;
      background-color: rgba(0, 0, 0, 0.9);
      text-align: center;
      border-radius: 8px;

      // display: flex;
      // align-items: flex-end;
      // justify-content: center;
      .center-top {
        // padding-top:  59px;
        // padding-bottom:  24px;
        position: relative;
        img {
          width: 100%;
        }
      }
    }
  }

  .shut-icon {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 8px;
    // color: #cccccc;
    font-size: 40px;
    // z-index: 25;
  }
}
</style>