const deviceType = {
  column: [{
      label: "种类",
      prop: "cla_type",
    },
    {
      label: "种类名",
      prop: "cla_name"
    },
    {
      label: "类型值",
      prop: "value"
    },
    {
      label: "共几路视频流",
      prop: "stream"
    },
    {
      label: "状态",
      prop: "state"
    },
    {
      label: "备注",
      prop: "notes"
    },
    {
      label: "创建时间",
      prop: "create_time"
    }, {
      label: "更新时间",
      prop: "modified_time"
    }, {
      label: "操作",
      prop: "operation"
    }
  ],
  addTitle: '添加设备类型',
  editTitle: '编辑设备类型',
  formLable: {
    cla_type: '种类',
    cla_name: "种类名",
    value: "类型值",
    stream: "视频流路数",
    notes: "备注",
    state: '状态'

  },
  placeholder: {
    cla_type: '请选择设备类型种类',
    cla_name: '请输入设备类型种类名',
    value: '请输入设备类型值',
    stream: '请选择设备类型视频流路数',
    stream_1: '视频流路数在0-3之间',
    notes: '请输入备注',
  },
  loading: '正在提交，请稍候。。。',
  successTipAdd: '设备类型添加成功',
  successTipEdit: '设备类型编辑成功',
  successTipDel: '设备类型删除成功',
  deleteContent: '是否确定删除该设备类型？',
  delTip: '删除提示',
  confirm: '确定',
  cancel: '取消',
  disabledContent: '是否确定禁用该设备类型？',
  disabledTip: '禁用提示',
  deleted: '已删除',
  addBtn: '添加类型',
  editBtn: "编辑"


}
export default deviceType
