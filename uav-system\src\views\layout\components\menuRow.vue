<!-- 菜单项 -->
<template>
  <div
    class="header-nav-cell"
    :class="$loadingEnUI ? 'header-nav-cell-en' : ''"
    @click="openRouter(item, $event)"
    @contextmenu.prevent="rightClick(item, $event)"
  >
    <!-- 文字 -->
    <span class="nav-cell-title">{{ menuInfo[item.name] }}</span>

    <div class="nav-cell-img">
      <!-- 没有选中 -->
      <img
        :src="mode == 'left' ? leftUnselected : rightUnselected"
        alt=""
        v-show="openNav !== item.name"
        class="cell-nav-no-select-img"
      />

      <!-- 选中 -->
      <img
        :src="mode == 'left' ? leftSelect : rightSelect"
        alt=""
        v-show="openNav == item.name"
        class="cell-nav-select-img"
      />
    </div>
  </div>
</template>

<script>
import leftUnselected from "@/assets/img/layout/left-unselected.png";
import leftSelect from "@/assets/img/layout/left-select.png";
import rightUnselected from "@/assets/img/layout/right-unselected.png";
import rightSelect from "@/assets/img/layout/right-select.png";

export default {
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    mode: {
      type: String,
      default: "left",
    },
    // openNav: String
  },
  data() {
    return {
      leftUnselected,
      leftSelect,
      rightUnselected,
      rightSelect,

      // openNav: "",
    };
  },
  computed: {
    menuInfo() {
      return this.$languagePackage.layout.menu;
    },
    openNav() {
      return this.$store.state.menus.openRouter;
    },
  },
  methods: {
    openRouter: function (item) {
      this.$emit("clickMenu", item);
    },
    rightClick: function (item, e) {
      if (item.children && item.children.length) {
        return false;
      } else {
        this.$emit("rightClick", item, e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1200px) {
  @radio: 100vw / 1920px;
  .header-nav-cell {
    width: @radio * 170px !important;
    height: @radio * 40px !important;
    font-size: @radio * 16px !important;
    margin-top: @radio * 10px !important;
    &.header-nav-cell-en {
      width: @radio * 160px !important;
    }
  }
}

.header-nav-cell {
  width: 170px;
  height: 40px;
  font-size: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
  color: rgb(96, 216, 252);
  margin-top: 10px;
  &.header-nav-cell-en {
    width: 160px;
  }
  &:hover .cell-nav-select-img {
    display: block !important;
  }
  &:hover .cell-nav-no-select-img {
    display: none !important;
  }
  .nav-cell-title {
    position: relative;
    z-index: 1;
  }
  .nav-cell-img {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>