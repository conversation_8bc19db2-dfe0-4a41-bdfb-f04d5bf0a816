<template>
  <div class="fixedType">
    <div class="content-item-1">
      <div class="content-item-1-state">
        <div class="state-all-content">
          <div class="content-item-1-state-row">
            <div class="state-col-content">
              <div class="img-div">
                <el-image
                  :src="weatherImg"
                  style="width: 50%"
                  fit="contain"
                ></el-image>
              </div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{ equipLanguage.equipStatus.weather }}
              </div>
              <div
                :class="
                  weatherState.weather && weatherState.weather.length <= 1
                    ? 'state-data-content-1'
                    : 'state-data-content-1-1'
                "
              >
                {{ weatherState.weather }}
              </div>
            </div>
            <div class="state-col-content">
              <div class="img-div">
                <el-image
                  :src="temperatureImg"
                  style="width: 24%"
                  fit="contain"
                ></el-image>
              </div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{ equipLanguage.equipStatus.humidity }}
              </div>
              <div class="state-data-content-1">
                {{
                  deviceItemList.humidity
                    ? parseInt(deviceItemList.humidity) + "%"
                    : weatherState.humidity
                }}
              </div>
            </div>
            <div class="state-col-content">
              <div class="img-div">
                <el-image
                  :src="windImg"
                  style="width: 45%"
                  fit="contain"
                ></el-image>
              </div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{ equipLanguage.equipStatus.wind }}
              </div>
              <div
                :class="
                  deviceItemList.wind_level
                    ? parseInt(deviceItemList.wind_level) < 7
                      ? 'state-data-content-2-1'
                      : 'state-data-content-2'
                    : parseInt(weatherState.windpower.replace(/[^\d]/g, ' ')) <
                      7
                    ? 'state-data-content-2-1'
                    : 'state-data-content-2'
                "
              >
                {{
                  deviceItemList.wind_level
                    ? parseInt(deviceItemList.wind_level)
                    : weatherState.windpower
                }}
              </div>
            </div>
          </div>
          <div class="content-item-1-state-row">
            <div class="state-col-content">
              <div>{{ equipLanguage.equipStatus.temperature }}</div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{
                  deviceItemList.temp_outside
                    ? parseInt(deviceItemList.temp_outside) + "℃"
                    : weatherState.temperature
                }}
              </div>
            </div>
            <div class="state-col-content">
              <div>{{ equipLanguage.equipStatus.rain }}</div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{
                  deviceItemList.rain_fall == 1
                    ? deviceItemList.rain_fall.toFixed(1) + "mm"
                    : NaN
                }}
              </div>
            </div>
            <div class="state-col-content">
              <div>{{ equipLanguage.equipStatus.windDirection }}</div>
            </div>
            <div class="state-col-content">
              <div class="state-data-title">
                {{
                  deviceItemList.wind_speed
                    ? deviceItemList.wind_speed.toFixed(1) + "m/s"
                    : NaN
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-item-1-video" ref="contentItemVideo">
        <div
          :style="video.inCabin.style"
          class="inCabinStyle"
          v-if="equipState && equipItem.inCabin"
        >
          <live-video
            v-if="equipItem.isExists"
            :title="equipLanguage.inCabin"
            :videoId="'inVideo'"
            :url="deviceItem.stream_in_list ? deviceItem.stream_in_list[0] : ''"
            :autoPlay="true"
            @clickVideo="changeVideo('inCabin')"
          ></live-video>
          <div class="tipinCabin" v-if="!equipItem.isExists">
            {{ equipLanguage.novideoTip }}
          </div>
          <div class="inCabinTitle" v-if="!equipItem.isExists">
            {{ equipLanguage.inCabin }}
          </div>
        </div>
        <div v-if="!equipState && equipItem.inCabin" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>
        <div
          class="batteryBox"
          v-if="!equipItem.inCabin"
          :style="video.inCabin.style"
        >
          <div class="battery-content">
            <div class="battery-header">{{ language.title }}</div>
            <div class="battery-main">
              <div
                class="battery-item"
                v-for="item in batteryInfo"
                :key="item.id"
              >
                <div class="item-label">{{ item.label }}</div>
                <div class="item-value">{{ format(item) }}{{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="content-item-1-video" ref="contentItemVideo1">
        <div :style="video.uav.style">
          <live-video
            v-if="equipState"
            :title="equipLanguage.uav"
            @clickVideo="changeVideo('uav')"
            :videoId="'uavVideo'"
            :url="
              deviceItem.stream_uav_list ? deviceItem.stream_uav_list[0] : ''
            "
            :autoPlay="true"
          ></live-video>
          <div v-if="!equipState" class="videoOutTip">
            <div class="titleTip">
              {{ equipLanguage.equipInfo.equipStateOut1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-item-2" ref="contentVideo">
      <div :style="video.outCabin.style">
        <live-video
          v-if="equipState"
          :title="equipLanguage.outCabin"
          :videoId="'outVideo'"
          :url="deviceItem.stream_out_list ? deviceItem.stream_out_list[0] : ''"
          :autoPlay="true"
          :isFill="true"
          @clickVideo="changeVideo('outCabin')"
        ></live-video>
        <div v-if="!equipState" class="videoOutTip">
          <div class="titleTip">
            {{ equipLanguage.equipInfo.equipStateOut1 }}
          </div>
        </div>

        <!-- :url="deviceItem.stream_out_list ? deviceItem.stream_out_list[0] : ''" -->
      </div>
      <div class="link-group">
        <el-radio-group v-model="form_cor" @change="formCorChange">
          <el-radio :label="0">{{ link.auto }}</el-radio>
          <el-radio :label="10">{{ link.fpv }}</el-radio>
          <el-radio :label="20">{{ link.uav }}</el-radio>
        </el-radio-group>
        <div class="rtk-status">
          {{ equipLanguage.rtkStateTitle
          }}<span
            :style="{
              color: msgDta.rtk_status
                ? rtkStatus[msgDta.rtk_status].color
                : rtkStatus[0].color,
            }"
            >{{
              msgDta.rtk_status
                ? rtkStatus[msgDta.rtk_status].text
                : rtkStatus[0].text
            }}</span
          >
        </div>
      </div>
    </div>
    <div class="content-item-3">
      <div class="content-item-3-title">
        {{ equipLanguage.airportStatus.title }}
      </div>
      <div
        class="content-item-3-btn"
        ref="contentBtn"
        :class="
          equipLanguage.language == 'en-US' ? 'content-item-3-btn-en' : ''
        "
      >
        <el-button
          :class="onKey.open == 3 || openCode ? 'active' : ''"
          :loading="onKey.open == 3 || openCode ? true : false"
          :disabled="errorCode"
          @click="UpCloseEvent(1)"
          >{{
            onKey.open == 3 || openCode
              ? equipLanguage.airportBtn.starting
              : equipLanguage.airportBtn.onKeyState
          }}</el-button
        >
        <el-button
          :class="onKey.close == 3 || closeCode ? 'active' : ''"
          :loading="onKey.close == 3 || closeCode ? true : false"
          class="lastBtn"
          ref="allclose"
          :disabled="errorCode"
          @click="UpCloseEvent(0)"
          >{{
            onKey.close == 3 || closeCode
              ? equipLanguage.airportBtn.closing
              : equipLanguage.airportBtn.onKeyClose
          }}</el-button
        >
      </div>
      <div class="content-item-3-content">
        <div
          class="content-index"
          v-for="(item, index) in operationLists"
          :key="index"
          :class="[
            equipItem.type == 12 ? 'content-index-1' : '',
            item.value
              ? 'active'
              : item.value1 == 2 || item.value1 == 3
              ? 'active'
              : '',
          ]"
        >
          {{ item.title }}
          <el-switch
            v-if="item.value1 != 2 && item.value1 != 3 ? true : false"
            class="content-switch"
            v-model="item.value"
            active-color="#0A2550"
            inactive-color="#2C2C31"
            :disabled="
              onKey.open == 3 ||
              onKey.close == 3 ||
              openCode ||
              closeCode ||
              errorCode
                ? true
                : false
            "
            @change="changeState(item)"
          >
          </el-switch>

          <el-button
            type="text"
            :loading="loadingCode"
            v-if="item.value1 == 2 || item.value1 == 3 ? true : false"
            >{{ item.value1 | getTipTitle(item) }}</el-button
          >
        </div>
        <div
          class="content-index"
          :class="[
            equipItem.type == 12 ? 'content-index-1' : '',
            uavState ? 'active' : '',
          ]"
        >
          {{ equipLanguage.airportStatus.drone }}
          <el-switch
            class="content-switch"
            v-model="uavState1"
            active-color="#0A2550"
            inactive-color="#2C2C31"
            :disabled="!equipItem.uavOpen"
            @change="changeUavState"
          >
          </el-switch>
        </div>
      </div>
      <div class="content-charge" v-if="equipItem.fitOn">
        <el-button
          :class="nest_get_battery == 3 ? 'active' : ''"
          :loading="nest_get_battery == 3 ? true : false"
          :disabled="
            onKey.open == 3 ||
            onKey.close == 3 ||
            openCode ||
            closeCode ||
            errorCode
              ? true
              : false
          "
          @click="nest_get_battery !== 3 ? getBattery() : ''"
          >{{
            nest_get_battery == 3
              ? equipLanguage.airportBtn.fitOning
              : equipLanguage.airportBtn.fitOn
          }}</el-button
        >
        <el-button
          :class="nest_put_battery == 3 ? 'active' : ''"
          :loading="nest_put_battery == 3 ? true : false"
          :disabled="
            onKey.open == 3 ||
            onKey.close == 3 ||
            openCode ||
            closeCode ||
            errorCode
              ? true
              : false
          "
          @click="nest_put_battery !== 3 ? putBattery() : ''"
          >{{
            nest_put_battery == 3
              ? equipLanguage.airportBtn.unfitOning
              : equipLanguage.airportBtn.unfitOn
          }}</el-button
        >
        <!-- <el-button>{{ equipLanguage.airportBtn.removeError }}</el-button> -->
      </div>
      <div class="content-cell" v-if="equipItem.cellList">
        <div
          class="content-cell-item"
          v-for="item in equipLanguage.cellList"
          :key="item.seq"
          ref="cellItem"
        >
          <div class="cell-title">
            {{ item.label }}
          </div>
          <div class="cell-value">
            <el-progress
              type="circle"
              :percentage="nest_battery_state[item.seq]"
              :stroke-width="progressWidth / 10"
              :width="progressWidth"
              define-back-color="rgb(255, 255, 255)"
              :color="colorFormat"
            ></el-progress>
          </div>
        </div>
      </div>
      <div class="content-item-3-btn">
        <el-button
          class="errorBtn"
          v-if="errorCode"
          @click="errorCode = false"
          >{{ equipLanguage.airportBtn.removeError }}</el-button
        >
        <el-button
          v-if="equipItem.stopBtn"
          @click="clickEvent(3)"
          :class="clickCode == 3 ? 'active' : ''"
          >{{ equipLanguage.airportBtn.stop }}</el-button
        >
        <el-button
          v-if="equipItem.stopBtn"
          @click="clickEvent(1)"
          :class="clickCode == 1 ? 'active' : ''"
          >{{ equipLanguage.airportBtn.continue }}</el-button
        >
        <el-button
          v-if="equipItem.crashStop"
          class="dangerBtn"
          @click="clickEvent(4)"
          :class="clickCode == 4 ? 'active' : ''"
          >{{ equipLanguage.airportBtn.crashStop }}</el-button
        >
        <el-button
          @click="clickEvent(2)"
          :class="clickCode == 2 ? 'active' : ''"
          >{{ equipLanguage.airportBtn.reset }}</el-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import liveVideo from "@/components/video/webrtcVideoHttps.vue";
import { errorMsg } from "@/utils/errorMsg";
export default {
  name: "fixedType",
  props: {
    video: {
      type: Object,
      default() {
        return {};
      },
    },
    onKey: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    operationList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    websocket1: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    weatherState: {
      type: [Object, String],
      default() {
        return {};
      },
    },
    watchError: {
      type: Number,
    },
    uavState: {
      type: Boolean,
    },
    changeStateCode: {
      type: Object,
      default() {
        return {};
      },
    },
    equipState: {
      type: Boolean,
      default: false,
    },
    equipLanguage: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
    showOnKeyError: {
      type: Boolean,
      default: false,
    },
    deviceItemList: {
      type: [Object, String],
      default: () => {
        return {};
      },
    },
    equipItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    liveVideo,
  },
  data() {
    return {
      time: 0,
      loadingCode: true,
      openCode: false,
      closeCode: false,
      errorCode: false,
      errorCodeClass: false,
      clickCode: 0,
      indexCode: "",
      progressWidth: 50,
      operationLists: [],
      // uavState1:false,
      nest_get_battery: 1,
      nest_put_battery: 0,
      nest_battery_state: {},
      form_cor: "",
      from_cno: "",
      deviceItem: "",
      msgDta: {},
      batteryInfo: [
        { id: "voltage", label: "电压", unit: "V", accuracy: 2 },
        { id: "current", label: "电流", unit: "A", accuracy: 2 },
        { id: "battery_remaining", label: "电量", unit: "%", accuracy: 0 },
        { id: "battery_temperature", label: "温度", unit: "℃", accuracy: 0 },
      ],
      temperatureImg: require("../../../assets/img/equipment/temperature.png"),
      weatherImg: require("../../../assets/img/equipment/weather.png"),
      windImg: require("../../../assets/img/equipment/wind.png"),
    };
  },
  created() {
    let battery = this.language.batteryInfo;
    for (let index = 0; index < this.batteryInfo.length; index++) {
      let item = this.batteryInfo[index];
      this.batteryInfo[index].label = battery[item.id];
    }
    this.getRtkStatus();
  },
  mounted() {
    // console.log(this.operationList)
    // console.log(navigator.getUserMedia)
    // console.log(this.deviceItem);
    this.operationLists = [];
    this.operationList.forEach((item) => {
      if (item.id === "charger") {
        this.equipItem.charger && this.operationLists.push(item);
      } else if (item.id === "Lift") {
        this.equipItem.lift && this.operationLists.push(item);
      } else {
        this.operationLists.push(item);
      }
    });
    if (this.equipItem.type == 12) {
      this.progressWidth = this.$refs.cellItem[0].offsetWidth - 20;
    }
  },
  methods: {
    //切换视频位置
    changeVideo(item) {
      if (this.video[item].code !== 2) {
        let keys;
        for (let key in this.video) {
          if (this.video[key].code == 2) {
            keys = key;
          }
        }
        this.video[keys].code = this.video[item].code;
        this.video[item].code = 2;
        let style = this.video[item].style;
        this.video[item].style = this.video[keys].style;
        this.video[keys].style = style;
      }
    },
    //点击改变状态
    changeState(item) {
      let stateCode = "";
      let index = this.operationList.findIndex((x) => {
        return x.id == item.id;
      });
      let changeStateCode = {
        num: index,
        state: true,
      };
      this.$emit("update:changeStateCode", changeStateCode);
      // this.changeStateCode=JSON.parse(JSON.stringify(this.operationList))
      // this.indexCode=this.operationList.findIndex(x=>{
      //   return x.id==item.id
      // })
      if (item.id == "Hatch") {
        if (item.value) {
          stateCode = 100;
        } else {
          stateCode = 101;
        }
      } else if (item.id == "Lift") {
        if (item.value) {
          stateCode = 102;
        } else {
          stateCode = 103;
        }
      } else if (item.id == "center") {
        if (item.value) {
          stateCode = 105;
        } else {
          stateCode = 104;
        }
      } else if (item.id == "charger") {
        if (item.value) {
          stateCode = 117;
        } else {
          stateCode = 118;
        }
      }
      let data = {
        nest_action_cmd: stateCode,
      };
      // console.log(data);
      this.websocket1.manualSend(data, 403);
    },
    //一键启动一键关闭事件
    UpCloseEvent(index) {
      let time = new Date().getTime();
      if (time - this.time > 1000) {
        if (index == 0) {
          this.closeCode = true;
          if (this.onKey.open != 3) {
            let a = 0;
            for (let index = 0; index < this.operationList.length; index++) {
              if (this.operationList[index].value1 == 2) {
                this.$message.warning({
                  message:
                    this.operationList[index].title +
                    this.equipLanguage.airportBtn.closing,
                  customClass: "message-info",
                });
                a = 1;
                this.closeCode = false;
              }
              if (this.operationList[index].value1 == 3) {
                this.$message.warning({
                  message:
                    this.operationList[index].title +
                    this.equipLanguage.airportBtn.starting1,
                  customClass: "message-info",
                });
                a = 1;
                this.closeCode = false;
              }
            }
            if (a == 0) {
              this.$emit("update:showOnKeyError", true);
              let data = {
                nest_action_cmd: 116,
              };
              // console.log(data);
              this.websocket1.manualSend(data, 403);
            }
          } else {
            this.closeCode = false;
            this.$message.warning({
              message: this.equipLanguage.noClose,
              customClass: "message-info",
            });
          }
          // if (this.onKey.close != 1) {

          // } else {
          //   this.closeCode = false;
          //   this.$message.warning({
          //     message: this.equipLanguage.inClosing,
          //     customClass: "message-info",
          //   });
          // }
          setTimeout(() => {
            this.closeCode = false;
          }, 500);
        } else if (index == 1) {
          this.openCode = true;
          if (this.onKey.close != 3) {
            let a = 0;
            for (let index = 0; index < this.operationList.length; index++) {
              if (this.operationList[index].value1 == 2) {
                this.$message.warning({
                  message:
                    this.operationList[index].title +
                    this.equipLanguage.airportBtn.closing,
                  customClass: "message-info",
                });
                a = 1;
                this.openCode = false;
              }
              if (this.operationList[index].value1 == 3) {
                this.$message.warning({
                  message:
                    this.operationList[index].title +
                    this.equipLanguage.airportBtn.starting1,
                  customClass: "message-info",
                });
                a = 1;
                this.openCode = false;
              }
            }
            if (a == 0) {
              this.$emit("update:showOnKeyError", true);
              let data = {
                nest_action_cmd: 115,
              };
              // console.log(data);
              this.websocket1.manualSend(data, 403);
            }
          } else {
            this.openCode = false;
            this.$message.warning({
              message: this.equipLanguage.noOpen,
              customClass: "message-info",
            });
          }
          setTimeout(() => {
            this.openCode = false;
          }, 300);
          // if (this.onKey.open != 1) {
          // } else {
          //   this.openCode = false;
          //   this.$message.warning({
          //     messgae: this.equipLanguage.inOpening,
          //     customClass: "message-info",
          //   });
          // }
        }
      }
      this.time = time;
      // if (this.onKey.open!=1 && this.onKey.close!=1) {
      //   if (index == 1) {
      //     stateCode = 30;
      //   } else if (index == 0) {
      //     stateCode = 31;
      //   }
      //   let data = {
      //     nest_action_cmd: stateCode,
      //   };
      //   console.log(data);
      //   this.websocket1.manualSend(data, 403);
      // } else {
      //   // if (this.onKey.open==1 && index == 0) {
      //   //   this.$message.warning("设备正在启动，暂时无法关闭！");
      //   // }
      //   // if (this.onKey.close==1 && index == 1) {
      //   //   this.$message.warning("设备正在关闭，暂时无法启动！");
      //   // }
      // }
    },
    //暂停，继续，复位，急停
    clickEvent(index) {
      if (!this.clickCode) {
        let num = 114;
        switch (index) {
          case 1:
            num = 119;
            break;
          case 2:
            num = 108;
            break;
          case 3:
            num = 114;
            break;

          case 4:
            num = 109;
            break;

          default:
            break;
        }
        let data = {
          nest_action_cmd: num,
        };
        this.websocket1.manualSend(data, 403);
        this.clickCode = index;
        setTimeout(() => {
          this.clickCode = 0;
        }, 1000);
      }
    },
    //装载电池
    getBattery() {
      if (this.nest_get_battery == 1) {
        this.$message.warning(this.equipLanguage.onend);
        return false;
      }
      this.nest_get_battery = 3;
      let data = {
        nest_action_cmd: 106,
      };
      this.websocket1.manualSend(data, 403);
    },
    //卸下电池
    putBattery() {
      if (this.nest_put_battery == 1) {
        this.$message.warning(this.equipLanguage.unonend);
        return false;
      }
      this.nest_put_battery = 3;
      let data = {
        nest_action_cmd: 107,
      };
      this.websocket1.manualSend(data, 403);
    },
    colorFormat(value) {
      if (value < 25) {
        return "#ff4949";
      } else if (value < 50) {
        return "#e6a23c";
      } else if (value < 75) {
        return "#1989fa";
      } else if (value < 100) {
        return "#83c561";
      } else if (value == 100) {
        return "#67c23a";
      }
    },
    //改变飞机状态
    changeUavState() {
      if (this.uavState1) {
        this.$emit("update:uavState", false);
        this.websocket1.manualSend(
          {
            nest_action_cmd: 111,
          },
          403
        );
      }
    },
    formCorChange() {
      this.websocket1.manualSend({}, 206);
    },
    getMessage(msg_id, data) {
      if (msg_id == 200) {
        this.deviceItem = data;
      }
      if (msg_id === 207) {
        console.log(msg_id, data);
        this.form_cor = data.cor;
        this.from_cno = data.cno;
        let list = this.deviceItem.stream_uav_list[0].split("/");
        if (list[list.length - 1] !== data.cno) {
          list[list.length - 1] = data.cno;
        }
        let url = list.join("/") + "&type=play";
        this.$set(this.deviceItem.stream_uav_list, 0, url);
      } else if (msg_id === 200) {
        this.form_cor = data.cor;
        this.from_cno = data.cno;
      } else if (msg_id === 206) {
        let cnoList = data.list;
        let index = cnoList.findIndex((item) => {
          return item.cor == this.form_cor;
        });
        let params = {};
        if (this.form_cor !== 0) {
          params = {
            cor: this.form_cor,
            cno: cnoList[index].cno,
          };
        } else {
          params = {
            cor: this.form_cor,
          };
        }
        this.websocket1.manualSend(params, 207);
      }
      if (msg_id == 432) {
        this.msgDta = data;
      }
    },
    format(item) {
      return this.msgDta[item.id]
        ? this.msgDta[item.id].toFixed(item.accuracy)
        : this.msgDta[item.id];
    },
    getRtkStatus() {
      let info = {};
      let obj = this.$languagePackage.dict.rtkStatus;
      for (const key in obj) {
        if (key == 0 || key == 1) {
          info[key] = {
            text: obj[key],
            color: "red",
          };
        } else if (key == 2) {
          info[3] = {
            text: obj[key],
            color: "red",
          };
        } else if (key == 3) {
          info[4] = {
            text: obj[key],
            color: "yellow",
          };
        } else if (key == 4) {
          info[6] = {
            text: obj[key],
            color: "rgb(19, 224, 19)",
          };
        }
      }
      console.log(info);
      this.rtkStatus = info;
    },
  },
  computed: {
    uavState1() {
      if (this.uavState) {
        return this.uavState;
      } else {
        return false;
      }
    },
    link() {
      return this.$languagePackage.navigation.link;
    },
    language() {
      return this.$languagePackage.navigation.batteryBox;
    },
  },
  watch: {
    watchError(value) {
      if (value > 12 && value < 21 && !this.errorCode) {
        this.errorCode = true;
        if (
          this.changeStateCode.num === "" &&
          !this.changeStateCode.state &&
          !this.showOnKeyError
        ) {
          let msg = errorMsg(value, this.equipLanguage.language);
          this.$message.error({ message: msg, customClass: "message-info" });
        }
      }
    },
    deviceItemList: {
      handler(value) {
        if (this.equipItem.type == 12) {
          this.nest_get_battery = value.nest_get_battery;
          this.nest_put_battery = value.nest_put_battery;
          this.nest_battery_state = {};
          for (
            let index = 0;
            index < value.nest_battery_state.length;
            index++
          ) {
            if (value.nest_battery_state[index].state) {
              this.nest_battery_state[value.nest_battery_state[index].seq] =
                value.nest_battery_state[index].percent;
            } else {
              this.nest_battery_state[value.nest_battery_state[index].seq] = 0;
            }
          }
        }
      },
      deep: true,
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .fixedType {
    .content-item-1 {
      .content-item-1-state {
        border-radius: @zoomIndex * 6px !important;
        .state-all-content {
          padding: @zoomIndex * 10px 0 !important;
          .content-item-1-state-row {
            .state-col-content {
              font-size: @zoomIndex * 14px !important;
              .state-data-title {
                font-size: @zoomIndex * 14px !important;
                line-height: @zoomIndex * 15px !important;
              }
              .state-data-content-1 {
                font-size: @zoomIndex * 30px !important;
                line-height: @zoomIndex * 31px !important;
              }
              .state-data-content-1-1 {
                font-size: @zoomIndex * 24px !important;
                line-height: @zoomIndex * 31px !important;
              }
              .state-data-content-2-1 {
                font-size: @zoomIndex * 28px !important;
                line-height: @zoomIndex * 31px !important;
              }
              .state-data-content-2 {
                font-size: @zoomIndex * 28px !important;
                line-height: @zoomIndex * 31px !important;
              }
            }
          }
        }
      }
      .content-item-1-video {
        .videoOutTip {
          .titleTip {
            font-size: @zoomIndex * 18px !important;
          }
        }
        .inCabinStyle {
          .tipinCabin {
            font-size: @zoomIndex * 16px !important;
          }
          .inCabinTitle {
            padding: @zoomIndex * 3px @zoomIndex * 10px !important;
          }
        }
        .batteryBox {
          border-radius: @zoomIndex * 6px !important;
          .battery-content {
            border-radius: @zoomIndex * 6px !important;
            .battery-header {
              padding: @zoomIndex * 10px !important;
              font-size: @zoomIndex * 18px !important;
            }
            .battery-main {
              padding: @zoomIndex * 10px !important;
              font-size: @zoomIndex * 14px !important;
              .battery-item {
                padding: @zoomIndex * 5px @zoomIndex * 10px !important;
              }
            }
          }
        }
      }
    }
    .content-item-2 {
      .videoOutTip {
        .titleTip {
          font-size: @zoomIndex * 18px !important;
        }
      }
    }
    .content-item-3 {
      border-radius: @zoomIndex * 6px !important;
      .content-item-3-title {
        font-size: @zoomIndex * 14px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
      .content-item-3-btn {
        .el-button {
          min-width: @zoomIndex * 120px !important;
          font-size: @zoomIndex * 14px !important;
          letter-spacing: @zoomIndex * 2px !important;
          padding: @zoomIndex * 12px !important;
        }
      }
      .content-item-3-content {
        .content-index {
          font-size: @zoomIndex * 18px !important;
          letter-spacing: @zoomIndex * 2px !important;
          .el-button {
            padding: @zoomIndex * 6px 0 !important;
            &.el-button--text {
              font-size: @zoomIndex * 16px !important;
            }
          }
        }
      }
      .content-charge {
        .el-button {
          min-width: @zoomIndex * 120px !important;
          font-size: @zoomIndex * 14px !important;
          letter-spacing: @zoomIndex * 2px !important;
          padding: @zoomIndex * 12px !important;
        }
      }
      .content-cell {
        margin: 0 @zoomIndex * 10px !important;
        .content-cell-item {
          font-size: @zoomIndex * 14px !important;
          margin: @zoomIndex * 5px !important;
          .cell-title {
            border-radius: @zoomIndex * 2px !important;
          }
          .cell-value {
            .el-progress {
              margin-top: @zoomIndex * 5px !important;
            }
          }
        }
      }
    }
  }
}
.fixedType {
  width: 100%;
  height: 100%;
  display: flex;
  .content-item-1 {
    width: 20%;
    margin: 2% 0 2% 1%;
    .content-item-1-state {
      width: 100%;
      height: 17%;
      margin-bottom: 1%;
      // display: flex;
      border-radius: 6px;
      overflow-y: auto;
      .state-all-content {
        width: 100%;
        height: auto;
        padding: 10px 0;
        .content-item-1-state-row {
          width: 96%;
          height: auto;
          padding: 0 2%;
          display: flex;
          align-items: center;
          .state-col-content {
            font-size: 14px;
            width: 16.6%;
            text-align: center;
            .state-data-title {
              font-size: 14px;
              line-height: 15px;
            }
            .state-data-content-1 {
              font-size: 30px;
              line-height: 31px;
            }
            .state-data-content-1-1 {
              font-size: 24px;
              line-height: 31px;
            }
            .state-data-content-2-1 {
              font-size: 28px;
              line-height: 31px;
            }
            .state-data-content-2 {
              font-size: 28px;
              line-height: 31px;
            }
          }
        }
      }
    }
    .content-item-1-video {
      width: 100%;
      height: 41%;
      .video-module {
        height: 100%;
        width: 100%;
      }
      .videoOutTip {
        width: 100%;
        height: 100%;
        color: white;
        position: relative;
        .titleTip {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 18px;
        }
      }
      .inCabinStyle {
        position: relative;
        .tipinCabin {
          width: 100%;
          text-align: center;
          vertical-align: middle;
          padding-top: 25%;
          font-size: 16px;
          position: absolute;
          top: 50%;
          left: 0;
          transform: translateY(-80%);
        }
        .inCabinTitle {
          position: absolute;
          top: 0;
          left: 0;
          border: none;
          padding: 3px 10px;
        }
      }
      .batteryBox {
        border-radius: 6px;
        overflow-y: auto;
        background-color: rgba(0, 0, 0, 1);
        .battery-content {
          width: 100%;
          height: 100%;
          border-radius: 6px;

          .battery-header {
            padding: 10px;
            font-size: 18px;
            color: aqua;
          }
          .battery-main {
            padding: 10px;
            font-size: 14px;
            color: #fff;
            .battery-item {
              padding: 5px 10px;
              display: flex;
              align-items: center;
              justify-content: space-between;
            }
          }
        }
      }
    }
  }
  .content-item-2 {
    margin: 2% 1%;
    width: 56%;
    .video-module {
      height: 100%;
      width: 100%;
    }
    .videoOutTip {
      width: 100%;
      height: 100%;
      position: relative;
      .titleTip {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 18px;
      }
    }
    .link-group {
      position: absolute;
      right: 22%;

      // padding: 0 10px;
      z-index: 20;
      .el-radio-group {
        padding: 5px 10px;
        background-color: rgba(0, 0, 0, 0.3);
        .el-radio {
          color: #fff;
        }
      }

      .rtk-status {
        margin: 3px 0 0 auto;
        padding: 2px 5px;
        color: #fff;
        width: fit-content;
        background-color: rgba(0, 0, 0, 0.3);
      }
    }
  }
  .content-item-3 {
    width: 20%;
    margin: 2% 1% 2% 0;
    border-radius: 6px;
    overflow: auto;
    .content-item-3-title {
      margin: 2% 8%;
      font-weight: 550;
      font-size: 14px;
      letter-spacing: 2px;
    }
    .content-item-3-btn {
      margin: 2% 8%;
      display: flex;
      flex-flow: row wrap;
      .el-button {
        white-space: normal;
        margin-left: 0;
        width: 45%;
        min-width: 120px;
        border: none;
        font-size: 14px;
        font-weight: 550;
        letter-spacing: 2px;
        margin-bottom: 1%;
        padding: 12px;
        &:nth-child(odd) {
          margin-right: 5%;
        }
        // &:nth-child(even){
        //   margin-left: 5%;
        // }
      }
    }
    // .content-item-3-btn-en{
    //   margin: 2% 5%;
    //   .el-button{
    //     width: 48%;
    //     white-space:normal;
    //   }

    // }
    .content-item-3-content {
      margin: 2% 8%;
      .content-index {
        margin: 10% 0;
        font-size: 18px;
        text-align: left;
        margin-left: 7%;
        font-weight: 500;
        letter-spacing: 2px;
        &.content-index-1 {
          margin: 5% 0;
        }
        .content-switch,
        .el-button {
          float: right;
        }
        .el-switch {
          height: 50%;
        }
        .el-button {
          border: none;
          padding: 6px 0;
          &.el-button--text {
            font-size: 16px;
          }
        }
      }
    }
    .content-charge {
      margin: 2% 8%;
      .el-button {
        white-space: normal;
        margin-left: 0;
        width: 45%;
        min-width: 120px;
        border: none;
        font-size: 14px;
        font-weight: 550;
        letter-spacing: 2px;
        margin-bottom: 1%;
        padding: 12px;
        &:nth-child(odd) {
          margin-right: 4%;
        }
      }
    }
    .content-cell {
      margin: 0 10px;
      display: flex;
      align-items: center;
      text-align: center;
      .content-cell-item {
        flex: 1;
        font-size: 14px;
        text-align: center;
        margin: 5px;
        .cell-title {
          border-radius: 2px;
        }
        .cell-value {
          .el-progress {
            margin-top: 5px;
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.fixedType {
  .content-item-3 {
    .content-item-3-content {
      .el-switch {
        .el-switch__core {
          height: 36px !important;
          width: 100px !important;
          border-radius: 20px !important;
          &::after {
            height: 32px !important;
            width: 32px !important;
          }
        }
      }
      .el-switch.is-checked .el-switch__core::after {
        margin-left: -30px !important;
      }
    }
    .content-cell {
      .content-cell-item {
        .cell-value {
          .el-progress {
            .el-progress__text {
              font-size: 14px !important;
            }
          }
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .fixedType {
    .content-item-3 {
      .content-item-3-content {
        .el-switch {
          .el-switch__core {
            height: @zoomIndex * 36px !important;
            width: @zoomIndex * 100px !important;
            border-radius: @zoomIndex * 20px !important;
            &::after {
              height: @zoomIndex * 32px !important;
              width: @zoomIndex * 32px !important;
            }
          }
        }
        .el-switch.is-checked .el-switch__core::after {
          margin-left: @zoomIndex * -30px !important;
        }
      }
      .content-cell {
        .content-cell-item {
          .cell-value {
            .el-progress {
              .el-progress__text {
                font-size: @zoomIndex * 14px !important;
              }
            }
          }
        }
      }
    }
  }
}
</style>