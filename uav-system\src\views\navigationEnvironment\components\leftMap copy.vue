<template>
  <div id="map" style="border-radius: 5px" class="custom-map zindex-style">
    <div class="fixed-center" @click.stop="isCenter = !isCenter">
      <img-icon
        :name="isCenter ? 'map-center-select' : 'mapCenter'"
        :size="20"
      />
    </div>
    <div
      class="zoom-height-content"
      v-if="basicData && basicData.type == 50 && isComputeHight"
    >
      {{
        getElevationcode
          ? `${routeLanguage.getHeightLoading}(${progress}%)`
          : `${routeLanguage.heightLabel}${zoomMaxHeight}m`
      }}
    </div>
  </div>
</template>

<script>
import maps from "@/utils/maps";
import request from "@/utils/api";
import imgIcon from "@/components/imgIcon/index.vue";
import { wgs84_to_gcj02, gcj02_to_wgs84 } from "@/utils/wgs84_to_gcj02.js";
import { orthoPhotoComputer } from "@/utils/orthoPhotoComputer.js";
import { computerElevation } from "@/utils/computerElevation";
export default {
  components: {
    imgIcon
  },
  props: {
    index: Number
  },
  data() {
    return {
      query: {}, // 路由参数
      map: null,
      polypon: null, //第一个围栏
      polyline: null, // 航线
      magnify: false, // 是否放大
      markerText: [], // 巡航点信息
      uavMarker: null, // 无人机位置
      fenceList: [], // 围栏列表
      taskLine: null,
      taskPath: [],
      basicData: null,

      isCenter: true,
      progress: 0,
      isComputeHight: false
    };
  },
  computed: {
    staveThreeData() {
      return this.$store.state.equipment.staveThreeData;
    },
    // 无人机
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS || {};
    },
    flightTask() {
      return this.$store.state.equipment.flightTask;
    },
    routeLanguage() {
      return this.$languagePackage.routes;
    },
    zoomMaxHeight() {
      return this.$store.state.equipment.zoomMaxHeight;
    },
    getElevationcode() {
      return this.$store.state.equipment.elevationcode;
    }
  },
  watch: {
    // 监听状态3回传
    staveThreeData: {
      handler: function(data) {
        let lon = data.longitude / 1e7,
          lat = data.latitude / 1e7;
        let position = wgs84_to_gcj02(lon, lat);

        this.updateUavLoca(position); // 更新飞机位置

        this.drawWarkFlight(position); // 绘制任务飞行航线V

        if (data.flight_status == 4) {
          this.$store.commit("setFlightTask", false);
        }
      },
      deep: true
    }
  },
  created() {
    this.query = this.$route.query || {};

    this.getElectronicFence();

    this.$store.commit("setCloseFlightTrack", this.closeFilghtPath);
    this.$store.commit("setWsMmessageFun", {
      key: "leftMap",
      message: this.disposeData
    });
  },
  mounted() {
    this.initMap();
  },
  methods: {
    disposeData: function(msg_id, data) {
      if (msg_id == 401 && data.cmd_type == 7) {
        // 航线下载成功
        let list = [];
        for (let i = 0; i < data.waypoints.length; i++) {
          let lon = data.waypoints[i].longitude / 1e7,
            lat = data.waypoints[i].latitude / 1e7;
          let position = wgs84_to_gcj02(lon, lat);
          list.push(position);
        }

        this.taskLine = new AMap.Polyline({
          map: this.map,
          path: list,
          isOutline: true,
          outlineColor: "#67C23A",
          borderWeight: 1,
          strokeColor: "#67C23A",
          strokeOpacity: 1,
          strokeWeight: 3,
          // 折线样式还支持 'dashed'
          strokeStyle: "solid",
          // strokeStyle是dashed时有效
          strokeDasharray: [10, 5],
          lineJoin: "round",
          lineCap: "round",
          zIndex: 50
        });
      }
      if (msg_id == 415) {
        console.log("断点415===>", data);
        this.drawBreakMarker(data);
        this.saveRoute(data);
        this.$store.commit("setFlightTask", false);
      }
    },
    //绘制断点
    drawBreakMarker(data) {
      let breakPoint = wgs84_to_gcj02(
        data.longitude / 1e7,
        data.latitude / 1e7
      );
      breakPoint = new AMap.LngLat(breakPoint[0], breakPoint[1]);
      if (this.breakMarker) {
        this.breakMarker.setPosition(breakPoint);
      } else {
        this.breakMarker = new AMap.Text({
          text: "b",
          position: breakPoint,
          anchor: "center", // 设置文本标记锚点
          zIndex: 52,
          offset: new AMap.Pixel(0, -5),
          style: {
            borderRadius: "50%",
            borderWidth: this.magnify ? "5px" : "2px",
            borderColor: "red",
            backgroundColor: "#FFFFFF",
            width: this.magnify ? "16px" : "8px",
            height: this.magnify ? "16px" : "8px",
            fontSize: this.magnify ? "16px" : "8px",
            color: "red",
            display: "flex",
            justifyContent: "center",
            alignItems: "center"
          }
        });
        this.map.add(this.breakMarker);
      }
    },

    // 更新/绘制飞机位置
    updateUavLoca: function(location) {
      if (!this.map) {
        return false;
      }
      let position = new AMap.LngLat(location[0], location[1]);
      // console.log("飞机位置-----------", position);
      this.isCenter && this.map.setCenter(position);

      const yaw = this.staveThreeData.attitude_yaw;
      if (this.uavMarker) {
        this.uavMarker.setPosition(position);
        this.uavMarker.setAngle(yaw);
        return false;
      }
      let imageUrl = require("@/assets/icon/uav-nav.png");
      this.uavMarker = new AMap.Marker({
        position: position,
        title: ``,
        icon: new AMap.Icon({
          image: imageUrl,
          imageSize: new AMap.Size(25, 25)
        }),
        offset: new AMap.Pixel(-12.5, -12.5),
        zIndex: 50,
        angle: yaw
      });
      this.map.add(this.uavMarker);
    },

    // 绘制任务飞行航线
    drawWarkFlight: function(position) {
      // console.log("绘制飞行航线任务状态--------->", this.flightTask);
      if (this.flightTask && this.map) {
        this.taskPath.push(position);

        if (this.taskLine) {
          this.taskLine.setPath(this.taskPath);
        } else {
          this.taskLine = new AMap.Polyline({
            path: this.taskPath,
            isOutline: true,
            outlineColor: "#67C23A",
            borderWeight: 1,
            strokeColor: "#67C23A",
            strokeOpacity: 1,
            strokeWeight: 3,
            strokeStyle: "solid",
            strokeDasharray: [10, 5],
            lineJoin: "round",
            lineCap: "round",
            zIndex: 50,
            showDir: true
          });
          this.map && this.map.add(this.taskLine);
        }
      }
    },

    // 获取电子围栏
    getElectronicFence: function() {
      let query = this.$route.query || {};
      if (query.state == 2) {
        return false;
      }
      if (!query.f_id) {
        return false;
      }
      request("fenceOne", {
        jointPmd: true,
        pmd: "f_id",
        f_id: this.$route.query.f_id
      }).then(res => {
        this.fenceList = res.data.point_list;
        this.drawFence(this.fenceList);
      });
    },

    // 地图初始化
    initMap: function() {
      this.$nextTick(() => {
        maps
          .initMap("map", {
            mapStyle: "amap://styles/92907bd07b27bf2b8ca66585015fdc7a",
            locationCode: false,
            showLayerType: true
          })
          .then(map => {
            this.map = map;
            this.dataDispose();
            this.drawFence(this.fenceList);
            this.map.add(this.markerText);
            this.drawMachine();
            this.$store.commit("setMaps", map);
            this.map.on("click", this.clickMap);
            let self = this;
            this.map.on("mouseover", function(e) {
              self.$emit("mouseEvent", false);
            });
            this.map.on("mouseout", function(e) {
              self.$emit("mouseEvent", true);
            });
          });
      });
    },

    // 绘制机巢
    drawMachine: function() {
      let query = this.$route.query;
      if (query.state == 2 || query.type == 200) {
        return false;
      }
      let imageUrl = require("@/assets/img/workingHome.png");
      let flightParam = sessionStorage.getItem("flightParam");
      let data = JSON.parse(flightParam);
      let icon = new AMap.Icon({
        image: imageUrl,
        imageSize: new AMap.Size(50, 50)
      });

      let position1 = data.point[query.sn_id];
      let position = wgs84_to_gcj02(position1[0], position1[1]);
      let marker = new AMap.Marker({
        position: position,
        title: `${query.sn_id}`,
        icon: icon,
        offset: new AMap.Pixel(-15, -30),
        zIndex: 50
      });
      this.map.add(marker);
    },

    clickMap: function() {
      if (this.index !== 3) {
        this.$emit("clickEvent", "map");
        this.partRefresh(true);
      }
    },

    // 地图切换修改巡航点样式
    partRefresh: function(stave) {
      this.magnify = stave;
      setTimeout(() => {
        this.magnifyMap();
        for (let i = 0; i < this.markerText.length; i++) {
          this.markerText[i].setStyle({
            borderWidth: this.magnify ? "5px" : "2px",
            width: this.magnify ? "24px" : "12px",
            height: this.magnify ? "24px" : "12px",
            fontSize: this.magnify ? "24px" : "12px"
          });
        }
        if (this.breakMarker) {
          this.breakMarker.setStyle({
            borderWidth: this.magnify ? "5px" : "2px",
            width: this.magnify ? "16px" : "8px",
            height: this.magnify ? "16px" : "8px",
            fontSize: this.magnify ? "16px" : "8px"
          });
        }
      }, 80);
    },

    // 切换地图时修改围栏左右边距
    magnifyMap: function() {
      let maps = document.querySelector("#map .amap-ui-control-container");
      let icon = document.querySelector("#map .fixed-center");

      if (!maps) {
        return false;
      }
      if (this.magnify) {
        maps.style.right = this.query.state == 2 ? "20px" : "300px";
        icon.style.right = "310px";
      } else {
        maps.style.right = "20px";
        icon.style.right = "20px";
      }
      maps.style.bottom = "20px";

      return false;

      let avoid = this.magnify ? [60, 110, 200, 200] : [10, 10, 10, 10];
      this.map &&
        this.map.setFitView &&
        this.map.setFitView([this.polyline], false, avoid, 30);
    },

    // 绘制围栏
    drawFence: function(data) {
      if (!this.map) {
        return console.log("地图未加载，不能绘制围栏");
      }
      let pointItem = data.map(row => {
        return [row.lon_int / 1e7, row.lat_int / 1e7];
      });
      this.polypon = maps.drawPolypon(pointItem);

      this.map.add(this.polypon);
    },

    // 正射影像绘制路线
    orthoimageDraw: function(data) {
      let orthimage = orthoPhotoComputer(this.basicData, this.map);
      let cal_alt_json = this.basicData.cal_alt_json
        ? JSON.parse(this.basicData.cal_alt_json)
        : "";
      this.isComputeHight = false;
      if (cal_alt_json && cal_alt_json.isComputeHight) {
        this.isComputeHight = true;
        this.computerMaxHeight(orthimage.points);
      }
      let params = {
        offset: -6,
        clickable: false,
        draggable: false,
        zIndex: 100
      };
      let startMarker = maps.drawMarker(
        "S",
        orthimage.points[0],
        "startend-marker",
        params
      );
      startMarker.setMap(this.map);
      let endMarker = maps.drawMarker(
        "E",
        orthimage.points[orthimage.points.length - 1],
        "startend-marker",
        params
      );
      endMarker.setMap(this.map);
      new AMap.Polyline({
        map: this.map,
        path: orthimage.points,
        showDir: true,
        strokeColor: "#28F", //线颜色
        strokeWeight: 6 //线宽
      });

      // console.log("正射影像------------->", orthimage);
    },

    //计算区域峰值
    computerMaxHeight(points) {
      this.progress = 0;
      this.$store.commit("setElevationcode", true);
      let wgs84Points = points.map(x => {
        let a = gcj02_to_wgs84(x.lng, x.lat);
        return {
          lng: a[0],
          lat: a[1]
        };
      });
      computerElevation(wgs84Points, 120, this.progressFun, this.map).then(
        res => {
          this.$store.commit("setElevationcode", false);
          this.$store.commit("setZoomMaxHeight", res);
        }
      );
    },
    progressFun(progress) {
      this.progress = progress;
    },
    // 绘制巡航路线
    drawPath: function(item) {
      if (!this.map) {
        console.log("地图未加载，不能绘制巡航路线");
        return false;
      }

      let index = 1;
      if (!item.point_list) {
        return false;
      }
      let pointItem = item.point_list.map(row => {
        // 巡航点位置
        let position = [row.lon_int / 1e7, row.lat_int / 1e7];

        this.drawText(index, position);
        index++;
        return position;
      });

      // 正射影像
      let type = this.basicData.type;
      if (type == 50) {
        this.polyLine = new AMap.Polygon({
          path: pointItem,
          bubble: true,
          fillColor: "#1357B1",
          fillOpacity: 0.2,
          strokeOpacity: 1,
          strokeColor: "#0092f8",
          strokeWeight: 6,
          strokeStyle: "soild",
          map: this.map
        });
        this.orthoimageDraw();
      } else if (type == 20) {
        this.polyline = maps.drawPolyline(pointItem);
        this.map.add(this.polyline);
      }
      // this.map.setFitView(this.polyline, false, avoid, 30);
      this.map.setCenter(pointItem[0]);

      this.magnifyMap(); // 切换视图
    },

    // 数据处理
    dataDispose: function() {
      let query = this.$route.query || {};
      if (query.state == 1) {
        let flightParam = sessionStorage.getItem("flightParam");
        try {
          let data = JSON.parse(flightParam);
          if (!this.query.m_id) {
            return false;
          }

          // 航线
          let airRoute = data.route.filter(item => {
            return item.m_id == this.query.m_id;
          })[0];
          this.basicData = airRoute;

          this.drawPath(airRoute);
          if (airRoute.break_json) {
            try {
              let data = JSON.parse(airRoute.break_json);
              data = data.data;
              console.log(data);
              this.drawBreakMarker(data);
            } catch (error) {
              console.log("解析失败");
            }
          }
        } catch (error) {
          console.error("数据解析失败------>", error);
        }
      } else if (query.type == 200) {
        let params = sessionStorage.getItem("wipeOut");
        try {
          let data = JSON.parse(params);
          this.drawMarker(data.alarmItem);
        } catch (error) {
          console.error("数据解析失败------>", error);
        }
      }
    },

    //存储断点
    saveRoute(breakData) {
      let flightParam = sessionStorage.getItem("flightParam");
      try {
        let data = JSON.parse(flightParam);
        // 航线
        let j = data.route.findIndex(item => item.m_id == this.query.m_id);
        if (j !== -1) {
          data.route[j].break_json = JSON.stringify({ data: breakData });
          sessionStorage.setItem("flightParam", JSON.stringify(data));
        }
      } catch (error) {
        console.error("数据解析失败------>", error);
      }
    },
    //绘制火情点
    drawMarker(alarmItem) {
      if (!this.map) {
        console.log("地图未加载，不能绘制巡航路线");
        return false;
      }
      let imgList = {
        1: {
          imgSrc: require("@/assets/img/alarm.png"),
          state: "发现火情"
        },
        10: {
          imgSrc: require("@/assets/img/alarm1.png"),
          state: "处理中"
        },
        20: {
          imgSrc: require("@/assets/img/alarm2.png"),
          state: "处理完成"
        }
      };
      let img = imgList[alarmItem.state].imgSrc;
      let icon = new AMap.Icon({
        // 图标的取图地址
        image: img,
        imageSize: new AMap.Size(35, 50)
      });
      let center =
        alarmItem.coo_type == 1
          ? wgs84_to_gcj02(alarmItem.lon_int / 1e7, alarmItem.lat_int / 1e7)
          : [alarmItem.lon_int / 1e7, alarmItem.lat_int / 1e7];
      new AMap.Marker({
        map: this.map,
        position: center,
        icon: icon,
        offset: new AMap.Pixel(-15, -30),
        title: imgList[alarmItem.state].state + "，经纬度：" + center
      });
      this.map.setCenter(center);
    },
    // 绘制巡航点
    drawText: function(index, position) {
      let text = new AMap.Text({
        text: index,
        position: position,
        anchor: "center", // 设置文本标记锚点
        zIndex: 50,
        style: {
          borderRadius: "50%",
          borderWidth: this.magnify ? "5px" : "2px",
          borderColor: "#0092f8",
          backgroundColor: "#FFFFFF",
          width: this.magnify ? "24px" : "12px",
          height: this.magnify ? "24px" : "12px",
          fontSize: this.magnify ? "24px" : "12px",
          color: "#000000",
          display: "flex",
          justifyContent: "center",
          alignItems: "center"
        }
      });
      this.markerText.push(text);
    },

    // 下载航线
    downloadAirRoute: function() {
      this.equipmentWS.manualSend({ cmd_type: 7 }, 401);
    },
    // 清空飞行轨迹
    closeFilghtPath: function() {
      if (this.taskLine) {
        this.taskPath = [];
        this.map.remove(this.taskLine);
        this.taskLine = null;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.custom-map {
  width: 100%;
  background-color: #000;
  position: relative;
  z-index: 22;
  .fixed-center {
    position: absolute;
    z-index: 20;
    right: 30px;
    bottom: 70px;
  }
  .zoom-height-content {
    position: absolute;
    bottom: 0;
    left: 1%;
    color: rgb(70, 216, 25);
    z-index: inherit;
  }
}
</style>
<style lang="less">
.custom-map {
  .amap-logo,
  .amap-copyright {
    display: none !important;
  }
  .startend-marker {
    height: 12px;
    width: 12px;
    border-radius: 50%;

    text-align: center;
    background-color: #ffffff;
    border: 1px solid #0728fc;
    color: #0015a1;
    font-size: 12px;
    .text {
      position: absolute;
      top: 50%;
      left: 50%;
      font-weight: 800;
      vertical-align: middle;
      transform: translate(-50%, -50%);
    }
  }
}
</style>
