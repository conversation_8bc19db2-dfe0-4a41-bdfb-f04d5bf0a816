<template>
  <div id="layout-header">
    <!-- 背景 -->
    <div class="layout-header-bg">
      <div class="header-bg-main">
        <img
          :src="headerTop"
          alt=""
          class="bg-top"
          :class="$loadingEnUI ? 'bg-top-en' : ''"
        />
        <img :src="headerFooter" alt="" class="bg-bot" />
      </div>

      <!-- 标题 -->
      <div class="layout-title" :class="$loadingEnUI ? 'layout-title-1' : ''">
        <span>{{ title }}</span>
      </div>

      <!-- 线条 -->
      <div class="segmentation">
        <div class="dynamic-light-waves left">
          <!-- <div class="waves-cell"></div> -->
        </div>

        <div class="dynamic-light-waves right">
          <!-- <div class="waves-cell"></div> -->
        </div>
      </div>

      <!-- logo -->
      <div class="header-logo">
        <img :src="logo" alt="" class="logo" />
        <img :src="logoBg" alt="" class="logo-bg" />
      </div>

      <!-- 用户 -->
      <div class="header-user">
        <div class="user-message mr20 mt10">
          <div class="el-icon-message-solid"></div>
          <div class="message-text mt12">
            {{ $language == "english" ? "News" : "新消息" }}
          </div>
        </div>
        <!-- 用户设置 -->
        <user-info></user-info>
      </div>
    </div>

    <!-- 导航 -->
    <div class="header-nav">
      <!-- 左侧导航 -->
      <div class="nav-left">
        <div class="" v-for="(item, index) in leftMenu" :key="index">
          <menu-item
            :item="item"
            mode="left"
            @openMenuOperate="openMenuOperate"
          ></menu-item>
        </div>
      </div>

      <!-- 右侧导航 -->
      <div class="nav-right">
        <div class="" v-for="(item, index) in rightMenu" :key="index">
          <menu-item
            :item="item"
            mode="right"
            @openMenuOperate="openMenuOperate"
          ></menu-item>
        </div>
      </div>
    </div>
    <div class="menu-list-operate" v-if="openShow" :style="style">
      <div
        class="menu-li"
        v-for="item in menuOperateList"
        :key="item.id"
        @click="item.fun()"
      >
        {{ item.label }}
      </div>
    </div>
    <div
      class="mask-layer"
      v-if="openShow"
      @click="closeShow"
      @contextmenu.prevent=""
    ></div>
  </div>
</template>

<script>
import userInfo from "./userInfo.vue";
import menuItem from "./menuItem.vue";

// 引入图片
import leftUnselected from "@/assets/img/layout/left-unselected.png";
import leftSelect from "@/assets/img/layout/left-select.png";
import rightUnselected from "@/assets/img/layout/right-unselected.png";
import rightSelect from "@/assets/img/layout/right-select.png";

import headerFooter from "@/assets/img/layout/header-footer.png";
import headerTop from "@/assets/img/layout/header-top.png";
import logoBg from "@/assets/img/layout/logo-bg.png";
import logo from "@/assets/img/logo.png";
import menuSelect from "@/assets/icon/menu-select.png";

export default {
  name: "layoutHeader",
  components: {
    // accountSettings,
    // passSet,
    userInfo,
    menuItem,
  },
  data() {
    return {
      headerFooter,
      headerTop,
      logo,
      logoBg,
      menuSelect,
      leftUnselected,
      leftSelect,
      rightUnselected,
      rightSelect,

      openNav: "",
      menuOperateList: [{ label: "", id: 1, fun: this.openNewPath }],
      openShow: false,
      style: {},
      openItem: "",
    };
  },
  computed: {
    funList() {
      return this.$store.state.user.userInfo.fun_list;
    },
    admin() {
      return this.funList && this.funList.indexOf("super_administrator") !== -1;
    },
    menusList() {
      let list = this.$store.state.menus.menuList.filter((item) => {
        return item.grade == 1;
      });
      if (this.admin) {
        return list;
      }
      let list_1 = JSON.parse(JSON.stringify(list));
      for (let index = 0; index < list_1.length; index++) {
        let item = list_1[index];

        if (item.children) {
          item.children = item.children.filter((children) => {
            return (
              !children.fun_id ||
              (this.funList && this.funList.indexOf(children.fun_id) !== -1)
            );
          });
        }
      }
      return list_1;
    },
    leftMenu() {
      return this.menusList.slice(0, 3);
    },
    rightMenu() {
      return this.menusList.slice(3, 7);
    },

    userInfo() {
      return this.$store.state.user.userInfo;
    },
    title() {
      return this.$languagePackage.layout.header.title;
    },
    titleStyle() {
      return {
        "letter-spacing": this.$language == "chinest" ? "10px" : 0,
      };
    },
    headerInfo() {
      return this.$languagePackage.layout.header;
    },
    operateList() {
      return this.$languagePackage.layout.menuOperateList;
    },
  },
  created() {
    this.openNav = this.$route.name;
    this.$store.commit("setOpenRouter", this.$route.name);
    for (let index = 0; index < this.operateList.length; index++) {
      let item = this.menuOperateList[index];
      this.menuOperateList[index] = Object.assign(
        item,
        this.operateList[index]
      );
    }
  },
  methods: {
    openMenuOperate(item, e) {
      this.openItem = item;
      this.openShow = true;
      this.style = {
        top: e.clientY + "px",
        left: e.clientX + "px",
      };
    },
    openNewPath() {
      this.openShow = false;
      if (this.openItem) {
        const newwin = this.$router.resolve({
          path: this.openItem.path,
        });
        console.log(newwin);
        window.open(newwin.href, "_blank");
      }
    },
    closeShow() {
      this.openShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
@keyframes waverLeft {
  0% {
    left: 100vw / 1920px * 663px;
  }
  100% {
    left: 100vw / 1920px * 300px;
  }
}
@keyframes waverRight {
  0% {
    left: 100vw / 1920px * 1206px;
  }
  100% {
    left: 100vw / 1920px * 1569px;
  }
}

@media screen and(min-width: 1200px) {
  @radit: 100vw / 1920px;
  #layout-header {
    height: @radit * 100px !important;

    // 背景
    .layout-header-bg {
      .layout-title {
        left: @radit * 775px !important;
        top: @radit * 10px !important;
        font-size: @radit * 34px !important;
        letter-spacing: @radit * 10px !important;
      }
      .layout-title-1 {
        left: @radit * 750px !important;
        font-size: @radit * 30px !important;
        letter-spacing: @radit * 1px !important;
      }

      .header-logo {
        width: @radit * 210px !important;

        .logo-bg {
          left: @radit * -55px !important;
          top: @radit * 15px !important;
        }
        .logo {
          margin-bottom: @radit * 10px !important;
          margin-left: @radit * 20px !important;
        }
      }

      .header-user {
        right: @radit * 20px !important;
        width: @radit * 190px !important;
      }

      .header-bg-main {
        .bg-bot {
          margin-bottom: @radit * -10px !important;
          margin-right: @radit * -12px !important;
        }
      }

      .segmentation {
        .dynamic-light-waves {
          width: @radit * 64px !important;
          height: @radit * 2px !important;
          top: @radit * 18px !important;
        }
      }
    }

    .header-nav {
      @width: @radit * 465px;
      width: calc(100% - @width) !important;
      left: @radit * 240px !important;
      top: @radit * 27px !important;
    }
  }
}

#layout-header {
  height: 100px;
  background-color: rgba(17, 27, 47, 1);
  position: relative;

  // 背景
  .layout-header-bg {
    width: 100vw;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;

    .layout-title {
      position: absolute;
      left: 775px;
      top: 10px;
      color: #fff;
      font-size: 34px;
      font-weight: 700;
      letter-spacing: 10px;
    }
    .layout-title-1 {
      left: 750px;
      font-size: 30px;
      letter-spacing: 1px;
    }

    .header-logo {
      width: 210px;
      height: 100%;
      display: flex;
      align-items: center;
      position: absolute;
      left: 0;
      top: 0;

      .logo-bg {
        position: absolute;
        left: -55px;
        top: 15px;
        width: 150%;
      }
      .logo {
        position: relative;
        z-index: 2;
        margin-bottom: 10px;
        margin-left: 20px;
        width: 85.5%;
      }
    }

    .header-user {
      position: absolute;
      right: 20px;
      top: 0;
      width: 190px;
      height: 100%;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .user-message {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .el-icon-message-solid {
          padding-top: 4px;
        }
        .message-text {
          color: rgb(96, 216, 252);
          font-size: 12px;
          width: 100%;
          margin-top: 6px;
        }
      }
    }

    .header-bg-main {
      position: relative;
      width: 100%;
      height: 100%;

      display: flex;
      justify-content: center;
      align-items: flex-end;

      .bg-top {
        position: absolute;
        top: 0;
        left: 0%;
        width: 100%;
        &.bg-top-en {
          left: -5%;
          width: 110%;
        }
      }
      .bg-bot {
        margin-bottom: -10px;
        margin-right: -12px;
        width: 81.96666666666666666666666666666666666666%;
      }
    }

    .segmentation {
      .dynamic-light-waves {
        width: 64px;
        height: 2px;
        // background-color: red;
        position: absolute;
        top: 18px;
        background-image: linear-gradient(
          to right,
          rgba(119, 161, 246, 0.1),
          rgba(59, 117, 235, 0.4),
          rgba(255, 255, 255, 1),
          rgba(59, 117, 235, 0.4),
          rgba(119, 161, 246, 0.1)
        );
        animation-duration: 8s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
      }
      .left {
        left: 663px;
        animation-name: waverLeft;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
      }
      .right {
        left: 1206px;
        animation-name: waverRight;
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
      }
    }
  }

  .header-nav {
    display: flex;
    justify-content: space-between;
    width: calc(100% - 465px);
    position: absolute;
    left: 240px;
    top: 27px;
    .nav-left,
    .nav-right {
      display: flex;
    }
  }
}
</style>

<style lang="less">
.header-user-popover {
  background-color: #13213d;
  border: none !important;
  padding: 8px 0 !important;
  min-width: 120px !important;
  .user-popover {
    .popover-item {
      height: 30px;
      line-height: 30px;
      cursor: pointer;
      padding: 0 16px;
      color: #90969b;
      font-weight: 700;
      &:hover {
        color: #60d8fc;
        background-color: #0d1930;
      }
    }
  }
  .popper__arrow {
    display: none;
  }
}
#layout-header {
  .menu-list-operate {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #fff;
    // border-radius: 6px;
    z-index: 9999;
    .menu-li {
      height: 30px;
      line-height: 30px;
      padding: 0 20px;
      font-size: 16px;
      cursor: pointer;
      &:hover {
        background-color: rgb(220, 227, 228);
      }
    }
  }
}
.mask-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: transparent;
  z-index: 9900;
}
@media screen and(min-width: 1200px) {
  @radit: 100vw / 1920px;
  #layout-header {
    .menu-list-operate {
      .menu-li {
        height: @radit * 30px !important;
        line-height: @radit * 30px !important;
        padding: 0 @radit * 20px !important;
        font-size: @radit * 16px !important;
      }
    }
  }
}
</style>