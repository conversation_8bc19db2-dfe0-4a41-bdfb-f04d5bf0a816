<template>
  <div class="camera-pattern pattern">
    <!-- 头部导航 -->
    <div class="pattern-header">
      <div
        class="header-item"
        v-for="(item, index) in headerNav"
        :key="index"
        @click="cutType(item)"
      >
        <div class="item-cell">
          <div
            class="cell-label"
            :class="headerIndex == item.value ? 'item-cell-style' : ''"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>

    <div class="pattern-main">
      <div class="item-content">
        <div class="content-title">ISO</div>
        <div class="content-main">
          <tab-scroll
            :list="isoList"
            v-model="isoIndex"
            @onChange="isoChange"
            :readonly="headerIndex === 0"
          />
        </div>
      </div>

      <div class="item-content">
        <div class="content-title">{{ language.shutter }}</div>
        <div class="content-main">
          <tab-scroll
            :list="shutterList"
            v-model="shutterIndex"
            @onChange="shutterChange"
            :readonly="headerIndex === 0"
          />
        </div>
      </div>

      <div class="item-content">
        <div class="content-title">{{ language.exp }}</div>
        <div
          class="content-main"
          style="border: none; padding: 0; width: 191px"
        >
          <exposure-compensation
            v-model="expVaule"
            @onChange="autoExposurnChange"
          />
        </div>
      </div>

      <div class="item-content">
        <div class="content-title">{{ language.white }}</div>
        <div class="content-main">
          <tab-scroll
            :list="whiteBalanceList"
            v-model="whiteBalanceIndex"
            @onChange="whiteBalanceChange"
          />
        </div>
      </div>

      <div class="item-content" v-if="staveTwoData.camera_type == 214">
        <div class="content-title">{{ language.pseudoColor }}</div>
        <div class="content-main">
          <tab-scroll
            :list="pseudoColorList"
            v-model="colorize_type"
            @onChange="pseudoColorChange"
          />
        </div>
      </div>

      <div class="item-content" v-if="staveTwoData.camera_type == 214">
        <div class="content-title">{{language.areaThermal}}</div>
        <div class="content-main" style="height: 37px">
          <tab-scroll
            :list="thermalImageryList"
            v-model="pip_mode"
            @onChange="thermalImageryChange"
          >
            <template v-slot:content="scope">
              <div
                class="thermal"
                :class="scope.row.value == pip_mode ? 'select-style' : ''"
              >
                <div class="thermal-imagery" :style="scope.row.style">
                  <div
                    class="thermal-imagery-item"
                    :style="scope.row.itemStyle"
                  ></div>
                </div>
              </div>
            </template>
          </tab-scroll>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import exposureCompensation from "./exposureCompensation.vue";
import progressBar from "./progressBar.vue";
import tabScroll from "@/components/tabScroll/index.vue";

export default {
  components: {
    exposureCompensation,
    progressBar,
    tabScroll,
  },
  data() {
    return {
      headerIndex: 0,
      headerNav: [
        { label: "AUTO", value: 0 },
        { label: "M", value: 1 },
      ],

      isoIndex: 1,

      shutterIndex: 1,

      whiteBalanceIndex: 0,
      expVaule: null,

      colorize_type: 0,

      cameraType: null,

      // 手动
      handMove: {
        ISOVal: 1,
        shutterVal: 1,
        exposureVal: -1,
      },
      pip_mode: 0,
      thermalImageryList: [
        { value: 0 },
        {
          value: 4,
          style: {
            "justify-content": "center",
            "align-items": "center",
          },
          itemStyle: {
            width: "8px",
            height: "8px",
          },
        },
        {
          value: 1,
          style: {
            "justify-content": "center",
            "align-items": "center",
          },
          itemStyle: {
            width: "100%",
            height: "100%",
          },
        },
        {
          value: 3,
          style: {
            "justify-content": "start",
            "align-items": "end",
          },
          itemStyle: {
            width: "25%",
            height: "40%",
            margin: "0 0 2px 2px",
          },
        },
        {
          value: 2,
          style: {
            "justify-content": "start",
            "align-items": "end",
          },
          itemStyle: {
            width: "50%",
            height: "100%",
          },
        },
      ],
    };
  },
  computed: {
    // 快门参数列表
    shutterList() {
      return this.$store.state.dict.shutterList;
    },
    isoList() {
      let data = [];
      for (let i = 1; i <= 48; i++) {
        data.push({
          label: i * 100,
          value: i * 100,
        });
      }
      return data;
    },
    exposureNegList() {
      return [
        0,4,8,12,16,20,24,28,32,36,40,44,48,52,56,60,64,68,72,76,80,84,88,92,96,100,104,108,112,128,132,136,140,144,148,152,156,160,164,168,172,176,180,184,188,192,196,200,204,208,212,216,220,224,228,232,236,240,244,248,255
      ];
      // let data = [];
      // let start = -320;
      // for (let i = 0; i < list.length; i++) {
      //   start += 20;
      //   data.push({
      //     value: start / 100,
      //     label: list[i],
      //   });
      // }

      // return data;
    },
    whiteBalanceList() {
      return this.$store.state.dict.whiteBalance;
    },
    pseudoColorList() {
      return this.$store.state.dict.careraPcolor;
    },
    // 无人机
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS || {};
    },
    moveShutter() {
      return this.shutterList[this.handMove.shutterVal].label;
    },
    moveISO() {
      return this.isoList[this.handMove.ISOVal].label;
    },
    // 状态二回传信息
    staveTwoData() {
      return this.$store.state.equipment.staveTwoData;
    },
    language() {
      return this.$languagePackage.navigation.cameraConfig.patterns;
    },
  },
  watch: {
    staveTwoData: {
      deep: true,
      handler: function (item) {
        // console.log("基础设置---------->", item);
        let camera_shutter = item.camera_shutter;
        let shutter = this.shutterList.filter((item) => {
          return item.index == camera_shutter;
        })[0];
        this.shutterIndex = shutter ? shutter.value : null;

        // console.log("相机快门值------->", camera_shutter);

        let camera_iso = item.camera_iso;
        this.isoIndex = camera_iso;

        // console.log("相机iso值------->", camera_iso);
        let camera_exp_value = item.camera_exp_value;
        for (let i = 0; i < this.exposureNegList.length; i++) {
          if (this.exposureNegList[i] == camera_exp_value) {
            this.expVaule = i;
            break;
          }
        }

        let camera_awb = item.camera_awb;
        this.whiteBalanceIndex = camera_awb;

        this.headerIndex = item.camera_exp_mode;

        this.cameraType = item.camera_type;

        this.colorize_type = item.colorize_type;
        this.pip_mode = item.pip_mode;
        // for (let i = 0; i < this.thermalImageryList.length; i++) {
        //   if (this.thermalImageryList[i].value == item.pip_mode) {
        //     this.pip_mode = i;
        //     break;
        //   }
        // }
      },
    },
  },
  created() {
    this.$store.commit("setWsMmessageFun", {
      key: "cameraPattern",
      message: (msg_id, data) => {
        if (msg_id == 402) {
          let type = data.cmd_type;
          let message = "";
          if (type == 8) {
            message = "模式切换成功";
          } else if (type == 6) {
            message = "iso值设置成功";
          } else if (type == 5) {
            message = "快门值设置成功";
          } else if (type == 7) {
            message = "白平衡设置成功";
          }
          message &&
            this.$message({
              type: "success",
              message: message,
            });
        }
      },
    });
  },
  methods: {
    cutType: function (item) {
      this.headerIndex = "";
      setTimeout(() => {
        this.headerIndex = item.value;
        let params = {
          exposure_mode: item.value,
          cmd_type: 8,
        };

        this.equipmentWS &&
          this.equipmentWS.manualSend &&
          this.equipmentWS.manualSend(params, 402);
      }, 0);
    },
    cutIso: function (item) {
      this.isoIndex = item.value;
    },
    cutShutter: function (item) {
      this.shutterIndex = item.value;
    },
    // 自动曝光值发生变化
    autoExposurnChange: function (val, index) {
      let params = {
        cmd_type: 9,
        exposure_value: this.exposureNegList[index],
      };
      this.manualSend(params, 402, true);
    },
    // 快门
    shutterChange: function (val) {
      let params = {
        cmd_type: 5,
        shutter: this.shutterList[val].index,
      };
      this.manualSend(params, 402);
    },
    // iso
    isoChange: function (val) {
      let index = val / 100 - 1;
      let params = {
        cmd_type: 6,
        iso: this.isoList[index].label,
      };
      this.manualSend(params, 402);
    },
    manualSend: function (params, msg_id, state) {
      // 自动不能改变
      if (this.headerIndex === 1 || state) {
        this.equipmentWS &&
          this.equipmentWS.manualSend &&
          this.equipmentWS.manualSend(params, msg_id);
      }
    },
    // 模式切换
    whiteBalanceChange: function (val) {
      let params = {
        cmd_type: 7,
        awb: val,
      };
      this.manualSend(params, 402, true);
    },
    //
    pseudoColorChange: function (val) {
      let params = {
        cmd_type: 13,
        colorize_type: val,
        pip_mode: this.pip_mode,
        alarm_onoff: 1,
        alarm_status: 1,
        alarm_value: 1,
      };
      this.manualSend(params, 402, true);
    },
    thermalImageryChange: function (val) {
      let params = {
        cmd_type: 13,
        colorize_type: this.colorize_type,
        pip_mode: val,
        alarm_onoff: 1,
        alarm_status: 1,
        alarm_value: 1,
      };

      this.manualSend(params, 402, true);
    },
  },
};
</script>

<style lang="less" scoped>
.pattern {
  padding: 0 50px;
  display: flex;
  flex-wrap: wrap;
  .pattern-header {
    margin: 21px 0 16px 0;
    display: flex;
    width: 100%;
    height: 24px;
    align-items: center;
    .header-item {
      width: 50%;
      font-size: 12px;
      // color: #fff;
      display: flex;
    }
    .cell-label {
      display: flex;
      cursor: pointer;
      align-items: center;
      // border: 2px solid #ffffff;
      padding: 0 18px;
      height: 22px;
      border-radius: 11px;
    }
    .item-cell-style {
      // border-color: #398bcc;
      // color: #398bcc;
      // padding: 0 18px;
      // height: 22px;
    }
  }
  .pattern-main {
    display: flex;
    flex-wrap: wrap;
    .item-content {
      margin-bottom: 29px;
      width: 50%;
      position: relative;
      .content-title {
        font-size: 12px;
        // color: #fff;
        margin-bottom: 11px;
      }
      .content-main {
        // border: 1px solid #c2c3c3;
        width: 191px;
        height: 22px;
        border-radius: 6px;
        // padding: 0 35px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .main-row {
          // color: #fff;
          font-size: 12px;
          cursor: pointer;
        }
        .select-row {
          position: relative;
          // color: #398bcc;
          &::after {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            // border-left: 5px solid transparent;
            // border-right: 5px solid transparent;
            // border-bottom: 5px solid #398bcc;
            bottom: -5px;
            left: 50%;
            margin-left: -5px;
          }
        }
      }
      .readonly-style {
        position: absolute;
        left: 0;
        top: 30px;
        height: 28px;
        width: 100%;
        z-index: 20;
      }
    }
  }

  .hand-move {
    .move-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 193px;
      .right {
        width: 60px;
        height: 20px;
        background-color: #252627;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #398bcc;
      }
    }
  }

  .select-style {
    background-color: #398bcc !important;
  }

  .thermal {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    padding: 0 2px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .thermal-imagery {
    border: 1px solid #fff;
    border-radius: 2px;
    height: 16px;
    width: 24px;
    display: flex;
    overflow: hidden;
    .thermal-imagery-item {
      background-color: #f804ab;
    }
  }
}
</style>