//飞行管理
.routes {
  .routeplan {
    background-color: rgba(3, 9, 33);

    #maps {
      background-color: rgba(3, 9, 33);

      .amap-markers {
        .amap-marker {
          .marker-edit {
            background-color: #ffffff;
            border: 3px solid #0092f8;
            color: #0092f8;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);

            &.active {
              background-color: #0c1865;
              color: #ffffff;
            }
          }

          .marker-edit-i {
            background-color: #ffffff;
            border: 2px solid #0728fc;
            color: #0728fc;
          }

          .marker-o-edit {

            background-color: #ffffff;
            border: 3px solid #07ff0e;
            color: #000000;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.5);

            &.active {
              background-color: #004310;
              color: #ffffff;
            }
          }

          .marker-o-edit-i {
            background-color: #ffffff;
            border: 2px solid #eeff00;
            color: #000000;
          }

          .startend-marker {

            background-color: #ffffff;
            border: 1px solid #0728fc;
            color: #0015a1;
          }

          .renderMarker {
            border: 1px solid #c84141;

            span {

              background-color: #c84141;
              color: white;
            }
          }
        }
      }

      .amap-info {

        .amap-info-content {
          background-color: rgba(8, 16, 39, 0.8) !important;
          color: #ffffff;

          .divider {

            border-bottom: 1px solid #0092f8;
          }

          .info-item-content {

            border-bottom: 1px solid rgb(231, 231, 231);

          }
        }
      }
    }

    .listShow {
      background-color: rgba(20, 20, 20, 0.9);

      .planList {
        .title {
          background-color: #040404;
          color: white;

          .title-item {
            .el-button {
              background-color: transparent;
              color: white;

              &.active {
                color: #0b58de;
              }
            }
          }

          .el-button {
            background-color: white;
            color: #0b58de;
            border: none;

            &.checked {
              background-color: #0b58de;
              color: white;
            }
          }

        }

        .addWord {
          background-color: rgba(14, 18, 42, 0.8);
          color: white;
          border: 2px dashed white;

          &.checked {
            border: 2px dashed #0b58de;
          }
        }

        .routeType {
          background-color: transparent;
        }

        .content-list {
          .fenceContent {
            background-color: rgba(14, 18, 42, 0.9);
            border: 1px solid #0b58de;

            .content-item-1 {

              background-color: #1349a7;
              color: white;

            }

            .content-item-2 {
              color: white;
            }

            .content-item-3 {
              .el-popover__reference-wrapper {
                .el-button {
                  background: transparent !important;

                  border: none !important;

                }
              }
            }
          }

          .entryDiv {

            background-color: rgba(14, 18, 42, 0.9);
            border: 1px solid #0b58de;

            color: white;

          }

          .content-list-item {
            background-color: rgba(14, 18, 42, 0.9);
            border: 1px solid #434343;


            color: white;

            &.active {
              border: 1px solid #0b58de;
            }

            .el-row {
              .el-col {
                .wordType {

                  color: #afa6a6;

                }

                &.el-col-end {
                  .el-button {
                    background-color: transparent;
                    border: none;
                  }
                }
              }
            }

            .operateBar {
              background-color: #11336d;

              &.operateBar_task {

                background-color: #31116d;
              }

              // 

              .el-button {
                background-color: transparent;
                border: none;

                color: white;


                &.active {
                  color: #468aff;
                }
              }
            }

            &.task_time {
              background-color: rgba(39, 13, 53, 0.9);

              &.active {
                border: 1px solid #4b0fb9;

              }
            }

          }

          // scrollbar-width: thin !important;
          // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
          // -ms-overflow-style: none !important;
          // scrollbar-color: #777777 #ccc;

          &::-webkit-scrollbar {
            width: 3px;
          }

          &::-webkit-scrollbar-track {
            background-color: #ccc;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }

          &::-webkit-scrollbar-thumb {
            background-color: #777777;
            -webkit-border-radius: 2em;
            -moz-border-radius: 2em;
            border-radius: 2em;
          }
        }

        .el-pagination {
          .el-pager li {
            color: white;
          }

          .el-pager li:not(.disabled).active {
            background-color: #124093 !important;
            border: 1px solid #124093;
          }
        }

        .renameDialog {
          .dividerDiv {
            background-color: #0b58de;
          }

          .savebtn {
            background-color: #0b58de;
            color: white;
          }

          .closeBtn {
            background-color: white;
            color: #0b58de;
          }
        }

        .searchFence {
          .el-input__inner {
            background-color: #141414 !important;
            color: white !important;
            border: 1px solid #dcdfe6 !important;
          }
        }

        .el-input-group__append {
          background-color: transparent !important;
        }
      }

      .dialogType {
        .el-dialog {
          background-color: rgba(20, 20, 20, 0.86) !important;
          border-radius: 8px !important;

          .tipInfo {
            color: white;
            border: 1px solid #fe0000;
            background-color: #131313;
          }

          .content-type {
            .el-button {
              background-color: transparent;
              color: #0092f8;
              border: none;
              background-color: rgba(0, 0, 0, 0.75);

              &.active {
                border: 1px solid white !important;
                color: white;
              }

              &.noActive {
                border: none;
                color: #6d6e70;
              }
            }
          }
        }
      }




    }



    .timer {
      background-color: rgba(8, 16, 39, 0.8);

      .timer-item {
        color: #0092f8;

        .timer-item-1 {
          color: #0092f8;
        }
      }
    }

    .layerBut {

      background-color: rgba(8, 16, 39, 0.86);

      .el-popover {
        background: transparent;
      }

      .el-button {
        background-color: transparent;
        border: none;
        color: white;
        border-bottom: 1px solid #eee;
      }
    }





    .infoMsg {

      background-color: #040404;
    }

    .amap-ui-control-layer-lis,
    .amap-ui-control-layer-expanded {
      background-color: transparent !important;
    }

    .amap-ui-control-layer-base-item,
    .amap-ui-control-layer-overlay-item {
      color: #ffffff;
      background-color: #081027;

    }

    .el-loading-mask {
      background-color: rgba(3, 9, 33, 0.9);

      .el-loading-spinner {
        i {
          color: #b2b2b2 !important;
        }
      }
    }

    input::-webkit-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-moz-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-ms-input-placeholder {
      color: #5e5e5e !important;
    }

  }

  .silderInput {
    .sliderValue {
      .el-slider__runway {
        .el-slider__bar {
          background-color: #0555ff !important;
        }
      }

      .el-slider__button-wrapper {
        .el-slider__button {
          border: 1px solid #0555ff !important;
        }
      }
    }

    .el-input {
      .el-input__inner {
        background-color: transparent;
        color: white;
      }

      .el-input__suffix {
        color: rgb(156, 156, 156);
      }
    }
  }

  .weatherDiv {
    background-color: rgba(8, 16, 39, 0.8);

    color: white;

    .el-divider {
      background-color: #0092f8;
    }

    .weaTitle {
      .el-button {
        border: none;
        background-color: transparent;
      }
    }

    .weaContent {
      .el-row {
        .el-col {
          &.temperaClass {
            color: #0092f8;

          }

        }
      }
    }
  }

  .operateBar_1 {
    background-color: rgba(8, 16, 39, 0.9);

    .el-button {
      background-color: transparent;
      border: none;
      color: white;
    }

    .chooseAcitve {
      // background-color: rgba(22, 188, 230, 0.5);
      color: #0092f8;
    }
  }

  .routeEdit,
  .fenceEdit,
  .orthoEdit {
    color: white;
    background-color: rgba(53, 57, 68, 0.94);

    .title {
      color: white;

      .el-button {
        color: #ffffff;
        background-color: transparent;
        border: none;

        &.returnIcon {
          color: #0070ff;
        }
      }
    }

    .content-item-1 {
      .el-form-item {
        .el-form-item__label {
          color: white !important;
        }

        .el-input {
          .el-input__inner {
            background-color: transparent !important;
            border: 1px solid #0070ff !important;
            color: white !important;
          }
        }

        .el-button {
          background-color: transparent;
          color: white;
          border: 1px solid #0070ff;
        }

        .el-collapse {
          border: none;

          .el-collapse-item {

            .addActionBtn {

              color: #ffffff;
              background-color: #0092f8;
              border: none;

              &.active {
                color: #ffffff;
                background-color: #0555ff;
              }
            }

            .routeActionDiv {

              .el-button {
                border: none;
              }
            }

            .el-input {
              .el-input__inner {
                background-color: transparent !important;
                color: white !important;
                border: 1px solid #0555ff !important;
              }
            }

            .el-collapse-item__header {
              background-color: rgba(17, 81, 189, 0.7) !important;
              border: none !important;
              color: white !important;
            }

            .el-collapse-item__wrap {
              background-color: rgba(5, 3, 45, 0.9) !important;
              border: none !important;

              .el-collapse-item__content {
                color: white !important;
              }
            }

          }
        }
      }

      // scrollbar-width: thin !important;
      // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
      // -ms-overflow-style: none !important;
      // scrollbar-color: #777777 #ccc;

      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #ccc;
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #777777;
        -webkit-border-radius: 2em;
        -moz-border-radius: 2em;
        border-radius: 2em;
      }

      .el-loading-mask {
        .el-loading-spinner {
          i {
            color: #409eff !important;
          }
        }
      }

      .saveBut {
        background-color: white;
        color: #0441c3;
        border: none;

        &.active {
          background-color: #0441c3;
          color: white;
        }
      }
    }

    input::-webkit-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-moz-input-placeholder {
      color: #5e5e5e !important;
    }

    input::-ms-input-placeholder {
      color: #5e5e5e !important;
    }

  }

  .cameraParam {
    .contentParam {
      .param-title {
        .el-input {
          .el-input__inner {
            background-color: black !important;
            border: 0.5px solid rgb(102, 102, 102) !important;
            color: white;
          }
        }
      }
    }
  }

  .deviceList {
    .el-dialog {
      background-color: rgba(8, 16, 39, 0.9) !important;

      .contentDiv {
        .check-group {
          .el-button {
            background-color: #ffffff;
            border: 6px solid rgba(112, 112, 112, 1);

            &.active {
              border: 6px solid rgba(11, 89, 222, 0.8);
            }

            .nameDiv {
              color: #000000;
              font-weight: 550;
            }

            .idDiv {
              // margin-top: 1%;
              color: #000000;
              font-weight: 550;

            }
          }
        }

        .entryDevice {
          color: white;
        }

        // scrollbar-width: thin !important;
        // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
        // -ms-overflow-style: none !important;
        // scrollbar-color: #777777 #ccc;

        &::-webkit-scrollbar {
          width: 3px;
        }

        &::-webkit-scrollbar-track {
          background-color: #ccc;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }

        &::-webkit-scrollbar-thumb {
          background-color: #777777;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
      }

      .btnDiv {
        .sureBtn {
          background-color: #0b58de;
          color: white;
          border: none;
        }

        .closeBtn {
          background-color: #123571;
          color: white;
          border: none;

        }
      }

      .el-dialog__header {
        .el-dialog__title {
          color: white !important;
        }
      }

      .pageDiv {
        .el-pagination {

          .btn-prev,
          .btn-next {
            background-color: transparent !important;
            color: white !important;
            border: 1px solid white !important;
          }

          .el-pager {
            li {
              background-color: transparent !important;
              border: 1px solid #ffffff !important;
              color: white !important;
            }

            li:not(.disabled).active {
              background-color: #409eff !important;
              color: white !important;
            }
          }
        }
      }
    }
  }


}
