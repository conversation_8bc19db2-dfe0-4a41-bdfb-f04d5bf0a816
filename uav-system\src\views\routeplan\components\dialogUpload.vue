<template>
  <div class="dialogUpload">
    <el-dialog
      :title="downCode ? '航线下载进度' : '航线下发进度'"
      :visible.sync="dialogCode"
      :modal="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="50%"
      @close="closeUplod"
    >
      <el-progress
        :text-inside="true"
        :stroke-width="26"
        :percentage="waypoint_percent"
        status="success"
      ></el-progress>
      <!-- <el-button @click="downRoute" :disabled="downCode">下载航线</el-button> -->
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "dialogUpload",
  props: {
    waypoint_percent: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      dialogCode: false,
      downCode: false,
    };
  },
  methods: {
    openDialog() {
      this.dialogCode = true;
    },
    closeUplod() {
      this.$emit("close", "");
    },
    downRoute() {
      this.downCode = true;
      this.$emit("downRoute", "");
    },
  },
  // watch:{
  //     "waypoint_percent"(val){
  //         if(this.downCode &&val==100){
  //             this.downCode
  //         }
  //     }
  // }
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .dialogUpload {
    .el-dialog {
      .el-button {
        margin: @zoomIndex * 10px !important;
      }
    }
  }
}
.dialogUpload {
  .el-dialog {
    .el-button {
      margin: 10px;
      background-color: rgb(58, 115, 230);
      color: white;
    }
  }
}
</style>
<style lang="less">
.dialogUpload {
  .el-dialog {
    height: auto !important;
    background-color: white !important;
    text-align: center !important;
    .el-dialog__header {
      text-align: center !important;
      height: 6% !important;
      padding: 2% !important;
      padding-bottom: 0 !important;
      .el-dialog__title {
        color: black !important;
      }
    }
    .el-dialog__body {
      height: auto !important;
      padding: 1% 2% !important;
      margin-bottom: 2%;
    }
  }
}
</style>