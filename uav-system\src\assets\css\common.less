// 滚动条样式
.scrollbar-style {
  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #000;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(69, 99, 250, 0.8);
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }
}

.scrollbar-style-thin {
  // scrollbar-width: thin;
  // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
  // -ms-overflow-style: none;
  // scrollbar-color: #777777 #ccc;

  &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #ccc;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #777777;
    -webkit-border-radius: 2em;
    -moz-border-radius: 2em;
    border-radius: 2em;
  }
}

.el-table__body-wrapper {
  .scrollbar-style;
}


// 修改输入框边框为白色，无背景，白色字体
.el-input-white {
  .el-input__inner {
    background-color: rgba(0, 0, 0, 0) !important;
    border: 1px solid #1a1919 !important;
    color: #fff !important;
  }

  .el-range-input {
    background-color: rgba(0, 0, 0, 0) !important;
    color: #fff !important;
  }

  .el-range__icon {
    display: none;
  }
}

// select选择器弹出框透明黑色
.el-input-white {
  .el-select-dropdown {
    background-color: rgba(0, 0, 0, 0.7) !important;
    border: none !important;
    left: 0 !important;
  }

  .el-select-dropdown__item {
    color: #ffffff !important;

    &:hover {
      background-color: #0B58DE !important;
    }
  }

  .hover {
    background-color: #0B58DE !important;
  }

  .popper__arrow {
    display: none;
  }
}

// 日期弹出框暗黑风格
.date-poper {
  background-color: #040C1A !important;
  border-color: #1f314e !important;

  .prev-month,
  .available,
  .next-month {
    background-color: #091934;
  }

  .prev-month,
  .next-month {
    color: #303847 !important;
  }

  .available {
    color: #fff !important;
  }

  .el-date-table th {
    border: none;
    color: #fff;
  }

  .el-picker-panel__icon-btn {
    color: #fff !important;

    &:hover {
      color: #129DEE !important;
    }
  }

  .el-date-picker__header-label {
    color: #fff;
  }

  .popper__arrow {
    border-top-color: #1f314e;
    border-bottom-color: #1f314e;
    display: none;

    &::after {
      border-top-color: rgba(0, 0, 0, 0);
      border-bottom-color: rgba(0, 0, 0, 0);
    }
  }

  .el-picker-panel__footer {
    background-color: #040C1A !important;
    border-top-color: #1f314e;

    .el-button--default {
      background-color: #091934 !important;
      color: #0B58DE;
      font-size: 12px;
      border: none;
    }

    .el-button--custom {
      background-color: #8991A1;
      width: 54px;
      height: 26px;
      font-size: 12px;
      display: flex;
      cursor: pointer;
      margin-left: 10px;
      justify-content: center;
      align-items: center;
      border-radius: 2px;
      color: #151516;
    }
  }

  .el-date-range-picker__time-header {
    border-color: #1f314e;
  }

  .el-date-range-picker__content.is-left {
    border-color: #1f314e;
  }

  .el-date-range-picker__header {
    color: #fff;
  }

  .el-input__inner {
    background-color: rgba(0, 0, 0, 0) !important;
    color: #fff;
    border-color: #1f314e !important;
  }

  .in-range {
    color: #000 !important;

    div {
      background-color: #BFC5D1 !important;
    }
  }

  .start-date,
  .end-date {
    span {
      background-color: #103B87 !important;
    }
  }

  .el-picker-panel__sidebar {
    background-color: #040C1A !important;

    .el-picker-panel__shortcut {
      color: #fff !important;

      &:hover {
        color: #409EFF !important;
      }
    }
  }
}


// 生成内外边距，1-50
each(range(50), {
    @size: 1px;
    @val: @value * 1;

    .mr@{val} {
      margin-right: (@value * @size);
    }

    .ml@{val} {
      margin-left: (@value * @size);
    }

    .mt@{val} {
      margin-top: (@value * @size);
    }

    .mb@{val} {
      margin-bottom: (@value * @size);
    }

    .pr@{val} {
      padding-right: (@value * @size);
    }

    .pl@{val} {
      padding-left: (@value * @size);
    }

    .pt@{val} {
      padding-top: (@value * @size);
    }

    .pb@{val} {
      padding-bottom: (@value * @size);
    }
  }

);
