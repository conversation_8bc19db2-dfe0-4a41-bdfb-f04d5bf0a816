<!-- 二次封装表格 -->
<template>
  <div class="custom-table" ref="templateTable">
    <!-- 内容 -->
    <slot name="customContent" :data="tableData" :pagesHeight="pagesHeight">
      <el-table
        ref="table"
        v-loading="tableLoading"
        :element-loading-text="elementLoading.text"
        :element-loading-spinner="elementLoading.spinner"
        :element-loading-background="elementLoading.background"
        :height="tableHeihght"
        :data="tableData"
        :size="size"
        style="width: 100%"
        :row-class-name="tableRowClassName"
        :header-row-class-name="headerRowClassName"
        v-if="tableCode"
        :border="border"
        @selection-change="selectChange"
      >
        <!-- 空数据时展示 -->
        <template v-slot:empty>
          <slot name="empty">
            <div class="table-no-data" v-if="!tableLoading && emptyCopy">
              <img-icon name="no-data" :size="104"></img-icon><br />
              <span style="color: #fff; font-size: 30px">{{
                language.noData
              }}</span>
            </div>
            <div class="table-no-data" v-if="!tableLoading && !emptyCopy">
              <img-icon name="no-data-1" :size="100"></img-icon><br />
              <span style="color: #2a2f40; font-size: 20px">{{
                language.noData
              }}</span>
            </div>
          </slot>
        </template>

        <el-table-column
          :label="language.index"
          type="index"
          width="60"
          align="center"
          v-if="isShowIndex && !selectionCode"
        >
        </el-table-column>
        <el-table-column
          type="selection"
          width="50"
          align="center"
          v-if="selectionCode"
        >
        </el-table-column>

        <!-- 在表格前插入 -->
        <slot name="front"></slot>

        <el-table-column
          v-for="(item, index) in column"
          :key="index"
          :prop="item.prop"
          :label="item.label"
          :width="item.width"
          align="center"
          :fixed="item.fixed"
        >
          <template slot-scope="scope">
            <slot :name="item.prop" :row="scope.row" :index="scope.$index">
              <!-- 数据多时不要使用转换 -->
              <span v-if="item.transition">
                {{ transitionText(item.transition, scope.row[item.prop]) }}
              </span>

              <!-- map格式转换 -->
              <span v-else-if="item.map">
                {{ item.map[scope.row[item.prop]] }}
              </span>

              <span v-else>
                {{ scope.row[item.prop] }}
              </span>
            </slot>
          </template>
        </el-table-column>

        <!-- 在表格后插入 -->
        <slot name="later"></slot>
      </el-table>
    </slot>

    <!-- 分页 -->
    <slot name="pages">
      <pages
        ref="pages"
        v-if="isShowPage"
        @onChange="pageChange"
        :total="pageInfo.total"
        :current.sync="pageInfo.current"
        :pageSize.sync="pageInfo.pageSize"
      ></pages>
    </slot>
  </div>
</template>

<script>
import pages from "../pages/index.vue";
import request from "@/utils/api";
import imgIcon from "../imgIcon/index.vue";
export default {
  components: {
    pages,
    imgIcon,
  },
  props: {
    isShowIndex: {
      type: Boolean,
      default: true,
    },
    column: {
      // 表头列表
      type: Array,
      default: () => {
        return [];
      },
    },
    data: {
      // 表格显示的数据
      type: Array,
      default: () => {
        return [];
      },
    },
    beforeRequest: Function, // 请求前
    requestResponse: Function, // 响应后
    isShowPage: {
      // 是否显示分页
      type: Boolean,
      default: true,
    },
    params: {
      // 请求参数
      type: Object,
      default: () => {
        return {};
      },
    },
    urlName: String, // url名称
    pageProps: {
      // 分页参数字段
      type: Object,
      default: () => {
        return {
          current: "page",
          pageSize: "size",
        };
      },
    },
    isSetHeight: {
      // 开启后，将会自动适应当前高度
      type: Boolean,
      default: true,
    },
    pmd: String,
    loading: {
      type: Boolean,
      default: false,
    },
    // 遮罩层信息
    elementLoading: {
      type: Object,
      default: () => {
        return {
          text: "努力加载中...",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.7)",
          default: true,
        };
      },
    },
    border: {
      type: Boolean,
      default: false,
    },
    // 分页信息
    pageInfo: {
      type: Object,
      default: () => {
        return {
          current: 1,
          pageSize: 10,
          total: 0,
        };
      },
    },
    size: {
      type: String,
      default: "",
    },
    selectionCode: {
      type: Boolean,
      default: false,
    },
    emptyCopy: {
      type: Boolean,
      default: true,
    },
    headerRowClassName: {
      type: String,
      default: "custom-table-header",
    },
    dataTier: {
      type: Boolean,
      default: false,
    },
    os_timestampCode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: this.data,
      tableHeihght: undefined,
      tableLoading: false,
      pagesHeight: 0,
      tableCode: true,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.components.customTable;
    },
  },
  watch: {
    data: function (val) {
      this.tableData = val || [];
    },
    selectionCode: function () {
      this.tableCode = false;
      setTimeout(() => {
        this.tableCode = true;
      });
    },
  },
  created() {
    this.getTableData();
    if (this.elementLoading.default) {
      this.elementLoading.text = this.language.loading;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.setTableHeight();
      window.addEventListener("resize", this.setTableHeight);
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.setTableHeight);
  },
  methods: {
    // 设置表格内容宽度
    setTableHeight: function () {
      if (!this.isSetHeight) {
        return false;
      }

      let templateTable = this.$refs.templateTable;
      let templateTop = templateTable.offsetTop;
      let parentElement = templateTable.parentElement;
      let height = parentElement.offsetHeight - templateTop;

      // 如果显示分页，则减去分页的高度
      if (this.isShowPage && this.$refs.pages) {
        let pages = this.$refs.pages.$el;
        this.pagesHeight = pages ? pages.offsetHeight : 0;
        height = height - this.pagesHeight;
      } else {
        height = height - 20;
      }
      this.tableHeihght = height;
    },

    getTableData: function () {
      if (!this.urlName) {
        return false;
      }
      // 请求前
      this.beforeRequest && this.beforeRequest();

      // 拷贝参数
      let params = Object.assign({}, this.params);
      this.tableLoading = true;
      // 如果显示分页，则添加分页参数
      if (this.isShowPage) {
        let props = this.pageProps;
        params[props.current] = this.pageInfo.current - 1;
        params[props.pageSize] = this.pageInfo.pageSize;
      }
      // 添加pmd参数
      let pmd = "";
      if (this.pmd) {
        let list = this.pmd.split(",");
        list.forEach((item) => {
          let p = params[item] || params[item] == 0 ? params[item] : "";
          pmd += p;
        });
      }
      if (this.os_timestampCode) {
        params.os_timestampCode = true;
      }
      params.pmd = pmd;
      request(this.urlName, params)
        .then((res) => {
          let data = res.data;
          this.tableData = data.list ? data.list : [];
          if (this.dataTier) {
            this.tableData = data ? data : [];
          }
          this.pageInfo.total = data.total_page
            ? data.total_page * this.pageInfo.pageSize
            : 0; // 总数不准
          this.tableLoading = false;
          // console.log("表格响应结束-------->", this.requestResponse);
          this.requestResponse && this.requestResponse(res);
          this.$emit("requestSuccess", data);
        })
        .catch((err) => {
          this.tableLoading = false;
        });
    },
    /**
     * 外部刷新调用，如果不传页码，则默认第一页
     */
    refresh: function (current = 1) {
      this.pageInfo.current = current;
      this.getTableData();
    },

    /**删除刷新 */
    removeRefresh: function () {
      if (this.tableData.length === 1 && this.pageInfo.current !== 1) {
        this.pageInfo.current--;
      }
      this.getTableData();
    },

    tableRowClassName: ({ row, rowIndex }) => {
      return rowIndex % 2 === 0 ? "singular-row" : "pairage-row";
    },

    // 分页改变时
    pageChange: function () {
      this.getTableData();
    },

    /**
     * 要回显枚举值，
     * @param {*} item
     * @param {*} item.list
     * @param {*} item.label
     * @param {*} item.label
     * @param {*} val
     */
    transitionText: function (item, val) {
      let data = item.list,
        value = item.value,
        label = item.label;
      let echoVal = data.filter((row) => {
        return row[value] == val;
      })[0];

      return echoVal ? echoVal[label] : val;
    },
    selectChange: function (rows) {
      this.$emit("selection-change", rows);
    },
  },
};
</script>

<style lang="less">
.custom-table {
  .el-table,
  .el-table__expanded-cell {
    // background-color: rgba(0, 0, 0, 0);
  }
  // 表
  .custom-table-header {
    // background-color: #0a0a54;
    .el-table__cell {
      // color: #9499a2;
      // background-color: rgba(0, 0, 0, 0);
    }
  }

  // 内容
  .el-table .singular-row {
    // background: #050e19;
  }

  .el-table .pairage-row {
    // background: #0c1a2d;
  }
  .el-table__cell {
    // background: rgba(0, 0, 0, 0) !important;
    border: none !important;
    // color: #ffffff;
  }
  .el-table--border::after,
  .el-table--group::after,
  .el-table::before {
    display: none !important;
  }
}
</style>
