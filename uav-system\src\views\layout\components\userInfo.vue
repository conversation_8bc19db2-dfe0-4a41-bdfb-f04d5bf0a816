<!-- 布局用户选项 -->
<template>
  <div class="user-operation mt8" ref="userOperate">
    <el-popover
      placement="bottom"
      width="130"
      trigger="click"
      popper-class="header-user-popover"
    >
      <template>
        <div class="user-popover">
          <div
            class="popover-item"
            @click="openUser(item)"
            v-for="(item, index) in userNavList"
            :key="index"
          >
            <span class="mr10" :class="item.icon"></span>{{ item.label }}
          </div>
        </div>
      </template>

      <template slot="reference">
        <div class="user-info" style="text-align: center">
          <div class="el-icon-user-solid"></div>
          <div class="mt5" style="font-size: 12px; color: rgb(96, 216, 252)">
            {{ userInfo.nick }}
          </div>
        </div>
      </template>
    </el-popover>
    <!-- 账户设置 -->
    <account-settings ref="accountSettings"></account-settings>
    <!-- 密码设置 -->
    <pass-set ref="passSet"></pass-set>
    <!-- 用户信息 -->
    <user-info ref="userInfo"></user-info>
    <!-- 系统设置 -->
    <system-set ref="systemSet"></system-set>
    <!-- 教学视频 -->
    <!-- <teaching-videos ref="teachingVideos"></teaching-videos> -->
  </div>
</template>

<script>
import { removeCookie } from "../../../utils/storage";
import accountSettings from "@/views/user/accountSettings.vue";
import passSet from "@/views/user/passSet.vue";
import userInfo from "@/views/user/userInfo.vue";
import systemSet from "@/views/user/systemSet.vue";
export default {
  components: {
    accountSettings,
    passSet,
    userInfo,
    systemSet,
  },
  data() {
    return {
      userNavList: [
        {
          label: "账户信息",
          icon: "el-icon-user-solid",
          path: "accountSettings",
        },
        // {
        //   label: "账号设置",
        //   icon: "el-icon-s-custom",
        //   path: "accountSettings",
        // },
        // {
        //   label: "教学视频",
        //   icon: "el-icon-video-camera-solid",
        //   path: "teachingVideos"
        // },
        { label: "密码修改", icon: "el-icon-lock", path: "passSet" },
        // { label: "系统设置", icon: "el-icon-s-tools", path: "systemSet" },
        { label: "退出登录", icon: "el-icon-switch-button", path: "esc" },
      ],
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo;
    },
  },
  created() {
    let language = this.$languagePackage.layout.user;
    for (let i = 0; i < this.userNavList.length; i++) {
      let key = this.userNavList[i].path;
      this.userNavList[i].label = language[key];
    }
  },
  methods: {
    openUser: function (item) {
      if (item.path === "esc") {
        let esc = this.$languagePackage.layout.message.esc;
        this.$confirm(esc.content, esc.title, {
          confirmButtonText: esc.confirm,
          cancelButtonText: esc.cancel,
          type: "warning",
        }).then(() => {
          this.$store.commit("setUserInfo", {});
          removeCookie("token");
          this.$router.push({ name: "login" });
        });
        return false;
      }
      // if (item.path == "teachingVideos") {
      //   if (this.$route.name == "teachingVideos") {
      //     return false;
      //   }
      //   this.$refs.userOperate && this.$refs.userOperate.click();
      //   this.$router.push({ name: "teachingVideos" });
      //   return false;
      // }

      this.$refs[item.path].open();
    },
  },
};
</script>
