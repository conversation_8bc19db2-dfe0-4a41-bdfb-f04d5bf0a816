<!-- 滑块 -->
<template>
  <div
    :class="vertical ? 'sliding-vertical' : 'sliding-block'"
    :style="{ width: width }"
    ref="slidingBlock"
    class="remote-sliding"
  >
    <div class="sliding-block-main">
      <div class="block-mobile" ref="blockMobile">
        <slot name="mobile">
          <div class="block-cell">
            <div class="cell-item" style="margin-top: 0"></div>
            <div class="cell-item"></div>
            <div class="cell-item" style="margin-right: 0"></div>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    width: {
      // 宽度
      type: String,
      default: "100%",
    },
    value: [Number, String], // 绑定的值
    // 方向
    vertical: [Boolean, String],
  },
  data() {
    return {
      left: 0, // 滑块左边距
      blockMobileWidth: 0, // 滑块长度
      slidingBlockWidth: 0, // 可滑行距离
      isDown: false, // 是否按下
      initLeft: 0, // 滑块初始位置
      mouseStart: 0, // 鼠标按下时的位置
      nextValue: null,

      inputTime: null,
      changeTime: null,

      changeValue: null,
    };
  },
  watch: {
    value: function (value) {},
  },
  mounted() {
    this.$nextTick(() => {
      this.echoLocation("50%");
    });
  },
  methods: {
    // 回显到指定的位置
    echoLocation: function (val) {
      if (this.vertical) {
        this.$refs.blockMobile.style.top = `calc(${val} - 16px)`;
      } else {
        this.$refs.blockMobile.style.left = `calc(${val} - 16px)`;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.sliding-block-style(vertical, 0 4px, 100%, 4px, calc(100% - 32px), -100%, 12px, 32px, 80%, 2px);

.sliding-vertical {
  height: 100%;
  position: relative;
  z-index: 10;
  .block-cell {
    flex-direction: column;
    .cell-item {
      margin-right: 0px !important;
      margin-top: 5px;
    }
  }
}

.sliding-block-style(block);

.sliding-block-style(
    @className, // 类名称
    @mainPadding: 4px 0, // 内容内边距
    @slidingBlockHeight: 4px, // 进度条高度
    @slidingBlockWidth: 100%, // 进度条高度
    @mobileTop: -100%, // 滑块上边距
    @mobileLeft: 0, // 滑块左边距
    @blockCellWidth: 32px, // 滑块宽度
    @blockCellHeight: 12px, // 滑块高度
    @blockCellItemWidth: 2px, // 滑块里面分隔线宽度
    @blockCellItemHeight: 80%, // 滑块里面分隔线高度
) {
  // 横屏样式
  .sliding-@{className} {
    padding: @mainPadding;
    .sliding-block-main {
      position: relative;
      height: @slidingBlockHeight;
      width: @slidingBlockWidth;
      // background-color: #fff;
      border-radius: 8px;
      .block-mobile {
        position: absolute;
        left: @mobileLeft;
        top: @mobileTop;
      }

      .block-cell {
        height: @blockCellHeight;
        width: @blockCellWidth;
        display: flex;
        align-items: center;
        justify-content: center;
        // background-color: #fff;
        border-radius: 8px;
        cursor: move;
        .cell-item {
          width: @blockCellItemWidth;
          height: @blockCellItemHeight;
          // background-color: rgb(123, 123, 124);
          margin-right: 5px;
        }
      }
    }
  }
}
</style>