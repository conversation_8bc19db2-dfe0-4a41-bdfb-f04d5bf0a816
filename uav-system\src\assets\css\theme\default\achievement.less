// 成果相关样式
.achievement {
  #achievement-admin {
    background-color: #000;
  }

  #data-analyse {
    .analyse-home {
      .home-logo {
        .login-logo {
          background-color: rgba(22, 155, 213, 1);
        }
      }

      .homt-main {
        h1 {
          color: rgb(67, 140, 255);
        }

        .line {
          background-image: linear-gradient(to right,
              rgba(120, 234, 245, 0.5),
              rgb(120, 234, 245),
              rgba(120, 234, 245, 0.5));
        }
      }

      .user-info {
        color: #333;
      }

    }

    .basic-analyse {
      .flight-height {
        .height-left {
          color: rgba(22, 155, 213, 1);
        }

        .height-right {
          color: rgb(0, 98, 255);
        }
      }
    }




    .layout-item {
      .title {
        color: rgb(0, 179, 255);
      }

      .line {
        background-color: rgba(22, 155, 213, 1);
      }
    }
  }

  #results-show {
    background-color: #000;

    .results-show-mian {
      background-color: #353c40;
    }
  }

  #result-total-list {
    background-color: #000;

    .right-content {
      .content-date {
        .content-date-main {
          background-image: linear-gradient(to top,
              rgb(48, 83, 173),
              rgba(48, 83, 173, 0.1));

          .date-time {
            color: #fff;
          }
        }
      }

      .content-list {
        background-color: rgba(21, 22, 22, 0.98);
      }
    }
  }

  .achievement-item {
    background-color: #fff;

    .content-li {
      .content-image {
        .mouse-hover-show {
          .show-mian {
            background-color: rgba(20, 69, 145, 0.5);
          }
        }
      }

      .content-title {
        color: #000000;
      }

      .content-text {
        color: #958f8f;

        .text-value {
          color: #000000;
        }
      }

      .right-top-icon {
        background-color: rgba(62, 58, 58, 0.5);
        color: #fff;
      }

      .img-bottom-icon {
        border: 1px solid #ccc;

        .circle {
          background-color: #746d6d;
        }

        &:hover {
          border-color: blue;
        }

        &:hover .circle {
          background-color: blue;
        }
      }
    }
  }

  .analyse-canvas {
    background-color: #eee;

    .title {
      color: #333;
    }
  }

  // 成果回放
  #route-replay {
    .main-left {
      background-color: rgba(0, 0, 0, 0.9);

      .left-content {
        .left-mode {
          .mode-item {
            .item-label {
              color: #989898;
            }

            .item-value {
              color: #ffffff;
            }
          }
        }
      }

      .left-top {
        .left-top-cell {
          .cell-label {
            color: #989898;
          }

          .cell-value {
            color: #0590ec;
          }
        }
      }

      .flight-data-change {
        .flight-title {
          color: #fff;
        }

        .color-list {
          .color-cell {
            .cell-bot {
              color: #fff;
            }
          }
        }
      }
    }

    .play-set {
      background-color: rgba(0, 0, 0, 0.6);
      color: #fff;

      .multiple {
        .multiple-prpover {

          .multiple-cell {

            background-color: rgba(0, 0, 0, 0.6);

            &:hover {
              color: #0590ec;
            }
          }

          .select-style {
            color: #0590ec;
          }
        }
      }

      .afresh-play {
        &:hover i {
          color: #0590ec;
        }
      }
    }

    .uav-info {
      background-color: rgba(0, 0, 0, 0.9);

      .uav-info-cell {
        .cell-label {
          color: #948d8d;
        }

        .cell-value {
          color: #fff;
        }
      }
    }
  }

}
