
const actionObj = {
    19: {
        action_id: 'hover',
        param_list_id: "hovertime"
    },//悬停
    115: {
        action_id: 'uav_yaw',
        param_list_id: "yaw"
    },//无人机偏航角
    205: {
        action_id: 'gimbal_ctrl',
        param_list_id: "gimbal_pitch"
    },//云台角度控制
    206: {
        action_id: 'takephoto',
    },//拍照
}
/**
 * 多个坐标点动作转换
 * @param {Array} list 传入坐标点动作数组
 * @returns {Array} 返回转换后坐标点动作对象数组
 */
export function actionChangeArray(list) {
    let actionList = []
    let i = list.findIndex(item => {
        return item.type == 206
    })
    let indexList = []
    if (i !== -1 && i > 0 && i < list.length - 1) {
        if (list[i - 1].type == 19 && list[i - 1].value1 == 1 && list[i + 1].type == 19 && list[i + 1].value1 == 1) {
            indexList = [i - 1, i + 1]
        }
    }
    for (let index = 0; index < list.length; index++) {
        let item = list[index]
        if(indexList.indexOf(index)==-1){
            actionList.push(actionChangeAlone(item))
        } 
    }
    return actionList;
}

/**
 * 单个坐标点动作转换
 * @param {Object} list 传入坐标点动作对象
 * @returns {Object} 返回转换后坐标点动作对象
 */
export function actionChangeAlone(obj) {
    let actionItme = actionObj[obj.type];
    let action = {
        action_id: actionItme.action_id,
        param_list: ''
    }
    if (actionItme.param_list_id) {
        action.param_list = [{
            param_id: actionItme.param_list_id,
            value: parseInt(obj.value1)
        }]
    }
    if (actionItme.action_id == 'gimbal_ctrl') {
        action.param_list.unshift({
            param_id: 'gimbal_yaw',
            value: 0
        })
    }
    return action;

}
