<template>
  <div class="versionRate">
    <el-dialog
      :visible.sync="show"
      :center="true"
      custom-class="versionRateDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      :destroy-on-close="true"
    >
      <el-progress
        type="circle"
        :percentage="deviceItemList.upgradePercent"
        :stroke-width="16"
        :width="400"
      ></el-progress>
      <div class="title">
        更新固件：{{ formatName(deviceItemList.moduleName) }}
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    versionList: {
      type: Array,
      default: [],
    },
    deviceItemList: {
      type: [Object, String],
      default: () => {
        return {};
      },
    },
  },
  watch: {
    "deviceItemList.upgrade_flag"(val) {
      if (val && val == 3003) {
        if(this.show){
            this.$emit("reStart",'')
        }
        this.close();
      }
    },
  },
  data() {
    return {
      show: false,
    };
  },
  methods: {
    open() {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    formatName(name) {
      let index = this.versionList.findIndex((x) => {
        return x.id == name;
      });
      if (index !== -1) {
        return this.versionList[index].label;
      }
    },
  },
};
</script>
<style lang="less">
.versionRate {
  .versionRateDialog {
    background-color: transparent;
    .el-dialog__header,
    .el-dialog__body {
      padding: 0;
      background-color: transparent;
    }
    .el-dialog__body {
      text-align: center;
      .el-progress__text {
        color: #fff;
      }
      .title {
        color: #fff;
        font-size: 20px;
        margin-top: 10px;
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .versionRate {
    .versionRateDialog {
      .el-dialog__body {
        .title {
          font-size: @zoomIndex * 20px !important;
          margin-top: @zoomIndex * 10px !important;
        }
      }
    }
  }
}
</style>