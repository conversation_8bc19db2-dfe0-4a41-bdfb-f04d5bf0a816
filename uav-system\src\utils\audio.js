import Vue from "vue";
class Audio {
    synth = window.speechSynthesis;
    speech = new SpeechSynthesisUtterance();
    config = null;
    queueing = [];
    play = false;

    constructor(config) {
        this.config = config;
    }

    // 可清空所有队列
    playSpeech = (text, config = {}) => {
        let {
            rate = '',
                pirch = 1,
                volume = 1,
                eliminate = false
        } = config;

        let synth = window.speechSynthesis;

        // 是否清空前面队列播放的语音
        if (eliminate) {
            synth.cancel();
        }

        let speech = new SpeechSynthesisUtterance(text);
        speech.lang = Vue.prototype.$loadingEnUI ? 'en-US' : "zh-CN";
        speech.volume = volume;
        speech.rate = rate ? rate : (Vue.prototype.$loadingEnUI ? 1.3 : 1);
        speech.pitch = pirch;

        speech.onerror = (e) => {
            console.log("播放出错", e);
        }

        synth.speak(speech);

        return {
            speech,
            synth
        }
    }

    // 按顺序播报语音
    handleSpeak = (text, config = {}) => {
        console.log("语音播放文本----------->", text);
        let {
            rate = '',
                pirch = 1,
                volume = 1,
                eliminate = false
        } = config;
        // console.log("语音")
        let speech = new window.SpeechSynthesisUtterance(text);
        speech.lang = Vue.prototype.$loadingEnUI ? 'en-US' : "zh-CN";
        speech.volume = volume;
        speech.rate = rate ? rate : (Vue.prototype.$loadingEnUI ? 1.3 : 1);
        speech.pitch = pirch;
        let synth = window.speechSynthesis;

        // 是否清除前面所有待播放语音
        if (eliminate) {
            this.queueing = [];
            this.play = false;
            synth.cancel(); // 取消所有
        }

        speech.onend = () => {
            this.queueing.shift();
            if (this.queueing.length > 0) {
                synth.speak(this.queueing[0]); // 播放
            } else {
                this.play = false;
            }
        }

        speech.onerror = (e) => {
            // console.log("调用失败-----------------")
            this.queueing[0] && synth.speak(this.queueing[0]); // 播放
        }

        this.queueing.push(speech)

        if (!this.play) {
            this.play = true;
            synth.speak(speech); // 播放
        }

        return synth
    }

    // 取消
    cancel = () => {}

    // 暂停
    pause = () => {}
}

export default Audio;