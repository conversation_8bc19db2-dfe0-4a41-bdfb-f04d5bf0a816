<template>
  <el-dialog
    :append-to-body="true"
    :visible.sync="isShow"
    width="1000px"
    :show-close="false"
    :destroy-on-close="true"
    custom-class="video-dialog"
  >
    <video
      height="520"
      width="1000"
      ref="dialogVideo"
      muted
      loop="loop"
      :poster="poster"
      :controls="true"
      v-if="isShow"
    >
      <source :src="videoSrc" type="video/mp4" />
      {{ language.error }}
    </video>

    <!-- 错误信息 -->
    <div class="error-info" v-if="errorIndex != 0">
      <!-- 网络不可用 -->
      <div class="eror-info-hint">{{ errorList[1] }}</div>
    </div>
  </el-dialog>
</template>

<script>
import fileVideo from "@/components/video/fileVideo.vue";
export default {
  name: "videoDialog",
  components: {
    fileVideo,
  },
  data() {
    return {
      isShow: false,
      // videoSrc: '',
      videoSrc: "",
      poster: null,
      errorIndex: 0,
      errorList: {
        1: "加载失败，视频链接不可用",
      },
    };
  },
  computed: {
    language() {
      return this.$languagePackage.components.video.videoDialog;
    },
  },
  created() {
    this.errorList = this.language.errorList;
  },
  methods: {
    openDialog: function (item) {
      this.isShow = true;
      this.videoSrc = item.o_url;
      this.poster = item.p_url;
      this.errorIndex = 0;
      this.$nextTick(() => {
        setTimeout(() => {
          const video = this.$refs.dialogVideo;
          if (video.networkState == 3) {
            this.errorIndex = 1;
          }
        });
      });
    },
  },
};
</script>

<style lang="less">
.video-dialog {
  position: relative;
  .el-dialog__header {
    padding: 0 !important;
  }
  .el-dialog__body {
    padding: 0 !important;
  }
}
</style>

<style lang="less" scoped>
.error-info {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .eror-info-hint {
    font-size: 16px;
    color: red;
  }
  // background-color: #fff;
}
</style>