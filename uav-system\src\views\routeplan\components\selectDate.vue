<template>
  <div class="select-date">
    <div
      class="select-date-item"
      v-for="(item1, index) in dateList"
      :key="index"
    >
      <div class="content-date">
        <el-date-picker
          class="select-picker"
          popper-class="date-poper date-poper_time"
          v-model="dateList[index]"
          type="datetime"
          default-time="08:00:00"
          placeholder="选择航线执行时间节点"
          @change="changeTime($event, index)"
        >
        </el-date-picker>
        <el-button
          type="text"
          icon="el-icon-plus"
          class="add-date"
          @click="addTimeNode"
        ></el-button>
        <el-button
          type="text"
          icon="el-icon-minus"
          class="add-date"
          @click="delTimeNode(index)"
        ></el-button>
      </div>
      <div class="input__error" v-if="error[index]">时间间隔不能小于30分钟</div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    plan_time: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      dateSelect: "",
      dateList: [],
      error: [],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.dateList = this.plan_time;
      if (!this.dateList.length) {
        this.dateList.push("");
        this.error.push(false);
      }
    });
  },
  methods: {
    changeTime(e, i) {
      for (let j = 0; j < this.error.length; j++) {
        this.error[j] = false;
      }
      if (e) {
        this.dateList[i] = e.getTime();
        // if (this.dateList.length) {
        //   for (let index = 1; index < this.dateList.length; index++) {
        //     if (this.dateList[index]) {
        //       for (let j = index - 1; j >= 0; j--) {
        //         if (this.dateList[j]) {
        //           if (
        //             this.dateList[index] - this.dateList[j] <
        //             30 * 60 * 1000
        //           ) {
        //             this.error[index] = true;
        //           }
        //           break;
        //         }
        //       }
        //     }
        //   }
        // }
      }
      this.$forceUpdate();
      this.submitEmit();
      // // this.dateList[i] = e.getTime();
      // // this.submitEmit();

      // if (i !== 0) {
      //   if (e.getTime() - this.dateList[i - 1] < 30 * 60 * 1000) {
      //     this.dateList[i] = "";
      //     this.error[i] = true;
      //     this.$forceUpdate();
      //     return false;
      //   }
      // }
      // console.log(this.dateList[i]);
    },
    addTimeNode() {
      this.dateList.push("");
      this.error.push(false);
    },
    delTimeNode(index) {
      if (this.dateList.length == 1) {
        this.dateList[index] = "";
        this.$forceUpdate();
      } else {
        this.dateList.splice(index, 1);
        this.error.splice(index, 1);
      }
      this.submitEmit();
    },
    //提交返回
    submitEmit() {
      let timeList = [];
      for (let index = 0; index < this.dateList.length; index++) {
        if (this.dateList[index]) {
          timeList.push(this.dateList[index]);
        }
      }
      this.$emit("update:plan_time", timeList);
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  @widthI:@zoomIndex * 60px;
  .select-date {
    .select-date-item {
      margin: @zoomIndex * 2px 0 !important;

      .content-date {
        margin: @zoomIndex * 2px 0 !important;
      }
      .select-picker {
        &.el-date-editor.el-input,
        &.el-date-editor.el-input__inner {
          width: calc(100% - @widthI) !important;
        }
      }

      .add-date {
        font-size: @zoomIndex * 20px !important;
        margin: 0 @zoomIndex * 3px !important;
      }
      .input__error {
        font-size: @zoomIndex * 12px !important;
        // line-height: 1;
        padding-top: @zoomIndex * 4px !important;
        // position: absolute;
        // top: 100%;
        // left: 0;
      }
    }
  }
}
.select-date {
  width: 100%;
  .select-date-item {
    width: 100%;
    margin: 2px 0;
    .content-date {
      width: 100%;
      display: flex;
      margin: 2px 0;
      align-items: center;
    }

    .select-picker {
      &.el-date-editor.el-input,
      &.el-date-editor.el-input__inner {
        width: calc(100% - 60px) !important;
      }
    }

    .add-date {
      font-size: 20px;
      padding: 0;
      border: none !important;
      margin: 0 3px;
    }
    .input__error {
      color: #f56c6c;
      font-size: 12px;
      // line-height: 1;
      padding-top: 4px;
      // position: absolute;
      // top: 100%;
      // left: 0;
    }
  }
}
</style>
<style lang="less">
.date-poper_time {
  width: 322px !important;
  border-width: 1px !important;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%) !important;
  border-radius: 4px !important;
  line-height: 30px !important;
  margin: 5px 0 !important;
  .el-picker-panel__body {
    .el-date-picker__time-header {
      border-color: #1f314e !important;
      border-bottom-width: 1px !important;
      font-size: 12px !important;
      padding: 8px 5px 5px !important;
      .el-date-picker__editor-wrap {
        padding: 0 5px !important;
        .el-input {
          font-size: 13px !important;
          .el-input__inner {
            height: 32px !important;
            line-height: 32px !important;
          }
        }
        .el-time-panel {
          margin: 5px 0 !important;
          border-width: 1px !important;
          box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%) !important;
          border-radius: 2px !important;
          width: 180px !important;
          .el-time-panel__content {
            .el-time-spinner__wrapper {
              max-height: 190px !important;
              .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
                padding-bottom: 15px !important;
              }
              .el-time-spinner__list {
                &::before,
                &::after {
                  height: 80px !important;
                }
                .el-time-spinner__item {
                  height: 32px !important;
                  line-height: 32px !important;
                  font-size: 12px !important;
                }
              }
              .el-scrollbar__bar {
                right: 2px !important;
                bottom: 2px !important;
                border-radius: 4px !important;
                &.is-horizontal {
                  height: 6px !important;
                  left: 2px !important;
                }
              }
            }
            &::before,
            &::after {
              margin-top: -15px !important;
              height: 32px !important;
              padding-top: 6px !important;
              border-top-width: 1px !important;
              border-bottom-width: 1px !important;
            }
          }
          .el-time-panel__footer {
            border-top-width: 1px !important;
            padding: 4px !important;
            height: 36px !important;
            line-height: 25px !important;
            .el-time-panel__btn {
              line-height: 28px !important;
              padding: 0 5px !important;
              margin: 0 5px !important;
              font-size: 12px !important;
            }
          }
        }
      }
    }
    .el-date-picker__header {
      margin: 12px !important;
      .el-picker-panel__icon-btn {
        font-size: 12px !important;
        margin-top: 8px !important;
      }
      .el-date-picker__header-label {
        font-size: 16px !important;
        padding: 0 5px !important;
        line-height: 22px !important;
      }
    }
    .el-picker-panel__content {
      width: 292px !important;
      .el-date-table {
        font-size: 12px !important;
        th {
          padding: 5px !important;
          border-bottom-width: 1px !important;
        }
        td {
          width: 32px !important;
          padding: 4px 0 !important;
          div {
            padding: 3px 0 !important;
            height: 30px !important;
            span {
              width: 24px !important;
              height: 24px !important;
              line-height: 24px !important;
            }
          }
        }
      }
    }
  }
  .el-time-panel {
    background-color: #040c1a !important;
    border-color: #1f314e !important;
    .el-time-spinner__item {
      color: #fff;
    }
    .el-time-spinner__item.active:not(.disabled) {
      color: #409eff;
    }
    .el-time-spinner__item:hover:not(.disabled):not(.active) {
      background-color: transparent;
    }
    .el-time-panel__btn {
      color: #e2e2e2;
    }
    .el-time-panel__btn.confirm {
      color: #2445da;
    }
  }
  .el-picker-panel__footer {
    .el-button:first-child {
      display: none !important;
    }
    .el-button--default {
      background-color: #0d3272 !important;
      color: #d9dee6;
    }

    border-top-width: 1px !important;
    padding: 4px !important;
    .el-button {
      padding: 7px 15px !important;
      font-size: 12px !important;
      border-radius: 3px !important;
      border-width: 1px !important;
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .date-poper_time {
    width: @zoomIndex * 322px !important;
    border-width: @zoomIndex * 1px !important;
    box-shadow: 0 @zoomIndex * 2px @zoomIndex * 12px 0 rgb(0 0 0 / 10%) !important;
    border-radius: @zoomIndex * 4px !important;
    line-height: @zoomIndex * 30px !important;
    margin: @zoomIndex * 5px 0 !important;
    .el-picker-panel__body {
      .el-date-picker__time-header {
        border-bottom-width: @zoomIndex * 1px !important;
        font-size: @zoomIndex * 12px !important;
        padding: @zoomIndex * 8px @zoomIndex * 5px @zoomIndex * 5px !important;
        .el-date-picker__editor-wrap {
          padding: 0 @zoomIndex * 5px !important;
          .el-input {
            font-size: @zoomIndex * 13px !important;
            .el-input__inner {
              height: @zoomIndex * 32px !important;
              line-height: @zoomIndex * 32px !important;
            }
          }
          .el-time-panel {
            margin: @zoomIndex * 5px 0 !important;
            border-width: @zoomIndex * 1px !important;
            box-shadow: 0 @zoomIndex * 2px @zoomIndex * 12px 0 rgb(0 0 0 / 10%) !important;
            border-radius: @zoomIndex * 2px !important;
            width: @zoomIndex * 180px !important;
            .el-time-panel__content {
              .el-time-spinner__wrapper {
                max-height: @zoomIndex * 190px !important;
                .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
                  padding-bottom: @zoomIndex * 15px !important;
                }
                .el-time-spinner__list {
                  &::before,
                  &::after {
                    height: @zoomIndex * 80px !important;
                  }
                  .el-time-spinner__item {
                    height: @zoomIndex * 32px !important;
                    line-height: @zoomIndex * 32px !important;
                    font-size: @zoomIndex * 12px !important;
                  }
                }
                .el-scrollbar__bar {
                  right: @zoomIndex * 2px !important;
                  bottom: @zoomIndex * 2px !important;
                  border-radius: @zoomIndex * 4px !important;
                  &.is-horizontal {
                    height: @zoomIndex * 6px !important;
                    left: @zoomIndex * 2px !important;
                  }
                }
              }
              &::before,
              &::after {
                margin-top: @zoomIndex * -15px !important;
                height: @zoomIndex * 32px !important;
                padding-top: @zoomIndex * 6px !important;
                border-top-width: @zoomIndex * 1px !important;
                border-bottom-width: @zoomIndex * 1px !important;
              }
            }
            .el-time-panel__footer {
              border-top-width: @zoomIndex * 1px !important;
              padding: @zoomIndex * 4px !important;
              height: @zoomIndex * 36px !important;
              line-height: @zoomIndex * 25px !important;
              .el-time-panel__btn {
                line-height: @zoomIndex * 28px !important;
                padding: 0 @zoomIndex * 5px !important;
                margin: 0 @zoomIndex * 5px !important;
                font-size: @zoomIndex * 12px !important;
              }
            }
          }
        }
      }
      .el-date-picker__header {
        margin: @zoomIndex * 12px !important;
        .el-picker-panel__icon-btn {
          font-size: @zoomIndex * 12px !important;
          margin-top: @zoomIndex * 8px !important;
        }
        .el-date-picker__header-label {
          font-size: @zoomIndex * 16px !important;
          padding: 0 @zoomIndex * 5px !important;
          line-height: @zoomIndex * 22px !important;
        }
      }
      .el-picker-panel__content {
        width: @zoomIndex * 292px !important;
        .el-date-table {
          font-size: @zoomIndex * 12px !important;
          th {
            padding: @zoomIndex * 5px !important;
            border-bottom-width: @zoomIndex * 1px !important;
          }
          td {
            width: @zoomIndex * 32px !important;
            padding: @zoomIndex * 4px 0 !important;
            div {
              padding: @zoomIndex * 3px 0 !important;
              height: @zoomIndex * 30px !important;
              span {
                width: @zoomIndex * 24px !important;
                height: @zoomIndex * 24px !important;
                line-height: @zoomIndex * 24px !important;
              }
            }
          }
        }
      }
    }
    .el-picker-panel__footer {
      border-top-width: @zoomIndex * 1px !important;
      padding: @zoomIndex * 4px !important;
      .el-button {
        padding: @zoomIndex * 7px @zoomIndex * 15px !important;
        font-size: @zoomIndex * 12px !important;
        border-radius: @zoomIndex * 3px !important;
        border-width: @zoomIndex * 1px !important;
      }
    }
  }
}
</style>