<!-- 操作介绍 -->
<template>
  <transition name="el-fade-in-linear">
    <div class="operation-introduced" v-show="show">
      <div class="header"></div>
      <div class="main">
        <div class="main-left">
          <!-- 左侧头部 -->
          <div class="main-left-top">
            <div class="left-top-content">
              <div class="content-title">实时信息显示区</div>
              <div class="content-main">
                <div class="mb8" style="font-size: 14px">显示实时信息</div>
                <div
                  class="content-main-item mb5"
                  v-for="(item, index) in IMType"
                  :key="index"
                >
                  <div
                    class="item-left mr5"
                    :style="{ backgroundColor: colorList[item.value] }"
                  ></div>
                  <div class="item-right">{{ item.label }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="main-left-bot mt8">
            <div class="bot-item mb7">
              <div class="bot-content">
                <div class="content-title">舱内监控</div>
                <div class="content-main">
                  <div class="mb8">
                    视频监测的方式实时监测固定机场内部的变化情况
                  </div>
                </div>
              </div>
            </div>
            <div class="bot-item mb7">
              <div class="bot-content">
                <div class="content-title">舱外监控</div>
                <div class="content-main">
                  <div class="mb5">
                    视频监测的方式实时监测固定机场内部的变化情况
                  </div>
                  <!-- <div class="mb3">
                    <span class="mr5">1</span>
                    <span>恶劣天气，暴雨风雪</span>
                  </div>
                  <div class="mb5">
                    <span class="mr5">2</span>
                    <span>防止他人破坏机场</span>
                  </div>
                  <div class="mb5">
                    <span class="mr5">3</span>
                    <span>监控机场的其他情况</span>
                  </div> -->
                </div>
              </div>
            </div>
            <div class="bot-item">
              <div class="bot-content">
                <div class="content-title">切换地图功能区</div>
                <div class="content-main">
                  <div class="mb8">展示任务围栏，航线，飞机实时位置</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="main-center">
          <div class="center-top">
            <img :src="keyboardImg" alt="" />
          </div>

          <div class="center-bot">
            <div class="center-bot-content">
              <div class="content-title" style="width: 120px">
                飞行数据显示区
              </div>
              <div class="content-text ml20">
                展示飞行距离，飞行高度等实时飞行数据
              </div>
            </div>
          </div>
        </div>

        <div class="main-right">
          <div class="main-right-top">
            <div class="top-content">
              <div class="content-title mb10" style="justify-content: flex-end">
                成果展示
              </div>
              <div class="content-main">
                <div class="mb8">显示成果信息</div>
                <div class="mb5">照片信息</div>
                <div class="mb5">经纬度信息</div>
                <div class="mb5">时间数据信息</div>
              </div>
            </div>
          </div>
          <div class="main-right-bot mt8" style="height: 309px">
            <div class="top-content">
              <div class="content-title mb10" style="justify-content: flex-end">
                无人机操控区
              </div>
              <div class="content-main">
                <div class="mb8">无人机的各项操作</div>
                <div class="mb5">全自动的飞机控制</div>
                <div class="mb5">手动的飞机控制</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关闭按钮 -->
      <div class="shut-icon" @click="shut">
        <i class="el-icon-error"></i>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  props: {
    show: Boolean,
  },
  data() {
    return {
      isShow: false,
      keyboardImg: require("@/assets/img/remote_sensing10.png"),

      IMType: [
        { label: "红色严重警告", value: "1" },
        { label: "黄色警告", value: "2" },
        { label: "绿色正常数据信息", value: "3" },
      ],
      colorList: {
        1: "rgba(29, 245, 14, 1)",
        2: "rgba(222, 245, 14, 1)",
        3: "#FC0900",
      },
    };
  },
  methods: {
    shut: function () {
      this.$emit("update:show", false);
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .operation-introduced {
    min-width: @radio * 1200px !important;
    .header {
      height: @radio * 50px !important;
    }

    .content-title {
      border-radius: @radio * 10px !important;
      height: @radio * 32px !important;
      padding: 0 @radio * 16px !important;
      font-size: @radio * 12px !important;
      margin-bottom: @radio * 10px !important;
    }

    .main {
      @heights: @radio * 90px;
      height: calc(100% - @heights) !important;
      padding: @radio * 20px !important;
      .main-left {
        min-width: @radio * 282px !important;
        max-width: @radio * 282px !important;
        .main-left-top {
          border-radius: @radio * 8px !important;
          .left-top-content {
            width: @radio * 120px !important;
            margin-left: @radio * 20px !important;
            .content-main {
              font-size: @radio * 12px !important;
              .content-main-item {
                .item-left {
                  width: @radio * 10px !important;
                  height: @radio * 10px !important;
                  border-radius: @radio * 2px !important;
                }
              }
            }
          }
        }
        .main-left-bot {
          @botH: @radio * 20px;
          height: calc(60% - @botH) !important;
          .bot-item {
            @heightItem: @radio * 16px;
            height: calc(33.333333% - @heightItem) !important;
            border-radius: @radio * 8px !important;
            .bot-content {
              margin-left: @radio * 20px !important;
              width: @radio * 120px !important;
              .content-main {
                font-size: @radio * 12px !important;
              }
            }
          }
        }
      }

      .main-center {
        width: 100%;
        .center-top {
          top: -100px;
        }
        .center-bot {
          width: 600px;
          height: 87px;
          border-radius: 8px;
          .center-bot-content {
            margin-top: -20px;
          }
        }
      }

      .main-right {
        min-width: 282px;
        max-width: 282px;
        .main-right-top,
        .main-right-bot {
          height: calc(100% - 335px);
          border-radius: 8px;
          .top-content {
            width: 120px;
            margin-right: 20px;
            .content-main {
              font-size: 12px;
            }
          }
        }
      }
    }

    .shut-icon {
      right: 30px;
      top: 10px;
      font-size: 48px;
    }
  }
}

.operation-introduced {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  //   background-color: rgba(0, 0, 0, 0.8);
  min-width: 1200px;
  z-index: 25;
  .header {
    height: 50px;
  }

  .content-title {
    // border: 2px solid blue;
    border-radius: 10px;
    display: flex;
    align-items: center;
    height: 32px;
    // color: #5c83f8;
    font-weight: 700;
    padding: 0 16px;
    font-size: 12px;
    margin-bottom: 10px;
    // background-color: #000000;
  }

  .main {
    display: flex;
    height: calc(100% - 90px);
    padding: 20px;
    justify-content: space-between;
    .main-left {
      min-width: 282px;
      max-width: 282px;
      .main-left-top {
        position: relative;
        height: 40%;
        // background-color: rgba(204,204,204,0.4);
        // border: 6px solid rgba(0,0,255, 0.6);
        border-radius: 8px;
        .left-top-content {
          position: absolute;
          left: 100%;
          top: 0;
          width: 120px;
          margin-left: 20px;
          .content-main {
            font-size: 12px;
            // color: #fff;
            .content-main-item {
              display: flex;
              align-items: center;
              .item-left {
                width: 10px;
                height: 10px;
                border-radius: 2px;
              }
            }
          }
        }
      }
      .main-left-bot {
        height: calc(60% - 20px);
        .bot-item {
          height: calc(33.333333% - 16px);
          // background-color: rgba(204,204,204,0.4);
          // border: 6px solid rgba(0,0,255, 0.6);
          border-radius: 8px;
          position: relative;
          .bot-content {
            position: absolute;
            left: 100%;
            top: 0;
            margin-left: 20px;
            width: 120px;
            .content-main {
              font-size: 12px;
              // color: #fff;
            }
          }
        }
      }
    }

    .main-center {
      width: 100%;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      .center-top {
        position: fixed;
        top: -100px;
        left: 0;
        bottom: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          max-width: 100%;
        }
      }
      .center-bot {
        width: 600px;
        height: 87px;
        // background-color: rgba(204,204,204,0.4);
        // border: 6px solid rgba(0,0,255, 0.6);
        border-radius: 8px;
        position: relative;
        .center-bot-content {
          position: absolute;
          bottom: 100%;
          left: 0;
          margin-top: -20px;
          width: 100%;
          display: flex;
          align-items: center;
          .content-text {
            font-size: 12px;
            // color: #fff;
          }
        }
      }
    }

    .main-right {
      min-width: 282px;
      max-width: 282px;
      .main-right-top,
      .main-right-bot {
        // width: 100%;
        position: relative;
        height: calc(100% - 335px);
        // background-color: rgba(204,204,204,0.4);
        border-radius: 8px;
        // border: 6px solid rgba(0,0,255, 0.6);
        .top-content {
          position: absolute;
          right: 100%;
          top: 0;
          width: 120px;
          margin-right: 20px;
          .content-main {
            font-size: 12px;
            // color: #fff;
            width: 100%;
            text-align: right;
          }
        }
      }
    }
  }

  .shut-icon {
    position: fixed;
    right: 30px;
    top: 10px;
    color: #cccccc;
    font-size: 48px;
    z-index: 25;
  }
}
</style>