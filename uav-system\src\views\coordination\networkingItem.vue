<template>
  <div class="networkingItem">
    <el-container>
      <el-aside>
        <div class="header">{{ groupLanguage.device.title }}</div>
        <el-button
          icon="el-icon-arrow-left"
          @click="backEvent"
          class="backBtn"
        ></el-button>
        <div class="title">
          <div class="title-1">{{ coordinationData.name }}</div>
          <div class="title-2">
            <span style="color: white">{{ deviceList.length }}</span
            >{{ groupLanguage.device.device }}
          </div>
          <div class="title-3">
            <el-button>{{ groupLanguage.device.toTask }}</el-button>
          </div>
        </div>
        <div class="deviceListDiv">
          <el-row
            v-for="(item, index) in deviceList"
            :key="item.sn_id"
            :class="item.sn_id == snId ? 'active' : ''"
            @click.native="clickDeviceItem(item.sn_id)"
          >
            <el-col :span="2">{{ index + 1 }}</el-col>
            <el-col :span="12">{{ item.name }}</el-col>
            <el-col
              class="stateClass"
              style="text-align: right"
              :span="8"
              :class="
                item.planState == 1
                  ? 'fontColor'
                  : item.planState == 2
                  ? 'fontColor-1'
                  : 'fontColor-2'
              "
              @click="onclickEvent(item)"
              >{{ item.planState | planStateFormat }}</el-col
            >
            <el-col :span="2" class="stateClass" @click="onclickEvent(item)"
              ><i
                class="el-icon-arrow-right"
                :class="
                  item.planState == 1
                    ? 'fontColor'
                    : item.planState == 2
                    ? 'fontColor-1'
                    : 'fontColor-2'
                "
              ></i
            ></el-col>
          </el-row>
        </div>
      </el-aside>
      <el-main>
        <div class="main-content">
          <div
            class="mian-content-item"
            :style="style"
            v-for="item in videoList"
            :key="item.sn_id"
            @click="clickDeviceItem(item.sn_id)"
          >
            <webrtc-video
              :videoItem="item"
              ref="webrtcVideo"
              :class="snId == item.sn_id ? 'active1' : ''"
              :videoLanguage="groupLanguage.video"
            ></webrtc-video>
          </div>
        </div>
        <div class="main-header">
          <el-button
            @click="changeView(4)"
            :class="viewCode == 4 ? 'active' : ''"
          >
            <el-row :gutter="5">
              <el-col :span="12"><div class="item-1"></div></el-col>
              <el-col :span="12"><div class="item-1"></div></el-col>
            </el-row>
            <el-row :gutter="5"
              ><el-col :span="12"><div class="item-1"></div></el-col>
              <el-col :span="12"><div class="item-1"></div></el-col
            ></el-row>
            <!-- <el-row>4 视 图</el-row> -->
          </el-button>
          <el-button
            @click="changeView(6)"
            :class="viewCode == 6 ? 'active' : ''"
          >
            <el-row :gutter="2">
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
            </el-row>
            <el-row :gutter="2">
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
            </el-row>
            <!-- <el-row>6 视 图</el-row> -->
          </el-button>
          <el-button
            class="nine"
            @click="changeView(9)"
            :class="viewCode == 9 ? 'active' : ''"
          >
            <el-row :gutter="2">
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
            </el-row>
            <el-row :gutter="2">
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
            </el-row>
            <el-row :gutter="2">
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
              <el-col :span="8"><div class="item-1"></div></el-col>
            </el-row>
            <!-- <el-row>9 视 图</el-row> -->
          </el-button>
          <span class="main-foot">
            <el-pagination
              @current-change="handleCurrentChange"
              layout="prev, pager, next"
              :prev-text="groupLanguage.device.previous"
              :next-text="groupLanguage.device.next"
              :page-size.sync="viewCode"
              :current-page="page"
              :total="deviceList.length"
              background
            >
            </el-pagination>
          </span>
        </div>
      </el-main>
    </el-container>
  </div>
</template>
<script>
// ffmpeg -re -rtsp_transport tcp -i "rtsp://admin:hk123456@*************:554" -f flv -vcodec libx264 -vprofile baseline -acodec aac -ar 44100 -strict -2 -ac 1 -f flv -flvflags no_duration_filesize  -s 1280x720 -q 10 "rtmp://ab.walkera.cn/live/abc"
import webrtcVideo from "./components/webrtcVideo";
export default {
  name: "networkingItem",
  components: {
    webrtcVideo,
  },
  data() {
    return {
      coordinationData: "",
      deviceList: [],
      viewCode: 4,
      videoList: [],
      style: {
        width: "48%",
        height: "49.9%",
        margin: "0.1% 1%  ",
      },
      player: "",
      page: 1,
      url: "",
      snId: "",
    };
  },
  computed: {
    groupLanguage() {
      return this.$languagePackage.coordination;
    },
  },
  async mounted() {
    await this.getDevice();
    await this.getVideoList();
    // let webrtcVideo=this.$refs.webrtcVideo
    // for (let index = 0; index < webrtcVideo.length; index++) {
    //   webrtcVideo[index].initVideo()

    // }
  },
  methods: {
    //获取设备列表
    // async getDevice() {
    //   let data = {
    //     page: 0,
    //     size: 200,
    //     type: 0,
    //   };
    //   data.pmd = data.page.toString() + data.type.toString();
    //   await requestHttp("deviceList", data).then((res) => {
    //     this.deviceList = res.data.list;
    //     for (let index = 0; index < this.deviceList.length; index++) {
    //       this.deviceList[index].videoType = 3;
    //       if (
    //         this.deviceList[index].is_pull_on &&
    //         this.deviceList[index].is_push_on
    //       ) {
    //         this.deviceList[index].planState = 1;
    //       } else if (
    //         !this.deviceList[index].is_pull_on &&
    //         this.deviceList[index].is_push_on
    //       ) {
    //         this.deviceList[index].planState = 2;
    //       } else if (!this.deviceList[index].is_push_on) {
    //         this.deviceList[index].planState = 0;
    //       }
    //     }
    //     this.deviceList[0].stream_uav_list[0] =
    //       "https://ab.walkera.cn/ms/index/api/webrtc?app=live&stream=abc&type=play";
    //   });
    // },
    async getDevice() {
      let list = JSON.parse(localStorage.getItem("coordinate"));
      let id = this.$route.query.id;
      let i = list.findIndex((item) => {
        return item.id == id;
      });
      this.coordinationData = list[i];
      this.deviceList = this.coordinationData.deviceList;
      for (let index = 0; index < this.deviceList.length; index++) {
        this.deviceList[index].videoType = 3;
        if (
          this.deviceList[index].is_pull_on &&
          this.deviceList[index].is_push_on
        ) {
          this.deviceList[index].planState = 1;
        } else if (
          !this.deviceList[index].is_pull_on &&
          this.deviceList[index].is_push_on
        ) {
          this.deviceList[index].planState = 2;
        } else if (!this.deviceList[index].is_push_on) {
          this.deviceList[index].planState = 0;
        }
        if (this.deviceList[index].stream_in_list) {
          this.deviceList[index].stream_in_list[0] = this.deviceList[
            index
          ].stream_in_list[0].replace(
            "rtmp://app.walkera.cn/wk/a/",
            "https://app.walkera.cn/ms/index/api/webrtc?app=/wk/a/"
          );
          this.deviceList[index].stream_in_list[0] = this.deviceList[
            index
          ].stream_in_list[0].replace("/i1", "/&stream=i1&type=play");
        }
        if (this.deviceList[index].stream_out_list) {
          this.deviceList[index].stream_out_list[0] = this.deviceList[
            index
          ].stream_out_list[0].replace(
            "rtmp://app.walkera.cn/wk/a/",
            "https://app.walkera.cn/ms/index/api/webrtc?app=/wk/a/"
          );
          this.deviceList[index].stream_out_list[0] = this.deviceList[
            index
          ].stream_out_list[0].replace("/o1", "/&stream=o1&type=play");
        }
        if (this.deviceList[index].stream_uav_list) {
          this.deviceList[index].stream_uav_list[0] = this.deviceList[
            index
          ].stream_uav_list[0].replace(
            "rtmp://app.walkera.cn/wk/a/",
            "https://app.walkera.cn/ms/index/api/webrtc?app=/wk/a/"
          );
          this.deviceList[index].stream_uav_list[0] = this.deviceList[
            index
          ].stream_uav_list[0].replace("/u1", "/&stream=u1&type=play");
        }
      }
    },
    //默认列表
    getVideoList() {
      this.videoList = [];
      let num = (this.page - 1) * this.viewCode;
      let len =
        num + this.viewCode > this.deviceList.length
          ? this.deviceList.length
          : num + this.viewCode;
      for (let index = num; index < len; index++) {
        this.videoList.push(this.deviceList[index]);
      }
    },
    //点击切换视图
    changeView(index) {
      this.viewCode = index;
      this.page = 1;
      if (index == 4) {
        this.style = {
          width: "48%",
          height: "49.9%",
          margin: " 0.1% 1%",
        };
      } else if (index == 6) {
        this.style = {
          width: "32.3%",
          height: "49.9%",
          margin: "0.1% 0.5%",
        };
      } else if (index == 9) {
        this.style = {
          width: "32.3%",
          height: "33.2%",
          margin: "0.1% 0.5%",
        };
      }
      this.getVideoList();
    },
    //当前页改变
    handleCurrentChange(e) {
      this.page = e;
      this.getVideoList();
    },
    //返回协同组网
    backEvent() {
      this.$router.push("/coordination");
    },
    //点击设备列表
    onclickEvent(item) {
      console.log(item);
    },
    //点击设备
    clickDeviceItem(sn_id) {
      this.snId = sn_id;
      let index = this.videoList.findIndex((item) => {
        return item.sn_id == sn_id;
      });
      if (index == -1) {
        let a = this.deviceList.findIndex((item) => {
          return item.sn_id == sn_id;
        });
        let b = parseInt((a + 1) / this.viewCode);
        if ((a + 1) % this.viewCode == 0) {
          this.handleCurrentChange(b);
        } else {
          this.handleCurrentChange(b + 1);
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
.networkingItem {
  width: 100%;
  height: 100%;
  .el-container {
    width: 98%;
    height: 98%;
    margin: 0 1%;
    .el-aside {
      width: 18% !important;
      height: 100%;
      border-radius: 6px;
      min-width: 350px;
      .header {
        font-size: 20px;
        text-align: center;
        margin: 5% 0;
        letter-spacing: 2px;
      }
      .backBtn {
        font-size: xx-large;
        position: absolute;
        top: 2%;
        left: 2%;
        padding: 0;
      }
      .title {
        width: 96%;
        border-radius: 4px;
        display: flex;
        margin: 0 2%;
        .title-1 {
          width: 30%;
          font-size: 18px;
          margin: 4%;
          white-space: normal;
        }
        .title-2 {
          width: 30%;
          font-size: 12px;
          margin-top: 5%;
          white-space: normal;
        }
        .title-3 {
          width: 40%;
          text-align: right;
          .el-button {
            font-size: 14px;
            font-weight: 550;
            padding: 13px;
            margin: 5% 0;
            margin-right: 5%;
            white-space: normal;
          }
        }
      }
      .deviceListDiv {
        width: 96%;
        margin: 2%;
        height: auto;
        overflow: auto;
        .el-row {
          border-radius: 4px;
          padding: 5%;
          padding-right: 2%;
          margin-bottom: 2%;
          cursor: pointer;
          .stateClass {
            cursor: pointer;
          }
        }
      }
    }
    .el-main {
      width: 70%;
      height: 100%;
      padding: 0;
      padding-left: 20px;
      .main-header {
        width: 100%;
        height: 5%;
        margin-top: 0.5%;
        .el-button {
          padding: 0;
          width: auto;
          height: auto;
          font-size: 16px;
          margin: 0 2%;
          margin-top: 10px;
          .el-row {
            width: 30px;
            height: 7px;
            margin-bottom: 5px;
            .el-col {
              height: 100%;
              .item-1 {
                height: 100%;
                border-radius: 4px;
              }
            }
          }
        }
        .nine {
          .el-row {
            width: 30px;
            height: 5px;
            margin-bottom: 3.33px;
          }
        }
        .main-foot {
          float: right;
          padding-top: 5px;
        }
      }
      .main-content {
        width: 100%;
        height: 94%;
        .mian-content-item {
          display: inline-block;
          position: relative;
          border-radius: 8px;

          .videoDiv {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            object-fit: fill;
          }
          .item-title {
            position: absolute;
            top: 0;
            left: 0;
            width: 96%;
            height: auto;
            padding: 10px 2%;
            font-size: 16px;
            color: white;
            letter-spacing: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
          }
          .item-button {
            position: absolute;
            top: 40px;
            left: 1%;
            height: auto;
            .el-button {
              display: block;
              margin: 0;

              border-radius: 8px;
              padding: 12px 0;
              padding-left: 7%;
              width: 90px;
              text-align: left;
              margin: 5px 0;
              opacity: 0.2;

              &:hover {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="less">
.networkingItem {
  .el-container {
    .el-aside {
      i[class*=" el-icon-"],
      i[class^="el-icon-"] {
        font-weight: 1000 !important;
      }
      .deviceListDiv {
        .el-row {
          .el-col {
            i[class*=" el-icon-"],
            i[class^="el-icon-"] {
              font-weight: 1000 !important;
            }
          }
        }
        // scrollbar-width: thin !important;
        // /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
        // -ms-overflow-style: none !important;
        // scrollbar-color: #777777 #ccc;
        &::-webkit-scrollbar {
          width: 3px;
        }
        &::-webkit-scrollbar-track {
          background-color: #ccc;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
        &::-webkit-scrollbar-thumb {
          background-color: #777777;
          -webkit-border-radius: 2em;
          -moz-border-radius: 2em;
          border-radius: 2em;
        }
      }
    }
    .el-main {
      .main-foot {
        .el-pagination {
          .btn-next,
          .btn-prev {
            padding: 0 10px !important;
          }
        }
      }
    }
  }
}
</style>