<template>
  <div id="routeplan-layer">
    <el-collapse v-model="typeCode">
      <el-collapse-item :title="toolLanguage.limitFly" name="1">
        <el-row class="type-item">
          <el-col :span="19">
            <div class="type-item-1"></div>
            <div>{{ toolLanguage.noflyzone }}</div>
          </el-col>
          <el-col :span="5">
            <el-switch
              v-model="code1"
              active-color="#124093"
              inactive-color="#5c5c5c"
              @change="changeState(code1, 1)"
            >
            </el-switch>
          </el-col>
        </el-row>
        <el-row class="type-item">
          <el-col :span="19">
            <div class="type-item-2"></div>
            <div>{{ toolLanguage.limitHeight }}</div>
          </el-col>
          <el-col :span="5">
            <el-switch
              v-model="code2"
              active-color="#124093"
              inactive-color="#5c5c5c"
              @change="changeState(code2, 3)"
            >
            </el-switch>
          </el-col>
        </el-row>

        <el-row class="type-item">
          <el-col :span="19">
            <div class="type-item-3"></div>
            <div>{{ toolLanguage.airportBuffer }}</div>
          </el-col>
          <el-col :span="5">
            <el-switch
              v-model="code3"
              active-color="#124093"
              inactive-color="#5c5c5c"
              @change="changeState(code3, 4)"
            >
            </el-switch>
          </el-col>
        </el-row>
        <el-row class="type-item">
          <el-col :span="19">
            <div class="type-item-4"></div>
            <div>{{ toolLanguage.temporaryNofly }}</div>
          </el-col>
          <el-col :span="5">
            <el-switch
              v-model="code4"
              active-color="#124093"
              inactive-color="#5c5c5c"
              @change="changeState(code4, 2)"
            >
            </el-switch>
          </el-col>
        </el-row>
      </el-collapse-item>
      <!-- <el-collapse-item title="机场图层" name="2"> </el-collapse-item> -->
      <el-collapse-item :title="toolLanguage.weatherLayer" name="3">
        <el-row class="weatherDiv">
          <el-col :span="19">
            <el-image :src="weatherImg"></el-image>
            {{ toolLanguage.RealTimeWeather }}
          </el-col>
          <el-col :span="5">
            <el-switch
              v-model="code"
              active-color="#124093"
              inactive-color="#5c5c5c"
              @change="changeWea"
            >
            </el-switch>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
export default {
  name: "routeplanLayer",
  props: {
    weatherCode: {
      type: Boolean,
      default: true,
    },
    toolLanguage: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      typeCode: "",
      code: false,
      code1: false,
      code2: false,
      code3: false,
      code4: false,
      weatherImg: require("../../../assets/img/routeplan/weatherImg.png"),
    };
  },
  methods: {
    //天气状态改变
    changeWea(e) {
      this.$emit("weacode", e);
    },
    //限飞区状态改变
    changeState(code, index) {
      let zoneState = {
        state: code,
        index: index,
      };
      this.$emit("zoneState", zoneState);
    },
  },
  watch: {
    weatherCode(e) {
      this.code = e;
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  #routeplan-layer {
    border-radius: @zoomIndex * 6px !important;
    .el-collapse {
      .el-collapse-item {
        .type-item {
          font-size: @zoomIndex * 16px !important;
          .type-item-1,
          .type-item-2,
          .type-item-3,
          .type-item-4 {
            width: @zoomIndex * 15px !important;
            height: @zoomIndex * 15px !important;
            border-radius: @zoomIndex * 2px !important;
          }
        }
        .weatherDiv {
          font-size: @zoomIndex * 18px !important;
        }
      }
    }
  }
}
#routeplan-layer {
  width: 100%;
  height: auto;
  border-radius: 6px;
  .el-collapse {
    width: 96%;
    margin: 2%;
    border: none;
    .el-collapse-item {
      margin-top: 1%;
      .type-item {
        margin: 1%;
        margin-right: 0;
        margin-top: 5%;
        font-size: 16px;
        .el-col {
          display: flex;
        }
        .type-item-1,
        .type-item-2,
        .type-item-3,
        .type-item-4 {
          margin-top: 3%;
          width: 15px;
          height: 15px;
          border-radius: 2px;
          margin-right: 5%;
        }
        .el-switch {
          width: 100%;
          margin-top: 15%;
        }
      }
      .weatherDiv {
        margin: 1%;
        margin-top: 5%;
        font-size: 18px;
        .el-image {
          width: 10%;
          float: left;
          margin-right: 2%;
        }
        .el-switch {
          width: 100%;
        }
      }
    }
  }
}
</style>
<style lang="less">
#routeplan-layer {
  .el-collapse {
    .el-collapse-item {
      .el-collapse-item__header {
        padding-left: 10px;
        font-size: 18px;
        height: 40px;
        line-height: 40px;
        .el-collapse-item__arrow {
          display: none !important;
        }
      }
      .weatherDiv {
        .el-image {
          .el-image__inner {
            vertical-align: middle !important;
          }
        }
      }
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  #routeplan-layer {
    .el-collapse {
      .el-collapse-item {
        .el-collapse-item__header {
          height: @zoomIndex * 40px !important;
        line-height:@zoomIndex *  40px !important;
          padding-left: @zoomIndex * 10px !important;
          font-size: @zoomIndex * 18px !important;
        }
        .el-switch {
          .el-switch__core {
            width: @zoomIndex * 40px !important;
            height: @zoomIndex * 20px !important;
            border-radius: @zoomIndex * 10px !important;
          }
          .el-switch__core:after {
            width: @zoomIndex * 16px !important;
            height: @zoomIndex * 16px !important;
            top: @zoomIndex * 1px !important;
            left: @zoomIndex * 1px !important;
          }
        }
        .el-switch.is-checked .el-switch__core::after {
          left: 100% !important;
          margin-left: @zoomIndex * -16px !important;
        }
      }
    }
  }
}
</style>
