import {
    gcj02_to_wgs84,
    wgs84_to_gcj02
} from "@/utils/wgs84_to_gcj02";
import L from "leaflet";
let map = '' //地图
    //返回正射影像的点
export function renderPolyline(param) {
    cpRPA.setDistanceFn(distance);
    cpRPA.setLatlng2PxFn(latlng2Px);
    cpRPA.setPx2LatlngFn(px2Latlng);
    let {
        paths = undefined,
            stepRotate = 0,
            spaceInp = 5,
            amap = undefined

    } = param
    map = amap
    let points = cpRPA.setOptions({
        polygon: paths,
        rotate: parseFloat(stepRotate),
        space: parseFloat(spaceInp)
    })
    return points

}

function distance(p1, p2) {
    return L.latLng(p1.lat, p1.lng).distanceTo(L.latLng(p2.lat, p2.lng))
        //   let point1XYZ = Cesium.Cartesian3.fromDegrees(p1.lng, p1.lat, p1.height ? p1.height : 0)
        //   let point2XYZ = Cesium.Cartesian3.fromDegrees(p2.lng, p2.lat, p2.height ? p2.height : 0)
        //   return Cesium.Cartesian3.distance(point1XYZ, point2XYZ)
}

function latlng2Px(latlng) {
    return map.latLngToLayerPoint(L.latLng(latlng.lat, latlng.lng))
        //   let point = gcj02_to_wgs84(latlng.lng, latlng.lat);
        // let point_1 = Cesium.Cartesian3.fromDegrees(point[0], point[1], latlng.height)
        // return Cesium.SceneTransforms.wgs84ToWindowCoordinates(
        //     map.scene,
        //     point_1
        // )
}

function px2Latlng(px) {
    return map.layerPointToLatLng(L.point(px[0], px[1]))
    let cartesian2 = new Cesium.Cartesian2(px[0], px[1])
    let cartesian = map.camera.pickEllipsoid(
        cartesian2,
        map.scene.globe.ellipsoid
    );
    var cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    //弧度转经纬度
    let lng = Cesium.Math.toDegrees(cartographic.longitude); // 经度
    let lat = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
    let point = wgs84_to_gcj02(lng, lat)
    return {
        lat: point[1],
        lng: point[0]
    }
}