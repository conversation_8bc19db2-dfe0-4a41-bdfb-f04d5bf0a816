<!-- 无人机操作 -->
<template>
  <div class="">
    <put-away
      class="single-uav"
      v-model="isOpen"
      :styles="{ bottom: 0, right: 0 }"
      :buttonStyle="buttonStyle"
      :tooltipText="singleLanguage.text"
      ref="putAway"
    >
      <template v-slot:main>
        <div class="uav-operation-main">
          <!-- 头部 -->
          <div class="main-header">
            <div class="header-left mr15" @click="uploadRoute('')">
              {{ singleLanguage.upload }}
            </div>
            <!-- 拍照 -->
            <div class="header-cell mr15" @click="uavPhotograph">
              <img-icon
                name="video-stop-fff"
                hover="video-stop-hover"
                width="36"
              />
            </div>

            <!-- 录像 -->
            <div class="header-video mr15" @click="pictureRecording(null)">
              <div
                class="video-cell"
                :class="isVideo ? 'open-video-style' : ''"
              ></div>
            </div>
            <!--  -->
            <div class="" @click="cameraSet">
              <img-icon name="video-stop-fff3" width="28" />
            </div>
          </div>

          <!-- 中间 -->
          <div class="main-center">
            <!-- :class="item.key == uavOperateType ? 'select-center-style' : ''" -->
            <div
              class="center-cell"
              :style="item.style"
              v-for="(item, index) in uavOperateList"
              :key="index"
            >
              <el-button
                type="text"
                style="padding: 0; font-size: 12px; width: 100%; height: 100%"
                @click="cutUavEvent(item)"
              >
                {{ item.label }}
              </el-button>
            </div>
          </div>

          <!-- 底部 -->
          <div class="main-footer">
            <!-- 左边 -->
            <virtual-joystick
              width="70"
              leftProgressHiehg="100px"
              @operation="uavOperation"
              @slidingInput="leftSlidingInput"
              @slidingUp="leftSlidingUp"
              @undo="leftUndo"
            >
              <template v-slot:circle-center>
                <div class="circle-center"></div>
              </template>
              <template v-slot:title>
                <span class="uav-left-title"> {{ singleLanguage.uav }} </span>
              </template>
            </virtual-joystick>

            <!-- 右边圆 -->
            <virtual-joystick
              width="70"
              :leftSliding="false"
              leftProgressHiehg="100px"
              @operation="panTiltOperation"
              @slidingInput="rightSlidingInput"
              @undo="rightUndo"
              @slidingUp="rightSlidingUp"
              :interval="80"
              style="margin-right: 0px"
            >
              <template v-slot:circle-center>
                <div class="right-circle-center" @click="straighten">
                  {{ singleLanguage.yuntai }}
                </div>
              </template>

              <template v-slot:bottom>
                <div class="cradle-head">
                  <el-button @click="straighten" size="mini">{{
                    singleLanguage.center
                  }}</el-button>
                  <el-button
                    @click="keydownEvent({ keyCode: 221 }, true)"
                    size="mini"
                    >{{ singleLanguage.vertical }}</el-button
                  >
                  <!-- <div  @click="straighten">
                    <img-icon title="镜头回中"  name="yuntai-center" :size="24" class="head-img"/>
                  </div>
                  
                  <div class=""  @click="keydownEvent({keyCode: 221})">
                    <img-icon title="镜头垂直"  name="yuntai-bottom" :size="24" class="head-img ml10"/>
                  </div> -->
                </div>
              </template>
            </virtual-joystick>
          </div>
        </div>
      </template>

      <template v-slot:showContent>
        <div class="" style="color: #fff">{{ singleLanguage.text }}</div>
      </template>
    </put-away>
    <!-- 相机配置 -->
    <camera-config ref="customDialog"></camera-config>
    <el-dialog
      :title="singleLanguage.dialogContent.titleUpload"
      :visible.sync="dialogCode"
      width="40%"
      center
      :close-on-click-modal="false"
      :modal="false"
      :show-close="false"
      :close-on-press-escape="false"
    >
      <el-progress
        :text-inside="true"
        :stroke-width="26"
        :percentage="percent"
        status="success"
      ></el-progress>
      <div
        v-if="successCode"
        style="text-align: center; margin-top: 10px; color: #33e933"
      >
        {{ singleLanguage.dialogContent.uploadSuccess }}
      </div>
    </el-dialog>
    <el-dialog
      :title="singleLanguage.dialogContent.selectTitle"
      class="modelDialog"
      :visible.sync="dialogModel"
      width="282px"
      center
      :modal="false"
      :show-close="false"
    >
      <el-select
        v-model="modelValue"
        :placeholder="singleLanguage.dialogContent.placeholder"
        popper-class="modelSelect"
        @change="changeModelValue"
      >
        <el-option
          v-for="item in flightModeList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled"
        >
        </el-option>
      </el-select>
    </el-dialog>
  </div>
</template>

<script>
import slidingBlock from "../components/slidingBlock.vue";
import imgIcon from "@/components/imgIcon/index";
import putAway from "../components/putAway.vue";
import virtualJoystick from "../components/virtualJoystick.vue";
import { createTooltip } from "@/components/infoTip/index.js";
import cameraConfig from "../components/cameraConfig/index.vue";
import { getRouteParam } from "@/utils/getRouteParam.js";
export default {
  components: {
    slidingBlock,
    imgIcon,
    putAway,
    virtualJoystick,
    cameraConfig,
  },
  props: {
    websocket: [Function, Object],
  },
  data() {
    return {
      isOpen: true,
      equipmentInfo: {
        sn_id: "",
        type: "",
      },
      percent: 0,
      dialogCode: false,
      dialogModel: false,
      successCode: false,
      modelValue: "",
      flightModeList: "",
      buttonStyle: {
        left: "-24px",
        "border-top-left-radius": "5px",
        "border-bottom-left-radius": "5px",
      },
      isVideo: false, // 是否正在录制视频

      // 当前正在执行的操作
      isExecuteTask: false,
      uavOperateList: [],
      heartbeatTime: null,
      cameraZoom: 1,
      yuntaiValue: 1500,

      frontBack: 1500, // 虚拟摇杆前后
      leftRight: 1500, // 虚拟摇杆左右
      riseFall: 1500, // 上升下降
      turnLeftRight: 1500, // 机身左转右转
      once: 0,
      loopTime: "",
      waypoint_percent: 0,
      waypoint_status: "",
      chooseArm: false,
    };
  },
  beforeDestroy() {
    window.removeEventListener("keydown", this.keydownEvent);
  },
  created() {
    this.flightModeList = this.singleLanguage.flightModeList;
    this.uavOperateList = [
      {
        label: this.singleLanguage.modelChange,
        key: "suspend",
        clickEvent: this.changeModel,
      },
      {
        label: this.singleLanguage.unlock,
        key: "lockFlight",
        clickEvent: this.lockFlight,
      },
      {
        label: this.singleLanguage.lock,
        key: "lockFlight",
        clickEvent: this.lockFlight1,
      },
      {
        label: this.singleLanguage.takeOff,
        key: "takeOff",
        clickEvent: this.uavTakeOff,
      },
    ];
    window.addEventListener("keydown", this.keydownEvent);
    window.addEventListener("keyup", this.keyupEvent);
    this.cameraZoom =
      this.$store.state.equipment.staveTwoData.camera_zoom_value || 1;
    this.$store.commit("setWsMmessageFun", {
      key: "singleUav",
      message: this.disposeData,
    });
  },
  computed: {
    video_record_state() {
      return this.$store.state.equipment.staveTwoData.video_record_state;
    },
    flightCourse() {
      return this.$store.state.equipment.flightCourseInfo;
    },
    railInfo() {
      return this.$store.state.equipment.railInfo;
    },
    // 飞行模式
    // flightMode() {
    //   this.modelValue = this.$store.state.equipment.staveThreeData.flight_mode;
    //   return this.$store.state.equipment.staveThreeData.flight_mode;
    // },
    sortId() {
      return this.$store.state.equipment.staveThreeData.sort_id;
    },
    // 起飞/返航
    laterTook() {
      return this.$store.state.equipment.laterTook;
    },
    // 飞行任务
    flightTask() {
      return this.$store.state.equipment.flightTask;
    },
    cameraZoomvalue() {
      return this.$store.state.equipment.staveTwoData.camera_zoom_value || 0;
    },

    //开锁闭锁状态
    flight_arm() {
      // console.log(this.$store.state.equipment.staveThreeData.flight_arm);
      return this.$store.state.equipment.staveThreeData.flight_arm;
    },
    flight_mode() {
      return this.$store.state.equipment.staveThreeData.flight_mode;
    },
    flight_status() {
      return this.$store.state.equipment.staveThreeData.flight_status;
    },
    maps() {
      return this.$store.state.equipment.maps;
    },
    leafletMaps() {
      return this.$store.state.equipment.leafletMaps;
    },
    singleLanguage() {
      return this.$languagePackage.singleUav;
    },
    uavOperate() {
      return this.$languagePackage.navigation.uavOperation.uavOperateList;
    },
  },
  watch: {
    video_record_state: function (val) {
      let message = "";
      if (val == 1) {
        this.isVideo = true;
        message = this.singleLanguage.message.messageVideo;
      } else if (val === 0 && this.isVideo) {
        this.isVideo = false;
        message = this.singleLanguage.message.messageVideo1;
      }
      message &&
        this.$message({
          type: "success",
          message: message,
        });
    },
    waypoint_percent: function (val) {
      if (this.waypoint_status == 3 && val == 100) {
        this.percent = val;
        this.successCode = true;
        setTimeout(() => {
          this.successCode = false;
          this.dialogCode = false;
        }, 1500);
      }
      if (val != 100) {
        this.percent = val;
      }
    },
    flight_mode: function (val) {
      // this.modelValue = val;
    },
  },
  methods: {
    // 键盘松开
    keyupEvent: function (item) {
      let code = item.keyCode;
      // 相机云台
      let cameraList = [38, 40];

      // 虚拟遥感
      let virtualList = [73, 74, 75, 76, 65, 68, 87, 83];

      if (cameraList[code] !== -1) {
        this.rightUndo();
      } else if (virtualList[code] !== -1) {
        // 虚拟遥感定时发送
        // clearInterval(this.websocket.virtualTime);

        setTimeout(() => {
          this.virtualJoystickUp();
        }, 50);
      }

      clearInterval(this.keydownTime);
      this.keydownTime = null;
    },
    // 键盘按下事件
    keydownEvent: function (item, state) {
      let keyCode = item.keyCode;

      // 可按键数值
      let list = [
        73, 74, 75, 76, 65, 68, 87, 83, 48, 221, 38, 37, 39, 40, 189, 187,
      ];
      if (list.indexOf(keyCode) === -1) {
        clearInterval(this.keydownTime);
        return false;
      }

      //
      if (!this.downState && !state) {
        this.$confirm(this.uavOperate.keydownTip, this.uavOperate.tips, {
          confirmButtonText: this.uavOperate.sure,
          cancelButtonText: this.uavOperate.cancel,
          type: "warning",
        }).then(() => {
          this.downState = true;
        });
        return;
      }

      // 虚拟遥感停止定时发送
      let virtualList = [73, 74, 75, 76, 65, 68, 87, 83, 38, 40];
      if (virtualList.indexOf(keyCode) != -1) {
        clearInterval(this.websocket.virtualTime);
      }

      if (this.keydownTime) {
        return false;
      }

      // 按下即触发
      this.keyboardOrder(keyCode);

      if (state) {
        return false;
      }

      // 按下每200毫秒
      this.keydownTime = setInterval(() => {
        this.keyboardOrder(keyCode);
      }, 200);
    },

    keyboardOrder: function (keyCode) {
      let value = 40;

      // 如果未赋值
      if (!this.cameraZoom) {
        this.cameraZoom = this.cameraZoomvalue;
      }

      switch (keyCode) {
        case 73: // 无人机向前 i
          this.frontBack -= 15;
          if (this.frontBack <= 1000) {
            this.frontBack = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 3,
              value: this.frontBack,
            },
            401
          );
          break;
        case 74: // 无人机左飞 j
          this.leftRight -= 15;
          if (this.leftRight <= 1000) {
            this.leftRight = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 7,
              value: this.leftRight,
            },
            401
          );
          break;
        case 75: // 无人机后退 k
          this.frontBack += 15;
          if (this.frontBack >= 2000) {
            this.frontBack = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 4,
              value: this.frontBack,
            },
            401
          );
          break;
        case 76: // 无人机右飞 d
          this.leftRight += 15;
          if (this.leftRight >= 2000) {
            this.leftRight = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 8,
              value: this.leftRight,
            },
            401
          );
          break;

        case 65: // 无人机机身左转 a
          this.turnLeftRight -= 15;
          if (this.turnLeftRight <= 1000) {
            this.turnLeftRight = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 5,
              value: this.turnLeftRight,
            },
            401
          );
          break;
        case 68: // 无人机机身右转 d
          this.turnLeftRight += 15;
          if (this.turnLeftRight >= 2000) {
            this.turnLeftRight = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 6,
              value: this.turnLeftRight,
            },
            401
          );
          break;
        case 87: // 无人机上升 w
          this.riseFall += 15;
          if (this.riseFall >= 2000) {
            this.riseFall = 2000;
          }

          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 1,
              value: this.riseFall,
            },
            401
          );
          break;
        case 83: // 无人机下降 s
          this.riseFall -= 15;
          if (this.riseFall <= 1000) {
            this.riseFall = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 1,
              action_cmd: 2,
              value: this.riseFall,
            },
            401
          );
          break;

        case 48: // 镜头回正 0
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 1,
              value: 0,
            },
            401
          );
          break;
        case 221: // 镜头垂直 ]}
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 2,
              value: -90,
            },
            401
          );
          break;
        case 38: // 向前看 ↑
          this.yuntaiValue += value;
          if (this.yuntaiValue >= 2000) {
            this.yuntaiValue = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 3,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 37: // 向左看 ←
          this.yuntaiValue -= value;
          if (this.yuntaiValue <= 1000) {
            this.yuntaiValue = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 6,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 39: // 向右看 →
          this.yuntaiValue += value;
          if (this.yuntaiValue <= 2000) {
            this.yuntaiValue = 2000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 7,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 40: // 向下看 ↓
          this.yuntaiValue -= value;
          if (this.yuntaiValue <= 1000) {
            this.yuntaiValue = 1000;
          }
          this.websocket.manualSend(
            {
              cmd_type: 5,
              action_cmd: 4,
              value: this.yuntaiValue,
            },
            401
          );
          break;
        case 189: // 镜头缩小 -
          let minZoom = this.cameraZoom <= 1 ? 1 : this.cameraZoom--;
          this.websocket.manualSend(
            {
              cmd_type: 3,
              zoom_multiple: minZoom,
            },
            402
          );
          break;
        case 187: // 镜头放大 +=
          let maxZoom = this.cameraZoom >= 30 ? 30 : this.cameraZoom++;
          this.websocket.manualSend(
            {
              cmd_type: 3,
              zoom_multiple: maxZoom,
            },
            402
          );
          break;
        default:
          console.log("不需要执行操作----------->");
          break;
      }
    },

    // 相机设置
    cameraSet: function () {
      this.$refs.customDialog.show();
    },

    close: function (state) {
      this.isOpen = state;
    },

    // 处理websocket获得数据时
    disposeData: function (msg_id, data) {
      switch (msg_id) {
        case 200: // 设备认证
          this.equipmentInfo = data;
          break;
        case 401:
          type = data.cmd_type;
          // 飞行模式
          if (type == 4) {
            let item = this.uavOperateList[0];
            let actionValue = data.action_value;
            // 自动巡航
            if (actionValue == 3) {
              item.state = 1;
              item.label = this.uavOperate.suspend;
              this.messageSuccess(this.singleLanguage.message.toplane);
            } // 悬停
            else if (actionValue == 5) {
              item.state = 2;
              item.label = this.singleLanguage.message.continue;
              this.messageSuccess(this.singleLanguage.message.hover);
            } // 返航
            else if (actionValue == 6) {
              this.messageSuccess(this.singleLanguage.message.messageReturn);
            }
            //降落
            else if (actionValue == 9) {
              this.messageSuccess(this.singleLanguage.message.land);
            }
          } // 航线上传
          // else if (type == 8) {
          //   this.$emit("openFlightStep", 0);
          // }
          break;

        case 402:
          if (data.cmd_type == 1) {
            this.$message({
              type: "success",
              message: this.uavOperate.photographSuccss,
            });
          }
          break;
        case 434:
          this.waypoint_percent = data.waypoint_percent;
          this.waypoint_status = data.waypoint_status;
          break;
        case 432:
          if (!this.chooseArm) {
            this.modelValue = data.flight_mode;
          }
          break;
      }
    },

    messageSuccess: function (msg) {
      this.$message({
        type: "success",
        message: msg,
      });
    },

    getDateTime: function () {
      let date = new Date();
      return date.getTime();
    },

    cutPutAwayState: function () {
      this.isOpen = !this.isOpen;
    },

    // 拍照
    uavPhotograph: function () {
      let uav = this.equipmentInfo.stream_uav_list;
      // if(!this.sortId){
      //   return this.$message({
      //     message: "请先执行任务",
      //     type: "error"
      //   })
      // }

      if (!uav) {
        return this.$message({
          message: this.singleLanguage.message.errorPhoto,
          type: "error",
        });
      }

      let data = {
        cmd_type: 1,
        cap_sort_id: this.sortId || "wk_003",
        cap_rtp_stream: uav[0],
        cap_timeout_sec: 10,
      };

      this.websocket.manualSend(data, 402);
      // this.websocket.manualSend(data, 220);
    },

    // 录像
    pictureRecording: function (ask_type) {
      let uav = this.equipmentInfo.stream_uav_list;
      if (!uav) {
        return this.$message({
          message: this.singleLanguage.message.errorVideo,
          type: "error",
        });
      }

      // if (!this.sortId) {
      //   return this.$message({
      //     message: "请先执行任务",
      //     type: "error",
      //   });
      // }

      let data = {
        cmd_type: 2,
        rec_rtp_stream: this.equipmentInfo.stream_uav_list[0], // 拉流地址
        rec_ask_type: ask_type || (this.isVideo ? 30 : 20), //
        sort_id: this.sortId,
        rec_sort_name: "",
      };

      this.websocket.manualSend(data, 402);
      // this.websocket.manualSend(data, 230);
    },

    // 上传航线
    uploadRoute: function (message) {
      // this.$emit("openFlightStep", 0);
      // return ;
      if (this.flightTask) {
        this.$message({
          type: "error",
          message: this.singleLanguage.message.messageUpload,
        });
        return false;
      }
      if (this.$store.state.equipment.elevationcode) {
        this.$message({
          type: "error",
          message: this.uavOperate.elevationLoading,
        });
        return false;
      }
      createTooltip({
        message: this.singleLanguage.message.sureUpload,
        confirm: () => {
          // 上传航线
          let query = this.$route.query;
          if (query.state == 2) {
            let wipeOut = sessionStorage.getItem("wipeOut");
            try {
              if (wipeOut) {
                let params = JSON.parse(wipeOut)[query.sn_id];
                let data = {
                  wain_id: params.alarmItem.wain_id,
                  state: 10,
                };
                this.websocket.manualSend(data, 290);
              }
            } catch (error) {
              console.log(error);
            }
          } else {
            let data = getRouteParam(
              this.flightCourse,
              this.railInfo,
              this.maps,
              this.leafletMaps
            );
            let flightParam = sessionStorage.getItem("flightParam");
            try {
              if (flightParam) {
                let params = JSON.parse(flightParam)[query.sn_id];
                data.uav_id = params.uav_id;
                let userInfo = this.$store.state.user.userInfo;
                data.user_id = userInfo.u_id;
                data.user_name = userInfo.nick;
                data.cmd_type = 8;
                this.websocket.manualSend(data, 401);
                this.percent = 0;
                this.dialogCode = true;
                this.waypoint_percent = 0;
              }
            } catch (error) {
              console.log(error);
            }
          }
        },
      });
    },

    // 无人机操作事件
    cutUavEvent: function (item) {
      if (this.laterTook) {
        this.$message({
          type: "error",
          message: `${this.laterTook.hintText}`,
        });
        return false;
      }
      this.uavOperateType = item.key;
      item.clickEvent(item);
    },

    // 模式切换
    changeModel: function (item) {
      this.dialogModel = true;
    },
    //选择模式
    changeModelValue(e) {
      this.chooseArm = true;
      let params = {
        cmd_type: 4,
        flight_mode: e,
      };
      this.websocket.manualSend(params, 401);
      setTimeout(() => {
        this.chooseArm = false;
      }, 1000);
    },

    // 起飞
    uavTakeOff: function () {
      createTooltip({
        message: this.singleLanguage.message.sureTakeOff,
        confirm: () => {
          this.websocket.manualSend(
            {
              cmd_type: 3,
              takeoff_height: 3.5,
            },
            401
          );
        },
      });
    },

    // 开锁
    lockFlight: function (item) {
      createTooltip({
        message: this.singleLanguage.message.sureunLock,
        confirm: () => {
          this.websocket.manualSend({ cmd_type: 2, arm: true }, 401);
        },
      });
    },
    //闭锁
    lockFlight1: function (item) {
      // if (this.flight_status == 0 || this.flight_status == 4) {
      createTooltip({
        message: this.singleLanguage.message.sureLock,
        confirm: () => {
          this.websocket.manualSend({ cmd_type: 2, arm: false }, 401);
        },
      });
      // } else {
      //   this.$message(this.singleLanguage.message.tipsLock);
      // }
    },
    // 无人机
    uavOperation: function (item) {
      // 按下触发的键
      let down = {
        left: 74, // 向左
        right: 76, // 向右
        top: 73, // 向前
        bottom: 75, // 向下
      };
      this.keydownEvent(
        {
          keyCode: down[item],
        },
        true
      );
    },
    // 云台
    panTiltOperation: function (item) {
      // 按下触发的键
      let down = {
        left: 37, // 向左
        right: 39, // 向右
        top: 38, // 向前
        bottom: 40, // 向下
      };

      this.keydownEvent(
        {
          keyCode: down[item],
        },
        true
      );
    },

    // 左边园滚动条滑动时触发
    leftSlidingInput: function (key, val) {
      let keyCode = null;
      if (key == "bottom") {
        keyCode = val >= 50 ? 68 : 65;
      } else {
        keyCode = val >= 50 ? 87 : 83;
      }

      this.keydownEvent(
        {
          keyCode: keyCode,
        },
        true
      );
    },
    // 右边园滚动条滑动时触发
    rightSlidingInput: function (key, val) {
      let keyCode = null;
      // 变焦
      if (key == "right") {
        keyCode = val > 50 ? 187 : 189;
        this.keydownEvent(
          {
            keyCode: keyCode,
          },
          true
        );
      }
    },
    // 云台回正
    straighten: function () {
      this.websocket.manualSend(
        {
          cmd_type: 5,
          action_cmd: 1,
          value: 0,
        },
        401
      );
    },
    // 右侧松开
    rightUndo: function () {
      this.yuntaiValue = 1500;

      for (let i = 0; i < 5; i++) {
        this.websocket.manualSend(
          {
            cmd_type: 5,
            action_cmd: 5,
            value: 1500,
          },
          401
        );
      }
      // this.websocket.virtualUp();
    },
    // 右侧变焦
    rightSlidingUp: function () {
      this.zoomTime = null;
    },
    // 左侧控制
    leftSlidingUp: function (type) {
      // if (type == "left") {
      //   this.riseFall = 1500;
      // } else if (type == "bottom") {
      //   this.turnLeftRight = 1500;
      // }

      this.virtualJoystickUp();
    },
    // 虚拟摇杆左侧松开
    leftUndo: function (type) {
      // if (type == "top" || type == "bottom") {
      //   this.frontBack = 1500; // 虚拟摇杆前后
      // } else {
      //   this.leftRight = 1500; // 虚拟摇杆左右
      // }
      this.virtualJoystickUp();
    },

    // 虚拟摇杆松开,停止
    virtualJoystickUp: function () {
      this.riseFall = 1500;
      this.turnLeftRight = 1500;
      this.frontBack = 1500; // 虚拟摇杆前后
      this.leftRight = 1500; // 虚拟摇杆左右

      for (let i = 0; i < 5; i++) {
        this.websocket.manualSend(
          {
            cmd_type: 1,
            action_cmd: 9,
            value: 1500,
          },
          401
        );
      }

      // this.websocket.virtualUp();
    },
  },
};
</script>

<style lang="less" scoped>
.single-uav {
  position: relative;
  height: 100%;
  .uav-operation-main {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 5px;
    height: 100%;
    overflow: hidden;
    .main-header {
      height: 55px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: linear-gradient(
        to bottom,
        rgb(86, 128, 156),
        rgba(86, 128, 156, 0.1)
      );
      .header-left {
        width: 84px;
        height: 28px;
        background-color: rgba(11, 88, 222, 1);
        color: #fff;
        font-size: 10px;
        border-radius: 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .header-video {
        width: 25px;
        height: 25px;
        border: 3px solid #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          border-color: #409eff;
        }
        .video-cell {
          width: 10px;
          height: 10px;
          background-color: red;
          border-radius: 50%;
        }
        @keyframes identifier {
          0% {
            transform: scale(1);
            // background-color: aquamarine;
          }
          100% {
            transform: scale(2);
            // background-color: rgb(0, 238, 255);
          }
        }
        .open-video-style {
          background-color: #1df50e;
          position: relative;
          &::after {
            content: "";
            width: 100%;
            height: 100%;
            position: absolute;
            left: 0%;
            top: 0%;
            border-radius: 50%;
            background-color: #1df50e;
            opacity: 0.5;
            animation-name: identifier;
            animation-duration: 2s;
            animation-iteration-count: infinite;
            animation-timing-function: linear;
          }
        }
      }
    }

    .main-center {
      // width: 100%;
      display: flex;
      flex-wrap: wrap;
      // padding-top: 12px;
      // justify-content: center;

      padding: 12px 28.5px 0 28.5px;

      .center-cell {
        width: 67px;
        height: 28px;
        border-radius: 5px;
        background-color: rgba(78, 84, 82, 1);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #fff;
        margin-right: 12px;
        margin-bottom: 12px;
        cursor: pointer;

        &:nth-child(3n) {
          margin-right: 0;
        }
      }
      .select-center-style {
        // color: rgba(83, 169, 243, 1);
        color: #0b58de;
        background-color: rgba(0, 0, 0, 0.7);
      }
    }
    .main-footer {
      width: 100%;
      display: flex;
      justify-content: space-between;
      overflow: hidden;
      background-image: linear-gradient(
        to bottom,
        rgba(4, 11, 43, 0.8),
        rgba(13, 134, 255, 0.5)
      );
      border-top-right-radius: 10px;
      border-top-left-radius: 10px;
      padding-top: 10px;
      .circle-center {
        width: 16px;
        height: 16px;
        background-color: rgba(27, 57, 86, 1);
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -8px 0 0 -8px;
      }
      .right-circle-center {
        width: 30px;
        height: 30px;
        border: 1px solid rgba(27, 57, 86, 1);
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -15px 0 0 -15px;
        transform: rotate(-45deg);
        font-size: 12px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        color: rgba(76, 166, 255, 1);
        font-weight: 700;
      }
      .uav-left-title {
        font-size: 12px;
        font-weight: 700;
        letter-spacing: 6px;
      }
      .cradle-head {
        display: flex;
        justify-content: center;
        margin-top: 16px;
        width: 100%;
        .el-button {
          padding: 5px 9px !important;
        }
        .head-link {
          &:hover {
            color: #0b58de;
          }
        }
        .head-img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 1);
        }
      }
    }
  }
}
</style>
<style lang="less">
.modelDialog {
  .el-dialog {
    color: white;
    background-color: black;
    background-image: linear-gradient(
      to bottom,
      rgba(4, 11, 43, 0.8),
      rgba(13, 134, 255, 0.5)
    );
    border-radius: 6px;
    margin-left: calc(100% - 282px - 20px);
    margin-top: 55vh !important;
  }
  .el-dialog__header {
    .el-dialog__title {
      color: white;
    }
    padding: 10px;
  }
  .el-dialog__body {
    text-align: center;
    padding: 10px;
    .el-select {
      .el-input__inner {
        background-color: transparent;
        color: white;
      }
    }
  }
}
.modelSelect {
  &.el-select-dropdown {
    background-color: rgb(0, 0, 0, 0.9);
    background-image: linear-gradient(
      to bottom,
      rgba(4, 11, 43, 0.8),
      rgba(13, 134, 255, 0.5)
    );
    border: none;
    .el-select-dropdown__item {
      color: white;
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-image: linear-gradient(
        to right,
        rgba(4, 11, 43, 0.8),
        rgba(13, 134, 255, 0.5)
      );
      color: #409eff;
    }
  }
  &.el-popper[x-placement^="bottom"] .popper__arrow,
  &.el-popper[x-placement^="bottom"] .popper__arrow::after {
    border-bottom-color: rgb(0, 0, 0, 0.9) !important;
  }
}
</style>