{"code": 2000, "msg": "菜单获取成功", "data": {"isLazyLoading": false, "list": [{"id": 1, "pid": 0, "title": "数据大屏", "name": "home", "path": "/home", "filePath": "", "sort": 1, "parentName": "layout", "grade": 1}, {"id": 2, "pid": 0, "title": "飞行管理", "name": "routeplan", "path": "/routeplan", "filePath": "routeplan/index.vue", "sort": 2, "parentName": "layout", "grade": 1}, {"id": 3, "pid": 0, "title": "设备管理", "name": "equipment", "path": "/equipment", "filePath": "equipment/index.vue", "sort": 3, "parentName": "layout", "grade": 1}, {"id": 4, "pid": 0, "title": "成果管理", "name": "totalResultList", "path": "/totalResultList", "filePath": "achievementAdmin/resultTotalList.vue", "sort": 4, "parentName": "layout", "grade": 1}, {"id": 5, "pid": 0, "title": "架次列表", "name": "achievementAdmin", "path": "/achievementAdmin", "filePath": "achievementAdmin/index.vue", "sort": 5, "parentName": "layout", "grade": 1}, {"id": 6, "pid": 0, "title": "系统设置", "name": "system", "path": "", "filePath": "", "sort": 6, "parentName": "layout", "grade": 1, "children": [{"id": 63, "pid": 6, "title": "定时任务管理", "name": "taskManage", "path": "/taskManage", "filePath": "taskManage/index.vue", "parentName": "layout", "grade": 2, "fun_id": "flight_10"}, {"id": 64, "pid": 6, "title": "固件管理", "name": "firmware", "path": "/firmware", "filePath": "firmware/index.vue", "parentName": "layout", "grade": 2}, {"id": 65, "pid": 6, "title": "设备类型管理", "name": "deviceType", "path": "/deviceType", "filePath": "deviceType/index.vue", "parentName": "layout", "grade": 2, "fun_id": "device_10"}, {"id": 67, "pid": 6, "title": "教学视频", "name": "teachingVideos", "path": "/teachingVideos", "filePath": "teachingVideos/index.vue", "parentName": "layout", "grade": 2}, {"id": 61, "pid": 6, "title": "功能管理", "name": "dictData", "path": "/dictData", "filePath": "dictData/index.vue", "parentName": "layout", "grade": 2, "fun_id": "fun_10"}, {"id": 62, "pid": 6, "title": "用户管理", "name": "userManage", "path": "/userManage", "filePath": "userManage/index.vue", "parentName": "layout", "grade": 2, "fun_id": "user_10"}, {"id": 66, "pid": 6, "title": "团队管理", "name": "teamManage", "path": "/teamManage", "filePath": "teamManage/index.vue", "parentName": "layout", "grade": 2, "fun_id": "user_10"}]}, {"id": 63, "pid": 6, "title": "协同组管理", "name": "coordination", "path": "/coordination", "filePath": "coordination/coordination.vue", "parentName": "layout", "grade": 2}, {"id": 8, "pid": 0, "title": "组网列表", "name": "networkingItem", "path": "/networkingItem", "filePath": "coordination/networkingItem.vue", "parentName": "layout", "grade": 2}, {"id": 9, "pid": 0, "title": "任务编辑", "name": "editWork", "path": "/editWork", "filePath": "coordination/editWork.vue", "parentName": "layout", "grade": 2}, {"id": 10, "pid": 0, "title": "航标飞行环境", "name": "navigationEnvironment", "path": "/navigationEnvironment", "filePath": "navigationEnvironment/index.vue", "parentName": "", "grade": 2}, {"id": 12, "pid": 0, "title": "离线地图", "name": "outLineMap", "path": "/outLineMap", "filePath": "outLineMap.vue", "parentName": "layout", "grade": 2}, {"id": 13, "pid": 0, "title": "googleMap", "name": "googleMap", "path": "/googleMap", "filePath": "routeplan/googleMap.vue", "parentName": "layout", "grade": 2}, {"id": 4, "pid": 0, "title": "解决方案", "name": "solution", "path": "", "filePath": "", "sort": 4, "parentName": "layout", "grade": 2}]}}