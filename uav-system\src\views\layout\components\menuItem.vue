<template>
  <div class="menu-item">
    <!-- 没有子集菜单 -->
    <menu-row
      :item="item"
      :mode="mode"
      v-if="!item.children"
      @clickMenu="openRouter"
      @rightClick="openMenuOperate"
    ></menu-row>

    <el-popover
      @show="navPopoverShow(item)"
      v-model="item.isShow"
      popper-class="layout-nav-poppers"
      placement="bottom"
      width="140"
      trigger="click"
      v-if="item.children"
    >
      <div class="nav-foxbase">
        <div
          class="foxbase-menus"
          v-for="(menu, menuIndex) in item.children"
          :key="menuIndex"
          @click="openFoxbase(menu, item)"
          @contextmenu.prevent="openMenuOperate(menu, $event)"
          :style="getSecondNavBgImg(menu)"
        >
          {{ menuInfo[menu.name] }}
          <img
            :src="menuSelect"
            v-show="menu.name == openNav"
            alt=""
            class="foxbase-menus-img"
          />
        </div>
        <div v-if="item.sort==6" class="foxbase-menus" @click="openChangeLanguage(item)">
         {{ menuInfo.systemSet}}

        </div>
      </div>

      <template v-slot:reference>
        <menu-row :item="item" :mode="mode"></menu-row>
      </template>
    </el-popover>
    <system-set ref="systemSet"></system-set>
  </div>
</template>

<script>
import menuRow from "./menuRow.vue";
import menuSelect from "@/assets/icon/menu-select.png";
import systemSet from "@/views/user/systemSet.vue"
export default {
  components: {
    menuRow,
    systemSet
  },
  props: {
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    mode: String,
  },
  data() {
    return {
      menuSelect,
      // openNav: "",
    };
  },
  computed: {
    menuInfo() {
      return this.$languagePackage.layout.menu;
    },
    openNav() {
      return this.$store.state.menus.openRouter;
    },
  },
  methods: {
    openChangeLanguage(item){
      this.$refs.systemSet.open();
      this.$set(item, "isShow", false);
    },
    getSecondNavBgImg: function (item) {
      let color = item.name == this.openNav ? "#60d8fc" : "";
      let bgColor = this.openNav == item.name ? "#0d1930" : "";
      return {
        color: color,
        backgroundColor: bgColor,
      };
    },
    navPopoverShow: function (item) {
      //   for (let i = 0; i < this.menusList.length; i++) {
      //     this.$set(this.menusList[i], "isShow", false);
      //   }
      this.$set(item, "isShow", true);
    },
    openRouter: function (item) {
      if (this.$route.name === item.name || !item.name) {
        return false; // 相同的路由不支持再次打开
      }

      if (item.name === "solution") {
        let message = this.$languagePackage.layout.message.warning;
        this.$message({
          type: "warning",
          message: message,
        });
        return false;
      }

      // this.openNav = item.name;
      this.$store.commit("setOpenRouter", item.name);
      this.$router.push({ name: item.name });
      this.$store.commit("setOpenRouter", item.name);
    },
    openFoxbase: function (row, item) {
      this.openRouter(row);
      this.$set(item, "isShow", false);
    },
    openMenuOperate(item, e) {
      if (item.name == "solution") {
        return false;
      }
      this.$emit("openMenuOperate", item, e);
    },
  },
};
</script>

<style lang="less">
.layout-nav-poppers {
  background-color: #13213d;
  border: none !important;
  min-width: 150px !important;
  width: 150px !important;
  padding: 8px 0 !important;
  margin-left: -13px;
  .foxbase-menus {
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    // padding: 0 24px;
    color: #90969b;
    font-weight: 700;
    text-align: center;
    &:hover {
      color: #60d8fc;
      background-color: #0d1930;
    }
    position: relative;
    &:hover .foxbase-menus-img {
      display: block !important;
    }
    .foxbase-menus-img {
      position: absolute;
      left: 50%;
      margin-left: -60px;
      bottom: 0;
    }
  }
  .popper__arrow {
    display: none;
  }
}
</style>