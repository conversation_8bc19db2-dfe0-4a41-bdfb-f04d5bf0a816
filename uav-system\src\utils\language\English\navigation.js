/**
 * 航行页面
 */

const navigation = {
    rtkStave: {
        0: "Ununited",
        1: "Not located",
        2: "Point positioning",
        3: "Floating installation",
        4: "Fixed solution",
    },

    flightMode: {
        // 2: "Pose mode",
        2: "Error",
        3: "Automatic mode",
        4: "Following mode",
        5: "GPS mode",
        6: "Return mode",
        9: "Landing flight mode",
        13: "Motor pattern",
    },

    filghtStep: {
        errorHint: "Process error, program terminated, please contact administrator",
        errorInfoTitle: 'Process error steps:',
        errorStep: {
            1: 'Start of UAV',
            2: 'Routes to upload',
            3: 'One click opening of the aircraft nest',
            4: 'Drone self check',
            5: 'Unmanned Aerial Vehicle RTK Inspection',
            6: 'Unlock the drone',
            7: 'Dr<PERSON> switches to automatic flight mode',
            8: 'One click closure of machine nest warehouse',
            9: 'Abnormal landing of drone',
            10: 'Abnormal takeoff',
            11: 'Electricity level below 50%'
        },
        errorTip: 'Attitude mode, flight prohibited',
        NoTF: 'No TF card detected',
        capacity: 'Insufficient TF card capacity',
        countDown: "Countdown to takeoff",
        countDown1: "Countdown of descent",
        continue: "Continue",
        pause: "Pause",
        interrupt: "Interrupt",
        close: "Close",
        altnlandReason: 'Reason for alternate landing:',
        process: "Continue with the process",
        process1: 'Continue process execution',
        pauseProcess: 'Process execution has been paused',
        endProcess: 'Process terminated',
        takeOffStep: {
            0: {
                title: "Drone start",
                hintText: "The drone is powering on.",
                voice: "Drone start"
            },
            1: {
                title: "Route upload",
                hintText: "The route is being uploaded.",
                voice: "Route upload"
            },
            2: {
                title: "Nest open",
                hintText: "The nest is opening in it's hatch",
                voice: "Nest open"
            },
            3: {
                title: "Platform rising",
                hintText: "The nest platform is rising",
                voice: "Platform rising"
            },
            4: {
                title: "Drone release",
                hintText: "Nest tripod in release",
                voice: "Drone release"
            },
            5: {
                title: "Drone self check",
                hintText: "The UAV is in self-test",
                voice: "RTK waiting"
            },
            6: {
                title: "Drone unlock",
                hintText: "Drone unlocking in progress",
                voice: "Drone unlock"
            },
            7: {
                title: "Drone take off",
                hintText: "Drone taking off",
                voice: "Drone take off"
            },
            8: {
                title: "Nest close",
                hintText: "The aircraft is closed within the hatch",
                voice: "Nest close"
            },
            9: {
                title: "Loading uav batteries",
                hintText: "Loading battery...",
                voice: "Load battery"
            }
        },
        landingStep: {
            0: {
                title: "Course reversal",
                hintText: "The drone is returning",
                voice: "Mission completed, start to return"
            },
            1: {
                title: "Nest open",
                hintText: "The nest is open in the hatch",
                voice: "Nest open"
            },
            2: {
                title: "Platform rising",
                hintText: "The nest platform is rising",
                voice: "Platform rising"
            },
            3: {
                title: "Drone release",
                hintText: "The nest tripod is releasing",
                voice: "Drone release"
            },
            4: {
                title: "Landing",
                hintText: "Drone landing",
                voice: "Landing"
            },
            5: {
                title: "Correction",
                hintText: "Nestpod is being corrected",
                voice: "Drone correction"
            },
            6: {
                title: "Declining",
                hintText: "The platform is descending",
                voice: "Platform declining"
            },
            7: {
                title: "Nest close",
                hintText: "The nest is closed in the cabin",
                voice: "Nest close"
            },
            8: {
                title: "Remove the uav battery",
                hintText: "Removing the battery...",
                voice: "Removing the battery"
            }
        },
        takeOffStep1: {
            0: {
                title: "Drone start",
                hintText: "The drone is powering on.",
                voice: "Drone start"
            },
            1: {
                title: "Route upload",
                hintText: "The route is being uploaded.",
                voice: "Route upload"
            },
            2: {
                title: "Nest open",
                hintText: "The nest is opening in it's hatch",
                voice: "Nest open"
            },
            3: {
                title: "Drone release",
                hintText: "Nest tripod in release",
                voice: "Drone release"
            },
            4: {
                title: "Drone self check",
                hintText: "The UAV is in self-test",
                voice: "RTK waiting"
            },
            5: {
                title: "Drone unlock",
                hintText: "Drone unlocking in progress",
                voice: "Drone unlock"
            },
            6: {
                title: "Drone take off",
                hintText: "Drone taking off",
                voice: "Drone take off"
            },
            7: {
                title: "Nest close",
                hintText: "The aircraft is closed within the hatch",
                voice: "Nest close"
            },
        },
        landingStep1: {
            0: {
                title: "Course reversal",
                hintText: "The drone is returning",
                voice: "Mission completed, start to return"
            },
            1: {
                title: "Nest open",
                hintText: "The nest is open in the hatch",
                voice: "Nest open"
            },
            2: {
                title: "Drone release",
                hintText: "The nest tripod is releasing",
                voice: "Drone release"
            },
            3: {
                title: "Landing",
                hintText: "Drone landing",
                voice: "Platform rising"
            },
            4: {
                title: "Correction",
                hintText: "Nestpod is being corrected",
                voice: "Drone correction"
            },
            5: {
                title: "Closing the tank",
                hintText: "The nest is closed in the cabin",
                voice: "Closing the hatch"
            },
        },
        errorList: {
            1: 'Alternate task loading failed!',
            2: 'Failed to upload the alternate task!',
            3: 'Alternate activation failed!',
            4: 'Repeated drone takeoff failed!',
            5: 'Repeated descent start failed!',
            6: 'Landing timeout!',
            7: 'Failed to close the nest of the landing aircraft!',
            8: 'Failed to close the alternate aircraft nest with one click!',
            9: 'Failed to load the descent task!',
            10: 'Failed to upload the landing task!',
            11: 'Repeated descent self test failed!',
            12: 'Repeated RTK timeout!',
            13: 'Failed to open the aircraft nest pod with one click!',
            14: 'Failed to unlock the drone!',
            15: 'Drone battery low!',
        },
        stepError: {
            1: "Excessive position deviation",
            2: "One click cabin opening failure of the aircraft nest",
            3: "Visual recognition landing failed",
        },
        alternateSteps: {
            0: {
                title: "Upload alternate landing point",
                hintText: "Uploading alternate landing points",
                voice: "Upload alternate landing point"
            },
            1: {
                title: "Execution of alternate landing action",
                hintText: "Alternate operation in progress",
                voice: "Alternate operation in progress"
            },
            2: {
                title: "Waiting for re landing",
                hintText: "Waiting for re landing",
                voice: "Waiting for re landing"
            },
            3: {
                title: "Uploading compound descent points",
                hintText: "Uploading of re landing points",
                voice: "Uploading compound descent points"
            },
            4: {
                title: "Drone self check",
                hintText: "The UAV is in self-test",
                voice: "Drone self check"
            },
            5: {
                title: "Nest open",
                hintText: "The nest is opening in it's hatch",
                voice: "Nest open"
            },
            6: {
                title: "Platform rising",
                hintText: "The nest platform is rising",
                voice: "Platform rising"
            },
            7: {
                title: "Drone release",
                hintText: "Nest tripod in release",
                voice: "Drone release"
            },
            8: {
                title: "Drone unlock",
                hintText: "Drone unlocking in progress",
                voice: "Drone unlock"
            },
            9: {
                title: "Drone take off",
                hintText: "Drone taking off",
                voice: "Drone taking off"
            },
            10: {
                title: "Go to the re landing point",
                hintText: "Drone heading to the re landing point",
                voice: "Drone heading to the re landing point"
            },
            11: {
                title: "Correction",
                hintText: "Nestpod is being corrected",
                voice: "Correction"
            },
            12: {
                title: "Declining",
                hintText: "The platform is descending",
                voice: "Declining"
            },
            13: {
                title: "Nest close",
                hintText: "The nest is closed in the cabin",
                voice: "Nest close"
            },
        },
        alternateSteps2: {
            0: {
                title: "Upload alternate landing point",
                hintText: "Uploading alternate landing points",
                voice: "Upload alternate landing point"
            },
            1: {
                title: "Execution of alternate landing action",
                hintText: "Alternate operation in progress",
                voice: "Alternate operation in progress"
            },
            2: {
                title: "Waiting for re landing",
                hintText: "Waiting for re landing",
                voice: "Waiting for re landing"
            },
            3: {
                title: "Uploading compound descent points",
                hintText: "Uploading of re landing points",
                voice: "Uploading compound descent points"
            },
            4: {
                title: "Drone self check",
                hintText: "The UAV is in self-test",
                voice: "Drone self check"
            },
            5: {
                title: "Nest open",
                hintText: "The nest is opening in it's hatch",
                voice: "Nest open"
            },
            6: {
                title: "Drone release",
                hintText: "Nest tripod in release",
                voice: "Drone release"
            },
            7: {
                title: "Drone unlock",
                hintText: "Drone unlocking in progress",
                voice: "Drone unlock"
            },
            8: {
                title: "Drone take off",
                hintText: "Drone taking off",
                voice: "Drone taking off"
            },
            9: {
                title: "Go to the re landing point",
                hintText: "Drone heading to the re landing point",
                voice: "Go to the re landing point"
            },
            10: {
                title: "Correction",
                hintText: "Nestpod is being corrected",
                voice: "Correction"
            },
            11: {
                title: "Nest close",
                hintText: "The nest is closed in the cabin",
                voice: "Nest close"
            },
        },
        alternateSteps1: {
            0: {
                title: "Upload alternate landing point",
                hintText: "Uploading alternate landing points",
                voice: "Upload alternate landing point"
            },
            1: {
                title: "Execution of alternate landing action",
                hintText: "Alternate operation in progress",
                voice: "Alternate operation in progress"
            }
        },
        changeLinkText: 'Currently in 4G, unable to perform alternate operation. Do you want to switch to fpv',
        changeLinkTip: 'Tips',
        changeLinkSubmit: 'Confirm'
    },

    leftVideoMap: {
        title: "Video Map",
        uavTitle: "Plane Monitoring",
        cabinTitle: "Monitoring",
        outboardTitle: "Extravehicular Monitoring",
    },

    uavOperation: {
        title: "Control Panel",
        executeTask: "Start task",
        uav: "UAV",
        payTilt: "PTZ",
        center: "Center",
        vertical: "Vertical",
        uavOperateList: {
            suspend: "Pause mission",
            keepOn: 'RecoveryTask',
            continueHint1: "Please confirm to resume task execution",
            errorHint: 'The current mode cannot be restored to continue executing tasks',
            continue: "Continue mission",
            courseReversal: "Return to base(RTL)",
            descent: "Landing",
            startVideo: "Start the video",
            endVideo: "End of the video",
            photographSuccss: "Photo success",
            beingShadowed: "Being Shadowed",
            startTask: "Start task",
            hover: "Hover",
            taskError: "Please execute the task first",
            photographError: "Failed to take photo (stream address error)",
            videoError: "Video recording failed (streaming address error)",
            executeTaskHint: "A task is being executed. Do not repeat the task",
            startTaskHint: "Ensure that the device is in a safe environment before performing the task.",
            suspendHint: "Once confirmed, the UAV will execute the pause task command",
            uavInterruptHint: "The interrupt command is about to be executed. Please confirm.",
            continueHint: "Please confirm the mission is about to continue",
            courseReversalHint: "The mission is about to continue, please confirm that the return operation is about to be performed.",
            descentHint: "The drone is about to execute the landing command, please confirm.",
            uavAutoFlightHint: "The drone is about to execute the automatic flight command, please confirm.",
            uavTakeOffHint: "Uavs are about to take off. Please confirm.",
            uavAdviceFlightHint: "Uavs are about to perform flight lock instructions, please confirm.",
            uavShuttingHint: "The UAV is about to execute the lock command, please confirm.",
            uavUnlockingHint: "The drone is about to execute the unlock command, please confirm.",
            uavManualFlightHint: "The UAV is about to execute the manual flight command, please confirm.",
            uavQRcodeLandHint: 'the UAV is about to start QR code recognition landing, please confirm.',
            uavcloseQRcodeLandHint: 'UAV is about to close QR code recognition landing, please confirm.',
            pictureMsg: 'The recorded video is only stored on the SD card and has not been uploaded to the system yet, please confirm',
            openQRError: 'Failed to open QR code recognition landing!',
            closeQRError: 'Failed to turn off QR code recognition landing!',
            openQRSuccess: 'QR code recognition landing is enabled!',
            closeQRSuccess: 'QR code recognition landing is disabled!',
            autoFlight: 'Automatic flight',
            adviceFlight: "Point flight",
            manualFlight: "Manual flight",
            uavTakeOff: "Take off",
            uavUnlocking: "Unlock",
            atresia: "Lock",
            interrupt: "End mission",
            downRoute: "Download",
            QRcodelanding: "Enable qrcode",
            closeQRcodelanding: "Disable qrcode",
            keydownTip: 'The keyboard event has not been turned on (please refer to the operating instructions for the corresponding events of the keyboard). If you want to turn it on, please confirm.',
            tips: 'Tips',
            sure: 'Determine',
            cancel: 'Cancel',
            performTip: "Please ensure that the nest direction is set correctly.",
            notExecute: 'We are currently in a 4G network and unable to execute the task. Please switch to image transmission and execute again',
            executeTip: 'Tips',
            executeSubmit: 'Confirm',
            executeSubmit1: 'Continue execution',
            executeCancel: 'Re execute',
            elevationLoading: 'Obtaining elevation data',
            breakPointText: 'The last route execution was not completed. Do you want to continue with the route execution?',
            notChooseRoute: "No route selected",
            crashStop: 'Crash-stop',
            crashStopTip: 'Nest is about to execute an emergency stop command, please confirm.',
            rain_fall_msg: 'Rain has been detected and flying is prohibited.',
            wind_speed_msg: 'Detected that the current wind speed is too high, flight is prohibited.'
        },
    },

    dataOverview: {
        distance: "Distance",
        height: "Altitude(m)",
        horizontalVelocity: "Horizontal velocity(m/s)",
        verticalVelocity: "Vertical velocity(m/s)",
        cameraZoom: "Zoom multiples"
    },

    // 操作说明页面
    instructions: {
        uavMap: {
            uav: {
                title: "Video",
                content: "A live video feedback of the aircraft"
            },
            cabin: {
                title: "Monitoring",
                content: "Video monitoring is used to keep track of the changes inside the fixed airport in real time"
            },
            outboard: {
                title: "Extravehicular monitoring",
                content: "Video monitoring is used to keep track of the changes inside the fixed airport in real time"
            },
            map: {
                title: "Map function area",
                content: "Display mission perimeters, flight path, and real-time aircraft location"
            }
        },
        centerData: {
            title: "Flight data display area",
            content: "Display real-time flight data such as flight distance and flight altitude"
        },
        instruct: {
            title: "Uav control area",
            content: [
                "The operation of the UAV",
                "Fully automatic aircraft control",
                "Manual aircraft control"
            ]
        },
        warning: {
            title: "Real-time information display area",
            content: "Display real-time information",
            type: {
                0: "Red Critical Warning",
                1: "Yellow warning",
                2: "Green normal data information"
            }
        },

        achievement: {
            title: "Results show",
            content: [
                "According to the results of",
                "photograph",
                "longitude and latitude",
                "time"
            ]
        }
    },

    // camera operation
    cameraConfig: {
        shut: "Close",

        headerNav: {
            patterns: "Basic Settings",
            camera: "Picture",
            videos: "Video",
            more: "More"
        },

        patterns: {
            shutter: "Shutter",
            exp: "Exposure compensation",
            white: "White balance",
            areaThermal: 'Thermal imaging area'
        },

        camera: {
            mode: "Picture mode",
            format: {
                title: "Image Format",
                front: "Format Selection"
            },
            size: "Photo Size"
        },

        videos: {
            previewResolution: "Preview resolution",
            previewCodeRate: "Preview bit rate",
            codeRate: "Video resolution",
            resolution: "Video bit rate",
            hz: "Video HZ"
        },

        more: {
            shut: "Shut",
            grid: "Grid",
            defogging: "Defogging",
            formatting1: "SD card",
            flicker: "Resistance to flicker",
            cardCapacity: "TF card capacity",
            noSD: "No SD card",
            formatting: "Format SD Card",
            capacity: "Remaining SD card capacity",
            TFcapacity: 'TF card capacity:',
            return: "Return",
            gridLine: "Grid line",
            centerPoint: 'Center point',
            gridlinesALines: 'Gridlines and Diagonal Lines',
            control: 'Control',
            close: 'Close',
            formatTip: 'The formatting process will erase all captured and stored video and sound files on the storage card, and the files cannot be restored after clearing',
            submit: 'Confirm',
            cancel: 'Cancel',
            formatLoad: 'In the format...',
            formatSuccess: "SD card formatted successfully!",
            formatError: "SD card formatting failed!",
            del: 'Delete',
            complete: "Complete",
            latLngList: {
                longitude: 'Longitude:',
                latitude: 'Latitude:',
                altitude: 'Altitude(m):'
            }

        },
        rtkset: {
            url: 'Url',
            port: 'Port',
            user: 'Username',
            password: 'Password',
            endpoint: 'Endpoint',
            state: 'State',
            network: 'Network',
            ntrip: 'Ntrip',
            getMountpoint: 'Get mount points',
            set: 'Set',
            inLine: 'Online',
            outLine: 'Offline',
            nodata: 'No data',
            ntrip_state: [
                "Initial state",
                "Connecting",
                "Connected",
                "Connection failed",
                "Authentication start",
                "Authentication succeeded",
                "Authentication failed",
                "Account is not bound",
                "Account password error",
                "Account is not activated",
                "Account expired",
                "Network unavailable",
            ],
            urlEmpty: 'Please enter the url',
            portEmpty: 'Please enter the port',
            getMountPointError: 'Failed to obtain mount point, please check if the address and port are correct'
        },

        moreInside: {
            formatting: {
                title: "Formatting SD Card",
                content: "The formatting process will clear all image and sound files stored on the memory card. The files then cannot be recovered after being cleared",
                affirm: "Confirm",
                cancel: "Cancel"
            }
        }
    },
    //天气模块
    weatherModule: {
        title: "Weather Module",
        title1: "Weather Information",
        title2: "Network Weather Module",
        // weatherLabel: ["temperature(℃)", "Air humidity(%)", "Wind speed(m/s)", "Wind force", "Wind direction(°, [0-360])", "rainfall(mm)"],
        weatherLabel: ["Cabin temperature(℃)", "Outside temperature(℃)", "Wind speed(m/s)", "Wind force", "Rainfall(mm)", "Humidity(%)"],
        weatherLabel1: ["Temperature(℃)", "Humidity(%)", "Wind", "Wind", "Weather"]
    },
    link: {
        auto: "automatic",
        fpv: 'fpv',
        uav: '4G'
    },
    errorMsg: {
        0: "Success",
        1: "One click process step error",
        2: "The azimuth angle of the machine nest is not set or the set value is invalid",
        3: "The nest placement point is not set or the set value is invalid",
        4: "The aircraft nest alternate landing point is not set or the set value is invalid",
        5: "Uploading to drone route data parsing failed",
        6: "Process start failed"
    },
    errorInfo: {
        openOperate: 'Enable keyboard operation',
        closeOperate: 'Turn off keyboard operations',
        openMsg: 'Successfully opened keyboard operation',
        closeMsg: 'Keyboard operation has been turned off'
    },
    batteryBox: {
        title: "BATTERY INFORMATION",
        batteryInfo: {
            voltage: 'Voltage',
            current: 'Current',
            battery_remaining: 'Quantity of electricity',
            battery_temperature: 'Temperature',
        },
        tip: 'Low Power Alarm ',
        content: 'The current battery level is low ([num%]), please charge it in a timely manner!',
        submit: 'Confirm'
    },
    shout: {
        checkError: "The browser does not support real-time call (Opus) function",
        realTimeShout: 'Real time shouting',
        timbre: 'Timbre',
        timbrePlaceholder: 'Please choose a tone color',
        speech_speed: 'Speed of sound',
        broadcast: "Record a broadcast",
        textToAudio: 'Text-to-speech',
        textPlaceholder: 'Please enter text',
        clear: 'Clear',
        save: 'Save',
        add: 'Add',
        operateList: {
            1: 'Transcribe',
            2: 'Text-to-speech',
            3: 'Recording file'
        },
        timbreList: [
            { id: 0x01, name: "Female Voice" },
            { id: 0x02, name: "Male Voice" },
            { id: 0x11, name: "English Female Voice" },
            { id: 0x12, name: "English Male Voice" },
            { id: 0x31, name: "Northeastern Dialect" },
            { id: 0x32, name: "Sichuan Dialect" },
            { id: 0x33, name: "Henan Dialect" },
            { id: 0x34, name: "Hunan Dialect" },
            { id: 0x35, name: "Shaanxi Dialect" },
            { id: 0x36, name: "Cantonese" },
            { id: 0x41, name: "Russian" },
            { id: 0x42, name: "French" },
            { id: 0x43, name: "German" },
            { id: 0x44, name: "Korean" },
            { id: 0x45, name: "Italian" },
            { id: 0x46, name: "Polish" },
            { id: 0x47, name: "Spanish" },
            { id: 0x48, name: "Portuguese" },
        ],
        close: 'close',
        ledMessage: {
            led_mo1: 'strobe',
            led_mo2: 'lighting',
            led_psm0: 'Alternating flash',
            led_psm1: 'Alternating full flashing',
            led_psm2: 'Alternating flashing'
        }
    }
}

export default navigation;