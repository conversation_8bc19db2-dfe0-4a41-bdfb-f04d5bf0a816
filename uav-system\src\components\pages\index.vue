<template>
  <div class="custom-pages">
    <el-pagination
      :class="className"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :layout="layout"
      :total="total"
      :background="background"
      @prev-click="prevClick"
      @next-click="nextClick"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  props: {
    current: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageSizes: {
      type: Array,
      default: () => {
        return [10, 20, 30, 40];
      },
    },
    total: {
      type: Number,
      default: 0,
    },
    layout: {
      type: String,
      default: "prev, pager, next",
    },
    prevText: String,
    nextText: String,
    background: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      prev: this.prevText,
      next: this.nextText,
      className: "",
    };
  },
  created() {
    let item = this.$languagePackage.components.pages;
    if (!this.prev) {
      this.prev = item.prevText;
    }

    if (!this.nextText) {
      this.next = item.nextText;
    }
  },
  methods: {
    // 页码大小改变
    handleSizeChange: function (val) {
      this.$emit("update:pageSize", val);
      this.$emit("onChange", {
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    // 页数改变
    handleCurrentChange: function (val) {
      this.$emit("update:current", val);
      this.$emit("onChange", {
        current: this.current,
        pageSize: this.pageSize,
      });
    },
    prevClick(e) {
      console.log(e);
      this.className = "prev-class";
      setTimeout(() => {
        this.className = "";
      }, 200);
    },
    nextClick(e){
      this.className = "next-class";
      setTimeout(() => {
        this.className = "";
      }, 200);

    }
  },
};
</script>

<style lang="less">
.custom-pages {
  padding: 10px;
  display: flex;
  justify-content: flex-end;
  // color: #fff;
  .btn-next,
  .btn-prev {
    padding: 0 !important;
    width: 28px !important;

    background-color: transparent;
    border: 1px solid #fff;
    color: #fff;
    &:disabled {
      color: #c0c4cc;
    }
    // background: rgba(0, 0, 0, 0) !important;
    // color: #fff !important;
  }
  .prev-class {
    .btn-prev {
      border: 1px solid #124093;
      color: #124093 !important;
    }
  }
  .next-class {
    .btn-next {
      border: 1px solid #124093;
      color: #124093 !important;
    }
  }
  .el-pager li{
    background-color: transparent !important;
    border: 1px solid #fff !important;
    color: #fff !important;
  }
  .el-pager li:not(.disabled).active {
    background-color: #124093 !important;
    border: 1px solid #124093 !important;

  }
}
</style>