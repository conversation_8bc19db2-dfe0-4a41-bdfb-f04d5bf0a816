import maps from '@/utils/maps'

const mapData = {
  state: {
    map: null,
    isShowMap: false,
    mapComplete: false
  },
  mutations: {
    setMap: function (state, val) {
      state.map = val;
    },
    setMapComplete: function(state, val){
        state.mapComplete = val;
    },
    setMapIsShow: function(state, val){
        state.isShowMap = val;
    }
  },
  actions: {
    // 地图初始化
    mapInit: function (content, val) {
      maps.initMap(val.id).then((map) => {
        content.commit("setMap", map);
        map.on("complete", () => {
          content.commit("setMapComplete", true);
        })
      })
    }
  }
}

export default mapData;
