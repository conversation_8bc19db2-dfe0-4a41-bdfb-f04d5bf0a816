const userManage = {
    allBtn: {
        search: 'Search',
        newUser: 'New User',
        set: 'Set',
        del: 'Delete',
        recovery: 'Recovery',
        save: 'Save',
        cancel: 'Cancel',
        del1: 'Delete',
        recover1: 'Recovery',

    },
    tipInfo: {
        placeholder: "Please enter the user's mobile phone number / email",
        placeholder1: 'The user has been deleted and cannot be recovered.'
    },
    dialogInfo: {
        title: 'New User',
        editTitle: 'Edit User',
        name: 'Nickname',
        placeholder: 'Please enter the account nickname',
        company: 'Select the company/department',
        placeholder1: "Please select the company/department to which the user belongs",
        account: "Mobile phone number/email",
        placeholder2: "Please enter the user's mobile phone number / email",
        password: 'password',
        message: 'Please enter the user password',
        placeholder3: 'Please enter the user password (6-32 numbers or letters in length)',
        placeholder4: "Please enter the user's password without filling in the original password",
        placeholder5: 'Password length is between 6 and 32 characters',
        placeholder6: 'Please select user term',
        power: 'User Rights',
        term: 'User Term',
        permanent: 'Permanently valid',
        limite: 'Valid for a limited time',
        startTime: 'Start date',
        endTime: 'End date',
        delTitle: 'User delete',
        recoverTitle: 'User Recovery',
        delText: 'After deleting the user, all the data associated with the user will be deleted and cannot be recovered. Please operate with caution.',
        recoverText: "Should the user's status be restored to normal?",
        successAdd: 'User added successfully!',
        successEdit: "User edited successfully!",
        successDel: 'Deletion succeeded!',
        successRecover: 'User recovery succeeded!',
        cancelDel: 'Deletion cancel!',
        cancelRecover: 'The recovery has been canceled!',
        errorMessage: 'Please enter the correct mobile phone number',
        errorMessage1: 'Please enter the correct e-mail',
        errorMessage2: 'Please select a limited time period',
    },
    options: [{
        label: "Walkera",
        value: "wk",
    }, {
        label: "GangHang",
        value: "gzGh",
    }, {
        label: "NanRui",
        value: "zrhk",
    }],
    column: [{
            label: "Nickname",
            prop: "nick",
        },
        {
            label: "Phone / Email",
            prop: "phone",
        },
        {
            label: "Company/department",
            prop: "company",
        },
        {
            label: "Function permission",
            prop: "fun_list",
        },
        {
            label: "State",
            prop: "state",
        },
        {
            label: "Start time",
            prop: "start_time",
        },
        {
            label: "End time",
            prop: "end_time",
        },
        {
            label: "Operation",
            prop: "operation",
            width: "240",
        },
    ],

}
export default userManage