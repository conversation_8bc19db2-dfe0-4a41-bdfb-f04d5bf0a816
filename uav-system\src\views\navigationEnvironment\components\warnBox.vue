<template>
  <div class="warn-box"></div>
</template>
<script>
export default {
  data() {
    return {};
  },
};
</script>
<style lang="less" scoped>
.warn-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 0 30px #ff0000 inset;
  z-index: 100;
  box-sizing: border-box;
  pointer-events: none;
  background-color: transparent;
  animation: bg 1s linear infinite;
}
@keyframes bg {
  50% {
    transform: scale(1.01);
  }
  100% {
    transform: scale(1);
  }
}
</style>