import html2canvas from "html2canvas";
import jsPDF from "jspdf";

const a4_w = 595;
const a4_h = 842;

export function htmlSavePdf(id, title) {
    const element = document.getElementById(`${id}`)
    const opts = {
        scale: 1, // 缩放比例，提高生成图片清晰度
        useCORS: true, // 允许加载跨域的图片
        allowTaint: false, // 允许图片跨域，和 useCORS 二者不可共同使用
        tainttest: true, // 检测每张图片已经加载完成
        logging: false // 日志开关，发布的时候记得改成 false
    }
    return new Promise((resolve, reject) => {
        html2canvas(element, opts)
            .then((canvas) => {
                const contentWidth = canvas.width
                const contentHeight = canvas.height
                    // 一页pdf显示html页面生成的canvas高度;
                const pageHeight = (contentWidth / a4_w) * a4_h
                    // const pageHeight = 1080
                    // 未生成pdf的html页面高度
                let leftHeight = contentHeight
                    // 页面偏移
                let position = 0
                    // a4纸的尺寸[595.28,841.89]，html页面生成的canvas在pdf中图片的宽高 595 / 842
                const imgWidth = a4_w
                const imgHeight = (a4_w / contentWidth) * contentHeight
                const pageData = canvas.toDataURL('image/jpeg', 1.0)
                    // a4纸纵向，一般默认使用；new JsPDF('landscape'); 横向页面
                const PDF = new jsPDF('', 'pt', 'a4')

                // 当内容未超过pdf一页显示的范围，无需分页
                if (leftHeight < pageHeight) {
                    PDF.addImage(pageData, 'JPEG', 0, 0, imgWidth, imgHeight)
                } else {
                    // 超过一页时，分页打印（每页高度841.89）
                    while (leftHeight > 0) {
                        PDF.addImage(pageData, 'JPEG', 0, position, imgWidth, imgHeight)
                        leftHeight -= pageHeight
                        position -= a4_h
                        if (leftHeight > 0) {
                            PDF.addPage()
                        }
                    }
                }

                PDF.save(title + '.pdf')
                resolve("成功")
            })
            .catch((error) => {
                console.log('打印失败', error)
                reject("失败", error);
            })
    })
}

/**
 * html保存为图片
 */
export function htmlSaveImg(id, title) {
    return new Promise((resolve, reject) => {
        const element = document.getElementById(`${id}`)
        const opts = {
            scale: 2, // 缩放比例，提高生成图片清晰度
            useCORS: true, // 允许加载跨域的图片
            allowTaint: false, // 允许图片跨域，和 useCORS 二者不可共同使用
            tainttest: true, // 检测每张图片已经加载完成
            logging: true // 日志开关，发布的时候记得改成 false
        }
        html2canvas(element, opts).then((canvas) => {
            const pageData = canvas.toDataURL('image/jpeg', 1.0)
            const link = document.createElement("a");
            link.href = pageData;
            link.download = title + '.jpeg';
            link.click();
            // document.removeChild(link);
            resolve('成功')
        }).catch((err) => {
            reject(err);
        })
    })
}

/**
 * 添加空白dom，避免分页被切割
 * @param {*} ele 
 * @param {*} pdfFileName 
 * @returns 
 */
export function outPutPdfFn(ele, pdfFileName) {
    return new Promise((resolve, reject) => {

        ele.style.height = 'initial';

        let pageHeight = ele.offsetWidth / a4_w * a4_h;
        // 获取分割dom，此处为class类名为item的dom
        let domList = document.getElementsByClassName(pdfFileName);
        // 进行分割操作，当dom内容已超出a4的高度，则将该dom前插入一个空dom，把他挤下去，分割
        let pageNum = 1; //pdf页数
        let eleBounding = ele.getBoundingClientRect();

        for (let i = 0; i < domList.length; i++) {
            let node = domList[i];
            let bound = node.getBoundingClientRect();
            let offset2Ele = bound.top - eleBounding.top
            let currentPage = Math.ceil((bound.bottom - eleBounding.top) / pageHeight); //当前元素应该在哪一页
            if (pageNum < currentPage) {
                pageNum++
                let divParent = domList[i].parentNode; // 获取该div的父节点
                let newNode = document.createElement('div');
                newNode.className = 'emptyDiv';
                newNode.style.backgroundColor = 'rgba(0, 0, 0, 0.8);';
                newNode.style.height = (pageHeight * (pageNum - 1) - offset2Ele + 30) + 'px'; //+30为了在换下一页时有顶部的边距
                newNode.style.width = '100%';
                let next = domList[i].nextSibling; // 获取div的下一个兄弟节点
                // 判断兄弟节点是否存在
                if (next) {
                    // 存在则将新节点插入到div的下一个兄弟节点之前，即div之后
                    divParent.insertBefore(newNode, node);
                } else {
                    // 不存在则直接添加到最后,appendChild默认添加到divParent的最后
                    divParent.appendChild(newNode);
                }
            }
        }
        resolve("成果");
    })
};


/**
 * 分页补充空白
 * @param { el } 要转换成pdf的html
 * @param { className } el的子元素类名
 * @param { fillStyle } 补充的div元素样式
 */
export const supplement = (config) => {
    let {
        el,
        className,
        fillStyle = {}
    } = config || {};

    el.style.height = "initial";
    const pageHeight = el.offsetWidth / a4_w * a4_h; // 获取每一个分页的高度
    const childList = document.getElementsByClassName(className);

    let total = 0;

    const createDom = (node) => {
        let newNode = document.createElement('div');
        newNode.className = 'emptyDiv';
        newNode.style.backgroundColor = fillStyle.backgroundColor;
        newNode.style.height = pageHeight - total + 'px';
        newNode.style.width = '100%';
        return newNode
    }


    for (let i = 0; i < childList.length; i++) {
        let node = childList[i];

        let bound = node.getBoundingClientRect();

        if (total + bound.height >= pageHeight) {
            let newNode = createDom(node);
            el.insertBefore(newNode, node);
            total = 0;

            if (i < childList.length && i > 0) {
                i--;
            }

        } else {
            total += bound.height;

        }
    }
    return Promise.resolve("----------------->");
}