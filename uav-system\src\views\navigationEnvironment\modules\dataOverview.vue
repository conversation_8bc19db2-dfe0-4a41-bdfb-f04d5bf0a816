<!-- 数据总览 -->
<template>
  <div class="data-overview remote-uav-data">
    <div class="overview-cell" v-for="(item, index) in data" :key="index">
      <div class="cell-top">{{ item.label }}</div>
      <div class="cell-val mt5">{{ item.value }}</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      doorClose: 1,
      data: [
        { label: "距离(米)", value: NaN, key: "distance" },
        { label: "高度(米)", value: NaN, key: "height" },
        { label: "水平速度(米/秒)", value: NaN, key: "horizontalVelocity" },
        { label: "垂直速度(米/秒)", value: NaN, key: "verticalVelocity" },
        { label: "变焦倍数", value: "x1", key: "cameraZoom" },
      ],
    };
  },
  created() {
    let dataOverview = this.$languagePackage.navigation.dataOverview;
    for (let i = 0; i < this.data.length; i++) {
      let item = this.data[i];
      this.data[i].label = dataOverview[item.key];
    }
  },
  computed: {
    staveTwoData: function () {
      return this.$store.state.equipment.staveTwoData;
    },
    // 飞行状态
    flightStatus() {
      return this.$store.state.equipment.staveThreeData.flight_status;
    },
  },
  watch: {
    staveTwoData: {
      deep: true,
      handler: function (item) {
        this.data[4].value = "x" + item.camera_zoom_value;
      },
    },
  },
  methods: {
    disposeData: function (msg_id, data) {
      switch (msg_id) {
        case 434:
          this.doorClose = data.nest_door_close;
          break;

        // case 431:
        //   this.data[4].value = "x" + data.camera_zoom_value;
        //   break;
        case 432:
          if (
            (data.flight_status == 0 || data.flight_status == 4) &&
            this.doorClose === 1
          ) {
            this.data[0].value = NaN;
            this.data[1].value = NaN;
            this.data[2].value = NaN;
            this.data[3].value = NaN;
          } else {
            this.data[0].value = (data.distence_home / 100).toFixed(2);
            this.data[1].value = (data.relative_altitude / 100).toFixed(2);
            let hSpeed = Number(data.hSpeed) / 100;
            this.data[2].value = hSpeed.toFixed(2);
            let vSpeed = Number(data.vSpeed) / 100;
            this.data[3].value = vSpeed.toFixed(2);
          }
          break;
        default:
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .data-overview {
    height: @radio * 80px !important;
    border-radius: @radio * 5px !important; 
    .overview-cell {
      .cell-top {
        font-size: @radio * 14px !important;
      }
      .cell-val {
        font-size: @radio * 24px !important;
        height: @radio * 36px !important;
      }
    }
  }
}

.data-overview {
  width: 100%;
  height: 80px;
  // background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  .overview-cell {
    // color: #fff;
    flex-grow: 1;
    text-align: center;

    &:nth-child(1) .cell-val {
      // color: #1df50e;
    }
    &:nth-child(2) .cell-val {
      // color: #1df50e;
    }
    .cell-top {
      font-size: 14px;
    }
    .cell-val {
      font-size: 24px;
      font-weight: 700;
      height: 36px;
    }
  }
}
</style>