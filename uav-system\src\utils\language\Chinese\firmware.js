const firmware = {
    language: 'zh-CN',
    typeTitle: "类型：",
    uploadTitle: '上传固件',
    typeList: [
        { label: "全部", value: "" },
        { label: "机巢", value: 17 },
    ],
    normal: '正常',
    edit: '编辑',
    down: '下载',
    column: {
        type: "类型",
        version_code: "版本号",
        version_name: "版本名",
        state: "状态",
        description: "升级说明",
        description_en: "升级说明-英文",
        create_time: '创建时间',
        operation: "操作"
    },
    upgradeFirmware: {
        dialogTitle: '升级固件',
        dialogTitle1: '上传固件',
        dialogTitle2: '编辑固件',
        fileTitle: '固件文件',
        type: "固件类型",
        vCode: '固件版本号',
        vName: '固件版本名',
        desc: '升级说明',
        desc_en: '升级说明-英文',
        placeholder: '选择文件自动生成',
        placeholder1: '请输入英文升级说明',
        errorFile: '文件选择错误，请重新选择',
        clickChoose: '点击选择',
        uploadTip: '只能上传.wkimg文件',
        submit: '确\xa0定',
        cancel: '取\xa0消',
        errorTip: '请上传固件文件',
        loadingText: '正在上传固件。。。',
        loadingText1: '正在更新固件。。。',
        successTip: '固件更新成功',
        successTip1: '固件上传成功',
        reupgrade: '重新上传',
        upgraded: '已上传',
        upgradeText: '升级',
        tipText: "是否确定选择该版本升级？",
        Tip: '升级提示'

    },
}
export default firmware;