<template>
  <div class="firmware">
    <div class="firmware-form">
      <div class="type-choose">
        <div class="title">{{ language.typeTitle }}</div>
        <el-tag
          :class="typeCode == item.value ? 'active' : ''"
          v-for="item in typeList"
          :key="item.value"
          @click="changeType(item.value)"
          >{{ item.label }}</el-tag
        >
      </div>
      <div class="operate-btn">
        <el-button @click="addFirmware" type="primary"
          ><i class="el-icon-upload"></i> {{ language.uploadTitle }}</el-button
        >
      </div>
    </div>
    <custom-table
      :column="column"
      :params="params"
      urlName="firmwareList"
      ref="customTable"
    >
      <template #state="scope">
        <!-- .replace(/\n|\r\n/g, '<br>')  -->
        {{ scope.row.state == 10 ? language.normal : "" }}
      </template>
      <template #description="scope">
        <!-- .replace(/\n|\r\n/g, '<br>')  -->
        <div class="desc-div" v-html="formatDesc(scope.row.description)"></div>
      </template>
      <template #description_en="scope">
        <!-- .replace(/\n|\r\n/g, '<br>')  -->
        <div
          class="desc-div"
          v-html="formatDesc(scope.row.description_en)"
        ></div>
      </template>
      <template #operation="scope">
        <!-- .replace(/\n|\r\n/g, '<br>')  -->
        <el-button type="text" @click="editItem(scope.row)">{{
          language.edit
        }}</el-button>
        <el-button type="text" @click="downItem(scope.row)">{{
          language.down
        }}</el-button>
      </template>
    </custom-table>
    <firmware-operate
      ref="firmwareOperate"
      @refresh="refresh"
    ></firmware-operate>
  </div>
</template>
<script>
import customTable from "@/components/customTable/index.vue";
import firmwareOperate from "./components/firmwareOperate.vue";
export default {
  data() {
    return {
      params: {
        type: "",
      },
      typeCode: "",
      column: [
        { label: "类型", prop: "type", width: "180" },
        { label: "版本号", prop: "version_code", width: "180" },
        { label: "版本名", prop: "version_name", width: "180" },
        { label: "状态", prop: "state", width: "180" },
        { label: "升级说明", prop: "description" },
        { label: "升级说明-英文", prop: "description_en" },
        { label: "创建时间", prop: "create_time", width: "180" },
        { label: "操作", prop: "operation", width: "180" },
      ],
    };
  },
  components: {
    customTable,
    firmwareOperate,
  },
  computed: {
    language() {
      return this.$languagePackage.firmware;
    },
    typeList() {
      return this.$languagePackage.firmware.typeList;
    },
  },
  created() {
    for (let index = 0; index < this.column.length; index++) {
      this.column[index].label = this.language.column[this.column[index].prop];
    }
  },
  methods: {
    addFirmware() {
      this.$refs.firmwareOperate.open();
    },
    changeType(value) {
      this.typeCode = value;
      this.params.type = value;
      this.$refs.customTable.refresh();
    },
    refresh() {
      this.$refs.customTable.refresh();
    },
    formatDesc(desc) {
      let desc1 = desc.replace(/\n|\r\n/g, "\n");
      return desc1.replace(/\n\n/g, "\n");
    },
    editItem(item) {
      this.$refs.firmwareOperate.open(item);
    },
    downItem(item) {
      window.open(item.file_url);
    },
  },
};
</script>
<style lang="less" scoped>
.firmware {
  background-color: rgba(0, 0, 0, 1);
  padding: 20px;
  padding-bottom: 0;
  height: calc(100% - 20px);
  .firmware-form {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .operate-btn {
      .el-button {
        font-size: 16px;
        background-color: white;
        color: #397ff8;
        border: none;
      }
    }
    .type-choose {
      display: flex;
      align-items: center;
      font-size: 16px;
      .title {
        color: rgb(190, 190, 190);
      }
      .el-tag {
        cursor: pointer;
        background-color: transparent;
        margin: 0 10px;
        border: none;
        color: #fff;
        font-size: 14px;
        height: 24px;
        line-height: 24px;
        min-width: 60px;
        text-align: center;
        &.active {
          background: transparent;
          border: 1px solid #3455e9;
          color: #3455e9;
        }
      }
    }
  }
  .custom-table {
    .desc-div {
      white-space: pre-line;
    }
  }
}
</style>