<!-- 飞行数据分析页面 -->
<template>
  <div class="data-analyse" id="data-analyse" ref="dataAnalyse">
    <!-- 操作按钮 -->
    <!-- <div class="header-operation" v-if="isShowOperation">
      <el-button @click="htmlToPdfEvent(null)">生成pdf</el-button>
      <el-button @click="htmlToimg">保存为图片</el-button>
    </div> -->

    <!-- 封面 start -->
    <div class="analyse-home data-analyse-model" :style="homeStyle">
      <div class="home-logo">
        <div class="login-logo">
          <img
            class="logo-img"
            :style="logoSize"
            src="@/assets/img/logo.png"
            alt=""
          />
        </div>

        <h1 :style="fontSize32">{{ language.title }}</h1>
      </div>
      <div class="homt-main">
        <h1 :style="fontSize32" ref="homeTile">{{ language.name }}</h1>
        <div class="line" :style="homeLineStyle"></div>
        <div class="sort-code" :style="fontSize18" ref="homeCode">
          {{ sortId }}
        </div>
      </div>
      <div class="user-info" :style="fontSize16">
        <div class="">
          <span>{{ language.createUser }}：</span>{{ pringUserName }}
        </div>
        <div class="">
          <span>{{ language.executor }}：</span>{{ sortName }}
        </div>
        <div class="">{{ printTms }}</div>
      </div>

      <div class="usv-bg">
        <img
          src="@/assets/img/uav/uav-169bd5.png"
          :style="uavImgStyle"
          alt=""
        />
      </div>
    </div>
    <!-- 封面 end -->

    <!-- 基本信息 start -->
    <div class="basic-analyse data-analyse-model mb40" ref="contentWidth">
      <div class="layout-item">
        <div class="title" :style="fontSize20">{{ basicInfo.title }}</div>
        <div class="line"></div>
      </div>

      <analyse-canvas
        :title="basicInfo.uavInfo.title"
        class="mr40 mt40"
        :style="basicItemStyle"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div class="flight-height">
            <span class="height-left" :style="fontSize16">{{
              basicInfo.uavInfo.sn_id
            }}</span>
            <span class="height-right" :style="fontSize24">{{ snId }}</span>
          </div>
          <div class="flight-height">
            <span class="height-left" :style="fontSize16">{{
              basicInfo.uavInfo.code
            }}</span>
            <span class="height-right" :style="fontSize24">{{ uavId }}</span>
          </div>

          <div class="flight-height">
            <span class="height-left" :style="fontSize16">{{
              basicInfo.uavInfo.taskType
            }}</span>
            <span class="height-right" :style="fontSize24">{{
              missionType
            }}</span>
          </div>
        </template>
      </analyse-canvas>

      <!-- 飞行时间预览 -->
      <analyse-canvas
        :title="basicInfo.flightTime.title"
        class="mr40 mt40"
        :style="basicItemStyle"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div class="flight-height" :style="timeStyle">
            <span class="height-left" :style="fontSize16">{{
              basicInfo.flightTime.total
            }}</span>
            <span class="height-right" :style="fontSize18">
              {{ totalTms }}
            </span>
          </div>

          <div class="flight-height" :style="timeStyle">
            <span class="height-left" :style="fontSize16">{{
              basicInfo.flightTime.start
            }}</span>
            <span class="height-right" :style="fontSize18">
              {{ startTms }}
            </span>
          </div>

          <div class="flight-height" :style="timeStyle">
            <span class="height-left" :style="fontSize16">{{
              basicInfo.flightTime.end
            }}</span>
            <span class="height-right" :style="fontSize18">
              {{ endTms }}
            </span>
          </div>
        </template>
      </analyse-canvas>

      <!-- 水平速度概览 start -->
      <analyse-canvas
        :title="basicInfo.horizontalVelocity.title"
        class="mr40 mt40"
        :style="basicItemStyle"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.horizontalVelocity.average }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ horizontalVelocity.average }}
            </span>
            <span :style="fontSize16">m/s</span>
          </div>

          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.horizontalVelocity.max }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ horizontalVelocity.max }}
            </span>
            <span :style="fontSize16">m/s</span>
          </div>

          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.horizontalVelocity.min }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ horizontalVelocity.min }}
            </span>
            <span :style="fontSize16">m/s</span>
          </div>
        </template>
      </analyse-canvas>

      <!-- 垂直速度概览 -->
      <analyse-canvas
        :title="basicInfo.verticalVelocity.title"
        class="mr40 mt40"
        :style="basicItemStyle"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.verticalVelocity.average }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ verticalVelocity.average }}
            </span>
            <span :style="fontSize16">m/s</span>
          </div>

          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.verticalVelocity.max }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ verticalVelocity.max }}
            </span>
            <span :style="fontSize16">m/s</span>
          </div>

          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.verticalVelocity.min }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ verticalVelocity.min }}
            </span>
            <span :style="fontSize16">m/s</span>
          </div>
        </template>
      </analyse-canvas>

      <!-- 飞行高度概览 -->
      <analyse-canvas
        :title="basicInfo.flightHeight.title"
        class="mr40 mt40"
        :style="basicItemStyle"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.flightHeight.average }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ height.average }}
            </span>
            <span :style="fontSize16">m</span>
          </div>

          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.flightHeight.max }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ height.max }}
            </span>
            <span :style="fontSize16">m</span>
          </div>

          <div class="flight-height">
            <span class="height-left" :style="fontSize16">
              {{ basicInfo.flightHeight.min }}
            </span>
            <span class="height-right" :style="fontSize24">
              {{ height.min }}
            </span>
            <span :style="fontSize16">m</span>
          </div>
        </template>
      </analyse-canvas>

      <analyse-canvas
        :title="basicInfo.flightDistance.title"
        class="mr40 mt40"
        :style="basicItemStyle"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div class="" style="text-align: center">
            <span
              style="color: red; font-weight: 700"
              class="mr8"
              :style="fontSize36"
            >
              {{ distance }}
            </span>
            <span :style="fontSize16">m</span>
          </div>
        </template>
      </analyse-canvas>
    </div>
    <!-- 基本信息 end -->

    <!-- 速度折线图 start-->
    <div
      class="layout-item data-analyse-model"
      style="width: calc(100% - 80px); margin: 0 40px"
    >
      <div class="title" :style="fontSize20">{{ lineChart.title }}</div>
      <div class="line"></div>
    </div>
    <div class="line-chart data-analyse-model mb40">
      <analyse-canvas
        :title="lineChart.height.title"
        class="echart-item"
        :titleStyle="fontSize14"
        ref="canvasHeight"
      >
        <template v-slot:content>
          <div id="flight-height" class="echart-examples"></div>
        </template>
      </analyse-canvas>

      <analyse-canvas
        :title="lineChart.horizontalVelocity.title"
        class="echart-item"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div id="horizontal-velocity" class="echart-examples"></div>
        </template>
      </analyse-canvas>

      <analyse-canvas
        :title="lineChart.verticalVelocity.title"
        class="echart-item"
        :titleStyle="fontSize14"
      >
        <template v-slot:content>
          <div id="vertical-velocity" class="echart-examples"></div>
        </template>
      </analyse-canvas>

      <div class="mr42" style="width: calc(50% - 64px)"></div>
    </div>
    <!-- 速度折线图 end-->

    <!-- 飞行轨迹 start -->
    <div class="map-route data-analyse-model mb40">
      <div class="layout-item">
        <div class="title" :style="fontSize20">
          {{ language.flightpath.title }}
        </div>
        <div class="line"></div>
      </div>

      <div id="flight-track"></div>
    </div>
    <!-- 飞行轨迹 end-->

    <!-- 成果展示 start -->
    <div
      class="layout-item data-analyse-model"
      style="width: calc(100% - 80px); margin: 0 40px"
    >
      <div class="title" :style="fontSize20">
        {{ language.resultsShow.title }}
      </div>
      <div class="line"></div>
    </div>

    <ul
      class="achievement-ul data-analyse-model"
      v-for="(item, index) in achievementList"
      :key="index"
    >
      <li
        class="achievement-li"
        v-for="(row, len) in item"
        :key="index + '-' + len"
      >
        <img :src="row.o_url" alt="" v-if="!formatUrl(row.o_url)" />
        <div :class="'img' + row.id" v-if="formatUrl(row.o_url)"></div>
        <!-- <el-image :src="row.tifImg"></el-image> -->
        <!-- <img :src="tifImg[row.id]" alt=""  /> -->
        <!-- <img :src="row.p_url" alt="" /> -->
      </li>
    </ul>
    <!-- 成果展示 end -->
  </div>
</template>

<script>
import analyseCanvas from "./components/analyseCanvas.vue";
import { htmlSavePdf, htmlSaveImg, supplement } from "@/utils/htmlToPdf.js";
import maps from "@/utils/maps";
import { getLocalStorage } from "@/utils/storage.js";
import { getCsvData } from "@/views/routeReplay/index.js";
import { getMistiming, nowDate } from "@/utils/date.js";
import request from "@/utils/api";
import createMap from "@/utils/cesium/createMap";
import mapMethods from "@/utils/cesium/mapMethods";
export default {
  name: "data-analyse",
  components: {
    analyseCanvas,
  },
  data() {
    return {
      isShowOperation: true,
      sort_id: null,
      logoImg: require("@/assets/img/logo.png"),
      homeStyle: {
        // 封面，单独占一个
        height: "100vh",
      },
      magnification: 1,
      homeLineStyle: {
        width: "198px",
      },
      basicItemStyle: {
        // width: `calc(50% - 64px)`,
      },
      basicInfoStyle: {},
      canvasEchartsStyle: {},
      achievementList: [],

      // csv解析数据
      csvData: {},

      // 高度
      height: {
        average: 0,
        max: 0,
        min: 0,
      },

      // 水平速度
      horizontalVelocity: {
        average: 0,
        max: 0,
        min: 0,
      },
      // 垂直速度
      verticalVelocity: {
        average: 0,
        max: 0,
        min: 0,
      },

      startTms: "00:00:00",
      endTms: "00:00:00",
      snId: null,
      uavId: null,
      missionType: null,
      totalTms: "00:00:00",
      distance: "0.00",
      printTms: "00:00:00",
      loading: null,
      imgLoading: false,
    };
  },
  computed: {
    fontSize14() {
      return {
        fontSize: `calc(14px * ${this.magnification})`,
      };
    },
    fontSize16() {
      return {
        fontSize: `calc(16px * ${this.magnification})`,
      };
    },
    fontSize18() {
      return {
        fontSize: `calc(18px * ${this.magnification})`,
      };
    },
    fontSize20() {
      return {
        fontSize: `calc(20px * ${this.magnification})`,
      };
    },
    fontSize24() {
      return {
        fontSize: `calc(24px * ${this.magnification})`,
      };
    },
    fontSize32() {
      return {
        fontSize: `calc(32px * ${this.magnification})`,
        "letter-spacing": this.$language == "chinese" ? "10px" : 0,
      };
    },
    fontSize36() {
      return {
        fontSize: `calc(36px * ${this.magnification})`,
      };
    },
    logoSize() {
      return {
        width: `calc(171px * ${this.magnification})`,
      };
    },
    timeStyle() {
      return {
        height: `calc(36px * ${this.magnification})`,
        // fontSize: `calc(18px * ${this.magnification})`,
      };
    },
    uavImgStyle() {
      return {
        width: `calc(300px * ${this.magnification})`,
      };
    },
    language() {
      return this.$languagePackage.dataAnalyse;
    },
    basicInfo() {
      return this.language.basicInfo;
    },
    lineChart() {
      return this.language.lineChart;
    },
  },
  watch: {
    magnification: function (val) {
      this.$nextTick(() => {
        let lineW = this.$refs.homeCode.offsetWidth;
        if (val === 1) {
          lineW = 198;
        }
        this.homeLineStyle.width = lineW + 20 + "px";
      });
    },
  },
  beforeDestroy() {},
  mounted() {
    this.$nextTick(() => {});
  },
  created() {
    this.loading = this.$loading({
      lock: true,
      text: this.language.message.loading,
      spinner: "el-icon-loading",
      background: "rgba(0, 0, 0, 0.7)",
    });

    this.printTms = nowDate().dateTime;
    this.dataInit(null, true);
  },
  methods: {
    dataInit: function (row, download) {
      if (!row) {
        let sort_id = this.$route.query.sort_id;
        this.routeQuery = this.$route.query;
        if (!sort_id) {
          return this.$message({
            type: "error",
            message: this.language.message.dataGetErr,
          });
        }
        this.sort_id = "dataAnalyse" + sort_id;
        this.taskInfo = getLocalStorage(this.sort_id);
      }

      // 获取飞行日志基本信
      this.startTms = this.taskInfo.start_time;
      this.endTms = this.taskInfo.end_time;
      this.uavId = this.taskInfo.uav_id;
      this.snId = this.taskInfo.sn_id;
      this.missionType = this.taskInfo.mission_type_label;
      this.totalTms = getMistiming(this.startTms, this.endTms).time;
      this.sortId = this.taskInfo.sort_id;
      this.sortName = this.taskInfo.user_name;

      //
      let userInfo = getLocalStorage("userInfo");
      this.pringUserName = userInfo.nick;

      let url = this.taskInfo.file_url;
      return new Promise((resolve, reject) => {
        getCsvData(url)
          .then((item) => {
            this.csvData = item;

            // 高度
            this.height = this.dataDispose(item.hData);
            this.horizontalVelocity = this.dataDispose(item.hsData);
            this.verticalVelocity = this.dataDispose(item.vsData);
            this.distance = item.distance;
            this.$nextTick(() => {
              Promise.all([
                this.echartsInit(),
                this.mapTrack(),
                this.resultsList(),
              ]).then((item) => {
                // 外部调用时直接
                this.$nextTick(() => {
                  this.changeUrl(0, 0);
                  let time = setInterval(() => {
                    if (document.readyState == "complete") {
                      if (this.imgLoading) {
                        resolve("加载完毕");
                        download && this.htmlToPdfEvent();
                        clearInterval(time);
                      }
                    }
                  }, 400);
                });
              });
            });
          })
          .catch((err) => {
            this.$message({
              type: "error",
              message: this.language.message.filesAnalysisErr,
            });
            reject(err);

            setTimeout(() => {
              window.close();
            }, 1000);
            console.error(err);
          });
      });
    },

    // 数据处理，获取最大、最小、平均值
    dataDispose: function (list) {
      let min = 0;
      let max = 0;
      let total = 0;
      for (let i = 0; i < list.length; i++) {
        let item = Number(list[i]);
        total += item * 100;
        if (min > item) {
          min = item;
        }
        if (max < item) {
          max = item;
        }
      }

      return {
        average: (total / list.length / 100).toFixed(2),
        max: max.toFixed(2),
        min: min.toFixed(2),
      };
    },

    resultsList: function () {
      let params = {
        search: this.taskInfo.sort_id,
        sn_id: "",
        type: 10,
        start_tms: "",
        end_tms: "",
        pmd: "page",
        page: 0,
        size: 100,
        jointPmd: true,
      };

      return new Promise((resolve, reject) => {
        request("resultList", params).then((res) => {
          // this.achievementList = res.data.list;
          let list = res.data.list;
          let data = [];
          if (list) {
            for (let i = 0; i < list.length; i += 4) {
              data.push(list.slice(i, i + 4));
            }
          }

          this.achievementList = data;

          this.$nextTick(() => {
            resolve("成果列表");
          });
        });
      });
    },

    htmlToPdfEvent: function () {
      this.isShowOperation = false;

      let pageH = (this.$refs.dataAnalyse.offsetWidth / 595) * 842;
      this.magnification = 1.8;

      this.homeStyle = {
        height: pageH + "px",
      };
      let width = "";
      if (this.$refs.contentWidth.offsetWidth * 0.33 < 600) {
        width = `calc(50% - 64px)`;
      } else {
        width = `calc(33.3333% - 64px)`;
      }
      this.basicItemStyle = {
        width: width,
      };

      this.$nextTick(() => {
        setTimeout(() => {
          supplement({
            el: this.$refs.dataAnalyse,
            className: "data-analyse-model",
            fillStyle: {},
          })
            .then(() => {
              // console.log(this.startTms,this.sortName)
              let tms = this.startTms
                .replaceAll("-", "")
                .replaceAll(":", "")
                .replaceAll(" ", "");
              let a = this.taskInfo.mission_name + tms;
              return htmlSavePdf("data-analyse", a);
            })
            .then(() => {
              this.isShowOperation = true;
              this.magnification = 1;
              this.homeStyle = {
                height: "100vh",
              };
              this.basicItemStyle = {};
              this.basicInfoStyle = {};

              this.loading.close();
              let sort_id = this.$route.query.sort_id;
              localStorage.removeItem("dataAnalyse" + sort_id);
              window.close();
            });
        }, 20);
      });
    },

    htmlToimg: function () {
      this.isShowOperation = false;
      setTimeout(() => {
        htmlSaveImg("data-analyse", this.sortId).then(() => {
          this.isShowOperation = true;
        });
      }, 20);
    },

    echartsInit: function () {
      // 飞行高度
      this.generateEchaert({
        id: "flight-height",
        seriesName: this.lineChart.height.chartName,
        lineColor: "#F38E00",
        colorStops: [
          {
            offset: 1,
            color: "rgba(138,84,10,0.34)", // 0% 处的颜色
          },
          {
            offset: 0,
            color: "rgba(255,175,0, 1)", // 100% 处的颜色
          },
        ],
        xAxisData: this.csvData.xAxisData,
        seriesData: this.csvData.hData,
      });

      // 垂直速度
      this.generateEchaert({
        id: "vertical-velocity",
        seriesName: this.lineChart.horizontalVelocity.chartName,
        lineColor: "#2947B2",
        colorStops: [
          {
            offset: 1,
            color: "rgba(83,249,0,0.3)", // 0% 处的颜色
          },
          {
            offset: 0,
            color: "rgba(90,226,32, 1)", // 100% 处的颜色
          },
        ],
        xAxisData: this.csvData.xAxisData,
        seriesData: this.csvData.vsData,
      });

      // 水平速度
      this.generateEchaert({
        id: "horizontal-velocity",
        seriesName: this.lineChart.verticalVelocity.chartName,
        lineColor: "#4CDC4C",
        colorStops: [
          {
            offset: 1,
            color: "rgba(0,220,255,0.3)", // 0% 处的颜色
          },
          {
            offset: 0,
            color: "rgba(0,0,241, 1)", // 100% 处的颜色
          },
        ],
        xAxisData: this.csvData.xAxisData,
        seriesData: this.csvData.hsData,
      });
      return new Promise((resolve) => {
        this.$nextTick(() => {
          resolve("图表加载完毕-----");
        });
      });
    },

    //
    generateEchaert: function (config) {
      let { lineColor, xAxisData, colorStops, seriesData, id, seriesName } =
        config || {};

      let option = {
        color: lineColor, //设置线条颜色
        tooltip: {
          trigger: "axis",
          axisPointer: {},
        },
        toolbox: {
          show: false,
          feature: {
            saveAsImage: {},
          },
        },
        grid: {
          left: "20px",
          right: "20px",
          bottom: "0px",
          top: "20px",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: xAxisData,
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: seriesName,
            type: "line",
            stack: "Total",
            areaStyle: {
              normal: {
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: colorStops,
                  globalCoord: false, // 缺省为 false
                },
              },
            },
            emphasis: {
              focus: "series",
            },
            symbol: "none",
            data: seriesData,
          },
        ],
      };
      let echarts = this.$echarts.init(document.getElementById(id));
      echarts.setOption(option);
      return echarts;
    },

    // 地图轨迹
    mapTrack: function () {
      return new Promise((resolve) => {
        let map = createMap.createMap("flight-track", {
          layerIndex: 1,
          modeIndex: 1,
          sceneModeButton: false,
        });
        let logLatList = [];
        this.csvData.logLatList.forEach((item) => {
          logLatList.push({
            lng: item[0],
            lat: item[1],
            height: 1,
          });
        });
        let polyLine = mapMethods.drawLine(logLatList, {
          color: "#2288ff",
          width: 6,
        });
        map.entities.add({ id: "polyline", polyline: polyLine });
        setTimeout(() => {
          let mapEntity = map.entities.getById("polyline");
          map.zoomTo(
            mapEntity,
            new Cesium.HeadingPitchRange(
              Cesium.Math.toRadians(90),
              Cesium.Math.toRadians(0),
              Cesium.Math.toRadians(100000)
            )
          );
        }, 200);

        var helper = new Cesium.EventHelper();
        helper.add(map.scene.globe.tileLoadProgressEvent, function (e) {
          console.log("每次加载地图服务矢量切片都会进入这个回调", e);
          if (e == 0) {
            console.log("矢量切片加载完成时的回调");
            setTimeout(() => {
              resolve("地图加载完毕");
            }, 1000);
          }
        });

        // maps
        //   .initMap("flight-track", {
        //     mapStyle: "amap://styles/92907bd07b27bf2b8ca66585015fdc7a",
        //     locationCode: false,
        //   })
        //   .then((map) => {
        //     map.setLayers([new AMap.TileLayer.Satellite()]);
        //     map.setZooms([3, 30]);
        //     // 创建折线实例
        //     const polyline = new AMap.Polyline({
        //       map: map,
        //       path: this.csvData.logLatList,
        //       showDir: true,
        //       strokeColor: "#28F", //线颜色
        //       strokeWeight: 6, //线宽
        //     });
        //     map.setFitView(polyline, false, [80, 80, 0, 0], 30);
        //     map.on("complete", function () {
        //       // 地图图块加载完成后触发
        //       resolve("地图加载完毕");
        //     });
        //     this.$nextTick(() => {});
        //   });
      });
    },
    changeUrl(index, i) {
      if (this.achievementList && this.achievementList.length) {
        if (index < this.achievementList.length) {
          if (i < this.achievementList[index].length) {
            if (
              this.achievementList[index][i].o_url.indexOf(".tiff") !== -1 ||
              this.achievementList[index][i].o_url.indexOf(".tif") !== -1
            ) {
              this.tiffImageLoad(this.achievementList[index][i].o_url).then(
                (res) => {
                  let img = document.createElement("img"); // 要插入的表情img
                  img.src = res;
                  img.setAttribute("style", `width:100%;`);
                  let doc = document.getElementsByClassName(
                    "img" + this.achievementList[index][i].id
                  )[0];
                  doc.appendChild(img);
                  setTimeout(() => {
                    this.changeUrl(index, i + 1);
                  }, 100);
                }
              );
            } else {
              this.changeUrl(index, i + 1);
            }
          } else {
            this.changeUrl(index + 1, 0);
          }
        } else {
          this.imgLoading = true;
        }
      } else {
        // setTimeout(() => {
        //   this.changeUrl(0,0);
        // }, 200);
        this.imgLoading = true;
      }
    },
    formatUrl: function (url) {
      if (url.indexOf(".tiff") !== -1 || url.indexOf(".tif") !== -1) {
        return true;
      } else {
        return false;
      }
    },
    tiffImageLoad: function (url) {
      return new Promise((resolve, reject) => {
        try {
          let xhr = new XMLHttpRequest();
          xhr.open("GET", url, true); //filename为tif文件地址
          xhr.responseType = "arraybuffer";
          xhr.onload = function (e) {
            let buffer = xhr.response;
            Tiff.initialize({ TOTAL_MEMORY: 100 * 1024 * 1024 });
            let tiff = new Tiff({ buffer: buffer });
            const imgData = tiff.toDataURL(); // 使用base64调此方法
            // var canvas = tiff.toCanvas();
            resolve(imgData);
          };
          xhr.send();
        } catch (error) {
          reject(error);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@bili: 1920 / 595;

#data-analyse {
  width: 100%;
  min-height: 100vh;
  // background-color: rgba(0, 0, 0, 0.8);
  .header-operation {
    position: fixed;
    top: 0;
    right: 40px;
    z-index: 21;
  }
  .analyse-home {
    width: 100%;
    height: 100vh;
    position: relative;
    page-break-inside: avoid;
    .home-logo {
      position: absolute;
      left: 0;
      top: 0;
      // padding: 30px;
      display: flex;
      align-items: center;
      // padding: 0 40px;
      position: relative;
      .login-logo {
        display: flex;
        align-items: center;
        padding: 12px 50px 12px 40px;
        border-width: 0px;
        background: inherit;
        // background-color: rgba(22, 155, 213, 1);
        border: none;
        border-radius: 20px;
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        box-shadow: none;
      }
      h1 {
        margin-left: 20px;
        letter-spacing: 8px;
        // text-shadow: 1px 1px 0 #ccc, 2px 2px 0 #ccc,
        //   /* end of 2 level deep grey shadow */ 3px 3px 0 #444, 4px 4px 0 #444,
        //   5px 5px 0 #444, 6px 6px 0 #444; /* end of 4 level deep dark shadow */
      }
    }
    .homt-main {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      // flex-wrap: wrap;
      flex-direction: column;
      h1 {
        // color: rgb(67, 140, 255);
        letter-spacing: 10px;
        padding: 0;
        margin: 0;
      }
      .line {
        margin: 15px 0;
        // width: (@bili * 218px);
        height: 3px;
        border-radius: 10px;
        // background-image: linear-gradient(
        //   to right,
        //   rgba(120, 234, 245, 0.5),
        //   rgb(120, 234, 245),
        //   rgba(120, 234, 245, 0.5)
        // );
      }
      .sort-code {
        // font-size: calc(@bili * 18px);
        font-weight: 700;
        letter-spacing: 3px;
      }
    }
    .user-info {
      position: absolute;
      left: 0;
      bottom: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      width: 100%;
      // color: #333;
      // font-size: calc(@bili * 16px);
    }
    .usv-bg {
      position: absolute;
      right: 0;
      top: 0;
      // margin: -150px 0 0 -150px;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 300px;
        opacity: 0.1;
      }
    }
  }

  .basic-analyse {
    display: flex;
    flex-wrap: wrap;
    padding: 0px 0 0px 40px;
    justify-content: center;
    .flight-height {
      margin-left: 16px;
      font-size: 14px;
      display: flex;
      align-items: center;
      .height-left {
        // color: rgba(22, 155, 213, 1);
        font-size: 16px;
      }
      .height-right {
        // color: rgb(0, 98, 255);
        font-size: 24px;
        font-weight: 700;
        margin: 0 8px;
      }
    }
  }

  .map-route {
    height: 100vh;
    padding: 40px;
    display: flex;
    flex-direction: column;
    .layout-item {
      width: 100%;
    }
    #flight-track {
      width: 100%;
      height: 90%;
    }
  }

  .achievement-img {
    // width: 100%;
    padding: 40px;
    img {
      width: 100%;
    }
  }

  .layout-item {
    // padding-top: 20px;
    padding: 40px 0;
    width: calc(100% - 40px);
    margin-right: 40px;
    .title {
      font-size: 20px;
      font-weight: 700;
      // color: rgb(0, 179, 255);
      padding-left: 20px;
    }
    .line {
      margin-top: 12px;
      height: 3px;
      width: 100%;
      // background-color: rgba(22, 155, 213, 1);
    }
  }

  .achievement-ul {
    margin: 0;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    padding: 0 0 40px 40px;
    align-items: center;

    // margin-bottom: 40px;
    .achievement-li {
      list-style: none;
      width: calc(25% - 40px);
      margin-right: 40px;
      img {
        width: 100%;
      }
    }
  }

  .line-chart {
    display: flex;
    flex-wrap: wrap;
    padding: 0 0 0 40px;
    .echart-item {
      width: calc(50% - 64px);
      height: 520px;
      margin-right: 40px;
      margin-top: 40px;
      .echart-examples {
        width: 100%;
        height: calc(100% - 100px);
      }
    }
  }
}
</style>