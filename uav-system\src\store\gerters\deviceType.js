import requestHttp from "@/utils/api";
const deviceType = {
  state: {
    typeList: []
  },
  mutations: {
    setTypeList(state, val) {
      state.typeList = val

    }
  },
  actions: {
    requestTypeList(content) {
      requestHttp("userGetDeviceType").then((res) => {
        if (res.data && res.data.length) {
          content.commit("setTypeList", res.data);
        }
      })
    }
  }

}
export default deviceType
