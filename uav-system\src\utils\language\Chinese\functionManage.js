/**
 * 功能管理
 */
const functionManage = {
    addTitle: "添加功能项",
    editTitle: "编辑功能项",

    tableOperate: {
        edit: "编辑",
        delete: "删除"
    },

    table: {
        name: "功能名",
        name_en: "功能英文名",
        fun_id: "功能ID",
        description: "功能描述",
        state: "状态",
        notes: "备注",
        operation: "操作"
    },
    delete: {
        content: "此操作将永久删除该数据, 是否继续?",
        title: "提示",
        confirmText: "确定",
        cancelText: "取消",
        success: "删除成功!"
    },
    addFun: {
        placeholder: {
            name: "请输入功能名",
            name_en: "请输入功能英文名",
            funID: "请输入功能ID",
            description: "请输入功能描述",
            notes: "请输入备注"
        },
        label: {
            name: "功能名",
            name_en: "功能英文名",
            funId: "功能ID",
            description: "功能描述",
            state: "状态",
            notes: "备注"
        },
        verify: {
            name: ["请输入功能名称"],
            name_en: ["请输入功能英文名称"],
            funID: ["请输入功能ID"],
            description: ["请输入功能描述"]
        },
    }
}

export default functionManage;