const taskManage = {
    language: 'en-US',
    taskState: [
        { label: 'All', value: '' },
        { label: "Waiting for execution", value: 1 },
        { label: "Pause task", value: 3 },
        { label: "Sent successfully", value: 10 },
        { label: "Sending failed:Timeout", value: 40 },
        { label: "Sending failed:Not online", value: 41 },
    ],
    dataState: [
        { label: 'All', value: '' },
        { label: "Normal", value: 0 },
        { label: "Deleted", value: 10 },
    ],
    dataStateList: [
        { label: "Normal", value: 0 },
        { label: "Delete", value: 10 },
    ],
    submit: 'Confirm',
    cancel: 'Cancel',
    column: [
        { label: "Fence Name", prop: "f_title" },
        { label: "Route Name", prop: "m_title" },
        { label: "SN_ID", prop: "sn_id" },
        { label: "Execution Time", prop: "task_tms" },
        { label: "Execution Status", prop: "task_state" },
        { label: "Creation Time", prop: "create_time" },
        { label: "operation", prop: "operation" },
    ],
    edit: 'Edit',
    NotEditable: 'Not editable',
    delete: 'Delete',
    deleted: 'Deleted',
    delTip: 'The scheduled task has been deleted!',
    delSumitTip: 'Are you sure you want to delete this scheduled task?',
    tip: 'Confirm Delete',
    delSuccess: 'Timed task deleted successfully!',
    editTask: 'Edit Task',
    taskTime: 'Execution Time',
    taskStateLabel: 'Execution Status',
    dataStateLabel: 'Status',
    placeholderTime: 'Select date and time',
    editSuccess: 'Task edited successfully!',
    searchTip: 'Fuzzy matching search',
    searchBtn: 'search'
}
export default taskManage