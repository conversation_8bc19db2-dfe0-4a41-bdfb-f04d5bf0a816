const user = {
    userInfo: {
        title: "Personal Details",
        label: {
            nick: "account",
            account: "phone/Email",
            startTime: "start time",
            endTime: "end time"
        },
        placeholder: {
            nick: "enter account number",
            account: "enter mobile phone/email",
            startTime: "select the start time",
            endTime: "select the end time"
        },
        validity: "permanent validity"
    },

    passSet: {
        title: "Password Setting",
        label: {
            password: "old password",
            newPass: "new password",
            affirmPass: "confirm password"
        },
        placeholder: {
            password: "enter your old password",
            newPass: "enter new password",
            affirmPass: "enter new password again"
        },
        verify: {
            password: [
                "enter your old password",
                "The length ranges from 6 to 32 characters"
            ],
            newPass: [
                "enter a new password",
                "The length ranges from 6 to 32 characters"
            ],
            affirmPass: [
                "Enter the new password again",
                "The length ranges from 6 to 32 characters",
                "The two passwords are different!"
            ]
        },
        confirm: {
            content: "The password is changed successfully. Please log in again.",
            title: "hint",
            cancelText: "cancel",
            confirmText: "confirm"
        }
    },

    accountSet: {
        title: "Account Information",
        label: {
            nick: "account",
            account: "phone/Email",
            startTime: "start time",
            endTime: "end time",
            in_com: 'Company/Unit',
        },
        placeholder: {
            nick: "enter the account",
            account: "enter mobile phone/email"
        },
        validity: "permanent validity",
        verify: {
            nick: ["enter your account nickname"],
            account: [
                "enter mobile phone/email",
                "Enter the correct cell phone number or email"
            ]
        },
        hint: {
            success: "modify successful"
        }
    },

    systemSet: {
        title: "Language setup",
        label: {
            langue: "language",
            theme: "theme"
        },
        message: {
            cut: "Switching languages"
        }
    }

}

export default user;