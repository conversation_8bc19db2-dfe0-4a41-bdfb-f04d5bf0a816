/**
 * 存储信息
 */

/**
 * 设置单个cookie
 * @param { key } 键
 * @param { value } 值
 * @param { hour } 过期时间(小时)，如果不设置，关闭浏览器则过期
 */
export function setCookie(key, value, hour) {
  if (!key) {
    return Promise.reject("键不能为空");
  }
  let date = new Date();
  let time = hour * 60 * 60 * 1000;
  date.setTime(date.getTime() + time);
  document.cookie = key + "=" + value + ";expires=" + date.toGMTString() + ";path=/";
}

/**
 * 读取Cookie
 * @param {key} 键
 */
export function getCookie(key) {
  let reg = new RegExp("(^| )" + key + "=([^;]*)(;|$)");
  let list = document.cookie.match(reg);
  return list && list[2] ? list[2] : "";
}

/**
 * 删除cookie
 * @param {key} 键
 */
export function removeCookie(key) {
  setCookie(key, null, -24);
}

/**
 * 设置多个cookie
 * @param { list } 列表 
 */
export function setMultiCookie(list) {
  for (let i = 0; i < list.length; i++) {
    let {
      key,
      value,
      hour
    } = list[i];
    setCookie(key, value, hour);
  }
}

/**
 * 设置多个cookie，对象
 * @param {item} 对象格式
 * @param {hour} 过期时间，每一个cookie相同
 */
export function setMultiCookieObject(item, hour) {
  for (let k in item) {
    setCookie(k, item[k], hour);
  }
}

/**
 * 获取多个cookie
 * @param { params } 对应的列表，数组或字符串(以逗号隔开)
 */
export function getMultiCookie(params) {
  let list = params;

  if (!Array.isArray(params)) {
    list = params.split(",");
  }

  let cookieObj = {};
  for (let i = 0; i < list.length; i++) {
    let key = list[i];
    let value = getCookie(key);
    cookieObj[key] = value;
  }

  return cookieObj;
}

/**
 * 删除多个cookie
 * @param { params } 对应的列表，数组或字符串(以逗号隔开)
 */
export function removeMultiCookie(params) {
  let list = params;
  if (!Array.isArray(params)) {
    list = params.split(",");
  }
  for (let i = 0; i < list.length; i++) {
    let key = list[i];
    removeCookie(key);
  }
}

// 使用localStorage
export function setLocalStorage(key, val) {
  try {
    if (typeof val === 'object') {
      val = JSON.stringify(val);
    }
    localStorage.setItem(key, val);
    return true;
  } catch (error) {
    return false;
  }
}

export function getLocalStorage(key) {
  let val = localStorage.getItem(key);
  let data = null;

  try {
    data = JSON.parse(val);
  } catch (error) {
    data = val;
  }

  return data;
}
