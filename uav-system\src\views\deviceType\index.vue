<template>
  <div class="deviceType">
    <div class="header">
      <el-button type="primary" @click="addType">{{
        language.addBtn
      }}</el-button>
    </div>
    <custom-table
      :column="column"
      :isShowPage="false"
      :dataTier="true"
      urlName="deviceTypeList"
      ref="deviceTypeList"
    >
      <template #state="scope">
        {{ formatState(scope.row.state) }}
      </template>
      <template #create_time="scope">
        {{ timeFormat(scope.row.create_time) }}
      </template>
      <template #modified_time="scope">
        {{ timeFormat(scope.row.modified_time) }}
      </template>
      <template #operation="scope">
        <el-button type="primary" @click="editType(scope.row)">{{
          language.editBtn
        }}</el-button>
      </template>
    </custom-table>
    <type-dialog ref="typeDialog" @refresh="refresh"></type-dialog>
  </div>
</template>
<script>
import customTable from "@/components/customTable/index";
import { nowDate } from "@/utils/date";
import typeDialog from "./modules/typeDialog.vue";
export default {
  data() {
    return {
      column: [],
      stateList: [],
    };
  },
  computed: {
    language() {
      return this.$languagePackage.deviceType;
    },
  },
  components: {
    customTable,
    typeDialog,
  },
  created() {
    this.column = this.language.column;
    this.column.forEach((item) => {
      if (item.prop == "cla_type") {
        item.map = this.$mapDict("devictType");
      }
    });
    this.stateList = JSON.parse(JSON.stringify(this.$dict.deviceTypeState));
    this.stateList.forEach((item) => {
      if (item.value == 30) {
        item.label = this.language.deleted;
      }
    });
  },
  methods: {
    timeFormat(time) {
      return nowDate(time).dateTime;
    },
    formatState(state) {
      let str = "";
      this.stateList.forEach((item) => {
        if (item.value == state) {
          str = item.label;
        }
      });
      return str;
    },
    addType: function () {
      this.$refs.typeDialog.open();
    },
    editType: function (row) {
      this.$refs.typeDialog.open(row);
    },
    refresh: function () {
      this.$refs.deviceTypeList.refresh();
    },
  },
};
</script>
<style lang="less" scoped>
.deviceType {
  background-color: #040404;
  width: 100%;
  height: 100%;
  padding: 2%;
  padding-top: 1%;
  box-sizing: border-box;
  .header {
    margin-bottom: 6px;
    text-align: right;
  }
}
</style>