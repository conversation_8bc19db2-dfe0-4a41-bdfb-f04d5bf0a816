<template>
  <div class="equipment">
    <div class="searchDiv">
      <el-input
        v-model="searchValue"
        :placeholder="equipLanguage.input.placeholder"
        @keyup.enter.native="searchCode ? '' : searchEvent()"
        clearable
      ></el-input>
      <el-button
        @click="searchCode ? '' : searchEvent()"
        :class="searchCode ? 'active' : ''"
        >{{ equipLanguage.button.searchBtn }}</el-button
      >
      <el-button
        class="addButton"
        icon="el-icon-plus"
        @click="createEquip"
        :class="clickCode ? 'active' : ''"
        >{{ equipLanguage.button.addEquip }}</el-button
      >
    </div>
    <div class="content" ref="content">
      <el-collapse v-model="activeName" accordion @change="openCollapseItem">
        <el-collapse-item
          :name="item.sn_id"
          v-for="(item, index) in deviceList"
          :class="index % 2 == 0 ? '' : 'el-collapse-item_1'"
          :key="index"
          ref="collapseItem"
          v-show="checkOpen ? (checkOpen == item.sn_id ? true : false) : true"
        >
          <template slot="title">
            <span class="num">{{ index + 1 }}</span>
            <el-image :src="item.imgSrc" fit="contain"></el-image>
            <div class="content-item-1">
              <el-tag
                :class="item.is_push_on ? 'firstDiv' : 'noneDiv noneDiv1'"
                >{{
                  item.is_push_on
                    ? equipLanguage.equipInfo.equipStateIn
                    : equipLanguage.equipInfo.equipStateOut
                }}</el-tag
              >
              <!-- <el-tag :class="item.is_pull_on ? 'secondDiv' : 'noneDiv'">{{
                item.is_pull_on
                  ? equipLanguage.equipInfo.personalStatus
                  : equipLanguage.equipInfo.personalStatusNo
              }}</el-tag> -->
              <button-popup :deviceItem="item" ref="buttonPopup"></button-popup>
            </div>
            <div class="content-item-2">
              <el-tag :class="item.state == 20 ? 'error' : ''">{{
                item.name
              }}</el-tag>
              <el-tag :class="item.state == 20 ? 'error' : ''"
                ><span class="spans"
                  >{{ equipLanguage.equipInfo.equipNum }}：</span
                >{{ item.sn_id }}</el-tag
              >
            </div>
            <div class="content-item-3">
              <div
                class="content-item-3-1"
                :class="item.state == 20 ? 'error' : ''"
                v-if="item.address ? true : false"
              >
                {{ equipLanguage.equipInfo.address }}：{{ item.address }}
              </div>
              <div
                class="task-data"
                v-if="
                  item.task_list &&
                  item.task_list.list &&
                  item.task_list.list.length
                "
              >
                <div class="circle-point"></div>
                <div class="task-data-content">
                  {{ equipLanguage.equipInfo.taskExecuted
                  }}<span style="color: #33e933; padding: 0 5px">{{
                    item.task_list.list.length
                  }}</span
                  >{{ equipLanguage.equipInfo.unit }}
                </div>
              </div>
            </div>
            <div class="operation">
              <el-button
                @click.stop="judgeToVideo(item)"
                v-if="item.judgeVideo"
              >
                {{ equipLanguage.button.outCabinVCR }}
              </el-button>

              <el-button
                @click.stop="setEquip(item)"
                :class="item.sn_id == sn_id ? 'active' : ''"
                v-show="!checkOpen"
              >
                <el-image
                  v-show="item.sn_id === sn_id"
                  :src="setImg1"
                  fit="contain"
                ></el-image>
                <el-image
                  v-show="item.sn_id !== sn_id"
                  :src="setImg"
                  fit="contain"
                ></el-image>
                {{ equipLanguage.button.setEquip }}
              </el-button>
              <el-button
                @click.stop="delEquip(item)"
                :class="delCode == item.sn_id ? 'active' : ''"
                v-show="!checkOpen"
              >
                <el-image
                  :src="delCode == item.sn_id ? delImg1 : delImg"
                  fit="contain"
                ></el-image>
                {{ equipLanguage.button.delEquip }}
              </el-button>
              <!-- <el-button v-show="checkOpen" class="firmware">{{equipLanguage.button.firmwareVersion}}{{versionCode}}</el-button> -->
              <!-- <calibration-list></calibration-list> -->
              <el-button
                v-if="!item.isAlone && checkOpen"
                @click.stop="openCalibration"
                class="firmware"
                >{{ equipLanguage.button.calibration }}</el-button
              >
              <el-popover
                placement="left-start"
                trigger="click"
                v-if="!item.isAlone && checkOpen && isShowData"
                popper-class="version-list-box"
              >
                <el-button slot="reference" class="firmware" @click.stop>{{
                  equipLanguage.button.firmwareVersion
                }}</el-button>
                <div class="version-list">
                  <div
                    class="version-item"
                    v-for="item in versionList"
                    :key="item.id"
                  >
                    <div class="label">{{ item.label }}</div>
                    <div class="">
                      {{ item.value ? formatVersion(item.value) : "" }}
                    </div>
                  </div>
                </div>
              </el-popover>
              <el-button
                v-if="!item.isAlone && checkOpen && !isShowData"
                class="firmware"
                @click.stop="clickTip"
                >{{ equipLanguage.button.firmwareVersion }}</el-button
              >
              <el-button
                :class="upgradeCode == item.sn_id ? 'active' : ''"
                v-show="item.type !== 200 && checkOpen"
                @click.stop="upgradeFirmware(item)"
                >{{ equipLanguage.button.Upgrade }}</el-button
              >
            </div>
          </template>
          <fixed-type
            ref="contentInfo"
            :video="video"
            :onKey="onKey"
            :operationList.sync="operationList"
            :weatherState="weatherState"
            :watchError.sync="watchError"
            :websocket1="websocket1"
            :uavState.sync="uavState"
            :changeStateCode.sync="changeStateCode"
            :equipLanguage="equipLanguage"
            :equipState="equipCode"
            :showOnKeyError="showOnKeyError"
            :deviceItemList="deviceItemList"
            :equipItem="item"
            v-if="item.isStable && checkOpen == item.sn_id"
          ></fixed-type>
          <single-soldier-uav
            :deviceItem="deviceItem"
            :cameraList="cameraList"
            :uavItemList="uavItemList"
            :websocket1="websocket1"
            v-if="item.isAlone && checkOpen == item.sn_id"
            :equipState="equipCode"
            :equipLanguage="equipLanguage"
          ></single-soldier-uav>
          <mobile-nest
            v-if="item.isMobile && checkOpen == item.sn_id"
            :video="video"
            :onKey="onKey"
            :operationList.sync="operationList"
            :isfloat="isfloat"
            :uavItemList="uavItemList"
            :deviceItem="deviceItem"
            :watchError.sync="watchError"
            :websocket1="websocket1"
            :uavState.sync="uavState"
            :changeStateCode.sync="changeStateCode"
            :equipLanguage="equipLanguage"
            :equipState="equipCode"
            ref="contentInfo"
          ></mobile-nest>
        </el-collapse-item>
      </el-collapse>
    </div>
    <el-pagination
      :class="className"
      layout="prev, pager, next"
      :total="total_page"
      @current-change="changePage"
      @prev-click="prevClick"
      @next-click="nextClick"
    >
    </el-pagination>
    <operation-device
      ref="operationDevice"
      :uavtypeList="uavtypeList"
      :clickCode.sync="clickCode"
      @chooseSize="chooseSize"
      @closeDialog="closeDialog"
      @getMentData="getMentData"
    ></operation-device>
    <map-site
      ref="mapSite"
      :site="site"
      :equipLanguage="equipLanguage"
      @siteData="backData"
    ></map-site>
    <firmware-Operate
      ref="firmwareOperate"
      :code="true"
      @refresh="() => (upgradeCode = false)"
      @updateVersion="updateVersion"
    ></firmware-Operate>
    <version-rate
      ref="versionRate"
      :versionList="versionList"
      :deviceItemList="deviceItemList"
      @reStart="reStart"
    ></version-rate>
    <calibration-list-dialog
      ref="calibrationListDialog"
    ></calibration-list-dialog>
    <!-- <el-upload
      class="upload-demo"
      ref="upload"
      action="http://app.walkera.com/nedv/sorties/upload"
      :data="params"
      :before-upload="beforeFile"
      :on-success="successFile"
      :on-error="errorFile"
    >
      <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
    </el-upload> -->
  </div>
</template>
<script>
import mapSite from "./components/chooseSite";
import requestHttp from "@/utils/api";
import fixedType from "./components/fixedType";
import { Websockets } from "../../utils/websocketUntil";
import baseUrl from "../../utils/global";
import { errorMsg } from "../../utils/errorMsg";
import singleSoldierUav from "./components/singleSoldierUav";
import mobileNest from "./components/mobileNest.vue";
import firmwareOperate from "../firmware/components/firmwareOperate.vue";
import versionRate from "./components/versionRate.vue";
import calibrationListDialog from "./components/calibrationListDialog.vue";
import buttonPopup from "./components/buttonPopup.vue";
import { typeJudge } from "@/utils/deviceTypeJudge";
import operationDevice from "./components/operationDevice.vue";
export default {
  name: "equipment",
  components: {
    mapSite,
    fixedType,
    singleSoldierUav,
    mobileNest,
    firmwareOperate,
    versionRate,
    calibrationListDialog,
    buttonPopup,
    operationDevice,
  },
  data() {
    return {
      isShowData: false,
      websocket: "",
      websocket1: "",
      ws: "",
      ws1: "",
      watchError: 0,
      deviceList: "",
      deviceItem: "",
      userInfo: "",
      uavtypeList: "",
      searchValue: "",
      activeName: "",
      deviceItemList: "",
      searchCode: false,
      clickCode: false,
      checkOpen: "",
      delCode: "",
      upgradeCode: "",
      uavState: false,
      uavItemList: "",
      weatherState: {
        humidity: NaN,
        temperature: NaN,
        weather: "无",
        winddirection: "无",
        windpower: "NaN",
      },
      sn_id: 1,
      changeStateCode: {
        num: "",
        state: false,
      },
      video: {
        uav: {
          style: {},
        },
        outCabin: {
          style: {},
        },
        inCabin: {
          style: {},
        },
      },
      operationList: [],
      onKey: {
        close: 1,
        open: 0,
      },
      site: {
        address: "",
        lat_int: 0,
        lng_int: 0,
      },
      isfloat: {
        float: "right",
      },
      setCode: false,
      total_page: 0,
      page: 0,
      options: "",
      disable: "",
      equipCode: false,
      cameraList: {},
      showOnKeyError: false,
      versionCode: "",
      className: "",
      takeOffTime: 10,
      countDown: "",
      openDeviceItem: "",
      delImg: require("../../assets/img/del.png"),
      delImg1: require("../../assets/img/deled.png"),
      setImg: require("../../assets/img/setting.png"),
      setImg1: require("../../assets/img/seted.png"),
      versionList: [
        {
          label: "机巢",
          id: "nest_version",
          value: "",
        },
        {
          label: "机巢驱动",
          id: "nest_mcu_version",
          value: "",
        },
        {
          label: "飞控", // 飞控系统
          id: "fcs_version",
          value: "",
        },
        {
          label: "相机",
          id: "camera_version",
          value: "",
        },
        {
          label: "副IC", //飞控从机
          id: "fcs_slave_version",
          value: "",
        },
        {
          label: "云台",
          id: "gimbal_version",
          value: "",
        },
        {
          label: "机载4G", //R500 Linux板 版本
          id: "drone_linux_version",
          value: "",
        },
      ],
      openDeviceType: "",
    };
  },
  created() {
    this.operationList = this.equipLanguage.operationList;
    for (let index = 0; index < this.versionList.length; index++) {
      this.versionList[index].label =
        this.equipLanguage.versionLabel[this.versionList[index].id];
    }
    this.$store.dispatch("requestTypeList");
  },
  computed: {
    equipLanguage() {
      return this.$languagePackage.equipment;
    },
  },
  mounted() {
    this.userInfo = this.$store.state.user.userInfo;
    this.$store.commit("setMultiMessage", {
      key: "equipment",
      message: this.getMessage,
    });
    this.getMentData();
    this.getUavType();
    // this.initWebsocket();
  },
  methods: {
    // successFile(file) {
    //   console.log("成功", file);
    // },
    // errorFile(flie) {
    //   console.log(flie);
    // },
    // beforeFile(file) {
    //   console.log(file);
    // },
    //建立websocket
    // initWebsocket() {
    //   this.websocket = new Websockets(baseUrl.WS_URL, {
    //     userVerify: true,
    //     heartbeat: 20000,
    //     message: this.getMessage,
    //   });
    //   this.ws = this.websocket.ws;
    // },
    //websocket返回的数据
    getMessage(msg_id, data) {
      if (msg_id == 110) {
        for (let index = 0; index < this.deviceList.length; index++) {
          if (this.openDeviceItem.sn_id == data.sn_id) {
            if (this.openDeviceItem.is_pull_on && !data.is_pull_on) {
              this.$message.warning({
                message: this.equipLanguage.disconnect,
                customClass: "message-info",
              });
              this.openDeviceItem = "";
            }
            if (this.openDeviceItem) {
              this.equipCode = this.openDeviceItem.is_push_on;
            } else {
              this.equipCode = false;
            }
          }
          if (data.sn_id == this.deviceList[index].sn_id) {
            // if (this.deviceList[index].is_pull_on && !data.is_pull_on) {
            //   this.$message.warning("已断开连接！");
            // }
            this.deviceList[index].is_pull_on = data.is_pull_on;
            this.deviceList[index].is_push_on = data.is_push_on;
            this.deviceList[index].pull_user_info = data.pull_user_info;
          }
        }
      }
    },
    //窗口改变时触发
    handleLableWidth(num) {
      let totalHeight = this.$refs.content.offsetHeight;
      let headerHeight = this.$refs.collapseItem[
        num
      ].$el.getElementsByClassName("el-collapse-item__header")[0].scrollHeight;
      let height = totalHeight - headerHeight + "px";
      this.$refs.collapseItem[num].$el
        .getElementsByClassName("el-collapse-item__content")[0]
        .setAttribute("style", "height:" + height + "!important");
      if (!this.deviceList[num].isAlone) {
        let content = this.$refs.contentInfo[0].$refs.contentBtn.offsetWidth;
        let btn = this.$refs.contentInfo[0].$refs.allclose.$el.offsetWidth;
        if (content > 2 * btn) {
          this.isfloat.float = "right";
        } else {
          this.isfloat.float = "none";
        }
        for (let key in this.video) {
          switch (this.video[key].code) {
            case 0:
              this.video[key].style = {
                top:
                  this.$refs.contentInfo[0].$refs.contentItemVideo.offsetTop +
                  "px",
                left:
                  this.$refs.contentInfo[0].$refs.contentItemVideo.offsetLeft +
                  "px",
                width:
                  this.$refs.contentInfo[0].$refs.contentItemVideo.offsetWidth +
                  "px",
                height:
                  this.$refs.contentInfo[0].$refs.contentItemVideo
                    .offsetHeight *
                    0.95 +
                  "px",
                marginTop:
                  this.$refs.contentInfo[0].$refs.contentItemVideo
                    .offsetHeight *
                    0.05 +
                  "px",
                position: "absolute",
              };

              break;
            case 1:
              this.video[key].style = {
                top:
                  this.$refs.contentInfo[0].$refs.contentItemVideo1.offsetTop +
                  "px",
                left:
                  this.$refs.contentInfo[0].$refs.contentItemVideo1.offsetLeft +
                  "px",
                width:
                  this.$refs.contentInfo[0].$refs.contentItemVideo1
                    .offsetWidth + "px",
                height:
                  this.$refs.contentInfo[0].$refs.contentItemVideo1
                    .offsetHeight *
                    0.95 +
                  "px",
                marginTop:
                  this.$refs.contentInfo[0].$refs.contentItemVideo1
                    .offsetHeight *
                    0.05 +
                  "px",
                position: "absolute",
              };
              break;
            case 2:
              this.video[key].style = {
                top:
                  this.$refs.contentInfo[0].$refs.contentVideo.offsetTop + "px",
                left:
                  this.$refs.contentInfo[0].$refs.contentVideo.offsetLeft +
                  "px",
                width:
                  this.$refs.contentInfo[0].$refs.contentVideo.offsetWidth +
                  "px",
                height:
                  this.$refs.contentInfo[0].$refs.contentVideo.offsetHeight +
                  "px",
                position: "absolute",
              };
              break;
          }
        }
      }
    },
    formatVersion(num) {
      // console.log(123,num)
      if (!num) {
        return "";
      }
      let version1 = parseInt(num / 1000);
      let version2 = parseInt((num % 1000) / 100);
      let version3 = parseInt(num % 100);
      // console.log(version1, version2, version3);
      return `V${version1}.${version2}.${version3}`;
    },
    //搜索
    async searchEvent() {
      this.searchCode = true;
      if (this.searchValue) {
        this.page = 0;
        await this.getMentData();
        if (this.deviceList.length == 0) {
          this.$message.warning({
            message: this.equipLanguage.searchTip,
            customClass: "message-info",
          });
        }
        setTimeout(() => {
          this.searchCode = false;
        }, 200);
      } else {
        this.$message.info({
          message: this.equipLanguage.searchReturn,
          customClass: "message-info",
        });
        await this.getMentData();
        setTimeout(() => {
          this.searchCode = false;
        }, 300);
      }
    },
    //获取设备列表
    async getMentData() {
      let data = {
        page: this.page,
        size: 10,
        type: 0,
      };
      if (this.searchValue) {
        data.search = this.searchValue;
      }
      data.pmd = data.page.toString() + data.type.toString();
      await requestHttp("deviceList", data).then((res) => {
        this.deviceList = res.data.list ? res.data.list : [];
        for (let index = 0; index < this.deviceList.length; index++) {
          this.deviceList[index] = typeJudge(this.deviceList[index]);
        }
        this.total_page = res.data.total_page * 10;
      });
    },
    //获取飞机列表
    getUavType() {
      requestHttp("uavType").then((res) => {
        this.uavtypeList = res.data;
      });
    },
    //点击设置
    setEquip(item) {
      this.sn_id = item.sn_id;
      if (!item.is_pull_on) {
        this.$refs.operationDevice.open("edit", item);
      } else {
        this.$message.warning({
          message: this.equipLanguage.noset,
          customClass: "message-info",
        });
        setTimeout(() => {
          this.sn_id = "";
        }, 200);
      }
    },
    //点击新增设备
    createEquip() {
      this.$refs.operationDevice.open("add");
    },
    //点击删除设备
    delEquip(item) {
      this.delCode = item.sn_id;
      if (!item.is_pull_on) {
        this.$confirm(this.equipLanguage.delTip, this.equipLanguage.tips, {
          confirmButtonText: this.equipLanguage.airportMap.sure,
          cancelButtonText: this.equipLanguage.airportMap.cancel,
          type: "warning",
          customClass: "messageTip",
        })
          .then(() => {
            let data = {
              sn_id: item.sn_id,
              type: item.type,
              state: 30,
              name: item.name,
              uav_type: item.uav_type,
              uav_sn: item.uav_sn,
              from_cor: item.from_cor,
            };
            if (item.description) {
              data.description = item.description;
            }
            if (item.isStable) {
              data.address = item.address;
              data.lat_int = item.lat_int;
              data.lon_int = item.lon_int;
              data.direction_angle = item.direction_angle;
              data.pmd =
                data.sn_id.toString() +
                data.type.toString() +
                data.name +
                data.address +
                data.lat_int.toString() +
                data.lon_int.toString() +
                data.uav_type.toString() +
                data.uav_sn.toString() +
                data.state.toString() +
                data.from_cor.toString();
            } else {
              data.pmd =
                data.sn_id.toString() +
                data.type.toString() +
                data.name +
                data.uav_type.toString() +
                data.uav_sn.toString() +
                data.state.toString() +
                data.from_cor.toString();
            }

            requestHttp("deviceEdit", data).then((res) => {
              this.$message({
                type: "success",
                message: this.equipLanguage.delSuccess,
                customClass: "message-info",
              });
              this.getMentData();
            });
          })
          .catch(() => {
            this.$message.info({
              // type: "info",
              message: this.equipLanguage.cancelDel,
              customClass: "message-info",
            });
          })
          .finally(() => {
            this.delCode = "";
          });
      } else {
        this.$message.warning({
          message: this.equipLanguage.noDel,
          customClass: "message-info",
        });
        setTimeout(() => {
          this.delCode = "";
        }, 200);
      }
    },
    //点击打开地图选择位置
    chooseSize(index, equipForm, site) {
      this.site = Object.assign(this.site, site);
      let data = {};
      if (index == 1) {
        data = {
          type: equipForm.type,
          sn_id: equipForm.sn_id,
          alternate_lat_int: equipForm.alternate_lat_int,
          alternate_lon_int: equipForm.alternate_lon_int,
        };
      } else {
        data = {
          type: equipForm.type,
          sn_id: equipForm.sn_id,
          get_address: true,
        };
      }
      this.$refs.mapSite.openMap(data);
    },
    //子组件传回的地址
    backData(e, index) {
      this.$refs.operationDevice.backData(e, index);
    },
    //页面切换
    changePage(e) {
      this.page = e - 1;
      this.getMentData();
      this.checkOpen = "";
    },
    //关闭页面
    closeDialog() {
      this.sn_id = "";
    },
    //点击打开
    openCollapseItem(e) {
      if (e) {
        this.checkOpen = e;
        let num = 0;
        for (let index = 0; index < this.deviceList.length; index++) {
          if (e == this.deviceList[index].sn_id) {
            this.openDeviceItem = this.deviceList[index];
            num = index;
            break;
          }
        }
        this.equipCode = this.openDeviceItem.is_push_on;
        // if (
        //   this.deviceList[num].type == 10 &&
        //   this.deviceList[num].lat_int &&
        //   this.deviceList[num].lon_int
        // ) {
        //   let point =
        //     this.deviceList[num].lon_int / 1e7 +
        //     "," +
        //     this.deviceList[num].lat_int / 1e7;
        //   //注释掉获取天气信息
        //   // searchLnglat(point).then(res => {
        //   //   let adcode = res.data.regeocode.addressComponent.adcode;
        //   //   searchSky(adcode).then(e => {
        //   //     this.weatherState = e.data.lives[0];
        //   //   });
        //   // });
        // }
        this.websocket1 = new Websockets(baseUrl.WS_URL, {
          equipmentVerify: {
            sn_id: this.deviceList[num].sn_id,
            type: this.deviceList[num].type,
            vst: 40,
          },
          heartbeat: 20000,
          message: this.returnMessage,
        });
        this.ws1 = this.websocket1.ws;
        let totalHeight = this.$refs.content.offsetHeight;
        let headerHeight = this.$refs.collapseItem[
          num
        ].$el.getElementsByClassName("el-collapse-item__header")[0]
          .scrollHeight;

        let height = totalHeight - headerHeight - 1 + "px";
        this.$refs.collapseItem[num].$el
          .getElementsByClassName("el-collapse-item__content")[0]
          .setAttribute("style", "height:" + height + "!important");
        if (!this.deviceList[num].isAlone) {
          setTimeout(() => {
            // this.$refs.content.scrollTop = headerHeight * num+bHeight;
            this.video.inCabin.style = {
              top:
                this.$refs.contentInfo[0].$refs.contentItemVideo.offsetTop +
                "px",
              left:
                this.$refs.contentInfo[0].$refs.contentItemVideo.offsetLeft +
                "px",
              width:
                this.$refs.contentInfo[0].$refs.contentItemVideo.offsetWidth +
                "px",
              height:
                this.$refs.contentInfo[0].$refs.contentItemVideo.offsetHeight *
                  0.95 +
                "px",
              marginTop:
                this.$refs.contentInfo[0].$refs.contentItemVideo.offsetHeight *
                  0.05 +
                "px",
              position: "absolute",
            };
            this.video.inCabin.code = 0;
            this.video.uav.style = {
              top:
                this.$refs.contentInfo[0].$refs.contentItemVideo1.offsetTop +
                "px",
              left:
                this.$refs.contentInfo[0].$refs.contentItemVideo1.offsetLeft +
                "px",
              width:
                this.$refs.contentInfo[0].$refs.contentItemVideo1.offsetWidth +
                "px",
              height:
                this.$refs.contentInfo[0].$refs.contentItemVideo1.offsetHeight *
                  0.95 +
                "px",
              marginTop:
                this.$refs.contentInfo[0].$refs.contentItemVideo1.offsetHeight *
                  0.05 +
                "px",
              position: "absolute",
            };
            this.video.uav.code = 1;
            this.video.outCabin.style = {
              top:
                this.$refs.contentInfo[0].$refs.contentVideo.offsetTop + "px",
              left:
                this.$refs.contentInfo[0].$refs.contentVideo.offsetLeft + "px",
              width:
                this.$refs.contentInfo[0].$refs.contentVideo.offsetWidth + "px",
              height:
                this.$refs.contentInfo[0].$refs.contentVideo.offsetHeight +
                "px",
              position: "absolute",
            };
            this.video.outCabin.code = 2;
            let content =
              this.$refs.contentInfo[0].$refs.contentBtn.offsetWidth;
            let btn = this.$refs.contentInfo[0].$refs.allclose.$el.offsetWidth;
            if (content > 2 * btn) {
              this.isfloat.float = "right";
            } else {
              this.isfloat.float = "none";
            }
            // window.onresize = () => {
            //   return (() => {
            //     this.handleLableWidth(num);
            //   })();
            // };
          }, 100);
        }
        setTimeout(() => {
          window.onresize = () => {
            return (() => {
              this.handleLableWidth(num);
            })();
          };
        });
      } else {
        this.isShowData = false;
        this.versionCode = "";
        window.onresize = null;
        if (this.websocket1) {
          this.websocket1.manualClone();
          this.websocket1 = "";
        }
        this.onKey = {
          close: 1,
          open: 0,
        };
        for (
          let index = 0;
          index < this.equipLanguage.operationList.length;
          index++
        ) {
          this.equipLanguage.operationList[index].value = false;
          this.equipLanguage.operationList[index].value1 = 0;
        }
        this.operationList = this.equipLanguage.operationList;
        this.deviceItem = "";
        this.checkOpen = e;
        this.deviceItemList = "";
      }
    },
    //打开时websocket返回的数据
    returnMessage(e) {
      // console.log(e)
      let msg_id = e.msg_id;
      let data = e.data;
      switch (msg_id) {
        case 200:
          this.deviceItem = data;
          break;
        case 430:
          //获取天气预报，暂时无使用，目前通过434获取
          // this.stateItemList = data;
          // console.log()
          break;
        case 434:
          // console.log(data)
          this.deviceItemList = data;
          // console.log(this.deviceItemList);
          this.watchError = this.deviceItemList.nest_error_code;
          for (let index = 0; index < this.operationList.length; index++) {
            if (this.operationList[index].id == "Hatch") {
              // console.log("舱门开："+this.deviceItemList.nest_door_open,
              //   "舱门关："+this.deviceItemList.nest_door_close)
              this.judgeState(
                this.deviceItemList.nest_door_open,
                this.deviceItemList.nest_door_close,
                index
              );
            }
            if (this.operationList[index].id == "Lift") {
              // console.log("平台升："+this.deviceItemList.nest_platform_up,
              //   "平台降："+this.deviceItemList.nest_platform_down)
              this.judgeState(
                this.deviceItemList.nest_platform_up,
                this.deviceItemList.nest_platform_down,
                index
              );
            }
            if (this.operationList[index].id == "center") {
              // console.log("脚架开："+this.deviceItemList.nest_correct_open,
              //   "脚架收："+this.deviceItemList.nest_correct_close)
              this.judgeState(
                this.deviceItemList.nest_correct_open,
                this.deviceItemList.nest_correct_close,
                index
              );
            }
            if (this.operationList[index].id == "charger") {
              this.judgeState(
                this.deviceItemList.nest_charge_on,
                this.deviceItemList.nest_charge_off,
                index
              );
            }
          }
          if (this.deviceItemList.nest_one_key_open == 0) {
            this.onKey.open = 0;
          } else if (this.deviceItemList.nest_one_key_open == 1) {
            if (this.onKey.open == 1 || this.onKey.open == 0) {
              this.onKey.open = 1;
            } else {
              this.onKey.open = 1;
              this.$message.success({
                message: this.equipLanguage.onKeyStartSuccess,
                customClass: "message-info",
              });
            }
          } else if (this.deviceItemList.nest_one_key_open == 3) {
            this.onKey.open = 3;
            this.$refs.contentInfo[0].openCode = false;
          } else if (this.deviceItemList.nest_one_key_open == 4) {
            if (this.showOnKeyError) {
              let msg = errorMsg(
                this.deviceItemList.nest_error_code,
                this.equipLanguage.language
              );
              this.$message.error({
                message: msg + this.equipLanguage.onKeyStartError,
                customClass: "message-info",
              });
              this.showOnKeyError = false;
            }
            //  if ((this.onKey.close !== 4)) {
            //   this.$message.error(this.equipLanguage.onKeyStartError);
            // }
            this.onKey.open = 4;
            // if ((this.onKey.open == 4)) {

            // } else {
            //   this.onKey.open = 4;
            //   this.$message.error(this.equipLanguage.onKeyCloseSuccess);
            // }
            this.$refs.contentInfo[0].openCode = false;
          }
          if (this.deviceItemList.nest_one_key_close == 0) {
            this.onKey.close = 0;
          } else if (this.deviceItemList.nest_one_key_close == 1) {
            if (this.onKey.close == 1 || this.onKey.close == 0) {
              this.onKey.close = 1;
            } else {
              this.onKey.close = 1;
              this.$message.success({
                message: this.equipLanguage.onKeyCloseSuccess,
                customClass: "message-info",
              });
            }
          } else if (this.deviceItemList.nest_one_key_close == 3) {
            this.$refs.contentInfo[0].closeCode = false;
            this.onKey.close = 3;
          } else if (this.deviceItemList.nest_one_key_close == 4) {
            if (this.showOnKeyError) {
              let msg = errorMsg(
                this.deviceItemList.nest_error_code,
                this.equipLanguage.language
              );
              this.$message.error({
                message: msg + this.equipLanguage.onKeyCloseError,
                customClass: "message-info",
              });
              this.showOnKeyError = false;
            }
            this.onKey.close = 4;
            this.$refs.contentInfo[0].closeCode = false;
          }
          for (let index = 0; index < this.versionList.length; index++) {
            // console.log(this.versionList[index].value,data[this.versionList[index].id])
            if (
              this.versionList[index].value != data[this.versionList[index].id]
            ) {
              this.versionList[index].value = data[this.versionList[index].id];
            }
          }
          let showData = this.versionList.findIndex((item) => {
            return item.value;
          });
          if (showData !== -1) {
            this.isShowData = true;
          } else {
            this.isShowData = false;
          }
          break;
        case 432:
          this.uavItemList = data;
          break;
        case 437:
          // console.log(data)
          if (data.flight_control_module == 1) {
            this.uavState = true;
          } else {
            this.uavState = false;
          }
          break;
        case 435:
          this.cameraList = data;
          break;
        default:
          break;
      }
      let fixedType = this.$refs.contentInfo;
      fixedType && fixedType.length && fixedType[0].getMessage(msg_id, data);
      let calibrationListDialog = this.$refs.calibrationListDialog;
      calibrationListDialog.getMessage(msg_id, data);
      // calibrationListDialog&&calibrationListDialog.length
      // if (e.msg_id == 442) {
      //   this.waypoint_percent = e.data.waypoint_percent;
      //   console.log("下载航线", e.data);
      //   // console.log(123,e.data)
      // }
      // if(e.code==2000&&e.msg_id==404){
      //   this.$message.success("拍照成功！")
      // }
    },
    //判断机巢信息状态
    judgeState(openValue, closeValue, index) {
      let result = "";
      let result1 = "";
      if (openValue == 0 || openValue == 1) {
        if (closeValue == 3) {
          result = true;
          result1 = 2;
        } else if (closeValue == 1) {
          result = false;
          result1 = 1;
        } else if (closeValue == 4) {
          if (
            this.operationList[index].value1 == 4 ||
            this.operationList[index].value1 == 0
          ) {
            result = true;
            result1 = 4;
            if (
              this.changeStateCode.num == index &&
              this.changeStateCode.state
            ) {
              let msg = errorMsg(
                this.deviceItemList.nest_error_code,
                this.equipLanguage.language
              );
              let msg1 = "";
              if (this.deviceItemList.nest_one_key_close == 4) {
                msg1 = this.equipLanguage.onKeyCloseError;
              }
              if (this.deviceItemList.nest_one_key_open == 4) {
                msg1 = this.equipLanguage.onKeyStartError;
              }
              if (msg || msg1) {
                this.$message.error({
                  message: msg + msg1,
                  customClass: "message-info",
                });
              }
              result = true;
              result1 = 4;
              this.changeStateCode = {
                num: "",
                state: false,
              };
            }
          } else {
            let msg = errorMsg(
              this.deviceItemList.nest_error_code,
              this.equipLanguage.language
            );
            let msg1 = "";
            if (this.deviceItemList.nest_one_key_close == 4) {
              msg1 = this.equipLanguage.onKeyCloseError;
            }
            if (this.deviceItemList.nest_one_key_open == 4) {
              msg1 = this.equipLanguage.onKeyStartError;
            }
            if (msg || msg1) {
              this.$message.error({
                message: msg + msg1,
                customClass: "message-info",
              });
            }
            result = true;
            result1 = 4;
            this.changeStateCode = {
              num: "",
              state: false,
            };
          }
        }
        //  else if(closeValue==2){
        //   if (
        //     this.operationList[index].value1 == 5 ||
        //     this.operationList[index].value1 == 0
        //   ) {
        //     result = true;
        //     result1 = 5;
        //   } else {
        //     this.$message.error(this.operationList[index].title + "操作超时！");
        //     result = true;
        //     result1 = 5;
        //   }
        // }
      }
      if (closeValue == 0 || closeValue == 1) {
        if (openValue == 3) {
          result = false;
          result1 = 3;
        } else if (openValue == 1) {
          result = true;
          result1 = 1;
        } else if (openValue == 4) {
          if (
            this.operationList[index].value1 == 4 ||
            this.operationList[index].value1 == 0
          ) {
            result = false;
            result1 = 4;
            if (
              this.changeStateCode.num == index &&
              this.changeStateCode.state
            ) {
              let msg = errorMsg(
                this.deviceItemList.nest_error_code,
                this.equipLanguage.language
              );
              let msg1 = "";
              if (this.deviceItemList.nest_one_key_close == 4) {
                msg1 = this.equipLanguage.onKeyCloseError;
              }
              if (this.deviceItemList.nest_one_key_open == 4) {
                msg1 = this.equipLanguage.onKeyStartError;
              }
              if (msg || msg1) {
                this.$message.error({
                  message: msg + msg1,
                  customClass: "message-info",
                });
              }
              result = true;
              result1 = 4;
              this.changeStateCode = {
                num: "",
                state: false,
              };
            }
          } else {
            let msg = errorMsg(
              this.deviceItemList.nest_error_code,
              this.equipLanguage.language
            );
            let msg1 = "";
            if (this.deviceItemList.nest_one_key_close == 4) {
              msg1 = this.equipLanguage.onKeyCloseError;
            }
            if (this.deviceItemList.nest_one_key_open == 4) {
              msg1 = this.equipLanguage.onKeyStartError;
            }
            if (msg || msg1) {
              this.$message.error({
                message: msg + msg1,
                customClass: "message-info",
              });
            }
            result = false;
            result1 = 4;
            this.changeStateCode = {
              num: "",
              state: false,
            };
          }
        }
        //  else if (openValue == 2) {
        //   if (this.operationList[index].value1 == 2) {
        //     result = false;
        //     result1 = 5;
        //   } else {
        //     this.$message.error(this.operationList[index].title + "操作超时！");
        //     result = false;
        //     result1 = 5;
        //   }
        // }
      }
      this.operationList[index].value = result;
      this.operationList[index].value1 = result1;
    },
    //升级固件
    upgradeFirmware(item) {
      if (!item.is_push_on) {
        this.$message.error(this.equipLanguage.upgradeMsg.noUpgrade);
        return false;
      }
      if (this.uavItemList) {
        if (
          !(
            this.uavItemList.flight_status === 0 ||
            this.uavItemList.flight_status === 4
          )
        ) {
          this.$message.error(this.equipLanguage.upgradeMsg.noUpgrade1);
          return false;
        }
      }
      this.upgradeCode = item.sn_id;
      this.$refs.firmwareOperate.open("", this.websocket1);
    },
    reStart() {
      this.$nextTick(() => {
        this.countDownFun();
        this.$confirm(
          this.equipLanguage.upgradeMsg.reStart,
          this.equipLanguage.upgradeMsg.reStartTip,
          {
            confirmButtonText: `${this.equipLanguage.upgradeMsg.submit}（${this.takeOffTime}）`,
            showClose: false,
            showCancelButton: false,
            confirmButtonClass: "restartConfirmBtn",
            type: "warning",
          }
        ).then(() => {
          let box = document.getElementsByClassName("restartConfirmBtn")[0];
          this.takeOffTime--;
          box.innerHTML = `<span>${this.equipLanguage.upgradeMsg.submit}</span>`;
          if (this.countDown) {
            this.takeOffTime = 10;
            clearInterval(this.countDown);
            this.countDown = null;
          }
          let data = {
            nest_action_cmd: 125,
          };
          this.websocket1 && this.websocket1.manualSend(data, 403);
        });
      });
    },
    countDownFun() {
      this.takeOffTime = 10;
      this.countDown = setInterval(() => {
        let box = document.getElementsByClassName("restartConfirmBtn")[0];
        this.takeOffTime--;
        box.innerHTML = `<span>${this.equipLanguage.upgradeMsg.submit}（${this.takeOffTime}）</span>`;
        if (this.takeOffTime == 0) {
          this.takeOffTime = 10;
          clearInterval(this.countDown);
          this.countDown = null;
          box.click();
          return;
        }
      }, 1000);
    },
    clickTip() {
      this.$message.info(this.equipLanguage.noVersionData);
    },
    prevClick(e) {
      console.log(e);
      this.className = "prev-class";
      setTimeout(() => {
        this.className = "";
      }, 200);
    },
    nextClick(e) {
      this.className = "next-class";
      setTimeout(() => {
        this.className = "";
      }, 200);
    },
    updateVersion() {
      this.$nextTick(() => {
        this.$refs.versionRate.open();
      });
    },
    openCalibration() {
      this.$refs.calibrationListDialog.open(this.websocket1);
    },
    //跳转到录像列表
    judgeToVideo(item) {
      this.$router.push({
        path: "/videoList",
        query: {
          sn_id: item.sn_id,
        },
      });
    },
    //接收返回参数
    // changeStateData(e){
    //   this.changeStateCode=e
    // },
    //关闭时触发
    // close() {
    //   this.$message.error("已断开连接！");
    // },
    //错误状态
    // errorMsg(code) {
    //   let msg = "";
    //   switch (code) {
    //     case 5:
    //       msg = "舱门未打开！";
    //       break;
    //     case 6:
    //       msg = "舱门未关闭！";
    //       break;
    //     case 7:
    //       msg = "升降台未升起！";
    //       break;
    //     case 8:
    //       msg = "升降台未降落！";
    //       break;
    //     case 9:
    //       msg = "脚架X方向未张开！";
    //       break;
    //     case 10:
    //       msg = "脚架X方向未收缩！";
    //       break;
    //     case 11:
    //       msg = "脚架Y方向未张开";
    //       break;
    //     case 12:
    //       msg = "脚架Y方向未收缩！";
    //       break;
    //     case 13:
    //       msg = "舱门打开超时！";
    //       break;
    //     case 14:
    //       msg = "舱门关闭超时";
    //       break;
    //     case 15:
    //       msg = "升降台升起超时";
    //       break;
    //     case 16:
    //       msg = "升降台下降超时！";
    //       break;
    //     case 17:
    //       msg = "脚架X方向打开超时！";
    //       break;
    //     case 18:
    //       msg = "脚架X方向收缩超时！";
    //       break;
    //     case 19:
    //       msg = "脚架Y方向打开超时！";
    //       break;
    //     case 20:
    //       msg = "脚架Y方向收缩超时！";
    //       break;
    //     default:
    //       break;
    //   }
    //   return msg;
    // },
  },
  beforeDestroy() {
    if (this.websocket) {
      this.websocket.manualClone();
      this.websocket = "";
    }
    if (this.websocket1) {
      this.websocket1.manualClone();
      this.websocket1 = "";
    }
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .equipment {
    .searchDiv {
      .el-button {
        padding: @zoomIndex * 13px @zoomIndex * 20px !important;
        border-radius: @zoomIndex * 8px !important;
        margin-top: @zoomIndex * 2px !important;
        font-size: @zoomIndex * 14px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
    }
    .content {
      .el-collapse {
        .el-collapse-item {
          .el-collapse-item__header {
            .num {
              font-size: @zoomIndex * 16px !important;
            }
            .el-image {
              border-radius: @zoomIndex * 4px !important;
            }
            .content-item-1 {
              .el-tag {
                padding: @zoomIndex * 6px @zoomIndex * 10px !important;
                font-size: @zoomIndex * 12px !important;
                line-height: @zoomIndex * 16px !important;
              }
              .firstDiv {
                margin-bottom: @zoomIndex * 8px !important;
              }
              .noneDiv1 {
                margin-bottom: @zoomIndex * 8px !important;
              }
            }
            .content-item-2 {
              .el-tag {
                padding: @zoomIndex * 6px !important;
                padding-top: @zoomIndex * 12px !important;
                font-size: @zoomIndex * 14px !important;
                line-height: @zoomIndex * 16px !important;
              }
            }
            .content-item-3 {
              // margin-top: @zoomIndex * -30px !important;
              .content-item-3-1 {
                padding: 0 @zoomIndex * 6px !important;
                font-size: @zoomIndex * 16px !important;
                line-height: @zoomIndex * 30px !important;
              }
              .task-data {
                height: @zoomIndex * 30px !important;
                margin-left: @zoomIndex * 20px !important;
                .circle-point {
                  width: @zoomIndex * 16px !important;
                  height: @zoomIndex * 16px !important;
                }
                .task-data-content {
                  padding: 0 @zoomIndex * 2px !important;
                  font-size: @zoomIndex * 16px !important;
                  // height: @zoomIndex * 26px !important;
                  margin-left: @zoomIndex * 6px !important;
                  // margin-bottom: @zoomIndex * -10px !important;
                  // line-height: @zoomIndex * 24px !important;
                }
              }
            }
            .operation {
              .el-button {
                font-size: @zoomIndex * 16px !important;
                // letter-spacing: 12px;
                .el-image {
                  width: @zoomIndex * 18px !important;
                }
              }
            }
          }
        }
      }
    }
  }
}
.equipment {
  width: 100%;
  height: 100%;
  .searchDiv {
    width: 96%;
    height: 6%;
    margin-left: 2%;
    padding-top: 1%;
    text-align: left;
    .el-input {
      width: 30%;
      margin-right: 1%;
    }
    .el-button {
      border: none;
      border-radius: 8px;
      margin-top: 2px;
      font-size: 14px;
      font-weight: 550;
      letter-spacing: 2px;
      padding: 13px 20px;
    }
    .addButton {
      float: right;
    }
  }
  .content {
    width: 96%;
    margin-left: 2%;
    height: 84%;
    margin-bottom: 1%;
    overflow: auto;
    .el-collapse {
      overflow: hidden;
      border: none;
      .el-collapse-item {
        .el-collapse-item__header {
          .num {
            margin-right: 2%;
            font-size: 16px;
            width: 20px;
          }
          .el-image {
            width: 8%;
            border: none;
            border-radius: 4px;
            flex-shrink: 0; /*防止被压缩*/
          }
          .content-item-1 {
            margin-left: 1.5%;
            .el-tag {
              padding: 6px 10px;
              font-size: 12px;
              display: block;
              border: none;
              height: auto;
              line-height: 16px;
            }
            .firstDiv {
              margin-bottom: 8px;
            }
            .noneDiv1 {
              margin-bottom: 8px;
            }
          }
          .content-item-2 {
            margin-left: 1%;
            width: 20%;
            text-align: left;
            .el-tag {
              padding: 6px;
              padding-top: 12px;
              font-size: 14px;
              display: block;
              border: none;
              height: auto;
              line-height: 16px;
              background-color: transparent;
              .spans {
                white-space: normal;
              }
            }
          }
          .content-item-3 {
            width: 47%;
            margin-left: 2%;
            text-align: left;
            .content-item-3-1 {
              padding: 0 6px;
              border: none;
              font-size: 16px;
              line-height: 30px;
            }
            .task-data {
              display: flex;
              align-items: center;
              height: 30px;
              margin-left: 20px;
              .circle-point {
                width: 16px;
                height: 16px;
                background-color: #33e933;
                border-radius: 50%;
              }
              .task-data-content {
                padding: 0 3px;
                font-size: 16px;
                // height: 26px;
                margin-left: 6px;
                // margin-bottom: -10px;
                // line-height: 24px;
                color: #fff;
                // background-color: #3535cc;
                border: none;
              }
            }
          }
          .operation {
            margin-left: 1%;
            width: 19%;
            text-align: right;
            display: flex;
            justify-content: flex-end;

            .el-button {
              font-size: 16px;
              border: none;
              // margin-right: 4%;
              // letter-spacing: 12px;
              .el-image {
                width: 18px;
                border: none;
                margin-right: 20%;
                vertical-align: text-bottom;
              }
            }
            .firmware {
              text-align: left;
              padding-left: 0;
              padding-right: 24px;
              &:first-child {
                margin-right: 24px;
              }
            }
          }
        }
      }
    }
  }
  .el-pagination {
    text-align: right;
    margin-right: 2%;
  }
}
</style>
<style lang="less">
.equipment {
  .searchDiv {
    .el-input {
      .el-input__inner {
        font-size: 16px !important;
        border-radius: 8px !important;
        border-width: 1px !important;
        height: 40px !important;
        line-height: 40px !important;
      }
      .el-input__suffix {
        .el-input__icon {
          width: 25px !important;
          font-size: 14px !important;
          line-height: 40px !important;
        }
      }
    }
  }

  .content {
    .el-collapse {
      .el-collapse-item {
        .el-collapse-item__header {
          height: auto !important;
          border: none !important;
          padding: 0.2% 1% !important;
          .el-collapse-item__arrow {
            display: none !important;
          }
        }
        .el-collapse-item__wrap {
          border: none !important;
          .el-collapse-item__content {
            padding: 0 !important;
            position: relative !important;
          }
        }
      }
      .collapse-transition {
        -webkit-transition: 0s height, 0s padding-top, 0s padding-bottom !important;
        transition: 0s height, 0s padding-top, 0s padding-bottom !important;
      }
      .horizontal-collapse-transition {
        -webkit-transition: 0s width, 0s padding-left, 0s padding-right !important;
        transition: 0s width, 0s padding-left, 0s padding-right !important;
      }
      .horizontal-collapse-transition
        .el-submenu__title
        .el-submenu__icon-arrow {
        -webkit-transition: 0s !important;
        transition: 0s !important;
        opacity: 0 !important;
      }
    }
    &::-webkit-scrollbar {
      width: 3px !important;
    }
  }

  .el-pagination {
    button {
      margin: 0 1px !important;
      padding: 0 !important;
      span {
        min-width: auto !important;
        width: auto !important;
        margin: 0 5px !important;
        font-size: large !important;
      }
    }
    &.prev-class {
      .btn-prev {
        border: 1px solid #124093;
        color: #124093 !important;
      }
    }
    &.next-class {
      .btn-next {
        border: 1px solid #124093;
        color: #124093 !important;
      }
    }
    .el-pager {
      li {
        font-size: large !important;
      }
    }
  }
}
.selectUavType {
  .el-select-dropdown__wrap {
    margin-bottom: -23px !important;
    margin-right: -23px !important;
    max-height: 274px !important;
    .el-select-dropdown__list {
      padding: 6px 0 !important;
      .el-select-dropdown__item {
        font-size: 14px !important;
        padding: 0 20px !important;
        height: 34px !important;
        line-height: 34px !important;
      }
    }
  }
}
.message-info {
  top: 20px !important;
  min-width: 380px !important;
  padding: 15px 15px 15px 20px !important;
  border-radius: 4px !important;
  .el-message__icon {
    font-size: 16px !important;
    margin-right: 10px !important;
  }
  .el-message__content {
    font-size: 14px !important;
  }
}
.version-list-box {
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: #fff;
  .version-list {
    .version-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px;
      .label {
        padding-right: 10px;
      }
    }
  }
  &.el-popper[x-placement^="left"] .popper__arrow {
    border-left-color: rgba(77, 75, 75, 0.788);
  }
  &.el-popper[x-placement^="left"] .popper__arrow::after {
    border-left-color: rgba(77, 75, 75, 0.788);
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .equipment {
    .searchDiv {
      .el-input {
        .el-input__inner {
          font-size: @zoomIndex * 16px !important;
          border-radius: @zoomIndex * 8px !important;
          border-width: @zoomIndex * 1px !important;
          height: @zoomIndex * 40px !important;
          line-height: @zoomIndex * 40px !important;
        }
        .el-input__suffix {
          .el-input__icon {
            font-size: @zoomIndex * 14px !important;
            width: @zoomIndex * 25px !important;
            line-height: @zoomIndex * 40px !important;
          }
        }
      }
    }
    .content {
      &::-webkit-scrollbar {
        width: @zoomIndex * 3px !important;
      }
    }

    .el-pagination {
      button {
        span {
          margin: 0 @zoomIndex * 5px !important;
        }
      }
      .btn-next,
      .btn-prev,
      .el-pager li {
        min-width: @zoomIndex * 30px !important;
        border-radius: @zoomIndex * 2px !important;
        margin: 0 @zoomIndex * 5px !important;
      }
      .btn-prev,
      .btn-next {
        span {
          font-size: @zoomIndex * 14px !important;
        }
      }
      button,
      span:not([class*="suffix"]) {
        height: @zoomIndex * 28px !important;
        line-height: @zoomIndex * 28px !important;
      }
      .el-pager li {
        padding: 0 @zoomIndex * 4px !important;
        font-size: @zoomIndex * 13px !important;
        min-width: @zoomIndex * 35.5px !important;
        height: @zoomIndex * 28px !important;
        line-height: @zoomIndex * 28px !important;
      }
    }
  }
  .selectUavType {
    .el-select-dropdown__wrap {
      margin-bottom: @zoomIndex * -23px !important;
      margin-right: @zoomIndex * -23px !important;
      max-height: @zoomIndex * 274px !important;
      .el-select-dropdown__list {
        padding: @zoomIndex * 6px 0 !important;
        .el-select-dropdown__item {
          font-size: @zoomIndex * 14px !important;
          padding: 0 @zoomIndex * 20px !important;
          height: @zoomIndex * 34px !important;
          line-height: @zoomIndex * 34px !important;
        }
      }
    }
  }
  .messageTip {
    &.el-message-box {
      padding-bottom: @zoomIndex * 10px !important;
      border-radius: @zoomIndex * 4px !important;
      border: @zoomIndex * 1px solid #ebeef5 !important;
      font-size: @zoomIndex * 18px !important;
      box-shadow: 0 @zoomIndex * 2px @zoomIndex * 12px 0 rgb(0 0 0 / 10%) !important;
      width: @zoomIndex * 420px !important;
      .el-message-box__header {
        padding: @zoomIndex * 15px @zoomIndex * 15px @zoomIndex * 10px !important;
        .el-message-box__title {
          font-size: @zoomIndex * 18px !important;
        }
        .el-message-box__headerbtn {
          top: @zoomIndex * 15px !important;
          right: @zoomIndex * 15px !important;
          font-size: @zoomIndex * 16px !important;
        }
      }
      .el-message-box__content {
        padding: @zoomIndex * 10px @zoomIndex * 15px !important;
        font-size: @zoomIndex * 14px !important;
        .el-message-box__status {
          font-size: @zoomIndex * 24px !important;
        }
        .el-message-box__status + .el-message-box__message {
          padding-left: @zoomIndex * 36px !important;
          padding-right: @zoomIndex * 12px !important;
        }
      }
      .el-message-box__btns {
        padding: @zoomIndex * 5px @zoomIndex * 15px 0 !important;
        .el-button--small {
          padding: @zoomIndex * 9px @zoomIndex * 15px !important;
          font-size: @zoomIndex * 12px !important;
          border-radius: @zoomIndex * 3px !important;
        }
      }
    }
  }
  .message-info {
    top: @zoomIndex * 20px !important;
    min-width: @zoomIndex * 380px !important;
    padding: @zoomIndex * 15px @zoomIndex * 15px @zoomIndex * 15px @zoomIndex *
      20px !important;
    border-radius: @zoomIndex * 4px !important;
    .el-message__icon {
      font-size: @zoomIndex * 16px !important;
      margin-right: @zoomIndex * 10px !important;
    }
    .el-message__content {
      font-size: @zoomIndex * 14px !important;
    }
  }
}
</style>
