<!-- 图片图标 -->
<template>
  <div class="img-icon">
    <img :src="imgSrc" alt="" :style="imgStyle" :title="title" @click="clickEvent"/>
    <img
      v-if="hover"
      :src="hoverImgSrc"
      alt=""
      :style="imgStyle"
      class="hover-img"
      :title="title"
    />
  </div>
</template>

<script>
export default {
  props: {
    width: {
      type: [Number, String],
      default: 16,
    },
    height: {
      type: [Number, String],
      //   default: 16,
    },
    type: {
      type: String,
      default: "png",
    },
    size: {
      type: Number,
      // default: 16
    },
    clickEvent:{
      type:Function,
      default:()=>{
        return {}
      }
    },
    title:String,
    name: String,
    hover: String,
  },
  computed: {
    imgSrc() {
      if (!this.name) {
        return "";
      }

      let src = `${this.name}.${this.type}`;
      return require("../../assets/icon/" + src);
    },
    hoverImgSrc() {
      if (!this.hover) {
        return "";
      }
      let src = `${this.hover}.${this.type}`;
      return require("../../assets/icon/" + src);
    },
    imgStyle() {
      return {
        width: this.size ? this.size + "px" : this.width + "px",
        height: this.size ? this.size + "px" : this.height + "px",
      };
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.hover-img {
  transition: 0.2s;
  transform: scale(0);
  position: absolute;
  left: 0;
  top: 0;
}
.img-icon {
  position: relative;
  &:hover .hover-img {
    transform: scale(1);
  }
}
</style>