<!-- 无人机设备列表、巡检记录、巡检排行榜背景布局 -->
<template>
  <div class="bg-layout">
    <div class="bg-layout-main">
      <div class="marquee" v-for="(item, index) in marqueeList" :key="index">
        <div
          class="marquee-cell"
          v-if="item.isShow"
          :class="item.class"
          :ref="item.ref"
        ></div>
      </div>

      <div class="" v-if="isShowQuadrangle">
        <div
          class="quadrangle"
          v-for="(item, index) in quadrangleList"
          :key="index"
          :style="item"
        ></div>
      </div>

      <slot name="header">
        <div class="layout-header">
          <div class="header-img">
            <img src="../../../assets/img/home/<USER>" alt="" />
          </div>
          <div class="header-title" :style="titleStyle">{{ title }}</div>
        </div>
      </slot>
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    isShowQuadrangle: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      marqueeList: [
        { class: "marquee-top", isShow: true, ref: "marqueeTop" },
        { class: "marquee-right", isShow: false, ref: "marqueeRight" },
        { class: "marquee-bottom", isShow: false, ref: "marqueeBottom" },
        { class: "marquee-left", isShow: false, ref: "marqueeLeft" },
      ],
      marqueeShowIndex: 0,

      //
      quadrangleList: [
        { left: "3px", top: "3px" },
        { right: "3px", top: "3px" },
        { left: "3px", bottom: "3px" },
        { right: "3px", bottom: "3px" },
      ],
      marqueeTime: null,
    };
  },
  beforeDestroy() {
    clearInterval(this.marqueeTime);
  },
  computed: {
    titleStyle(){
      let type = this.$language
      return {
        "letter-spacing": type == 'chinese' ? '3px' : 0
      }
    }
  },
  created() {
    this.$nextTick(() => {
      setTimeout(() => {
        this.isShow = true;
      }, 2000);

      this.marqueeTime = setInterval(() => {
        this.marqueeList[this.marqueeShowIndex].isShow = false;
        this.marqueeShowIndex++;
        if (this.marqueeShowIndex === 4) {
          this.marqueeShowIndex = 0;
        }
        let index = this.marqueeShowIndex;
        this.marqueeList[index].isShow = true;
      }, 3000);
    });
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.bg-layout {
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.5);
  .bg-layout-main {
    height: 0;
    // border: 2px solid rgba(59, 159, 233, 0.4);
    animation-name: identifier;
    animation-duration: 2s;
    animation-fill-mode: forwards;
    position: relative;
    opacity: 0;
  }

  .marquee {
    position: absolute;
    width: calc(100% + 4px);
    height: calc(100% + 4px);
    left: -2px;
    top: -2px;
    overflow: hidden;

    .marquee-cell {
      position: absolute;
      width: 64px;
      height: 2px;
      // background-image: linear-gradient(
      //   to right,
      //   rgba(119, 161, 246, 0.1),
      //   rgba(59, 117, 235, 0.4),
      //   rgba(255, 255, 255, 1),
      //   rgba(59, 117, 235, 0.4),
      //   rgba(119, 161, 246, 0.1)
      // );
      animation-duration: 3s;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
    }

    .marquee-top {
      left: -64px;
      top: 0px;
      animation-name: marqueeTop;
    }
    .marquee-right {
      right: -31px;
      top: 33px;
      transform: rotate(90deg);
      animation-name: marqueeRight;
    }
    .marquee-bottom {
      right: 0px;
      bottom: 0px;
      animation-name: marqueeBottom;
    }
    .marquee-left {
      left: -31px;
      bottom: 31px;
      -webkit-transform: rotate(90deg);
      transform: rotate(90deg);
      animation-name: marqueeLeft;
    }
    @keyframes marqueeTop {
      0% {
        left: -64px;
      }
      100% {
        left: 100%;
      }
    }
    @keyframes marqueeRight {
      0% {
        top: 31px;
      }
      100% {
        top: 100%;
      }
    }

    @keyframes marqueeBottom {
      0% {
        right: 0px;
      }
      100% {
        right: 100%;
      }
    }

    @keyframes marqueeLeft {
      0% {
        bottom: 33px;
      }
      100% {
        bottom: 100%;
      }
    }
  }

  @keyframes identifier {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      height: 100%;
      opacity: 1;
    }
  }

  @keyframes clipAnimation {
    // 0%,
    // 100% {
    //   clip: rect(0, 376px, 2px, 0);
    // }

    // 25% {
    //   clip: rect(0, 376px, 392px, 374px);
    // }

    // 50% {
    //   clip: rect(390px, 376px, 394px, 0px);
    // }

    // 75% {
    //   clip: rect(0px, 2px, 396px, 0px);
    // }

    0%,
    100% {
      clip-path: inset(0 0 calc(100% - 2px) 0);
    }
    25% {
      clip-path: inset(0 0 0 calc(100% - 2px));
    }
    50% {
      clip-path: inset(calc(100% - 2px) 0 0 0);
    }
    75% {
      clip-path: inset(0 calc(100% - 2px) 0 0);
    }

    // 0% {
    //   clip-path: inset(0 calc(100% - 20px) calc(100% - 2px) 0);
    // }
    // 25% {
    //   clip-path: inset(0 0 calc(100% - 20px) calc(100% - 2px));
    // }
    // 50% {
    //   clip-path: inset(calc(100% - 2px) 0 0 calc(100% - 20px));
    // }
    // 75% {
    //   clip-path: inset(calc(100% - 20px) calc(100% - 2px) 0 0);
    // }
  }

  .layout-header {
    width: 100%;
    height: 50px;
    text-align: center;
    position: relative;

    .header-title {
      //   line-height: 50px;
      font-weight: 900;
      // color: rgb(93, 189, 255);
      position: absolute;
      left: 0;
      top: 40%;
      text-align: center;
      width: 100%;
      font-size: 16px;
      letter-spacing: 3px;
    }
    .header-img {
      margin-left: -125px;
      margin-top: -3px;
      overflow: hidden;
    }
  }

  .quadrangle {
    position: absolute;
    width: 3px;
    height: 3px;
    // border: 2px solid rgba(70, 178, 247, 0.5);
  }
}
</style>