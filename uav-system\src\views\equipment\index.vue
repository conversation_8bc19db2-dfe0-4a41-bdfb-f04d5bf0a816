<template>
  <div class="equipment">
    <div class="searchDiv">
      <el-input
        v-model="searchValue"
        :placeholder="equipLanguage.input.placeholder"
        @keyup.enter.native="searchEvent"
        clearable
      ></el-input>
      <el-button @click="searchEvent" :class="searchCode ? 'active' : ''">{{
        equipLanguage.button.searchBtn
      }}</el-button>
      <el-button
        class="addButton"
        icon="el-icon-plus"
        @click="createEquip"
        :class="clickCode ? 'active' : ''"
        >{{ equipLanguage.button.addEquip }}</el-button
      >
    </div>
    <div class="content" ref="content">
      <el-collapse v-model="activeName" accordion @change="openCollapseItem">
        <el-collapse-item
          :name="item.sn_id"
          v-for="(item, index) in deviceList"
          :class="index % 2 == 0 ? '' : 'el-collapse-item_1'"
          :key="index"
          ref="collapseItem"
          v-show="checkOpen ? (checkOpen == item.sn_id ? true : false) : true"
          :disabled="item.state == 20"
        >
          <template slot="title">
            <collapse-item-header
              :index="index"
              :item="item"
              :checkOpen="checkOpen"
              :isShowData="isShowData"
              :versionList="versionList"
              @operateEvent="operateEvent"
              ref="collapseItemHeader"
              @sendGetLog="sendGetLog"
            ></collapse-item-header>
          </template>
          <component
            :is="checkOpen == item.sn_id ? componentId : null"
            :deviceItem="item"
            @changeLogCode="changeLogCode(index)"
            ref="contentInfo"
            @returnMessage="returnMessage"
          ></component>
        </el-collapse-item>
      </el-collapse>
    </div>

    <el-pagination
      :class="className"
      layout="prev, pager, next"
      :total="total_page"
      @current-change="changePage"
      @prev-click="prevClick"
      @next-click="nextClick"
    >
    </el-pagination>
    <component
      :is="componentDialog"
      ref="componentDialog"
      v-bind="componentProps"
      :clickCode.sync="clickCode"
      :deviceItemList="deviceItemList"
      @refresh="dialogRefresh"
      @updateVersion="updateVersion"
      @reStart="reStart"
      @chooseSize="chooseSize"
      @closeDialog="closeDialog"
      @getMentData="getMentData"
    ></component>
    <map-site
      ref="mapSite"
      :site="site"
      :equipLanguage="equipLanguage"
      @siteData="backData"
    ></map-site>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
import { typeJudge } from "@/utils/deviceTypeJudge";
import collapseItemHeader from "./components/collapseItemHeader.vue";
import equipMixins from "./equipMixins";
export default {
  name: "equipment",
  mixins: [equipMixins],
  components: {
    collapseItemHeader,
    mapSite: () => import("./components/chooseSite"),
  },
  data() {
    return {
      searchValue: "",
      searchCode: false,
      clickCode: false,
      deviceList: [],
      params: {
        page: 0,
        size: 10,
        type: 0,
      },
      total_page: 0,
      className: "",
      checkOpen: "",
      activeName: "",
      openDeviceItem: "",
      componentId: null,
      componentDialog: null,
      componentObj: {
        isStable: "fixedType",
        isMobile: "mobileNest",
        isAlone: "singleSoldierUav",
      },
      modules: {},
      isShowData: false,
      versionList: [
        {
          label: "机巢",
          id: "nest_version",
          value: "",
        },
        {
          label: "机巢驱动",
          id: "nest_mcu_version",
          value: "",
        },
        {
          label: "飞控", // 飞控系统
          id: "fcs_version",
          value: "",
        },
        {
          label: "相机",
          id: "camera_version",
          value: "",
        },
        {
          label: "副IC", //飞控从机
          id: "fcs_slave_version",
          value: "",
        },
        {
          label: "云台",
          id: "gimbal_version",
          value: "",
        },
        {
          label: "机载4G", //R500 Linux板 版本
          id: "drone_linux_version",
          value: "",
        },
      ],
      uavItemList: "",
      componentProps: {},
      deviceItemList: "",
      takeOffTime: 10,
      countDown: "",
      uavtypeList: "",
      site: {
        address: "",
        lat_int: 0,
        lng_int: 0,
      },
    };
  },
  computed: {
    equipLanguage() {
      return this.$languagePackage.equipment;
    },
    userInfo() {
      return this.$store.state.user.userInfo;
    },
  },
  created() {
    for (let index = 0; index < this.versionList.length; index++) {
      this.versionList[index].label =
        this.equipLanguage.versionLabel[this.versionList[index].id];
    }
    //获取设备类型列表
    this.$store.dispatch("requestTypeList");
  },
  mounted() {
    //获取到websocket信息
    this.$store.commit("setMultiMessage", {
      key: "equipment",
      message: this.getMessage,
    });
    this.getMentData();
    this.getUavType();
  },
  methods: {
    //获取websocket返回的信息,用户端
    getMessage(msg_id, data) {
      if (msg_id == 110) {
        for (let index = 0; index < this.deviceList.length; index++) {
          if (this.openDeviceItem.sn_id == data.sn_id) {
            if (this.openDeviceItem.is_pull_on && !data.is_pull_on) {
              this.$message.warning({
                message: this.equipLanguage.disconnect,
                customClass: "message-info",
              });
              this.openDeviceItem = "";
            }
          }
          if (data.sn_id == this.deviceList[index].sn_id) {
            this.deviceList[index].is_pull_on = data.is_pull_on;
            this.deviceList[index].is_push_on = data.is_push_on;
            this.deviceList[index].pull_user_info = data.pull_user_info;
          }
        }
      }
    },
    //设备websocket返回数据
    returnMessage(msg_id, data) {
      if (msg_id == 434) {
        for (let index = 0; index < this.versionList.length; index++) {
          // console.log(this.versionList[index].value,data[this.versionList[index].id])
          if (
            this.versionList[index].value != data[this.versionList[index].id]
          ) {
            this.versionList[index].value = data[this.versionList[index].id];
          }
        }
        let showData = this.versionList.findIndex((item) => {
          return item.value;
        });
        if (showData !== -1) {
          this.isShowData = true;
        } else {
          this.isShowData = false;
        }
        this.deviceItemList = data;
      }
      if (msg_id == 432) {
        this.uavItemList = data;
      }
    },
    //获取设备列表
    async getMentData() {
      let data = Object.assign({}, this.params);
      data.pmd = data.page.toString() + data.type.toString();
      await requestHttp("deviceList", data).then((res) => {
        this.deviceList = res.data.list ? res.data.list : [];
        for (let index = 0; index < this.deviceList.length; index++) {
          this.deviceList[index] = typeJudge(this.deviceList[index]);
        }
        this.total_page = res.data.total_page * 10;
      });
    },
    //获取飞机列表
    getUavType() {
      requestHttp("uavType").then((res) => {
        this.uavtypeList = res.data;
      });
    },
    //搜索设备
    async searchEvent() {
      if (this.searchCode) {
        return false;
      }
      if (this.searchValue == this.params.search) {
        return false;
      }
      this.searchCode = true;
      this.params.page = 0;
      this.params.search = this.searchValue;
      await this.getMentData();
      if (this.searchValue) {
        if (this.deviceList.length == 0) {
          this.$message.warning({
            message: this.equipLanguage.searchTip,
            customClass: "message-info",
          });
        }
        setTimeout(() => {
          this.searchCode = false;
        }, 200);
      } else {
        this.$message.info({
          message: this.equipLanguage.searchReturn,
          customClass: "message-info",
        });
        setTimeout(() => {
          this.searchCode = false;
        }, 200);
      }
    },
    //点击新增设备
    createEquip() {
      this.getComponents("operationDevice").then((component) => {
        this.componentDialog = component;
        this.componentProps = {
          uavtypeList: this.uavtypeList,
        };
        this.$nextTick(() => {
          this.$refs.componentDialog.open("add");
        });
      });
    },
    //编辑设备
    setEquip(item) {
      this.getComponents("operationDevice").then((component) => {
        this.componentDialog = component;
        this.componentProps = {
          uavtypeList: this.uavtypeList,
        };
        this.$nextTick(() => {
          this.$refs.componentDialog.open("edit", item);
        });
      });
    },
    //点击打开地图选择位置
    chooseSize(index, equipForm, site) {
      this.site = Object.assign(this.site, site);
      let data = {};
      if (index == 1) {
        data = {
          type: equipForm.type,
          sn_id: equipForm.sn_id,
          alternate_lat_int: equipForm.alternate_lat_int,
          alternate_lon_int: equipForm.alternate_lon_int,
        };
      } else {
        data = {
          type: equipForm.type,
          sn_id: equipForm.sn_id,
          get_address: true,
        };
      }
      this.$refs.mapSite.openMap(data);
    },
    //关闭页面
    closeDialog() {
      // if (this.getHeaderItem()) {
      //   this.getHeaderItem().sn_id = "";
      // }
    },
    //子组件传回的地址
    backData(e, index) {
      this.$refs.componentDialog.backData &&
        this.$refs.componentDialog.backData(e, index);
    },

    //加载组件模块
    getComponents(name) {
      if (this.modules[name]) {
        return Promise.resolve(this.modules[name]);
      }
      return new Promise((resolve) => {
        let module = require(`./components/${name}.vue`).default;
        this.modules[name] = module;
        resolve(module);
      });
    },
    //点击打开设备详细
    openCollapseItem(e) {
      this.checkOpen = e;
      if (e) {
        let num = 0;
        for (let index = 0; index < this.deviceList.length; index++) {
          if (e == this.deviceList[index].sn_id) {
            this.openDeviceItem = this.deviceList[index];
            num = index;
            break;
          }
        }
        let name = "";
        for (const key in this.componentObj) {
          if (this.openDeviceItem[key]) {
            name = this.componentObj[key];
          }
        }
        this.getComponents(name).then((component) => {
          this.componentId = component;
          this.changeLayout(num, true);
        });
        // if (
        //   this.deviceList[num].type == 10 &&
        //   this.deviceList[num].lat_int &&
        //   this.deviceList[num].lon_int
        // ) {
        //   let point =
        //     this.deviceList[num].lon_int / 1e7 +
        //     "," +
        //     this.deviceList[num].lat_int / 1e7;
        //   //注释掉获取天气信息
        //   // searchLnglat(point).then(res => {
        //   //   let adcode = res.data.regeocode.addressComponent.adcode;
        //   //   searchSky(adcode).then(e => {
        //   //     this.weatherState = e.data.lives[0];
        //   //   });
        //   // });
        // }
        setTimeout(() => {
          window.onresize = () => {
            return (() => {
              this.changeLayout(num);
            })();
          };
        });
      } else {
        this.isShowData = false;
        window.onresize = null;
        this.uavItemList = "";
      }
    },

    //修改页面布局,窗口改变时触发
    changeLayout(num, code) {
      let totalHeight = this.$refs.content.offsetHeight;
      let headerHeight = this.$refs.collapseItem[
        num
      ].$el.getElementsByClassName("el-collapse-item__header")[0].scrollHeight;

      let height = totalHeight - headerHeight - 1 + "px";
      this.$refs.collapseItem[num].$el
        .getElementsByClassName("el-collapse-item__content")[0]
        .setAttribute("style", "height:" + height + "!important");
      if (!this.deviceList[num].isAlone && !code) {
        this.$refs.contentInfo &&
          this.$refs.contentInfo[0] &&
          this.$refs.contentInfo[0].changeVideoLayout();
      }
    },

    //状态栏操作返回
    operateEvent(event, item) {
      this[event](item);
    },

    countDownFun() {
      this.takeOffTime = 10;
      this.countDown = setInterval(() => {
        let box = document.getElementsByClassName("restartConfirmBtn")[0];
        this.takeOffTime--;
        box.innerHTML = `<span>${this.equipLanguage.upgradeMsg.submit}（${this.takeOffTime}）</span>`;
        if (this.takeOffTime == 0) {
          this.takeOffTime = 10;
          clearInterval(this.countDown);
          this.countDown = null;
          box.click();
          return;
        }
      }, 1000);
    },
    sendGetLog(item) {
      this.$refs.contentInfo &&
        this.$refs.contentInfo[0] &&
        this.$refs.contentInfo[0].sendGetLog(item);
    },
    changeLogCode(index) {
      if (
        this.$refs.collapseItemHeader &&
        this.$refs.collapseItemHeader[index]
      ) {
        this.$refs.collapseItemHeader[index].getLogCode = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .equipment {
    .searchDiv {
      .el-button {
        padding: @zoomIndex * 13px @zoomIndex * 20px !important;
        border-radius: @zoomIndex * 8px !important;
        margin-top: @zoomIndex * 2px !important;
        font-size: @zoomIndex * 14px !important;
        letter-spacing: @zoomIndex * 2px !important;
      }
    }
  }
}
.equipment {
  width: 100%;
  height: 100%;
  .searchDiv {
    width: 96%;
    height: 6%;
    margin-left: 2%;
    padding-top: 1%;
    text-align: left;
    .el-input {
      width: 30%;
      margin-right: 1%;
    }
    .el-button {
      border: none;
      border-radius: 8px;
      margin-top: 2px;
      font-size: 14px;
      font-weight: 550;
      letter-spacing: 2px;
      padding: 13px 20px;
    }
    .addButton {
      float: right;
    }
  }
  .content {
    width: 96%;
    margin-left: 2%;
    height: 84%;
    margin-bottom: 1%;
    overflow: auto;
    .el-collapse {
      overflow: hidden;
      border: none;
    }
  }
  .el-pagination {
    text-align: right;
    margin-right: 2%;
  }
}
</style>
<style lang="less">
.equipment {
  .searchDiv {
    .el-input {
      .el-input__inner {
        font-size: 16px !important;
        border-radius: 8px !important;
        border-width: 1px !important;
        height: 40px !important;
        line-height: 40px !important;
      }
      .el-input__suffix {
        .el-input__icon {
          width: 25px !important;
          font-size: 14px !important;
          line-height: 40px !important;
        }
      }
    }
  }

  .content {
    .el-collapse {
      .el-collapse-item {
        .el-collapse-item__header {
          height: auto !important;
          border: none !important;
          padding: 0.2% 1% !important;
          .el-collapse-item__arrow {
            display: none !important;
          }
        }
        .el-collapse-item__wrap {
          border: none !important;
          .el-collapse-item__content {
            padding: 0 !important;
            position: relative !important;
          }
        }
      }
      .collapse-transition {
        -webkit-transition: 0s height, 0s padding-top, 0s padding-bottom !important;
        transition: 0s height, 0s padding-top, 0s padding-bottom !important;
      }
      .horizontal-collapse-transition {
        -webkit-transition: 0s width, 0s padding-left, 0s padding-right !important;
        transition: 0s width, 0s padding-left, 0s padding-right !important;
      }
      .horizontal-collapse-transition
        .el-submenu__title
        .el-submenu__icon-arrow {
        -webkit-transition: 0s !important;
        transition: 0s !important;
        opacity: 0 !important;
      }
    }
    &::-webkit-scrollbar {
      width: 3px !important;
    }
  }

  .el-pagination {
    button {
      margin: 0 1px !important;
      padding: 0 !important;
      span {
        min-width: auto !important;
        width: auto !important;
        margin: 0 5px !important;
        font-size: large !important;
      }
    }
    &.prev-class {
      .btn-prev {
        border: 1px solid #124093;
        color: #124093 !important;
      }
    }
    &.next-class {
      .btn-next {
        border: 1px solid #124093;
        color: #124093 !important;
      }
    }
    .el-pager {
      li {
        font-size: large !important;
      }
    }
  }
}
.selectUavType {
  .el-select-dropdown__wrap {
    margin-bottom: -23px !important;
    margin-right: -23px !important;
    max-height: 274px !important;
    .el-select-dropdown__list {
      padding: 6px 0 !important;
      .el-select-dropdown__item {
        font-size: 14px !important;
        padding: 0 20px !important;
        height: 34px !important;
        line-height: 34px !important;
      }
    }
  }
}
.message-info {
  top: 20px !important;
  min-width: 380px !important;
  padding: 15px 15px 15px 20px !important;
  border-radius: 4px !important;
  .el-message__icon {
    font-size: 16px !important;
    margin-right: 10px !important;
  }
  .el-message__content {
    font-size: 14px !important;
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .equipment {
    .searchDiv {
      .el-input {
        .el-input__inner {
          font-size: @zoomIndex * 16px !important;
          border-radius: @zoomIndex * 8px !important;
          border-width: @zoomIndex * 1px !important;
          height: @zoomIndex * 40px !important;
          line-height: @zoomIndex * 40px !important;
        }
        .el-input__suffix {
          .el-input__icon {
            font-size: @zoomIndex * 14px !important;
            width: @zoomIndex * 25px !important;
            line-height: @zoomIndex * 40px !important;
          }
        }
      }
    }
    .content {
      &::-webkit-scrollbar {
        width: @zoomIndex * 3px !important;
      }
    }

    .el-pagination {
      button {
        span {
          margin: 0 @zoomIndex * 5px !important;
        }
      }
      .btn-next,
      .btn-prev,
      .el-pager li {
        min-width: @zoomIndex * 30px !important;
        border-radius: @zoomIndex * 2px !important;
        margin: 0 @zoomIndex * 5px !important;
      }
      .btn-prev,
      .btn-next {
        span {
          font-size: @zoomIndex * 14px !important;
        }
      }
      button,
      span:not([class*="suffix"]) {
        height: @zoomIndex * 28px !important;
        line-height: @zoomIndex * 28px !important;
      }
      .el-pager li {
        padding: 0 @zoomIndex * 4px !important;
        font-size: @zoomIndex * 13px !important;
        min-width: @zoomIndex * 35.5px !important;
        height: @zoomIndex * 28px !important;
        line-height: @zoomIndex * 28px !important;
      }
    }
  }
  .selectUavType {
    .el-select-dropdown__wrap {
      margin-bottom: @zoomIndex * -23px !important;
      margin-right: @zoomIndex * -23px !important;
      max-height: @zoomIndex * 274px !important;
      .el-select-dropdown__list {
        padding: @zoomIndex * 6px 0 !important;
        .el-select-dropdown__item {
          font-size: @zoomIndex * 14px !important;
          padding: 0 @zoomIndex * 20px !important;
          height: @zoomIndex * 34px !important;
          line-height: @zoomIndex * 34px !important;
        }
      }
    }
  }
  .messageTip {
    &.el-message-box {
      padding-bottom: @zoomIndex * 10px !important;
      border-radius: @zoomIndex * 4px !important;
      border: @zoomIndex * 1px solid #ebeef5 !important;
      font-size: @zoomIndex * 18px !important;
      box-shadow: 0 @zoomIndex * 2px @zoomIndex * 12px 0 rgb(0 0 0 / 10%) !important;
      width: @zoomIndex * 420px !important;
      .el-message-box__header {
        padding: @zoomIndex * 15px @zoomIndex * 15px @zoomIndex * 10px !important;
        .el-message-box__title {
          font-size: @zoomIndex * 18px !important;
        }
        .el-message-box__headerbtn {
          top: @zoomIndex * 15px !important;
          right: @zoomIndex * 15px !important;
          font-size: @zoomIndex * 16px !important;
        }
      }
      .el-message-box__content {
        padding: @zoomIndex * 10px @zoomIndex * 15px !important;
        font-size: @zoomIndex * 14px !important;
        .el-message-box__status {
          font-size: @zoomIndex * 24px !important;
        }
        .el-message-box__status + .el-message-box__message {
          padding-left: @zoomIndex * 36px !important;
          padding-right: @zoomIndex * 12px !important;
        }
      }
      .el-message-box__btns {
        padding: @zoomIndex * 5px @zoomIndex * 15px 0 !important;
        .el-button--small {
          padding: @zoomIndex * 9px @zoomIndex * 15px !important;
          font-size: @zoomIndex * 12px !important;
          border-radius: @zoomIndex * 3px !important;
        }
      }
    }
  }
  .message-info {
    top: @zoomIndex * 20px !important;
    min-width: @zoomIndex * 380px !important;
    padding: @zoomIndex * 15px @zoomIndex * 15px @zoomIndex * 15px @zoomIndex *
      20px !important;
    border-radius: @zoomIndex * 4px !important;
    .el-message__icon {
      font-size: @zoomIndex * 16px !important;
      margin-right: @zoomIndex * 10px !important;
    }
    .el-message__content {
      font-size: @zoomIndex * 14px !important;
    }
  }
}
</style>