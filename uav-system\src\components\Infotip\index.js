import Vue from 'vue'

import infoTip from "./index.vue"

// 将组件注册到Vue
const PopupBox = Vue.extend(infoTip)

PopupBox.install = (data) => {

  // 把元素挂载在body上
  let instance = new PopupBox({
    data
  }).$mount()
  document.body.appendChild(instance.$el)
  Vue.nextTick(() => {
    instance.show()
  })
  return instance;
}

export function createTooltip(data) {

  let defaultConfig = {
    title: '', // 标题
    type: 'scroll', // 操作类型，scroll，button
    autoShut: true, // 自动关闭
  }

  // 销毁实列事件
  data.destroy = () => {
    document.body.removeChild(instance.$el);
  }

  // 确定或校验通过
  data.submitCallback = (state) => {
    data.confirm && data.confirm(instance, state);
  }

  // 取消或校验不通过
  data.cancelCallback = (state) => {
    data.cancel && data.cancel(instance, state);
  }

  let params = Object.assign(defaultConfig, data);

  let instance = PopupBox.install(params)

  return instance;
}

/**
 * 使用示例
 * 
 * 1、引入文件
 * import { createTooltip } from "@/components/infotip/index.js"
 * 2、使用
 * createTooltip({
 *    title // 标题
 *    type  // 类型
 *    destroy // 销毁
 * })
 */
