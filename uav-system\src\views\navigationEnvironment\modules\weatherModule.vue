<template>
  <div class="weatherModule">
    <put-away
      class="checking-type"
      v-model="isOpen"
      :styles="{ top: 0, right: 0 }"
      :buttonStyle="buttonStyle"
      :tooltipText="language.title"
    >
      <template v-slot:main>
        <div class="weather-module-main">
          <div
            class="scrollbar-style"
            style="overflow: auto; height: calc(100%)"
          >
            <div class="weather-item">
              <div class="weather-item-title">{{ language.title1 }}</div>
              <div class="weather-list">
                <div
                  class="weather-list-item"
                  v-for="(item, index) in weatherModel"
                  :key="index"
                >
                  <div class="item-title">{{ item.label }}</div>
                  <div class="item-value">{{ item.value }}</div>
                </div>
              </div>
              <div class="weather-item-title">{{ language.title2 }}</div>
              <div class="weather-list">
                <div
                  class="weather-list-item"
                  v-for="(item, index) in weatherModel1"
                  :key="index"
                >
                  <div class="item-title">{{ item.label }}</div>
                  <div class="item-value">{{ item.value }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-slot:showContent>
        <div class="" style="color: #fff">{{ language.title }}</div>
      </template>
    </put-away>
  </div>
</template>
<script>
import putAway from "../components/putAway.vue";
import { searchLnglat, searchSky } from "@/utils/mapApi";
import { computedMethod } from "@/utils/computedMap";
import { wgs84_to_gcj02 } from "@/utils/wgs84_to_gcj02.js";
export default {
  data() {
    return {
      isOpen: true,
      buttonStyle: {
        left: "-24px",
        "border-top-left-radius": "5px",
        "border-bottom-left-radius": "5px",
      },
      weatherModel: [
        // { label: "空气温度(℃)", value: NaN, id: "temperature" },
        // { label: "空气湿度(%)", value: NaN, id: "humidity" },
        { label: "舱内温度(℃)", value: NaN, id: "temp_inside" },
        { label: "舱外温度(℃)", value: NaN, id: "temp_outside" },
        { label: "风速(m/s)", value: NaN, id: "wind_speed" },
        { label: "风力", value: NaN, id: "wind_level" },
        // { label: "风向(°,[0-360])", value: NaN, id: "windDirection" },
        { label: "雨量(mm)", value: NaN, id: "rain_fall" },
        { label: "湿度(%)", value: NaN, id: "humidity" },
      ],
      weatherModel1: [
        { label: "温度(℃)", value: NaN, id: "temperature" },
        { label: "湿度(%)", value: NaN, id: "humidity" },
        { label: "风力", value: NaN, id: "windpower" },
        { label: "风向", value: NaN, id: "winddirection" },
        { label: "天气", value: NaN, id: "weather" },
      ],
      lnglat: "",
    };
  },
  components: {
    putAway,
  },
  computed: {
    staveThreeData() {
      return this.$store.state.equipment.staveThreeData;
    },
    language() {
      return this.$languagePackage.navigation.weatherModule;
    },
  },
  watch: {
    staveThreeData: {
      handler: function (data) {
        // this.changeWea(data);
      },
      deep: true,
    },
  },
  created() {
    for (let index = 0; index < this.weatherModel.length; index++) {
      this.weatherModel[index].label = this.language.weatherLabel[index];
    }
    for (let index = 0; index < this.weatherModel1.length; index++) {
      this.weatherModel1[index].label = this.language.weatherLabel1[index];
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$store.commit("setWsMmessageFun", {
        key: "weatherModule",
        message: this.disposeData,
      });
      let query = this.$route.query || {};
      this.equipmentType = query.type;
      if (query.state == 1) {
        // 获取航线
        let flightParam = sessionStorage.getItem("flightParam");
        try {
          if (!flightParam) {
            return false;
          }
          let data = JSON.parse(flightParam)[query.sn_id];
          let a = wgs84_to_gcj02(data.point[0], data.point[1]);
          // this.getWeather(new AMap.LngLat(a[0], a[1]));
        } catch (error) {
          console.error(error);
        }
      }
    });
  },
  methods: {
    changeWea(data) {
      let lon = data.longitude / 1e7,
        lat = data.latitude / 1e7;
      let position = wgs84_to_gcj02(lon, lat);
      let point = new AMap.LngLat(position[0], position[1]);
      if (this.lnglat) {
        let result = computedMethod(1, { point1: this.lnglat, point2: point });
        if (Math.abs(result) > 10000) {
          this.getWeather(point);
        }
      } else {
        this.getWeather(point);
      }
    },
    //获取天气信息
    async getWeather(points) {
      this.lnglat = points;
      let point = points.lng + "," + points.lat;
      let adcode = 0;
      //注释掉获取天气信息
      // await searchLnglat(point).then(res => {
      //   adcode = res.data.regeocode.addressComponent.adcode;
      // });
      // await searchSky(adcode).then(e => {
      //   let weatherData = e.data.lives[0];
      //   if (weatherData) {
      //     for (let index = 0; index < this.weatherModel1.length; index++) {
      //       this.weatherModel1[index].value =
      //         weatherData[this.weatherModel1[index].id];
      //     }
      //   }
      // });
    },
    disposeData: function (msg_id, data) {
      // if (msg_id == 430) {
      //   for (let index = 0; index < this.weatherModel.length; index++) {
      //     if (this.weatherModel[index].id != "windSpeed") {
      //       if (
      //         this.weatherModel[index].id == "windPower" ||
      //         this.weatherModel[index].id == "windDirection"
      //       ) {
      //         this.weatherModel[index].value =
      //           data[this.weatherModel[index].id];
      //       } else {
      //         this.weatherModel[index].value =
      //           data[this.weatherModel[index].id] / 10;
      //       }
      //     }
      //   }
      // }
      if (msg_id == 434) {
        for (let index = 0; index < this.weatherModel.length; index++) {
          this.weatherModel[index].value = data[this.weatherModel[index].id]
            ? parseFloat(data[this.weatherModel[index].id].toFixed(2))
            : 0;
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
.weatherModule {
  width: 100%;
  .checking-type {
    height: 100%;
  }
  .weather-module-main {
    height: calc(100% - 16px);
    border-radius: 5px;
    background-color: rgba(104, 104, 104, 0.5);
    width: 100%;
    padding: 8px 0;
    .weather-item {
      color: #fff;
      .weather-item-title {
        background-image: linear-gradient(
          to bottom,
          rgba(13, 134, 255, 0.5),
          rgba(4, 11, 43, 0.8)
        );
        padding: 10px;
      }
      .weather-list {
        background-color: rgba(71, 71, 71, 0.5);
        padding: 15px 10px;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .weather-list-item {
          width: calc((100% - 20px) / 2);
          margin: 3px 0;
          .item-title {
            color: rgb(187, 187, 187);
            font-size: 14px;
          }
          .item-value {
            color: #fff;
            font-size: 18px;
          }
        }
      }
    }
  }
}
</style>
