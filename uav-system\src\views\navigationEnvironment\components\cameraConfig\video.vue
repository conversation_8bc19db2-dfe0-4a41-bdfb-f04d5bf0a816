<template>
  <div class="camera-video video">
    <div class="video-item">
      <div class="item-content">
        <div class="content-title">{{ language.previewResolution }}</div>
        <div class="content-main">
          <tab-scroll
            :list="previewResolutionList"
            v-model="previewResolutionIndex"
            @onChange="previewChannelChange($event,0)"
          ></tab-scroll>
        </div>
      </div>
    </div>

    <div class="video-item">
      <div class="item-content">
        <div class="content-title">{{ language.previewCodeRate }}</div>
        <div class="content-main">
          <tab-scroll
            :list="previewCodeRateList"
            v-model="previewCodeRateIndex"
            @onChange="previewChannelChange($event,1)"
          ></tab-scroll>
        </div>
      </div>
    </div>

    <div class="video-item">
      <div class="item-content">
        <div class="content-title">{{ language.codeRate }}</div>
        <div class="content-main">
          <tab-scroll
            :list="codeRateLit"
            v-model="codeRateIndex"
            @onChange="videoChannelChange($event,0)"
          >
            <template v-slot:content="scope">
              <div
                class="video-bit-rate"
                :class="
                  codeRateIndex == scope.row.value ? 'select-bit-rate' : ''
                "
              >
                <div class="label">
                  {{ scope.row.label }}
                </div>
                <div class="subhead">
                  {{ scope.row.subhead }}
                </div>
              </div>
            </template>
          </tab-scroll>
        </div>
      </div>
    </div>

    <div class="video-item">
      <div class="item-content">
        <div class="content-title">{{ language.resolution }}</div>
        <div class="content-main" style="">
          <tab-scroll
            :list="resolutionList"
            v-model="resolutionIndex"
            @onChange="videoChannelChange($event,1)"
          />
        </div>
      </div>
    </div>

    <div class="video-item">
      <div class="item-content">
        <div class="content-title">{{ language.hz }}</div>
        <div class="content-main">
          <tab-scroll :list="hertzList" v-model="hertzIndex" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import imgIcon from "@/components/imgIcon/index.vue";
import tabScroll from "@/components/tabScroll/index.vue";
export default {
  components: {
    imgIcon,
    tabScroll,
  },
  data() {
    return {
      // 录像码率
      codeRateIndex: -1,
      codeRateLit: [
        {
          label: "4k",
          subhead: "25",
          value: 0,
        },
        // {
        //   label: "4k",
        //   subhead: "30",
        //   value: 1,
        // },
        // {
        //   label: "4k",
        //   subhead: "60",
        //   value: 2,
        // },
        {
          label: "6k",
          subhead: "25",
          value: 3,
        },
      ],

      // 录像hz
      hertzIndex: 0,
      hertzList: [
        { label: "H264", value: 0 },
        // { label: "H265", value: 2 },
      ],

      // 分辨率
      resolutionIndex: -1,
      resolutionList: [
        { label: "8M", value: 0 },
        { label: "16M", value: 1 },
        { label: "32M", value: 2 },
        { label: "64M", value: 3 },
      ],

      // 预览分辨率
      previewResolutionIndex: -1,
      previewResolutionList: [
        { label: "720p", value: 1 },
        { label: "1080p", value: 0 },
      ],

      // 预览码率
      previewCodeRateIndex: -1,
      previewCodeRateList: [
        { label: "1M", value: 0 },
        { label: "2M", value: 1 },
        { label: "4M", value: 2 },
      ],
      backstageChange: false,
    };
  },
  computed: {
    // 无人机
    equipmentWS() {
      return this.$store.state.equipment.equipmentWS;
    },
    staveTwoData() {
      return this.$store.state.equipment.staveTwoData;
    },
    language() {
      return this.$languagePackage.navigation.cameraConfig.videos;
    },
  },
  watch: {
    staveTwoData: {
      deep: true,
      handler: function (item) {
        this.codeRateIndex = item.record_resolution;
        this.previewResolutionIndex = item.preview_resolution;
        // console.log("预览分辨率-------->", item.preview_resolution);
        this.resolutionIndex = item.record_bitrate;
        this.previewCodeRateIndex = item.preview_bitrate;
        // console.log("视频值发生变化------------------->");
      },
    },
  },
  methods: {
    // 预览通道
    previewChannelChange: function (e,index) {
      if(index==1){
        this.previewCodeRateIndex=e
      }else{
        this.previewResolutionIndex=e
      }
      this.equipmentWS.manualSend(
        {
          cmd_type: 10,
          preview_resolution: this.previewResolutionIndex, // 预览分辨率
          preview_bitrate: this.previewCodeRateIndex, // 预览码率
        },
        402
      );
    },
    videoChannelChange: function (e,index) {
      if(index==1){
        this.resolutionIndex=e
      }else{
        this.codeRateIndex=e
      }
      this.equipmentWS.manualSend(
        {
          cmd_type: 11,
          record_resolution: this.codeRateIndex, // 录像分辨率
          record_bitrate: this.resolutionIndex, // 录像码率
        },
        402
      );
    },
  },
};
</script>

<style lang="less" scoped>
.video {
  padding: 0 50px;
  display: flex;
  flex-wrap: wrap;
  .video-item {
    width: 50%;
    .item-content {
      margin-bottom: 20px;
      .content-title {
        font-size: 12px;
        // color: #fff;
        margin: 18px 0 11px 0;
      }
      .content-main {
        // border: 1px solid #c2c3c3;
        width: 221px;
        height: 24px;
        border-radius: 6px;
        // padding: 0 35px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .main-row {
          // color: #fff;
          font-size: 12px;
          cursor: pointer;
        }

        .select-row {
          position: relative;
          // color: #398bcc;
          &::after {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            // border-left: 5px solid transparent;
            // border-right: 5px solid transparent;
            // border-bottom: 5px solid #398bcc;
            bottom: -5px;
            left: 50%;
            margin-left: -5px;
          }
        }

        .select-bit-rate {
          // background-color: rgba(57, 139, 204, 1) !important;
          &::before {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            // border-left: 10px solid transparent;
            // border-right: 10px solid transparent;
            // border-bottom: 10px solid #398bcc;
            bottom: -16px;
            left: 50%;
            margin-left: -10px;
          }
        }
        .video-bit-rate {
          width: 30px;
          height: 16px;
          // border: 1px solid #ccc;
          border-radius: 4px;
          // color: #ffff;
          // background-color: rgba(26, 19, 17, 1);
          position: relative;

          .label {
            width: 200%;
            height: 200%;
            font-size: 20px;
            transform: scale(0.5);
            position: absolute;
            left: -50%;
            top: -50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: -6px;
          }
          .subhead {
            position: absolute;
            left: 0;
            top: 0;
            width: 400%;
            height: 400%;
            font-size: 32px;
            transform: scale(0.25);
            transform-origin: left top;
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;
            margin-left: -3px;
          }
          &::after {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            // border-left: 10px solid transparent;
            // border-right: 10px solid transparent;
            // border-bottom: 10px solid rgba(26, 19, 17, 0.3);
            bottom: -3px;
            right: -10px;
            transform: rotate(135deg);
          }
        }
      }
    }
  }
}
</style>