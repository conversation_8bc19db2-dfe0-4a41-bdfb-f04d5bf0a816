import layerButtonGroup from "@/components/layerButton/index"
Cesium.Ion.defaultAccessToken = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI2YjIxYmUxMy02MDg1LTQxMWUtODQ5MC04Y2YzOTkwZmFmYWYiLCJpZCI6MTAyNzA3LCJpYXQiOjE2NTg5MDcxNTR9.vAHUWvin6nZWak2SU5JxO4oJAN5L7vUzvO75UxgTeO8`;
Cesium.RequestScheduler.maximumRequests = 50; // 全局最大并发数
Cesium.RequestScheduler.maximumRequestsPerServer = 25; // 单服务器并发数
let url = [
    //高德地图街道瓦片
    "https://{s}.is.autonavi.com/appmaptile?lang=zh_cn&size=3&style=7&x={x}&y={y}&z={z}",

    //高德地图卫星瓦片 
    "https://{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}",
    //高德路网注记
    "https://{s}.is.autonavi.com/appmaptile?style=8&x={x}&y={y}&z={z}",
    //谷歌影像带注记
    "https://mt0.google.com/vt/lyrs=y&scale=2&hl=en&gl=cn&x={x}&y={y}&z={z}",
    //谷歌矢量
    "https://mt0.google.com/vt/lyrs=p&scale=2&hl=en&gl=cn&x={x}&y={y}&z={z}",
    //谷歌路网
    "https://mt1.google.com/vt/lyrs=h&x={x}&y={y}&z={z}",
    //谷歌影像
    "http://www.google.cn/maps/vt?lyrs=s@189&gl=cn&x={x}&y={y}&z={z}",
    //谷歌影像带注记
    // "https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}",
    //谷歌地形
    "https://mt1.google.com/vt/lyrs=t&x={x}&y={y}&z={z}",
    //谷歌地图矢量带地形渲染
    "https://mt1.google.com/vt/lyrs=r&x={x}&y={y}&z={z}",
    //街景地图
    "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
    //天地图影像
    "http://t7.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fb9784ae90381164a3bc83974d7f7ad4",
    //天地图影像注记
    "http://t7.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fb9784ae90381164a3bc83974d7f7ad4",
    // 天地图矢量
    "http://t7.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fb9784ae90381164a3bc83974d7f7ad4",

    // 天地图矢量注记
    "http://t7.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fb9784ae90381164a3bc83974d7f7ad4",

    // 天地图地形
    "http://t7.tianditu.gov.cn/ter_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=ter&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fb9784ae90381164a3bc83974d7f7ad4",
    // 天地图地形注记
    "http://t7.tianditu.gov.cn/cta_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cta&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=fb9784ae90381164a3bc83974d7f7ad4",

    // 腾讯地图矢量
    "http://rt0.map.gtimg.com/realtimerender?z={z}&x={x}&y={-y}&type=vector&style=0",

]
let subdomains = [
        ["wprd01", "wprd02", "wprd03", "wprd04", ],
        ["webst01", "webst02", "webst03", "webst04"],
        ["webst01", "webst02", "webst03", "webst04"]
    ]
    //初始化地图
function createMap(tagId, params = {}) {
    let mode = [Cesium.SceneMode.SCENE3D, Cesium.SceneMode.SCENE2D]
    let {
        layerIndex = 0,
            homeButton = false,
            geocoder = false,
            sceneModePicker = false,
            modeIndex = 0,
            sceneMode = mode[modeIndex], //3D:SCENE3D，2D:SCENE2D
            enableTilt = modeIndex == 0 ? true : false,
            loadLayerText = true,
            layerButton = true,
            sceneModeButton = false,


    } = params

    //初始化viewer控件
    let map = new Cesium.Viewer(tagId, {
        animation: false, //是否显示动画控件
        homeButton, //是否显示Home按钮
        geocoder, //是否显示地名查找控件
        baseLayerPicker: false, //是否显示图层选择控件
        timeline: false, //是否显示时间线控件
        fullscreenButton: false, //是否显示全屏按钮
        infoBox: false, //是否显示点击要素之后显示的信息
        selectionIndicator: false, //创建SelectionIndicator小部件。
        navigationInstructionsInitiallyVisible: false, //初始导航是否可见
        navigationHelpButton: false, //是否显示帮助信息控件
        vrButton: false, //vr按钮，地图双屏按钮
        sceneModePicker, //是否显示投影方式控件
        requestRenderMode: false, //启用请求渲染模式
        scene3DOnly: false, //每个几何实例将只能以3D渲染以节省GPU内存
        sceneMode, //初始场景模式 1 2D模式 2 2D循环模式 3 3D模式  Cesium.SceneMode
        credits: false,
        heightReference: Cesium.HeightReference.NONE,
        // terrainProvider: Cesium.createWorldTerrain({
        //     requestVertexNormals: true,
        //     requestWaterMask: true, // 动态水流
        // }), //地形
        // 设置基础图层
        imageryProvider: returnLayer(layerIndex)
    })

    //使用给定的图像提供者创建一个新图层，并将其添加到集合中。
    if (loadLayerText) {
        map.imageryLayers.addImageryProvider(returnLayer(2));
    }

    // map.camera.zoomIn(3)
    //去除版权信息

    map._cesiumWidget._creditContainer.style.display = "none"
    map.scene.fxaa = true
    map.scene.globe.maximumScreenSpaceError = 4 / 3
        // 关闭、开启深度检测
    map.scene.globe.depthTestAgainstTerrain = false
    map.scene.screenSpaceCameraController.enableTilt = enableTilt
        // 关闭光照
    map.scene.globe.enableLighting = false;

    // 关闭阴影计算
    map.scene.shadowMap.enabled = false;
    // map.scene.fog.enabled = false; // 关闭雾效
    // map.scene.sun.show = false; // 关闭太阳光
    // map.scene.moon.show = false; // 关闭月光
    // map.scene.skyAtmosphere.show = false; // 关闭大气层
    // 优先加载可视区域的瓦片
    map.scene.globe.tileLoadProgressEvent.addEventListener((remaining) => {
        map.scene.requestRender();
    });

    // Cesium.Camera.DEFAULT_VIEW_RECTANGLE =
    //     Cesium.Rectangle.fromDegrees(80, 22, 130, 50); //home定位到中国范围
    // 将三维球定位到中国
    // map.camera.flyTo({
    //     destination: Cesium.Cartesian3.fromDegrees(108.923611, 34.540833, 20000000),
    //     orientation: {
    //         heading: Cesium.Math.toRadians(0),
    //         pitch: Cesium.Math.toRadians(-90),
    //         roll: Cesium.Math.toRadians(0)
    //     },
    //     complete: function callback() {
    //         // 定位完成之后的回调函数
    //     }
    // });
    map.camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(108.923611, 34.540833, 20000000),
        // 方向，俯视和仰视的视角
        orientation: {
            heading: Cesium.Math.toRadians(0), //坐标系旋转
            pitch: Cesium.Math.toRadians(-90), //设置俯仰角度
            roll: Cesium.Math.toRadians(0)
        }
    })


    function returnFun(e) {
        let layers = map.imageryLayers._layers
        let index = layers.findIndex(item => {
            return Number(item.imageryProvider.credit.html) === e
        })
        let index1 = layers.findIndex(item => {
            return Number(item.imageryProvider.credit.html) === 2
        })
        if (index === -1) {
            layers.forEach(item => {
                item.show = false
            });
            map.imageryLayers.addImageryProvider(returnLayer(e));
            if (e == 1) {
                map.imageryLayers.addImageryProvider(returnLayer(2));
            }

        } else {
            if (layers && layers.length) {
                if (e == 0) {
                    layers.forEach((item, i) => {
                        if (i === index) {
                            item.show = true
                        } else {
                            item.show = false
                        }
                    });
                } else {
                    layers.forEach((item, i) => {
                        if (i === index || i === index1) {
                            item.show = true
                        } else {
                            item.show = false
                        }
                    });
                }
            }
        }
    }

    function sceneModeChange(e) {
        if (e) {
            map.scene.morphTo3D(0)
            map.scene.screenSpaceCameraController.enableTilt = e
        } else {
            map.scene.morphTo2D(0)
            map.scene.screenSpaceCameraController.enableTilt = e
        }
    }
    if (layerButton || sceneModeButton) {
        new layerButtonGroup({
            viewer: map,
            layerIndex,
            layerButton,
            sceneModeButton,
            sceneMode3D: enableTilt,
            returnFun: returnFun,
            sceneModeChangeFun: sceneModeChange
        })
    }
    return map
}
//图层返回
function returnLayer(index) {
    return new Cesium.UrlTemplateImageryProvider({
        url: url[index],
        subdomains: subdomains[index] ? subdomains[index] : '',
        enablePickFeatures: true,
        maximumLevel: 18, //图像提供程序支持的最大详细程度
        minimumLevel: 3, //图像提供支持的最低详细程度
        credit: index.toString(),

    })
}


export default {
    createMap,

}