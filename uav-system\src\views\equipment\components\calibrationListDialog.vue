<template>
  <div class="calibrationListDialog">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="visible"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :modal="false"
      custom-class="calibrationDialog"
      width="1000px"
      center
    >
      <div class="calibration-list" v-if="!changeContentCode">
        <div class="calibration-list-item" v-for="item in list" :key="item.id">
          <div class="item-label">{{ item.label }}</div>
          <div
            class="item-state"
            :class="uavState[item.name]? 'success' : 'error'"
          >
            {{ returnState(item) }}
          </div>
          <div class="item-operate">
            <el-button @click="calibrationEvent(item)">{{
              language.calibrationTitle
            }}</el-button>
          </div>
        </div>
      </div>
      <calibration-content
        ref="calibrationContent"
        v-else
        :calibrationItem="calibrationItem"
        :websocket="websocket"
      ></calibration-content>
      <div class="close-btn" @click="close">
        <el-image :src="closeImg"></el-image>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import calibrationContent from "./calibrationContent.vue";
export default {
  data() {
    return {
      dialogTitle: "无人机传感器设置",
      visible: false,
      list: [
        { label: "陀螺仪", name:'gyro_calib' , id: "gyroscope" },
        { label: "电子罗盘", name:'compass_calib', id: "compass" },
        // { label: "加速度", name:'acc_calib', id: "acceleration" },
      ],
      stateList: {
        0: "异常",
        1: "正常",
      },
      changeContentCode: false,
      closeImg: require("@/assets/img/equipment/close.png"),
      calibrationItem: "",
      websocket:'',
      uavState:{}
    };
  },
  components: {
    calibrationContent,
  },
  computed: {
    language() {
      return this.$languagePackage.equipment.calibration;
    },
  },
  created() {
    for (let index = 0; index < this.list.length; index++) {
      this.list[index].label = this.language.list[this.list[index].id];
    }
    this.stateList = this.language.stateList;
    this.dialogTitle = this.language.title;
  },
  methods: {
    getMessage(msg_id,data){
      let calibrationContent=this.$refs.calibrationContent
      calibrationContent&&calibrationContent.getMessage(msg_id,data)
      if(msg_id==432){
        this.uavState=data
      }
    },
    open(websocket) {
      //   this.dialogTitle = item.label;
      this.visible = true;
      this.websocket=websocket
    },
    close() {
      if (
        this.$refs.calibrationContent &&
        this.$refs.calibrationContent.resultCode == "success"
      ) {
        return false;
      }
      // this.dialogTitle=''
      this.dialogTitle = this.language.title;
      this.visible = false;
      this.calibrationItem = "";
      this.changeContentCode = false;
    },
    calibrationEvent(item) {
      this.changeContentCode = true;
      this.dialogTitle = item.label + this.language.title1;
      this.calibrationItem = item;
    },
    returnState(item){
      if(this.uavState[item.name]){
        return this.stateList[1]
      }else{
        return this.stateList[0]
      }
    }
  },
};
</script>
<style lang="less" scoped>
.calibrationListDialog {
  .calibrationDialog {
    .calibration-list {
      .calibration-list-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 80px;
        line-height: 80px;
        padding: 0 40px;
        cursor: pointer;
        &:last-child {
          border-radius: 0 0 10px 10px;
        }
        &:hover {
          background-color: #ffffff;
        }
        .item-label {
          flex: 1;
          text-align: center;
          color: #010101;
          font-weight: 600;
          font-size: 18px;
        }
        .item-state {
          flex: 1;
          text-align: center;
          font-size: 16px;
          &.error {
            color: #ff0000;
          }
          &.success {
            color: #2eac4c;
          }
        }
        .item-operate {
          flex: 1;
          text-align: center;
          .el-button {
            font-size: 16px;
            background-color: transparent;
            color: #070707;
            padding: 3px 0;
            border: 1px solid #000000;
            width: 80px;
          }
        }
      }
    }
    .close-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 22px;
      height: 22px;
      cursor: pointer;
    }
  }
}
</style>
<style lang="less">
</style>
<style lang="less">
.calibrationListDialog {
  .calibrationDialog {
    background-color: transparent;
    border-radius: 10px;
    .el-dialog__header {
      background-color: #2e2b2b;
      border-radius: 10px 10px 0 0;
      padding: 20px;
      .el-dialog__title {
        color: #ffffff;
        font-size: 24px;
      }
    }
    .el-dialog__body {
      padding: 0;
      background-color: #808080;
      border-radius: 0 0 10px 10px;
    }
  }
}
</style>