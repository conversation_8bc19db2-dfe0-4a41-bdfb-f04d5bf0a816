<template>
  <div class="teachingVideos">
    <div class="teach-header"></div>
    <custom-table
      :column="column"
      :params="params"
      pmd="page"
      urlName="videoUserList"
      ref="customTable"
    >
      <template #preview_url="scope">
        <el-image
          style="width: 120px; height: 67.5px"
          :src="scope.row.preview_url"
          :preview-src-list="[scope.row.preview_url]"
        ></el-image>
      </template>
      <template #operation="scope">
        <el-button type="text" @click="playVideo(scope.row)">{{
          language.playBtn
        }}</el-button>
      </template>
    </custom-table>
    <video-play ref="videoPlay"></video-play>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
import customTable from "@/components/customTable/index";
import videoPlay from "./module/videoPlay.vue";
export default {
  data() {
    return {
      column: [
        { label: "预览图", prop: "preview_url" },
        { label: "视频标题", prop: "title" },
        { label: "视频描述", prop: "describe" },
        { label: "语言", prop: "notes" },
        { label: "操作", prop: "operation" },
      ],
      params: {
        first_class: 9,
        requestUrl: "fly",
      },
    };
  },
  components: {
    customTable,
    videoPlay,
  },
  computed: {
    language() {
      return this.$languagePackage.layout.teachVideo;
    },
  },
  created() {
    this.column = this.language.column;
  },
  methods: {
    playVideo: function (row) {
      this.$refs.videoPlay.open(row);
    },
  },
};
</script>
<style lang="less" scoped>
.teachingVideos {
  width: 100%;
  height: 100%;
  background-color: #040404;
  .teach-header {
    padding: 1% 2%;
  }
  .custom-table {
    width: 96%;
    margin: 0 2%;
  }
}
</style>