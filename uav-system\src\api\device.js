import {
    urlsplit
} from './urlsplit'
import baseUrl from '@/utils/global'
// let port = "8190";
// let port = "/nedv";

let deviceList = {
    deviceList: '/device/list',
    uavType: '/device/uav/type',
    deviceAdd: '/device/add',
    deviceEdit: "/device/edit",
    insRanking: "/ins/ranking",
    resultList: "/result/list",
    resultDelete: "/result/delete/one", // 单个删除
    resultDeleteIds: "/result/delete/ids", // 多个删除
    resultDeleteSort: "/result/delete/sort", // 批量根据架次删除
    sortiesDelete: '/sorties/delete', //批量删除架次数据

    fenceList: "/fence/list", //围栏列表
    fenceAdd: "/fence/add", //新增围栏
    fenceEdit: "/fence/edit", //编辑围栏
    missionType: "/mission/type", //任务类型
    missionList: "/mission/list", //任务列表
    missionAdd: "/mission/add", //新增任务
    missionEdit: '/mission/edit', //编辑任务
    missionInfo: '/mission/info', //获取任务信息
    clearBreak: '/mission/clearBreak', //清除航线断点
    deviceOnline: '/device/connect', //在线设备列表
    fenceOne: '/fence/one', //围栏id获取围栏数据
    sortiesList: "/sorties/list", // 获取（搜索）架次列表

    firmwareList: '/rom/list', //获取固件列表
    uploadFirmware: '/rom/add', //上传固件
    editFirmware: '/rom/edit', //编辑固件

    taskAdd: '/task/add', //添加定时任务
    taskEdit: '/task/edit', //编辑定时任务
    taskList: '/task/list', //定时任务列表

    deviceTypeList: '/deviceType/adminList', //管理员获取设备类型
    deviceTypeAdd: "/deviceType/add", //添加设备类型
    deviceTypeEdit: "/deviceType/edit", //编辑设备类型
    userGetDeviceType: "/deviceType/list", //获取设备类型

    allGetTeamList: '/com/getList', //获取全部团队信息
    getFirstTeamList: "/com/getOne", //获取一级正常团队信息
    addTeam: "/com/add", //新增团队信息
    editTeam: "/com/edit", //编辑团队信息

    megTextList: "/megText/list", //文本列表
    megTextAdd: "/megText/add", //文本添加
    megTextEdit: "/megText/edit", //文本添加
    megTextDel: "/megText/delete", //文本添加
}

deviceList = urlsplit(deviceList, baseUrl.DEVICE_PROT)
export default deviceList;