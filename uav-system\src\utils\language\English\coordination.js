const coordination = {
    allBtn: {
        search: 'Search',
        newSet: 'New set',
        sure: "Determining network",
        set: 'Set',
        del: 'Delete',
        sure: 'Sure',
        cancel: 'Cancel',
        toGroup: 'EnterGroup',
        editGroup: 'EditGroup'
    },
    tipInfo: {
        placeholder: 'Please enter the device name / address',
        placeholder1: 'Please enter a group name',
        errorMessage: 'Network name is empty!',
        errorMessage1: 'No device selected!',
        successMessage: 'Network editing success!',
        successMessage1: 'Networking created!',
        placeholder2: 'The deletion cannot be recovered. Are you sure you want to erase?',
        tip: 'Tips'
    },
    group: {
        name: 'Group name:',
        select: 'Selected ',
        deviceGroup: " Equipment",
        normal: 'Normal',
        delete: 'Delete',
        on_line: 'Online',
        out_line: 'Offline',
        noconnect: 'No connection',
        connect: 'Already connected'
    },
    table: {
        name: 'Name',
        time: 'Creation time',
        num: 'Equipment Qty',
        online: 'Online device',
        state: "State",
        option: 'Operation',
        choose: 'Choice',
        onState: 'Online status',
        photo: 'Equipment picture',
        name1: 'Equipment name',
        type: 'Equipment type',
        sn_id: 'Equipment No',
        address: 'Device address',

    },
    device: {
        title: 'Equipment list',
        device: ' Equipment',
        toTask: 'To task',
        previous: 'PgUp',
        next: 'PgDn',
    },
    video: {
        videoLoading: 'Video loading',
        uavVideo: 'Drone video',
        inVideo: 'Cabin monitoring',
        outVideo: 'Outside monitoring',
        errorVideo: 'Video loading failed',
        reconnect: 'Reconnect'
    },
    deviceList: {
        title: 'List of devices in the group',
        bind: 'Bound task',
        unbind: 'Unbound task',
        noData: 'No binding task',
        waitBind: 'Tasks selected',
        type: 'Type:',
        rename: 'Rename',
        del: 'Delete',
        unbind: "Unbind",
        binds: 'Bind',
        edit: 'Edit',
        nodata1: 'No task selected',
        taskRename: 'Task Rename',
        taskName: 'Task Name',
        nameMessage: 'Please enter the task name',
        save: 'Save',
        cancel: "Cancel",
        bindtip: 'Do you want to unbind the task?',
        tips: 'Tips',
        sure: 'Confirm',
        cancel1: 'Cancel',
        messageInfo: 'Task binding has been released!',
        messageInfo1: 'The task binding has been cancelled!',
        delMessage: 'This operation cannot be recovered. Are you sure you want to delete the task information?',
        delSuccess: 'Deletion succeeded',
        delInfo: 'Deletion cancelled',
        renameSuccess: "Rename succeeded!",
        renameInfo: 'Rename cancelled.'
    },
    route: {
        placeholder: 'The waypoint cannot exceed the perimeters!',
        placeholder1: 'The route is beyond the perimeters!',
        placeholder2: 'The effective distance between adjacent waypoints cannot exceed 2 kilometers!',
        placeholder3: 'Are you sure you would like to clear all information of this route?',
        messageInfo: 'Cleared successfully!',
        messageInfo1: 'Cancel cleanup',
        tip: 'Tips',
        saveBtn: 'Confirm',
        cancelBtn: 'Cancel',
    },
    routeLoading: "Waypoint loading",
    placeholder10: 'Withdrawn to original state',
}
export default coordination