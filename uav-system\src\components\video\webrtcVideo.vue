<!-- webrtc流播放 -->
<template>
  <video-layout
    :isPlay.sync="isPlay"
    :title="title"
    :showFooter="showFooter"
    :autoPlay="autoPlay"
  >
    <template v-slot:video>
      <video :id="videoId" ref="webrtcVideo"></video>

      <!-- 封面图 -->
      <!-- <div class="cover-picture" v-if="fengmiantu">
        <img :src="coverPicture" alt="">
      </div> -->
    </template>

    <!-- 错误回显 -->
    <template>
        <div>

        </div>
      </template>
  </video-layout>
</template>

<script>
import JSWebrtc from "@/assets/js/jswebrtc.min.js";
import videoLayout from "./videoLayout.vue";
export default {
  name: "webrtc-video",
  components: {
    videoLayout,
  },
  props: {
    url: String, // 视频流url
    title: String, // 标题
    autoPlay: Boolean, // 自动播放
    // 显示底部
    showFooter: {
      type: <PERSON>olean,
      default: true,
    },
    // video标签id
    videoId: {
      type: String,
      default: "webrtc-video",
    },
  },
  data() {
    return {
      isPlay: false, // 是否播放
      coverPicture: null, // 封面图
      player: null,
    };
  },
  watch: {
    url: function (val) {
      this.$nextTick(() => {
        this.initVideo();
      });
    },
  },
  created() {
    this.$nextTick(() => {
      this.initVideo();
    });
  },
  beforeDestroy() {
    // 播放器存在清除播放器
    if (this.player) {
      this.player.destroy();
    }
  },
  methods: {
    initVideo: function () {
      // 如果播放器存在，则销毁
      if (this.player) {
        this.player.destroy();
        this.player = null;
      }
      let videoDemo = this.$refs.webrtcVideo;
      this.player = new JSWebrtc.Player("https://ab.walkera.cn/ms/index/api/webrtc?app=live&stream=abc&type=play", {
        video: videoDemo,
        autoPlay: this.autoPlay,
        onPlay: () => {
          videoDom.addEventListener("canplay", function (e) {
            videoDom.play();
          });
        },
        onPause: () => {
          console.log("被暂停了----------->");
        },
      });
    },
    playVideo: function (state) {
      this.isPlay = state;
      if (state) {
        this.coverPicture = null;
        this.$nextTick(() => {
          this.$refs.video.play();
        });
      } else {
        this.$refs.video.pause();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.cover-picture{
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  width: 100%;
  height: 100%;
  img{
    width: 100%;
  }  
}
</style>