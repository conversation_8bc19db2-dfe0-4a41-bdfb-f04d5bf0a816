<template>
  <div class="buttonPopup">
    <el-popover
      placement="right-start"
      width="200"
      :title="popupLangauge.title"
      trigger="hover"
      popper-class="popup-online-user"
      v-if="deviceItem.is_pull_on"
    >
      <el-button
        slot="reference"
        :class="deviceItem.is_pull_on ? 'secondDiv' : ''"
        @click.stop=""
        >{{ language.personalStatus }}</el-button
      >
      <div class="list-data" v-for="item in pull_user_info" :key="item.u_id">
        <div class="list-item">
          <div class="label">{{ popupLangauge.nick }}</div>
          <div class="value">{{ item.nick }}</div>
        </div>
        <div class="list-item">
          <div class="label">{{ popupLangauge.account }}</div>
          <div class="value">{{ item.phone ? item.phone : item.email }}</div>
        </div>
      </div>
    </el-popover>
    <el-button v-else>{{ language.personalStatusNo }}</el-button>
  </div>
</template>
<script>
export default {
  props: {
    deviceItem: {
      type: Object,
      defalut: () => {
        return {};
      },
    },
  },
  data() {
    return {
      visible: true,
    };
  },
  computed: {
    language() {
      return this.$languagePackage.equipment.equipInfo;
    },
    popupLangauge() {
      return this.language.popupLabel;
    },
    pull_user_info() {
      let arr = [];
      if (
        !(
          this.deviceItem.pull_user_info &&
          this.deviceItem.pull_user_info.length
        )
      ) {
        return arr;
      }
      let dataArr = JSON.parse(JSON.stringify(this.deviceItem.pull_user_info));
      for (let index = 0; index < dataArr.length; index++) {
        let a = arr.findIndex((item) => {
          return item.u_id == dataArr[index].u_id;
        });
        if (a === -1) {
          arr.push(dataArr[index]);
        }
      }
      return arr;
    },
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.buttonPopup {
  .el-button {
    padding: 6px 10px;
    font-size: 12px;
    display: block;
    border: none;
    height: auto;
    line-height: 16px;
    background-color: #35373b;
    color: white;
    &.secondDiv {
      background-color: rgb(53, 53, 204);
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .buttonPopup {
    .el-button {
      padding: @zoomIndex * 6px @zoomIndex * 10px !important;
      font-size: @zoomIndex * 12px !important;
      line-height: @zoomIndex * 16px !important;
    }
  }
}
</style>
<style lang="less">
.popup-online-user {
  min-width: 200px !important;
  width: auto !important;
  .el-popover__title {
    margin-bottom: 8px;
  }
  .list-data {
    margin-bottom: 8px;
    .list-item {
      display: flex;
      align-items: center;
    }
  }
}
</style>