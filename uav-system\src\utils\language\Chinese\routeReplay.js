/**
 * 飞行回放
 */
const routeReplay = {
    mode: "飞行模式",
    modeTitle: "飞行数据变化图",
    basic: {
        mission_type_label: "任务类型",
        sn_id: '设备编号',
        uav_id: "无人机编号",
        start_time: "起飞时间",
        end_time: "着陆时间"
    },
    point: {
        lng: "经度",
        lat: "纬度",
        hs: "速度",
        height: "高度",
        yaw: "航向角",
        roll: "横滚角",
        pitch: "俯仰角",
        distance: "距离"
    },
    chart: {
        altitude: "高度",
        horizontalVelocity: "水平速度",
        verticalVelocity: "垂直速度"
    },
    unit: {
        m: "米"
    },

    message: {
        getDataError: "数据获取失败",
        dataParsingFailure: "数据解析失败",
        taskPlayConfirm: {
            title: "提示",
            content: "此操作将重新播放",
            confirmText: "确定",
            camceLText: "取消"
        },
        refresh: {
            title: "提示",
            content: "此操作将重置轨迹信息，请确定？",
            confirmText: "确定",
            camceLText: "取消"
        }
    }
}

export default routeReplay;