<template>
  <div class="silderInput">
    <el-slider
      v-model="numParam"
      :show-tooltip="false"
      :step="1"
      :max="max"
      :min="0"
      v-show="numCode"
      @input="changeValue1"
      @change="changeValue2"
      class="sliderValue"
    >
    </el-slider>
    <el-input
      v-model="numParam1"
      @input="valueFormat(numParam1)"
      @change="changeValue"
    >
      <template slot="suffix">{{ suffixCode }}</template>
    </el-input>
  </div>
</template>
<script>
export default {
  name: "silderInput",
  props: {
    suffixCode: {
      type: String,
      default: "",
    },
    numValue: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 0,
    },
    originCode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      numParam1: 0,
      numParam: 0,
      numCode: true,
    };
  },
  mounted() {
    this.numParam = this.numValue;
    this.numParam1 = this.numValue;
    setTimeout(() => {
      this.numParam = this.numValue;
      this.numParam1 = this.numValue;
    }, 100);
  },
  methods: {
    valueFormat(val) {
      let temp = val.toString();
      temp = temp.replace(/[^\d]/g, ""); //清除"数字"和"."以外的字符
      temp = parseInt(temp);
      if (temp > this.max) {
        temp = this.max;
      }
      if (!temp) {
        temp = 0;
      }
      this.numParam1 = temp;
    },
    changeValue() {
      this.numParam = this.numParam1;
      this.$emit("mouseupEvent", "");
    },
    changeValue1() {
      this.numParam1 = this.numParam;
      //   console.log(this.numParam)
      if (this.originCode) {
        this.$emit("update:numValue", this.numParam);
      }
    },
    changeValue2() {
      this.$emit("mouseupEvent", "");
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .silderInput {
    .el-input {
      border-radius: @zoomIndex * 8px !important;
    }
  }
}
.silderInput {
  width: 100%;
  display: flex;
  .sliderValue {
    width: 80%;
    margin-left: 2%;
  }
  .el-input {
    width: 17%;
    margin-left: 2%;
    border-radius: 8px;
  }
}
</style>
<style lang="less">
.silderInput {
  .sliderValue {
    .el-slider__runway {
      height: 2px !important;
      margin: 0 !important;
      margin-top: 15px !important;
      .el-slider__bar {
        height: 2px !important;
      }
    }
    .el-slider__button-wrapper {
      width: 10px !important;
      height: 10px !important;
      top: -4px;
      .el-slider__button {
        width: 8px !important;
        height: 8px !important;
      }
    }
  }
  .el-input {
    font-size: 14px !important;
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      padding: 0 5px;
    }
    .el-input__suffix {
      top: 12px;
    }
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .silderInput {
    .sliderValue {
      .el-slider__runway {
        height: @zoomIndex * 2px !important;
        margin-top: @zoomIndex * 15px !important;
        .el-slider__bar {
          height: @zoomIndex * 2px !important;
        }
      }
      .el-slider__button-wrapper {
        width: @zoomIndex * 10px !important;
        height: @zoomIndex * 10px !important;
        // top: @zoomIndex * -4px !important;
        .el-slider__button {
          width: @zoomIndex * 8px !important;
          height: @zoomIndex * 8px !important;
        }
      }
      .el-slider__button-wrapper .el-tooltip,
      .el-slider__button-wrapper::after {
        vertical-align: top;
      }
    }
    .el-input {
      font-size: @zoomIndex * 14px !important;
      .el-input__inner {
        height: @zoomIndex * 32px !important;
        line-height: @zoomIndex * 32px !important;
        padding: 0 @zoomIndex * 5px !important;
      }
      .el-input__suffix {
        top: @zoomIndex * 12px !important;
      }
    }
  }
}
</style>