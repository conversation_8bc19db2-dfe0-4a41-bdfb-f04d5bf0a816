<template>
  <div class="planList">
    <div class="header-title" ref="title">
      <div class="title">
        <div class="title-item">
          <el-button
            icon="el-icon-arrow-left"
            :class="iconCode ? 'active' : ''"
            v-if="code == 4 ? true : false"
            @click="returnEvent"
          ></el-button>
          {{ title }}
        </div>
        <div class="title-item1" :class="$loadingEnUI ? 'title-item1-en' : ''">
          <el-button
            v-if="code == 4 ? true : false"
            @click="addplan"
            :class="addplanCode ? 'checked' : ''"
            :style="$loadingEnUI ? 'padding:10px' : ''"
            >{{ routeLanguage.routeLine.addtask }}</el-button
          >
        </div>
        <!-- <el-row>
          <el-col :span="16" class="title-item">
            <el-button
              icon="el-icon-arrow-left"
              :class="iconCode ? 'active' : ''"
              v-if="code == 4 ? true : false"
              @click="returnEvent"
            ></el-button>
            {{ title }}
          </el-col>
          <el-col :span="8">
            <el-button
              v-if="code == 4 ? true : false"
              @click="addplan"
              :class="addplanCode ? 'checked' : ''"
              :style="routeLanguage.language == 'en-US' ? 'padding:10px' : ''"
              >{{ routeLanguage.routeLine.addtask }}</el-button
            >
          </el-col>
        </el-row> -->
      </div>
    </div>
    <div ref="inputDiv">
      <el-input
        class="searchFence"
        :placeholder="
          code == 0
            ? routeLanguage.fence.placeholder
            : routeLanguage.routeLine.placeholder2
        "
        v-model="searchValue"
        @keydown.enter.native="searchEvent"
      >
        <el-button slot="append" @click="searchEvent"
          ><el-image :src="searchCode ? searchImg_1 : searchImg"></el-image
        ></el-button>
      </el-input>
    </div>
    <div v-if="code == 0 ? true : false" ref="addFButton" class="addFButton">
      <el-button
        class="addWord"
        @click="addWord"
        :class="addwordCode ? 'checked' : ''"
      >
        {{ routeLanguage.fence.addFence }}
      </el-button>
    </div>
    <route-type
      v-if="code == 4 ? true : false"
      ref="routeType"
      :typeCode="checkCode"
      @clickType="clickTypeEvent"
    ></route-type>
    <div class="content-list" v-if="code == 0 ? true : false">
      <div
        class="fenceContent"
        v-for="item in fenceList"
        :key="item.f_id"
        @click="todetailList(item)"
      >
        <div class="content-item-1">{{ routeLanguage.fence.do }}</div>
        <div class="content-item-2">
          <div>{{ item.title }}</div>
          <div v-if="routeLanguage.language == 'zh-CN'" class="item-2-desc">
            {{ routeLanguage.fence.common }}{{ item.mission_size
            }}{{ routeLanguage.fence.strip }}
          </div>
          <div v-else class="item-2-desc">
            {{ item.mission_size }}{{ routeLanguage.fence.strip }}
          </div>
          <div v-if="item.bind_sn_arr" class="item-2-desc">
            {{ routeLanguage.fence.equipTitle }}{{ formatSN(item.bind_sn_arr) }}
          </div>
        </div>
        <el-popover
          placement="right"
          trigger="hover"
          class="content-item-3"
          :open-delay="200"
          popper-class="setPopover-item"
          @show="showbtn(item)"
          @hide="clickSetCode = ''"
        >
          <el-button type="text" class="btn1" @click="editFence(item)">{{
            routeLanguage.fence.edit
          }}</el-button>
          <el-button type="text" @click="delFence(item)">{{
            routeLanguage.fence.del
          }}</el-button>
          <el-button slot="reference" @click.stop="">
            <el-image
              v-show="clickSetCode == item.f_id"
              :src="setImg_1"
              fit="contain"
            ></el-image>
            <el-image
              v-show="clickSetCode != item.f_id"
              :src="setImg"
              fit="contain"
            ></el-image>
          </el-button>
        </el-popover>
      </div>
    </div>
    <div class="content-list" v-if="code == 4 ? true : false">
      <div v-if="missionList.length == 0 ? true : false" class="entryDiv">
        {{ routeLanguage.routeLine.noData }}
      </div>
      <div
        class="content-list-item"
        v-for="item in missionList"
        :key="item.m_id"
        @click="chooseRoute(item)"
        :class="{
          active: RouteCheck == item.m_id,
          task_time: item.is_timed_task,
        }"
      >
        <el-row>
          <el-col :span="18">
            <div>{{ item.title }}</div>
            <div class="wordType">
              {{ routeLanguage.routeLine.taskType }}：{{
                item.type | getTypeTitle(typeList)
              }}
            </div>
          </el-col>
          <el-col :span="6" class="el-col-end">
            <el-popover
              placement="right-start"
              trigger="hover"
              class="content-item-3"
              :open-delay="200"
              popper-class="setPopover-item-1"
              @show="showbtnR(item)"
              @hide="clickSetCode = ''"
            >
              <el-button type="text" class="btn1" @click="recoverName(item)">{{
                routeLanguage.routeLine.rename
              }}</el-button>
              <el-button type="text" @click="delRoute(item)">{{
                routeLanguage.routeLine.del
              }}</el-button>
              <el-button type="text" @click="exportRoute(item)">{{
                routeLanguage.routeLine.export
              }}</el-button>
              <el-button slot="reference" @click.stop="">
                <el-image
                  v-show="clickSetCode == item.m_id"
                  :src="setImg_1"
                  fit="contain"
                ></el-image>
                <el-image
                  v-show="clickSetCode != item.m_id"
                  :src="setImg"
                  fit="contain"
                ></el-image>
              </el-button>
            </el-popover>
          </el-col>
        </el-row>
        <div
          class="operateBar"
          :class="item.is_timed_task ? 'operateBar_task' : ''"
        >
          <el-button
            @click="toplan(item)"
            class="planDiv"
            :class="planCode == item.m_id ? 'active' : ''"
          >
            <el-image
              :src="planCode == item.m_id ? planImg_1 : planImg"
              fit="contain"
            ></el-image>
            {{
              item.is_timed_task
                ? routeLanguage.routeLine.issued
                : routeLanguage.routeLine.performTask
            }}
          </el-button>
          <el-button
            @click.stop="fitplan(item)"
            :class="fitCode == item.m_id ? 'active' : ''"
          >
            <el-image
              :src="fitCode == item.m_id ? fitImg_1 : fitImg"
              fit="contain"
            ></el-image
            >{{ routeLanguage.routeLine.set }}
          </el-button>
        </div>
      </div>
    </div>
    <el-pagination
      :class="className"
      layout="prev, pager, next"
      :pager-count="5"
      :total="code == 4 ? total_page_1 : total_page"
      :current-page="page"
      @current-change="changePage"
      @prev-click="prevClick"
      @next-click="nextClick"
      ref="paginations"
    >
    </el-pagination>
    <dialog-type
      ref="typeDialog"
      :dialogLanguage="routeLanguage.dialogType"
    ></dialog-type>
    <el-dialog
      :title="routeLanguage.routeLine.taskRename"
      :visible.sync="renameCode"
      :close-on-click-modal="false"
      :show-close="false"
      :modal-append-to-body="false"
      width="35%"
      center
      class="renameDialog"
    >
      <el-divider class="dividerDiv"></el-divider>
      <el-form
        :model="planFrom"
        ref="planFrom"
        label-position="right"
        label-width="20%"
      >
        <el-form-item
          :label="routeLanguage.routeLine.taskName"
          prop="name"
          :rules="[
            {
              required: true,
              message: routeLanguage.routeLine.placeholder,
              trigger: 'blur',
            },
          ]"
        >
          <el-input v-model="planFrom.name" style="width: 80%; float: left">
          </el-input>
        </el-form-item>
      </el-form>
      <div>
        <el-button class="savebtn" @click="submitRename">{{
          routeLanguage.routeLine.save
        }}</el-button>
        <el-button class="closeBtn" @click="closeEvent">{{
          routeLanguage.routeLine.cancel
        }}</el-button>
      </div>
    </el-dialog>
    <div id="dragContent"></div>
  </div>
</template>
<script>
import requestHttp from "../../../utils/api";
import dialogType from "./dialogType";
import routeType from "@/components/routeType";
export default {
  name: "planeList",
  components: {
    dialogType,
    routeType,
  },
  props: {
    planCode: {
      type: [Number, String],
      default: "",
    },
    routeLanguage: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      addplanCode: false,
      searchValue: "",
      searchCode: false,
      addwordCode: false,
      clickSetCode: 0,
      iconCode: false,
      renameCode: false,
      item: "",
      fitCode: "",
      checkCode: 20,
      total_page: 0,
      total_page_1: 0,
      page: 1,
      RouteCheck: "",
      title: "",
      fenceList: "",
      missionList: "",
      planFrom: {
        name: "",
      },
      planItem: "",
      originalX: 0,
      originalY: 0,
      dragItem: "",
      className: "",
      tsPage: 1,
      searchImg: require("../../../assets/img/routeplan/search.png"),
      searchImg_1: require("../../../assets/img/routeplan/search_1.png"),
      setImg: require("../../../assets/img/routeplan/set.png"),
      setImg_1: require("../../../assets/img/routeplan/set_1.png"),
      fitImg: require("../../../assets/img/routeplan/fit.png"),
      fitImg_1: require("../../../assets/img/routeplan/fit_1.png"),
      planImg: require("../../../assets/img/routeplan/plan.png"),
      planImg_1: require("../../../assets/img/routeplan/plan_1.png"),
    };
  },
  async mounted() {
    this.title = this.routeLanguage.fence.title;

    // await this.getFenceList()
  },
  methods: {
    prevClick(e) {
      this.className = "prev-class";
      setTimeout(() => {
        this.className = "";
      }, 200);
    },
    nextClick(e) {
      this.className = "next-class";
      setTimeout(() => {
        this.className = "";
      }, 200);
    },
    // getHeight() {
    //   let a = this.$refs.title.offsetHeight;
    //   let a1 = this.$refs.inputDiv.offsetHeight;
    //   let a2 = this.$refs.addFButton ? this.$refs.addFButton.offsetHeight : 0;
    //   let a3 = this.$refs.routeType ? this.$refs.routeType.$el.offsetHeight : 0;
    //   let a4 = this.$refs.paginations.$el.offsetHeight;
    //   this.height = "calc(100% - " + (a + a1 + a2 + a3 + a4 + 10) + "px)";
    //   console.log(a, a1, a2, a3, a4, this.height);
    // },
    //获取围栏信息
    async getFenceList() {
      let data = {
        page: this.page - 1,
        size: 10,
      };
      if (this.searchValue) {
        data.search = this.searchValue;
      }
      data.pmd = data.page.toString();
      await requestHttp("fenceList", data).then((res) => {
        this.total_page = res.data.total_page ? res.data.total_page * 10 : 0;
        this.fenceList = res.data.list ? res.data.list : [];
        this.$store.commit("setFence", this.fenceList);
      });
    },
    //列表查询
    async searchEvent() {
      this.searchCode = true;
      if (this.code == 0) {
        if (this.searchValue) {
          this.page = 1;
          await this.getFenceList();
          if (this.fenceList.length == 0) {
            this.$message.info({
              message: this.routeLanguage.fence.noSeachMessage,
              customClass: "message-info-tip",
            });
          }
        } else {
          this.$message.info({
            message: this.routeLanguage.fence.entryReturn,
            customClass: "message-info-tip",
          });
          this.page = 1;
          await this.getFenceList();
        }
      } else {
        if (this.searchValue) {
          this.page = 1;
          await this.getMissionList();
          if (this.missionList.length == 0) {
            this.$message.info({
              message: this.routeLanguage.fence.noSeachMessage,
              customClass: "message-info-tip",
            });
          }
        } else {
          this.$message.info({
            message: this.routeLanguage.fence.entryReturn,
            customClass: "message-info-tip",
          });
          this.page = 1;
          await this.getMissionList();
        }
      }

      setTimeout(() => {
        this.searchCode = false;
      }, 200);
    },
    //点击新建围栏
    addWord() {
      this.addwordCode = true;
      setTimeout(() => {
        this.$store.commit("changeCode", 2);
        this.addwordCode = false;
      }, 200);
    },
    //围栏：获取焦点切换按钮颜色
    showbtn(item) {
      setTimeout(() => {
        this.clickSetCode = item.f_id;
      }, 100);
    },
    //进入具体的列表
    todetailList(item) {
      this.tsPage = this.page;
      this.page = 1;
      this.$store.commit("changeCode", 4);
      this.searchValue = "";
      this.title = item.title;
      this.item = item;
      this.checkCode = 20;
      this.getMissionList();
      this.$store.commit("fenceItemId", item.f_id);
      // setTimeout(() => {
      //   this.getHeight();
      // }, 200);
    },
    //获取任务列表
    getMissionList() {
      let data = {
        f_id: this.item.f_id,
        type: this.checkCode,
        page: this.page - 1,
        size: 10,
      };
      if (this.searchValue) {
        data.search = this.searchValue;
      }
      data.pmd = data.page.toString() + data.f_id + data.type.toString();
      requestHttp("missionList", data).then((res) => {
        if (res.data.list) {
          this.total_page_1 = res.data.total_page * 10;
          this.missionList = res.data.list;
        } else {
          this.total_page_1 = 0;
          this.missionList = [];
        }
      });
    },
    //返回首页列表
    returnEvent() {
      this.iconCode = true;
      this.title = this.routeLanguage.fence.title;
      setTimeout(() => {
        this.$store.commit("changeCode", 0);
        this.$store.commit("routeItem", "");
        this.$store.commit("fenceItemId", "");
        this.searchValue = "";
        this.page = this.tsPage;
        this.getFenceList();
        this.iconCode = false;
      }, 200);
      // setTimeout(() => {
      //   this.getHeight();
      // }, 250);
    },
    //点击切换类型
    clickTypeEvent(item) {
      this.checkCode = item.value;
      this.getMissionList();
      this.RouteCheck = "";
      this.$store.commit("routeItem", "");
    },
    //设置按钮事件
    fitplan(item) {
      this.fitCode = item.m_id;
      setTimeout(() => {
        if (item.type == 20) {
          this.$store.commit("changeCode", 1);
        } else if (item.type == 50) {
          this.$store.commit("changeCode", 3);
        }
        item.typeCode = 1;
        for (let index = 0; index < this.typeList.length; index++) {
          if (this.typeList[index].value == item.type) {
            this.$store.commit("checkType", this.typeList[index]);
            break;
          }
        }
        this.$store.commit("routeItem", "");
        this.$store.commit("routeItem", item);
        this.fitCode = "";
      }, 200);
    },
    //点击执行任务
    toplan(item) {
      this.$emit("openDeviceEvent", item);
    },
    //删除围栏
    delFence(e) {
      this.$confirm(
        this.routeLanguage.fence.delMessage,
        this.routeLanguage.fence.delTip,
        {
          confirmButtonText: this.routeLanguage.dialogEquip.save,
          cancelButtonText: this.routeLanguage.dialogEquip.cancel,
          type: "warning",
          customClass: "messageTip",
        }
      )
        .then(() => {
          let data = {
            f_id: e.f_id,
            state: 30,
            title: e.title,
            type: e.type,
            lat_int: e.lat_int,
            lon_int: e.lon_int,
            height_limit: e.height_limit,
            point_list: JSON.stringify(e.point_list),
          };
          data.pmd = data.f_id + data.state.toString();
          requestHttp("fenceEdit", data).then((res) => {
            this.$message.success({
              message: this.routeLanguage.fence.delSuccess,
              customClass: "message-info-tip",
            });
            e.code = 0;
            this.$store.commit("operateFItem", e);
            this.getFenceList();
          });
        })
        .catch((e) => {
          this.$message.info({
            message: this.routeLanguage.fence.cancelDel,
            customClass: "message-info-tip",
          });
        });
    },
    //编辑围栏
    editFence(e) {
      this.clickSetCode = "";
      // this.addwordCode = true;
      this.$store.commit("changeCode", 2);
      e.code = 1;
      this.$store.commit("operateFItem", e);
    },
    //点击新增任务航线
    addplan() {
      this.addplanCode = true;
      this.$store.commit("routeItem", "");
      this.$refs.typeDialog.openDialog();
    },
    //点击航线
    chooseRoute(item) {
      this.RouteCheck = item.m_id;
      this.$store.commit("routeItem", item);
    },
    //切换按钮颜色
    showbtnR(item) {
      setTimeout(() => {
        this.clickSetCode = item.m_id;
      }, 100);
    },
    //重命名
    recoverName(item) {
      this.planFrom.name = item.title;
      this.planItem = item;
      this.renameCode = true;
    },
    //删除
    delRoute(item) {
      this.$confirm(
        this.routeLanguage.routeLine.delMessage,
        this.routeLanguage.routeLine.delTip,
        {
          confirmButtonText: this.routeLanguage.dialogEquip.save,
          cancelButtonText: this.routeLanguage.dialogType.cancel,
          type: "warning",
          customClass: "messageTip",
        }
      )
        .then(() => {
          let data = {
            m_id: item.m_id,
            f_id: item.f_id,
            state: 30,
            type: item.type,
            title: item.title,
            auto_speed: item.auto_speed,
            max_speed: item.max_speed,
            default_height: item.default_height,
            return_height: item.return_height,
            action_completed: item.action_completed,
            point_json: JSON.stringify(item.point_list),
          };
          data.pmd = data.m_id.toString() + data.state.toString();
          requestHttp("missionEdit", data).then((res) => {
            this.$message.success({
              message: this.routeLanguage.routeLine.delSuccess,
              customClass: "message-info-tip",
            });
            this.getMissionList();
            this.$store.commit("routeItem", "");
          });
        })
        .catch(() => {
          this.$message.info({
            message: this.routeLanguage.routeLine.cancelDel,
            customClass: "message-info-tip",
          });
        });
    },
    //取消重命名
    closeEvent() {
      this.renameCode = false;
      this.planFrom.name = "";
      this.planItem = "";
      this.$message.info({
        message: this.routeLanguage.routeLine.cancelRename,
        customClass: "message-info-tip",
      });
    },
    //提交重命名
    submitRename() {
      let data = {
        m_id: this.planItem.m_id,
        f_id: this.planItem.f_id,
        state: this.planItem.state,
        type: this.planItem.type,
        title: this.planFrom.name,
        auto_speed: this.planItem.auto_speed,
        max_speed: this.planItem.max_speed,
        default_height: this.planItem.default_height,
        return_height: this.planItem.return_height,
        action_completed: this.planItem.action_completed,
        height_type: this.planItem.height_type,
      };
      for (let index = 0; index < this.planItem.point_list.length; index++) {
        this.planItem.point_list[index].state = 10;
      }
      data.point_json = JSON.stringify(this.planItem.point_list);
      data.pmd =
        data.f_id.toString() +
        data.title +
        data.point_json +
        data.type.toString() +
        data.max_speed.toString() +
        data.default_height.toString() +
        data.return_height.toString() +
        data.action_completed.toString() +
        data.m_id.toString() +
        data.state.toString();
      requestHttp("missionEdit", data).then((res) => {
        this.$message.success({
          message: this.routeLanguage.routeLine.renameSuccess,
          customClass: "message-info-tip",
        });
        this.renameCode = false;
        this.planFrom.name = "";
        this.planItem = "";
        this.getMissionList();
      });
    },
    //切换页数
    changePage(e) {
      this.page = e;
      if (this.code == 0) {
        this.getFenceList();
      } else {
        this.getMissionList();
        this.RouteCheck = "";
        this.$store.commit("routeItem", "");
      }
    },
    exportRoute(item) {
      this.chooseRoute(item);
      this.$emit("exportRouteEvent", item);
    },
    formatSN(bind_sn_arr) {
      let arr = JSON.parse(bind_sn_arr);
      let str = "";
      for (let index = 0; index < arr.length; index++) {
        str = str ? str + "、" + arr[index] : arr[index];
      }
      return str;
    },
    //鼠标按下时触发
    // mouseDownEvent(item,e){
    //   this.dragItem=item
    //   e = e || window.event;
    //   this.originalX = e.clientX;
    //   this.originalY = e.clientY;
    //   document.onmousemove = this.dragRouteDiv;
    //   document.onmouseup=this.mouseUpEvent
    // },
    // dragRouteDiv(e){
    //   let divId = document.getElementById("dragRouteDiv");
    //   let divId1=document.getElementById("dragContent")
    //   divId1=divId
    //   e = e || window.event;
    //   // calculate the new cursor position:
    //   let x = this.originalX - e.clientX;
    //   let y = this.originalY - e.clientY;
    //   // this.originalX = e.clientX;
    //   // this.originalY = e.clientY;
    //   // set the element's new position:
    //   let bottom = parseInt(window.getComputedStyle(divId1).bottom) + y;
    //   let right = parseInt(window.getComputedStyle(divId1).right) + x;
    //   let top = parseInt(window.getComputedStyle(divId1).top) - y;
    //   let left = parseInt(window.getComputedStyle(divId1).left) - x;
    //   if (bottom > 0 && top > 0) {
    //     divId1.style.bottom = bottom + "px";
    //   }
    //   if (right > 0 && left > 365) {
    //     divId1.style.right = right + "px";
    //   }
    //   console.log(divId1)
    // },
    // mouseUpEvent(){
    //   this.dragItem=''
    //   document.onmouseup = null;
    //   document.onmousemove = null;
    // }
  },
  computed: {
    code() {
      return this.$store.state.route.code;
    },
    checkType() {
      return this.$store.state.route.checkType;
    },
    typeList() {
      return this.$store.state.route.typeList;
    },
  },
  watch: {
    checkType(val) {
      if (val.value == 20 || val.value == 40) {
        this.$store.commit("changeCode", 1);
      }
      if (val.value == 50) {
        this.$store.commit("changeCode", 3);
      }
      this.addplanCode = false;
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .planList {
    .header-title {
      padding-top: @zoomIndex * 30px !important;
      .title {
        .title-item {
          font-size: @zoomIndex * 18px !important;
          .el-button {
            font-size: @zoomIndex * 20px !important;
          }
        }
        .title-item1 {
          .el-button {
            margin: @zoomIndex * 10px @zoomIndex * 5px !important;
            padding: @zoomIndex * 10px @zoomIndex * 20px !important;
            font-size: @zoomIndex * 14px !important;
            letter-spacing: @zoomIndex * 2px !important;
          }
          &.title-item1-en {
            .el-button {
              letter-spacing: 0 !important;
            }
          }
        }
      }
    }

    .searchFence {
      .el-button {
        // padding: @zoomIndex * 12px 0 !important;
        .el-image {
          width: @zoomIndex * 14px !important;
        }
      }
    }
    .addFButton {
      .addWord {
        border-radius: @zoomIndex * 8px !important;
        letter-spacing: @zoomIndex * 2px !important;
        font-size: @zoomIndex * 14px !important;
      }
    }
    .routeType {
      .el-button {
        .typeTitle {
          margin-top: @zoomIndex * 7px !important;
          font-size: @zoomIndex * 12px !important;
          letter-spacing: @zoomIndex * 2px !important;
        }
      }
    }
    .content-list {
      padding: @zoomIndex * 10px 3% !important;
      .fenceContent {
        border-radius: @zoomIndex * 8px !important;
        padding: @zoomIndex * 6px 3% !important;
        .content-item-1 {
          font-size: @zoomIndex * 40px !important;
          border-radius: @zoomIndex * 8px !important;
        }
        .content-item-2 {
          font-size: @zoomIndex * 14px !important;
          .item-2-desc {
            font-size: @zoomIndex * 12px !important;
            margin-top: @zoomIndex * 2px !important;
          }
        }
      }
      .entryDiv {
        border-radius: @zoomIndex * 8px !important;
        font-size: @zoomIndex * 16px !important;
        padding: @zoomIndex * 12px 3% !important;
        letter-spacing: @zoomIndex * 3px !important;
      }
      .content-list-item {
        border-radius: @zoomIndex * 8px !important;
        padding: @zoomIndex * 6px 3% !important;
        font-size: @zoomIndex * 16px !important;
        .el-row {
          .el-col {
            .wordType {
              font-size: @zoomIndex * 13px !important;
            }
          }
        }
        .operateBar {
          border-radius: @zoomIndex * 8px !important;
          .el-button {
            font-size: @zoomIndex * 14px !important;
            letter-spacing: @zoomIndex * 2px !important;
          }
        }
      }
    }
    .el-pagination {
      .el-button {
        margin: @zoomIndex * 2px !important;
      }
    }
  }
  .renameDialog {
    .dividerDiv {
      margin: @zoomIndex * 5px 0 !important;
      height: @zoomIndex * 2px !important;
    }
    .el-form {
      margin-top: @zoomIndex * 50px !important;
    }
    .savebtn {
      border-radius: @zoomIndex * 8px !important;
      width: @zoomIndex * 130px !important;
    }
    .closeBtn {
      border-radius: @zoomIndex * 8px !important;
      width: @zoomIndex * 130px !important;
    }
  }
}
.planList {
  width: 100%;
  height: 99%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .header-title {
    padding-top: 30px;
    .title {
      width: 95%;
      height: auto;
      // margin-top: 8%;

      text-align: left;
      padding-left: 5%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title-item {
        margin: 4.5% 0;
        font-size: 18px;
        font-weight: 520;
        // letter-spacing: 2px;
        .el-button {
          padding: 0;
          font-size: 20px;
          margin: 0;
          .el-icon-arrow-left {
            font-weight: 600;
          }
        }
      }
      .title-item1 {
        .el-button {
          margin: 10px 5px;
          padding: 10px 20px;
          font-size: 14px;
          letter-spacing: 2px;
        }
        &.title-item1-en {
          .el-button {
            letter-spacing: 0;
          }
        }
      }
    }
  }

  .searchFence {
    width: 94%;
    padding: 3%;
    .el-button {
      // padding: 12px 0;
      .el-image {
        width: 14px;
      }
    }
  }
  .addFButton {
    padding: 3%;
    width: 94%;
    .addWord {
      width: 100%;
      padding: 10%;

      border-radius: 8px;
      letter-spacing: 2px;
      font-weight: 520;
      font-size: 14px;
    }
  }

  .routeType {
    width: 94%;
    padding: 3%;
    height: auto;
    display: flex;
    align-items: flex-start;
    .el-button {
      .typeTitle {
        margin-top: 7px;
        font-size: 12px;
        letter-spacing: 2px;
      }
    }
  }
  .content-list {
    padding: 10px 3%;
    width: 94%;
    // height: 68%;
    overflow: auto;
    // padding-bottom: 2%;
    flex: 1;
    .fenceContent {
      margin: 2% 0;
      width: 100%;
      border-radius: 8px;
      padding: 6px 3%;
      text-align: left;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      .content-item-1 {
        width: 20%;
        font-size: 40px;
        text-align: center;
        // padding: 3% 0;
        margin-right: 5%;
        border-radius: 8px;
        font-weight: 600;
        flex-shrink: 0;
      }
      .content-item-2 {
        flex-grow: 1;
        font-size: 14px;
        .item-2-desc {
          font-size: 12px;
          margin-top: 2px;
        }
      }
    }
    .entryDiv {
      text-align: center;
      margin: 1% 0;
      width: 92%;
      border-radius: 8px;
      font-size: 16px;
      padding: 12px 3%;
      letter-spacing: 3px;
    }
    .content-list-item {
      margin: 1% 0;
      width: 92%;

      border-radius: 8px;
      padding: 6px 3%;
      text-align: left;

      font-size: 16px;
      padding-top: 5%;
      .el-row {
        .el-col {
          .wordType {
            margin: 1% 0;
            font-size: 13px;
          }
          &.el-col-end {
            float: right;
            text-align: right;
            .el-button {
              padding: 0;
            }
          }
        }
      }
      .operateBar {
        border-radius: 8px;
        padding: 1% 3%;
        margin-top: 3%;
        .el-button {
          padding: 0;
          text-align: left;
          font-size: 14px;
          letter-spacing: 2px;
          margin-top: 1%;
          width: 30%;
          .el-image {
            float: left;
            width: 20%;
            margin-right: 10%;
          }
        }
        .planDiv {
          margin-right: 32%;
        }
      }
    }
  }
  .el-pagination {
    width: 94%;
    // margin: 3%;
    padding: 3%;
    .el-button {
      margin: 2px;
    }
  }
}
.renameDialog {
  .dividerDiv {
    margin: 5px 0;
    height: 2px;
  }
  .el-form {
    margin-top: 50px;
  }
  .savebtn {
    border-radius: 8px;
    width: 130px;
  }
  .closeBtn {
    border-radius: 8px;
    width: 130px;
  }
}
</style>
<style lang="less">
.planList {
  .searchFence {
    .el-input__inner {
      height: 32px !important;
      font-size: 14px !important;
      letter-spacing: 2px;
    }
  }
  .el-input-group__append {
    text-align: center !important;
    .el-button {
      // padding: 12px 0;
      padding: 0 !important;
    }
  }
  .title {
    .title-item {
      .el-button {
        .el-icon-arrow-left {
          font-weight: 700 !important;
        }
      }
    }
  }
  .content-list {
    .el-col-end {
      .content-item-3 {
        width: 100% !important;

        .el-popover__reference-wrapper {
          width: 100% !important;
          .el-button {
            width: 35% !important;
            // padding: 1px !important;
            // width: 10% !important;
            .el-image {
              width: 60% !important;
            }
          }
        }
      }
    }
  }
  .content-list {
    .fenceContent {
      .content-item-3 {
        flex-shrink: 0;
        width: 10% !important;
        .el-popover__reference-wrapper {
          .el-button {
            padding: 1px !important;
            width: 100% !important;
            .el-image {
              width: 60% !important;
            }
          }
        }
      }
    }
  }

  .el-pagination {
    button {
      margin: 0 3px !important;
      padding: 0 !important;
      background-color: transparent;
      border: 1px solid #fff;
      color: #fff;
      width: 28px;
      height: 28px;
      line-height: 28px;
      min-width: 0;
      border-radius: 4px;
      &:disabled {
        color: #c0c4cc;
      }
    }

    &.prev-class {
      .btn-prev {
        border: 1px solid #124093;
        color: #124093 !important;
      }
    }
    &.next-class {
      .btn-next {
        border: 1px solid #124093;
        color: #124093 !important;
      }
    }
    .el-pager li {
      width: 28px !important;
      height: 28px !important;
      line-height: 26px !important;
      color: #fff !important;
      background-color: transparent;
      border: 1px solid #fff;
      padding: 0 !important;
      min-width: 0 !important;
      margin: 0 3px;
      border-radius: 4px !important;
    }
    .el-pager li:not(.disabled).active {
      color: white !important;
    }
  }

  .routeType {
    .el-button {
      .typeTitle {
        font-size: 12px !important;
      }
    }
  }
}
.renameDialog {
  .el-dialog {
    border-radius: 6px !important;
  }
  .el-dialog__header {
    padding: 5px !important;
    text-align: left !important;
  }
  .el-dialog__body {
    padding-top: 0 !important;
    padding-left: 5px !important;
    padding-right: 5px !important;
    text-align: center !important;
  }
}
.setPopover-item,
.setPopover-item-1 {
  &.el-popover {
    width: auto !important;
    min-width: 0 !important;
    padding: 0 !important;
    margin-left: 20px !important;
    border-radius: 8px !important;
  }
  .popper__arrow,
  .popper__arrow::after {
    display: none;
  }
  .el-button {
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    padding: 6px 15px !important;
    margin: 0 !important;
    border-radius: 8px !important;
    width: 100% !important;
    font-size: 12px !important;
    &.btn1 {
      margin-bottom: 5px !important;
    }
  }
}
.setPopover-item-1 {
  &.el-popover {
    margin-left: 25px !important;
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .planList {
    .searchFence {
      .el-input__inner {
        height: @zoomIndex * 32px !important;
        letter-spacing: @zoomIndex * 2px !important;
        font-size: @zoomIndex * 14px !important;
      }
      .el-input-group__append {
        padding: 0 @zoomIndex * 20px !important;
      }
    }
    .content-list {
      .fenceContent {
        .content-item-3 {
          .el-popover__reference-wrapper {
            .el-button {
              padding: @zoomIndex * 1px !important;
            }
          }
        }
      }
    }
    .el-pagination {
      button {
        margin: 0 @zoomIndex * 3px !important;
        width: @zoomIndex * 28px !important;
        height: @zoomIndex * 28px !important;
        line-height: @zoomIndex * 28px !important;
      }
      .btn-next,
      .btn-prev,
      .el-pager li {
        width: @zoomIndex * 28px !important;
        height: @zoomIndex * 28px !important;
        border-radius: @zoomIndex * 4px !important;
        margin: 0 @zoomIndex * 5px !important;

        line-height: @zoomIndex * 26px !important;

        margin: 0 @zoomIndex * 3px !important;
      }
      .btn-prev,
      .btn-next {
        .el-icon {
          font-size: @zoomIndex * 14px !important;
        }
        background-size: @zoomIndex * 16px !important;
      }
      button,
      span:not([class*="suffix"]) {
        height: @zoomIndex * 28px !important;
        line-height: @zoomIndex * 28px !important;
      }
      .el-pager li {
        // padding: 0 @zoomIndex * 4px !important;
        font-size: @zoomIndex * 13px !important;
        // min-width: @zoomIndex * 35.5px !important;
        width: @zoomIndex * 28px !important;
        height: @zoomIndex * 28px !important;
        line-height: @zoomIndex * 26px !important;
      }
    }

    .routeType {
      .el-button {
        .typeTitle {
          font-size: @zoomIndex * 12px !important;
        }
      }
    }
  }
  .renameDialog {
    .el-dialog {
      border-radius: @zoomIndex * 6px !important;
    }
    .el-dialog__header {
      padding: @zoomIndex * 5px !important;
    }
    .el-dialog__body {
      padding-left: @zoomIndex * 5px !important;
      padding-right: @zoomIndex * 5px !important;
    }
  }
  .setPopover-item,
  .setPopover-item-1 {
    &.el-popover {
      margin-left: @zoomIndex * 20px !important;
      border-radius: @zoomIndex * 8px !important;
    }
    .el-button {
      padding: @zoomIndex * 6px @zoomIndex * 15px !important;
      border-radius: @zoomIndex * 8px !important;
      font-size: @zoomIndex * 12px !important;
      &.btn1 {
        margin-bottom: @zoomIndex * 5px !important;
      }
    }
  }
  .setPopover-item-1 {
    &.el-popover {
      margin-left: @zoomIndex * 25px !important;
    }
  }
}
</style>
