<!-- 密码设置 -->
<template>
  <custom-dialog
    @submit="saveUserInfo"
    :title="userLanguage.title"
    :visible.sync="isShow"
    @close="close"
  >
    <template v-slot:main>
      <el-form
        :model="form"
        :rules="rules"
        ref="passSetForm"
        label-width="150px"
      >
        <el-form-item :label="formLabel.password" prop="password">
          <el-input
            show-password
            :placeholder="formPlaceholder.password"
            v-model="form.password"
          />
        </el-form-item>

        <el-form-item :label="formLabel.newPass" prop="newPass">
          <el-input
            show-password
            :placeholder="formPlaceholder.newPass"
            v-model="form.newPass"
          />
        </el-form-item>

        <el-form-item :label="formLabel.affirmPass" prop="affirmPass">
          <el-input
            show-password
            :placeholder="formPlaceholder.affirmPass"
            v-model="form.affirmPass"
          />
        </el-form-item>
      </el-form>
    </template>
  </custom-dialog>
</template>

<script>
import customDialog from "@/components/customDialog/index.vue";
import request from "@/utils/api.js";
import { getCode } from "@/utils/rsa.js";
import { getLocalStorage } from "@/utils/storage";
export default {
  name: "passSet",
  components: {
    customDialog,
  },
  data() {
    let _this = this;
    return {
      form: {
        password: "",
        newPass: "",
        affirmPass: "",
      },
      rules: {
        password: [
          { required: true, message: "请输入旧密码", trigger: "blur" },
          {
            min: 6,
            max: 32,
            message: "长度在 6 到 32 个字符",
            trigger: "blur",
          },
        ],
        newPass: [
          { required: true, message: "请输入新密码", trigger: "blur" },
          {
            min: 6,
            max: 32,
            message: "长度在 6 到 32 个字符",
            trigger: "blur",
          },
        ],
        affirmPass: [
          { required: true, message: "请再次输入新密码", trigger: "blur" },
          {
            min: 6,
            max: 32,
            message: "长度在 6 到 32 个字符",
            trigger: "blur",
          },
          {
            validator: (rule, value, callback) => {
              if (value === "") {
                callback(new Error(this.formVerify.affirmPass[0]));
              } else if (value !== _this.form.newPass) {
                callback(new Error(this.formVerify.affirmPass[2]));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
      isSave: false,
      isShow: false,
    };
  },
  computed: {
    userLanguage() {
      return this.$languagePackage.user.passSet;
    },
    formLabel() {
      return this.userLanguage.label;
    },
    formPlaceholder() {
      return this.userLanguage.placeholder;
    },
    formVerify(){
      return this.userLanguage.verify
    }
  },
  created(){
    this.init();
  },
  methods: {
    init: function () {
      this.rules.password[0].message = this.formVerify.password[0];
      this.rules.password[1].message = this.formVerify.password[1];

      this.rules.password[0].newPass = this.formVerify.newPass[0];
      this.rules.password[1].newPass = this.formVerify.newPass[1];

      this.rules.password[0].affirmPass = this.formVerify.affirmPass[0];
      this.rules.password[1].affirmPass = this.formVerify.affirmPass[1];
    },
    saveUserInfo: function () {
      this.isSave = true;
      this.$refs.passSetForm.validate((volid) => {
        if (volid) {
          let userInfo = getLocalStorage("userInfo");
          // params.password = getCode(md5(params.password))
          request("userUpdatePsw", {
            account: userInfo.phone || userInfo.email,
            password: getCode(md5(this.form.password)),
            new_psw: getCode(md5(this.form.newPass)),
            pmd: "account,password,new_psw",
            jointPmd: true,
          }).then(() => {
            let item = this.userLanguage.confirm;

            this.$confirm(item.content, item.title, {
              confirmButtonText: item.confirmText,
              cancelButtonText: item.cancelText,
              type: "warning",
            }).then(() => {
              this.$router.push({ name: "login" });
              this.$store.commit("setUserInfo", {}); // 清空用户信息
            });

            this.isShow = false;
          });

          return false;
        }
        this.isSave = false;
      });
    },
    open: function () {
      this.isShow = true;
    },
    shut: function () {
      this.isShow = false;
    },
    close: function () {
      this.isShow = false;
      this.form = {
        password: "",
        newPass: "",
        affirmPass: "",
      };
      this.$refs.passSetForm.resetFields();
    },
  },
};
</script>