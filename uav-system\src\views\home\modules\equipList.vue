<template>
  <div class="equipList" v-if="show" :style="style">
    <div class="equip-header">选择执行任务的飞机</div>
    <div class="equip-main">
      <div v-if="loading" class="loading-tip">
        <i class="el-icon-loading"></i>正在加载。。。
      </div>
      <div v-if="!loading && !equipList.length" class="entry-tip">
        暂无在线飞机
      </div>
      <div
        class="equip-li"
        v-for="item in equipList"
        :key="item.sn_id"
        @click="goTask(item)"
      >
        <div class="equip-li-name">{{ item.name }}</div>
        <div class="equip-li-sn">{{ item.sn_id }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import request from "@/utils/api";
export default {
  data() {
    return {
      equipList: [],
      show: false,
      style: {},
      loading: false,
      alarmItem: "",
    };
  },
  methods: {
    open(e, alarmItem) {
      if (!this.show) {
        this.show = true;
        this.style = {
          top: e.originEvent.clientY - 25 + "px",
          left: e.originEvent.clientX + 16 + "px",
        };
        this.getList();
        this.alarmItem = alarmItem;
        console.log(this.alarmItem);
      }
    },
    async getList() {
      this.loading = true;
      this.equipList = [];
      let data = {
        page: 0,
        size: 100,
        type: 200,
      };
      data.pmd = data.page.toString() + data.type.toString();
      await request("deviceList", data).then((res) => {
        if (res.data.list) {
          for (let index = 0; index < res.data.list.length; index++) {
            if (res.data.list[index].is_push_on) {
              this.equipList.push(res.data.list[index]);
            }
          }
        }

        // this.equipList = res.data.list;
      });
      window.addEventListener("click", this.close);
      this.loading = false;
    },
    close(e) {
      let arr = [
        "equipList",
        "equip-header",
        "equip-main",
        "equip-li",
        "equip-li-name",
        "equip-li-sn",
      ];
      if (arr.indexOf(e.target.className) == -1) {
        this.show = false;
        window.removeEventListener("click", this.close);
      }
    },
    goTask(item) {
      this.show = false;
      window.removeEventListener("click", this.close);

      let flightParam = {
        equipItem: item,
        alarmItem: this.alarmItem,
      };
      let wipeOut = sessionStorage.getItem("wipeOut");
      if (wipeOut) {
        wipeOut = JSON.parse(wipeOut);
      } else {
        wipeOut = {};
      }
      wipeOut[item.sn_id] = flightParam;

      sessionStorage.setItem("wipeOut", JSON.stringify(wipeOut));
      const newwin = this.$router.resolve({
        path: `/navigationEnvironment`,
        query: {
          sn_id: item.sn_id,
          type: item.type,
          state: 2,
        },
      });
      window.open(newwin.href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.equipList {
  position: fixed;
  background-color: rgba(10, 34, 128, 0.5);
  color: #fff;
  min-width: 250px;
  &::after {
    content: "";
    border-top: solid 5px #00800000;
    border-left: solid 8px #00800000;
    border-right: solid 8px #0a2280a0;
    border-bottom: solid 5px #00800000;
    position: absolute;
    top: 50%;
    right: 100%;
  }
  .equip-header {
    text-align: center;
    margin: 5px 0;
    font-size: 16px;
  }
  .equip-main {
    padding: 5px 0;
    .loading-tip {
      font-size: 14px;
      color: rgb(169, 196, 233);
      text-align: center;
      i {
        margin-right: 3px;
        font-size: 16px;
      }
    }
    .entry-tip {
      font-size: 18px;
      color: red;
      text-align: center;
      font-weight: 660;
    }
    .equip-li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px 10px;
      cursor: pointer;
      &:hover {
        background-color: rgba(10, 34, 128, 0.8);
      }
    }
  }
}
</style>