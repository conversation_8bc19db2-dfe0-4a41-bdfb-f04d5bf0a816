<template>
  <div class="userManage">
    <div class="searchDiv">
      <el-input
        v-model="searchValue"
        :placeholder="userLanguage.tipInfo.placeholder"
        clearable
        @clear="clearEvent"
        @keyup.enter.native="search()"
      ></el-input>
      <el-button
        class="searchBut"
        @click="searchCode ? '' : search()"
        :class="searchCode ? 'active' : ''"
        :disabled="searchCode1 || searchValue ? false : true"
        >{{ userLanguage.allBtn.search }}</el-button
      >
      <el-button
        class="addBut"
        icon="el-icon-plus"
        @click="addUser"
        :class="clickCode ? 'active' : ''"
        >{{ userLanguage.allBtn.newUser }}</el-button
      >
    </div>
    <custom-table
      :column="column"
      urlName="userList"
      :isShowPage="true"
      :params="param"
      pmd="state,page"
      ref="userList"
      @requestResponse="isSeacherEntry"
    >
      <template v-slot:phone="scope">
        {{ scope.row.phone ? scope.row.phone : scope.row.email }}
      </template>
      <template v-slot:company="scope">
        {{ scope.row.in_com ? scope.row.in_com.name : "" }}
      </template>
      <template v-slot:fun_list="scope">
        {{ funfilter(scope.row.fun_list) }}
      </template>
      <template v-slot:state="scope">
        {{ userState(scope.row.state) }}
      </template>
      <template v-slot:start_time="scope">
        {{ timeFormat(scope.row.start_time) }}
      </template>
      <template v-slot:end_time="scope">
        {{ timeFormat(scope.row.end_time) }}
      </template>
      <template v-slot:operation="scope">
        <el-button
          @click="scope.row.state == 10 ? setUser(scope.row) : ''"
          :title="
            scope.row.state == 10 ? '' : userLanguage.tipInfo.placeholder1
          "
          :class="{
            active: u_id == scope.row.u_id,
            showAble: scope.row.state == 20,
          }"
        >
          <el-image :src="u_id == scope.row.u_id ? setImg1 : setImg"></el-image>
          {{ userLanguage.allBtn.set }}
        </el-button>
        <el-button
          v-if="scope.row.state == 10 ? true : false"
          @click="delUser(scope.row)"
          :class="u_id_1 == scope.row.u_id ? 'active' : ''"
        >
          <el-image
            :src="u_id_1 == scope.row.u_id ? delImg1 : delImg"
          ></el-image>
          {{ userLanguage.allBtn.del }}
        </el-button>
        <el-button
          v-if="scope.row.state == 20 ? true : false"
          @click="recoverUser(scope.row)"
          :class="u_id_2 == scope.row.u_id ? 'active' : ''"
        >
          <el-image
            style="width: 30%"
            :src="u_id_2 == scope.row.u_id ? recoverImg1 : recoverImg"
          ></el-image>
          {{ userLanguage.allBtn.recovery }}
        </el-button>
      </template>
    </custom-table>
    <user-dialog
      ref="userDialog"
      :funList="funList"
      :clickCode.sync="clickCode"
      :u_id.sync="u_id"
      @refresh="refresh"
    ></user-dialog>
    <el-dialog
      :title="
        state == 10
          ? userLanguage.dialogInfo.recoverTitle
          : userLanguage.dialogInfo.delTitle
      "
      :visible.sync="delCode"
      :close-on-click-modal="false"
      class="delDialog"
      width="30%"
      center
      :show-close="false"
    >
      <el-divider></el-divider>
      <div class="deltext">
        {{
          state == 10
            ? userLanguage.dialogInfo.recoverText
            : userLanguage.dialogInfo.delText
        }}
      </div>
      <div class="delButDiv">
        <el-button
          class="saveBut"
          @click="sureDel"
          :class="{ active: deluserCode, recoverBtn: state == 10 }"
          >{{
            state == 10
              ? userLanguage.allBtn.recover1
              : userLanguage.allBtn.del1
          }}</el-button
        >
        <el-button
          class="closeBut"
          @click="closeDel"
          :class="closedelCode ? 'active' : ''"
          >{{ userLanguage.allBtn.cancel }}</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import requestHttp from "@/utils/api";
// import md5 from "../../node_modules/js-md5";

import customTable from "@/components/customTable/index";
import userDialog from "./components/userDialog.vue";
export default {
  name: "userManage",
  components: {
    customTable,
    userDialog,
  },
  data() {
    return {
      time: 0,
      searchValue: "",
      funList: [],
      options: "",
      u_id: "",
      u_id_1: "",
      u_id_2: "",
      searchCode: false,
      closedelCode: false,
      clickCode: false,
      deluserCode: false,
      userManageCode: false,
      delCode: false,
      searchCode1: false,
      column: "",
      param: {
        state: 0,
        page: 0,
        size: 10,
      },
      state: 10,
      delImg: require("@/assets/img/del.png"),
      delImg1: require("@/assets/img/deled.png"),
      setImg: require("@/assets/img/setting.png"),
      setImg1: require("@/assets/img/seted.png"),
      recoverImg: require("@/assets/img/recover.png"),
      recoverImg1: require("@/assets/img/recover_1.png"),
    };
  },
  created() {
    this.options = this.userLanguage.options;
    this.column = this.userLanguage.column;
  },
  mounted() {
    this.$nextTick(() => {
      this.getFunList();
    });
  },
  computed: {
    userLanguage() {
      return this.$languagePackage.userManage;
    },
  },
  methods: {
    userState(state) {
      if (state == 10) {
        return this.$language == "english" ? "normal" : "正常";
      } else {
        return this.$language == "english" ? "deleted" : "删除";
      }
    },
    funfilter(value) {
      let funStr = "";
      for (let index = 0; index < this.funList.length; index++) {
        for (let i = 0; i < value.length; i++) {
          if (value[i] == this.funList[index].fun_id) {
            if (this.$language == "english") {
              funStr =
                funStr == ""
                  ? this.funList[index].name_en
                  : funStr + "、" + this.funList[index].name_en;
            } else {
              funStr =
                funStr == ""
                  ? this.funList[index].name
                  : funStr + "、" + this.funList[index].name;
            }
          }
        }
      }

      return funStr;
    },
    //请求返回的
    isSeacherEntry() {
      if (this.searchValue) {
        this.searchCode1 = true;
      } else {
        this.searchCode1 = false;
      }
    },
    //搜索用户
    search() {
      this.searchCode = true;
      this.param.search = this.searchValue;
      if (!this.searchValue) {
        delete this.param.search;
      }
      this.$refs.userList.refresh();
      setTimeout(() => {
        this.searchCode = false;
      }, 200);
    },
    //清空输入框clearEvent
    clearEvent() {
      this.searchCode1 = true;
    },
    //获取功能列表
    getFunList() {
      requestHttp("funList").then((res) => {
        for (let index = 0; index < res.data.list.length; index++) {
          if (res.data.list[index].state == 10) {
            this.funList.push(res.data.list[index]);
          }
        }
      });
    },
    //点击新建用户
    addUser() {
      this.clickCode = true;
      this.$refs.userDialog.open("add");
    },

    //设置用户信息
    setUser(item) {
      this.$refs.userDialog.open("edit", item);
      this.u_id = item.u_id;
    },
    //删除用户信息
    delUser(item) {
      this.delCode = true;
      this.item = item;
      this.u_id_1 = item.u_id;
      this.state = 20;
    },
    //提交删除
    sureDel() {
      this.deluserCode = true;
      let data = {
        u_id: this.item.u_id,
        nick: this.item.nick,
        state: this.state,
        in_com_id: this.item.in_com.id,
        start_time: this.item.start_time,
        end_time: this.item.end_time,
        fun_json: JSON.stringify(this.item.fun_list),
        // notes:'',
      };
      var account = "";
      if (this.item.phone) {
        data.phone = this.item.phone;
        account = this.item.phone;
      } else {
        data.email = this.item.email;
        account = this.item.email;
      }
      data.pmd =
        data.u_id +
        data.state.toString() +
        data.start_time.toString() +
        data.end_time.toString() +
        account.toString() +
        data.in_com_id.toString() +
        data.nick +
        data.fun_json;
      data.os_timestampCode = true;
      requestHttp("userEdit", data).then((res) => {
        this.$message({
          type: "success",
          message:
            this.state == 10
              ? this.userLanguage.dialogInfo.successRecover
              : this.userLanguage.dialogInfo.successDel,
          customClass: "message-info-tip",
        });

        this.$refs.userList.getTableData();
      });
      setTimeout(() => {
        this.delCode = false;
        this.u_id_1 = false;
        this.deluserCode = false;
      }, 200);
    },
    //取消删除
    closeDel() {
      this.closedelCode = true;
      setTimeout(() => {
        this.u_id_1 = false;
        this.delCode = false;
        this.closedelCode = false;
        this.$message({
          type: "info",
          message:
            this.state == 10
              ? this.userLanguage.dialogInfo.cancelRecover
              : this.userLanguage.dialogInfo.cancelDel,
          customClass: "message-info-tip",
        });
      }, 200);
    },
    //恢复用户
    recoverUser(item) {
      this.delCode = true;
      this.item = item;
      this.u_id_1 = item.u_id;
      this.state = 10;
      // this.u_id_2 = item.u_id;
      // let data = {
      //   u_id: item.u_id,
      //   nick: item.nick,
      //   state: 10,
      //   start_time: item.start_time,
      //   end_time: item.end_time,
      //   fun_json: JSON.stringify(item.fun_list),
      //   // notes:'',
      // };
      // var account = "";
      // if (item.phone) {
      //   data.phone = item.phone;
      //   account = item.phone;
      // } else {
      //   data.email = item.email;
      //   account = item.email;
      // }
      // data.pmd =
      //   data.u_id +
      //   data.state.toString() +
      //   data.start_time.toString() +
      //   data.end_time.toString() +
      //   account.toString() +
      //   data.nick +
      //   data.fun_json;

      // setTimeout(() => {
      //   this.u_id_2 = "";
      //   requestHttp("userEdit", data).then((res) => {
      //     this.$message({
      //       type: "success",
      //       message: this.userLanguage.dialogInfo.successRecover,
      //       customClass: "message-info-tip",
      //     });

      //     this.$refs.userList.getTableData();
      //   });
      // }, 200);
    },
    //表头行的风格
    tableHeader() {
      return {
        background: "rgba(10, 10, 84, 0.8) !important",
        //   "color":'#FFFFFF'
      };
    },
    //返回table中class名
    tabRowClassName({ rowIndex }) {
      let index = rowIndex + 1;
      if (index % 2 == 0) {
        return "warning-row";
      } else return "";
    },
    timeFormat(value) {
      if (value >= **********) {
        return this.$language == "english" ? "Permanently valid" : "永久有效";
      } else {
        let time = new Date(parseInt(value * 1000));
        return (
          time.getFullYear() +
          "-" +
          this.dataFormat(time.getMonth() + 1) +
          "-" +
          this.dataFormat(time.getDate()) +
          "  " +
          this.dataFormat(time.getHours()) +
          ":" +
          this.dataFormat(time.getMinutes()) +
          ":" +
          this.dataFormat(time.getSeconds())
        );
      }
    },
    dataFormat(value) {
      if (value > 9) {
        return value;
      } else {
        return "0" + value;
      }
    },
    refresh() {
      this.$refs.userList.getTableData();
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .userManage {
    .searchDiv {
      .el-button {
        border-radius: @zoomIndex * 6px !important;
        font-size: @zoomIndex * 14px !important;
        padding: @zoomIndex * 13px @zoomIndex * 20px !important;
      }
    }
    .custom-table {
      .el-button {
        padding: @zoomIndex * 6px !important;
        font-size: @zoomIndex * 15px !important;
      }
    }
    .el-dialog {
      .userForm {
        .fromButton {
          .el-button {
            font-size: @zoomIndex * 18px !important;
            padding: @zoomIndex * 12px @zoomIndex * 30px !important;
            border-radius: @zoomIndex * 8px !important;
          }
        }
      }
      .delButDiv {
        .el-button {
          font-size: @zoomIndex * 18px !important;
          padding: @zoomIndex * 12px 0 !important;
          border-radius: @zoomIndex * 8px !important;
        }
      }
    }
  }
}
.userManage {
  width: 100%;
  height: 100%;
  .searchDiv {
    padding: 1% 2%;
    text-align: left;
    width: 96%;
    .el-input {
      width: 20%;
      margin-right: 1%;
    }
    .el-button {
      border-radius: 6px;
      font-size: 14px;
      font-weight: 550;
      padding: 13px 20px;
    }
    .addBut {
      float: right;
    }
  }
  .custom-table {
    width: 96%;
    height: 84%;
    margin: 0.5% 2%;
    .el-button {
      padding: 6px;
      width: 40%;
      font-size: 15px;
      &.showAble {
        height: 0;
        overflow: hidden;
        padding: 0 !important;
      }

      .el-image {
        width: 30%;
        margin-right: 10%;
        vertical-align: middle;
      }
    }
  }
  .el-dialog {
    .userForm {
      .el-form-item {
        margin-bottom: 4%;
        .el-input {
          width: 100%;
        }
        .el-select,
        .el-radio-group {
          width: 100%;
        }
        .el-checkbox-group {
          float: left;
          width: 100%;
        }
        .chooseDate {
          width: 100%;
          .el-range-input {
            width: 40%;
          }
        }
      }
      .fromButton {
        width: 100%;
        text-align: center;
        margin-top: 5%;
        .el-button {
          font-size: 18px;
          padding: 12px 30px;
          border-radius: 8px;
        }
        .saveBut {
          margin-right: 15%;
        }
      }
    }
    .delButDiv {
      width: 100%;
      text-align: center;
      margin-top: 10%;
      .el-button {
        font-size: 18px;
        padding: 12px 0;
        border-radius: 8px;
        width: 35%;
      }
      .saveBut {
        margin-right: 10%;
      }
    }
    .deltext {
      margin: 0 10%;
    }
  }
}
</style>
<style lang="less">
.userManage {
  .searchDiv {
    .el-input {
      .el-input__inner {
        font-size: 16px !important;
        border-radius: 8px !important;
        border-width: 1px !important;
        height: 40px !important;
        line-height: 40px !important;
      }
      .el-input__suffix {
        .el-input__icon {
          width: 25px !important;
          font-size: 14px !important;
          line-height: 40px !important;
        }
      }
    }
  }
  .el-dialog {
    width: 800px !important;
    margin-top: 8vh !important;
    border-radius: 8px !important;
    .el-form {
      .el-form-item {
        .el-form-item__label {
          line-height: 20px !important;
          padding-right: 12px !important;
          font-size: 14px !important;
        }
        .el-form-item__content {
          font-size: 14px !important;
          line-height: 40px !important;
          .el-input {
            font-size: 14px !important;
            line-height: 40px !important;
            .el-input__inner {
              border-width: 1px !important;
              border-radius: 4px !important;
              height: 40px !important;
              line-height: 40px !important;
              padding: 0 15px !important;
            }
            .el-input__suffix {
              right: 5px !important;
              .el-select__caret {
                font-size: 14px !important;
                width: 25px !important;
                line-height: 40px !important;
              }
            }
          }
          .el-form-item__error {
            font-size: 12px !important;
            padding-top: 4px !important;
          }
          .el-radio-group {
            display: flex;
            align-items: center;
            .el-radio {
              display: flex;
              align-items: center;
              margin-right: 30px !important;
              .el-radio__label {
                font-size: 14px !important;
                padding-left: 10px !important;
              }
              .el-radio__inner {
                width: 14px !important;
                height: 14px !important;
                border-width: 1px !important;
                &::after {
                  width: 4px !important;
                  height: 4px !important;
                }
              }
            }
          }
          .el-checkbox-group {
            .el-checkbox {
              font-size: 14px !important;
              margin-right: 30px !important;
              .el-checkbox__label {
                padding-left: 10px !important;
                line-height: 19px !important;
                font-size: 14px !important;
              }
              .el-checkbox__inner {
                border: 1px solid #dcdfe6 !important;
                border-radius: 2px !important;
                width: 14px !important;
                height: 14px !important;
              }
            }
          }
          .chooseDate {
            &.el-range-editor.el-input__inner {
              padding: 3px 10px !important;
              height: 40px !important;
              line-height: 40px !important;
              border-radius: 4px !important;
              border-width: 1px !important;
            }
            .el-range__icon {
              font-size: 14px !important;
              margin-left: -5px !important;
              line-height: 32px !important;
              width: 25px !important;
            }
            .el-range-input,
            .el-range-separator {
              font-size: 14px !important;
            }
            .el-range__close-icon {
              font-size: 14px !important;
              width: 25px !important;
              line-height: 32px !important;
            }
            .el-range-separator {
              padding: 0 5px !important;
              line-height: 32px !important;
            }
          }
        }
      }
    }
  }

  .el-dialog__wrapper {
    .el-dialog__header {
      margin: 0 100px !important;
      padding: 20px 20px 10px !important;
      .el-dialog__title {
        font-size: 24px !important;
        font-weight: 600 !important;
      }
    }
    .el-dialog__body {
      padding: 0 25px 30px 25px !important;
      margin-top: 0 !important;
      margin: 0 100px !important;
      .el-divider {
        margin: 0 !important;
        height: 5px !important;
        border-radius: 2.5px !important;
        margin-bottom: 2% !important;
      }
    }
  }
  .delDialog {
    margin-top: 20vh !important;
    text-align: center !important;
    .el-dialog__body {
      text-align: left !important;
      font-size: 18px !important;
      margin: 0 5% !important;
      font-weight: 550;
      line-height: 200%;
    }
  }
  .el-table .el-table__cell {
    padding: 10px 0 !important;
  }
}
.selectUserType {
  .el-select-dropdown__wrap {
    margin-bottom: -23px !important;
    margin-right: -23px !important;
    max-height: 274px !important;
    .el-select-dropdown__list {
      padding: 6px 0 !important;
      .el-select-dropdown__item {
        font-size: 14px !important;
        padding: 0 20px !important;
        height: 34px !important;
        line-height: 34px !important;
      }
    }
  }
}
.choose-date-poper {
  width: 646px !important;
  border-width: 1px !important;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%) !important;
  border-radius: 4px !important;
  line-height: 30px !important;
  margin: 5px 0 !important;
  .el-picker-panel__body {
    min-width: 513px !important;
    .el-date-range-picker__time-header {
      border-bottom-width: 1px !important;
      font-size: 12px !important;
      padding: 8px 5px 5px !important;
      .el-date-range-picker__editors-wrap {
        .el-date-range-picker__time-picker-wrap {
          padding: 0 5px !important;
          .el-input {
            font-size: 13px !important;
            .el-input__inner {
              height: 32px !important;
              line-height: 32px !important;
            }
          }
          .el-time-panel {
            margin: 5px 0 !important;
            border-width: 1px !important;
            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%) !important;
            border-radius: 2px !important;
            width: 180px !important;
            .el-time-panel__content {
              .el-time-spinner__wrapper {
                max-height: 190px !important;
                .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
                  padding-bottom: 15px !important;
                }
                .el-time-spinner__list {
                  &::before,
                  &::after {
                    height: 80px !important;
                  }
                  .el-time-spinner__item {
                    height: 32px !important;
                    line-height: 32px !important;
                    font-size: 12px !important;
                  }
                }
                .el-scrollbar__bar {
                  right: 2px !important;
                  bottom: 2px !important;
                  border-radius: 4px !important;
                  &.is-horizontal {
                    height: 6px !important;
                    left: 2px !important;
                  }
                }
              }
              &::before,
              &::after {
                margin-top: -15px !important;
                height: 32px !important;
                padding-top: 6px !important;
                border-top-width: 1px !important;
                border-bottom-width: 1px !important;
              }
            }
            .el-time-panel__footer {
              border-top-width: 1px !important;
              padding: 4px !important;
              height: 36px !important;
              line-height: 25px !important;
              .el-time-panel__btn {
                line-height: 28px !important;
                padding: 0 5px !important;
                margin: 0 5px !important;
                font-size: 12px !important;
              }
            }
          }
        }
      }

      & > .el-icon-arrow-right {
        font-size: 20px !important;
      }
    }
    .el-date-range-picker__content {
      padding: 16px !important;
      &.is-left {
        border-right-width: 1px !important;
      }
      .el-date-range-picker__header {
        height: 28px !important;
        .el-picker-panel__icon-btn {
          font-size: 12px !important;
          margin-top: 8px !important;
        }
        div {
          margin-left: 50px !important;
          margin-right: 50px !important;
          font-size: 16px !important;
        }
      }
      .el-date-table {
        font-size: 12px !important;
        th {
          padding: 5px !important;
          border-bottom-width: 1px !important;
        }
        td {
          width: 32px !important;
          padding: 4px 0 !important;
          div {
            padding: 3px 0 !important;
            height: 30px !important;
            span {
              width: 24px !important;
              height: 24px !important;
              line-height: 24px !important;
            }
          }
        }
      }
    }
  }
  .el-picker-panel__footer {
    border-top-width: 1px !important;
    padding: 4px !important;
    .el-button {
      padding: 7px 15px !important;
      font-size: 12px !important;
      border-radius: 3px !important;
      border-width: 1px !important;
    }
  }
}
.message-info-tip {
  top: 20px !important;
  min-width: 380px !important;
  padding: 15px 15px 15px 20px !important;
  border-radius: 4px !important;
  .el-message__icon {
    font-size: 16px !important;
    margin-right: 10px !important;
  }
  .el-message__content {
    font-size: 14px !important;
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .userManage {
    .searchDiv {
      .el-input {
        .el-input__inner {
          font-size: @zoomIndex * 16px !important;
          border-radius: @zoomIndex * 8px !important;
          border-width: @zoomIndex * 1px !important;
          height: @zoomIndex * 40px !important;
          line-height: @zoomIndex * 40px !important;
        }
        .el-input__suffix {
          .el-input__icon {
            width: @zoomIndex * 25px !important;
            font-size: @zoomIndex * 14px !important;
            line-height: @zoomIndex * 40px !important;
          }
        }
      }
    }
    .el-dialog {
      width: @zoomIndex * 800px !important;
      border-radius: @zoomIndex * 8px !important;
      .el-form {
        .el-form-item {
          .el-form-item__label {
            line-height: @zoomIndex * 20px !important;
            padding-right: @zoomIndex * 12px !important;
            font-size: @zoomIndex * 14px !important;
          }
          .el-form-item__content {
            font-size: @zoomIndex * 14px !important;
            line-height: @zoomIndex * 40px !important;
            .el-input {
              font-size: @zoomIndex * 14px !important;
              line-height: @zoomIndex * 40px !important;
              .el-input__inner {
                border-width: @zoomIndex * 1px !important;
                border-radius: @zoomIndex * 4px !important;
                height: @zoomIndex * 40px !important;
                line-height: @zoomIndex * 40px !important;
                padding: 0 @zoomIndex * 15px !important;
              }
              .el-input__suffix {
                right: @zoomIndex * 5px !important;
                .el-select__caret {
                  font-size: @zoomIndex * 14px !important;
                  width: @zoomIndex * 25px !important;
                  line-height: @zoomIndex * 40px !important;
                }
              }
            }
            .el-form-item__error {
              font-size: @zoomIndex * 12px !important;
              padding-top: @zoomIndex * 4px !important;
            }
            .el-radio-group {
              .el-radio {
                margin-right: @zoomIndex * 30px !important;
                .el-radio__label {
                  font-size: @zoomIndex * 14px !important;
                  padding-left: @zoomIndex * 10px !important;
                }
                .el-radio__inner {
                  width: @zoomIndex * 14px !important;
                  height: @zoomIndex * 14px !important;
                  border-width: @zoomIndex * 1px !important;
                  &::after {
                    width: @zoomIndex * 4px !important;
                    height: @zoomIndex * 4px !important;
                  }
                }
              }
            }
            .el-checkbox-group {
              .el-checkbox {
                font-size: @zoomIndex * 14px !important;
                margin-right: @zoomIndex * 30px !important;
                .el-checkbox__label {
                  padding-left: @zoomIndex * 10px !important;
                  line-height: @zoomIndex * 19px !important;
                  font-size: @zoomIndex * 14px !important;
                }
                .el-checkbox__inner {
                  border: @zoomIndex * 1px solid #dcdfe6 !important;
                  border-radius: @zoomIndex * 2px !important;
                  width: @zoomIndex * 14px !important;
                  height: @zoomIndex * 14px !important;
                }
              }
            }
            .chooseDate {
              &.el-range-editor.el-input__inner {
                padding: @zoomIndex * 3px @zoomIndex * 10px !important;
                height: @zoomIndex * 40px !important;
                line-height: @zoomIndex * 40px !important;
                border-radius: @zoomIndex * 4px !important;
                border-width: @zoomIndex * 1px !important;
              }
              .el-range__icon {
                font-size: @zoomIndex * 14px !important;
                margin-left: @zoomIndex * -5px !important;
                line-height: @zoomIndex * 32px !important;
                width: @zoomIndex * 25px !important;
              }
              .el-range-input,
              .el-range-separator {
                font-size: @zoomIndex * 14px !important;
              }
              .el-range__close-icon {
                font-size: @zoomIndex * 14px !important;
                width: @zoomIndex * 25px !important;
                line-height: @zoomIndex * 32px !important;
              }
              .el-range-separator {
                padding: 0 @zoomIndex * 5px !important;
                line-height: @zoomIndex * 32px !important;
              }
            }
          }
        }
      }
    }

    .el-dialog__wrapper {
      .el-dialog__header {
        margin: 0 @zoomIndex * 100px !important;
        padding: @zoomIndex * 20px @zoomIndex * 20px @zoomIndex * 10px !important;
        .el-dialog__title {
          font-size: @zoomIndex * 24px !important;
        }
      }
      .el-dialog__body {
        padding: 0 @zoomIndex * 25px @zoomIndex * 30px @zoomIndex * 25px !important;
        margin: 0 @zoomIndex * 100px !important;
        .el-divider {
          height: @zoomIndex * 5px !important;
          border-radius: @zoomIndex * 2.5px !important;
        }
      }
    }
    .delDialog {
      .el-dialog__body {
        font-size: @zoomIndex * 18px !important;
      }
    }
    .el-table .el-table__cell {
      padding: @zoomIndex * 10px 0 !important;
    }
  }
  .selectUserType {
    .el-select-dropdown__wrap {
      margin-bottom: @zoomIndex * -23px !important;
      margin-right: @zoomIndex * -23px !important;
      max-height: @zoomIndex * 274px !important;
      .el-select-dropdown__list {
        padding: @zoomIndex * 6px 0 !important;
        .el-select-dropdown__item {
          font-size: @zoomIndex * 14px !important;
          padding: 0 @zoomIndex * 20px !important;
          height: @zoomIndex * 34px !important;
          line-height: @zoomIndex * 34px !important;
        }
      }
    }
  }
  .choose-date-poper {
    width: @zoomIndex * 646px !important;
    border-width: @zoomIndex * 1px !important;
    box-shadow: 0 @zoomIndex * 2px @zoomIndex * 12px 0 rgb(0 0 0 / 10%) !important;
    border-radius: @zoomIndex * 4px !important;
    line-height: @zoomIndex * 30px !important;
    margin: @zoomIndex * 5px 0 !important;
    .el-picker-panel__body {
      min-width: @zoomIndex * 513px;
      .el-date-range-picker__time-header {
        border-bottom-width: @zoomIndex * 1px !important;
        font-size: @zoomIndex * 12px !important;
        padding: @zoomIndex * 8px @zoomIndex * 5px @zoomIndex * 5px !important;
        .el-date-range-picker__editors-wrap {
          .el-date-range-picker__time-picker-wrap {
            padding: 0 @zoomIndex * 5px !important;
            .el-input {
              font-size: @zoomIndex * 13px !important;
              .el-input__inner {
                height: @zoomIndex * 32px !important;
                line-height: @zoomIndex * 32px !important;
              }
            }
            .el-time-panel {
              margin: @zoomIndex * 5px 0 !important;
              border-width: @zoomIndex * 1px !important;
              box-shadow: 0 @zoomIndex * 2px @zoomIndex * 12px 0
                rgb(0 0 0 / 10%) !important;
              border-radius: @zoomIndex * 2px !important;
              width: @zoomIndex * 180px !important;
              .el-time-panel__content {
                .el-time-spinner__wrapper {
                  max-height: @zoomIndex * 190px !important;
                  .el-scrollbar__wrap:not(.el-scrollbar__wrap--hidden-default) {
                    padding-bottom: @zoomIndex * 15px !important;
                  }
                  .el-time-spinner__list {
                    &::after,
                    &::before {
                      height: @zoomIndex * 80px !important;
                    }
                    .el-time-spinner__item {
                      height: @zoomIndex * 32px !important;
                      line-height: @zoomIndex * 32px !important;
                      font-size: @zoomIndex * 12px !important;
                    }
                  }
                  .el-scrollbar__bar {
                    right: @zoomIndex * 2px !important;
                    bottom: @zoomIndex * 2px !important;
                    border-radius: @zoomIndex * 4px !important;
                    &.is-horizontal {
                      height: @zoomIndex * 6px !important;
                      left: @zoomIndex * 2px !important;
                    }
                  }
                }
                &::before,
                &::after {
                  margin-top: @zoomIndex * -15px !important;
                  height: @zoomIndex * 32px !important;
                  padding-top: @zoomIndex * 6px !important;
                  border-top-width: @zoomIndex * 1px !important;
                  border-bottom-width: @zoomIndex * 1px !important;
                }
              }
              .el-time-panel__footer {
                border-top-width: @zoomIndex * 1px !important;
                padding: @zoomIndex * 4px !important;
                height: @zoomIndex * 36px !important;
                line-height: @zoomIndex * 25px !important;
                .el-time-panel__btn {
                  line-height: @zoomIndex * 28px !important;
                  padding: 0 @zoomIndex * 5px !important;
                  margin: 0 @zoomIndex * 5px !important;
                  font-size: @zoomIndex * 12px !important;
                }
              }
            }
          }
        }

        & > .el-icon-arrow-right {
          font-size: @zoomIndex * 20px !important;
        }
      }
      .el-date-range-picker__content {
        padding: @zoomIndex * 16px !important;
        &.is-left {
          border-right-width: @zoomIndex * 1px !important;
        }
        .el-date-range-picker__header {
          height: @zoomIndex * 28px !important;
          .el-picker-panel__icon-btn {
            font-size: @zoomIndex * 12px !important;
            margin-top: @zoomIndex * 8px !important;
          }
          div {
            margin-left: @zoomIndex * 50px !important;
            margin-right: @zoomIndex * 50px !important;
            font-size: @zoomIndex * 16px !important;
          }
        }
        .el-date-table {
          font-size: @zoomIndex * 12px !important;
          th {
            padding: @zoomIndex * 5px !important;
            border-bottom-width: @zoomIndex * 1px !important;
          }
          td {
            width: @zoomIndex * 32px !important;
            padding: @zoomIndex * 4px 0 !important;
            div {
              padding: @zoomIndex * 3px 0 !important;
              height: @zoomIndex * 30px !important;
              span {
                width: @zoomIndex * 24px !important;
                height: @zoomIndex * 24px !important;
                line-height: @zoomIndex * 24px !important;
              }
            }
          }
        }
      }
    }
    .el-picker-panel__footer {
      border-top-width: @zoomIndex * 1px !important;
      padding: @zoomIndex * 4px !important;
      .el-button {
        padding: @zoomIndex * 7px @zoomIndex * 15px !important;
        font-size: @zoomIndex * 12px !important;
        border-radius: @zoomIndex * 3px !important;
        border-width: @zoomIndex * 1px !important;
      }
    }
  }
  .message-info-tip {
    top: @zoomIndex * 20px !important;
    min-width: @zoomIndex * 380px !important;
    padding: @zoomIndex * 15px @zoomIndex * 15px @zoomIndex * 15px @zoomIndex *
      20px !important;
    border-radius: @zoomIndex * 4px !important;
    .el-message__icon {
      font-size: @zoomIndex * 16px !important;
      margin-right: @zoomIndex * 10px !important;
    }
    .el-message__content {
      font-size: @zoomIndex * 14px !important;
    }
  }
}
</style>