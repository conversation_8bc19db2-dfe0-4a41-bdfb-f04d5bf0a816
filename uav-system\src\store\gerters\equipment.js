import request from "@/utils/api"


import baseUrl from "@/utils/global";
import {
    Websockets,
    equipmentCertification
} from "@/utils/websocketUntil";
import {
    timestampSwitch
} from "@/utils/date.js"
/**
 * 存放设备信息
 */
const equipment = {
    state: {
        equipmentInfo: {}, // 遥感设备信息
        equipmentLinkState: false, // 遥感设备连接状态
        equipmentList: [], // 设备列表
        isVideoScreen: false, // 是否全屏
        screenZIndex: "zindex-style",

        staveOntData: {}, // 状态一回转参数
        staveTwoData: {}, // 状态二回转参数
        staveThreeData: {}, // 状态三回转参数
        missionTime: "00:00:00", // 任务执行时间

        equipmentWS: null,

        playVideo: null, // 控制遥感播放视频方法
        timeDataIverview: null, // 遥感无人机页面数据更新

        flightCourseInfo: {}, // 航线信息
        railInfo: {}, // 围栏信息

        flightTask: false, // 是否正在执行任务
        laterTook: null,

        closeFlightTrack: null, // 取消飞行任务
        // 
        wsMmessageFun: {},

        maps: null,
        leafletMaps: null,
        rtkState: {}, //rtk状态

        uavVideoUrl: null,
        keyboardOperate: false, //开启关闭键盘事件
        from_cno: '',
        form_cor: '',
        zoomMaxHeight: 0,
        elevationcode: false,
        delBreakMarkerCode: false

    },
    mutations: {
        //删除断点
        setDelBreakMarkerCode(state, val) {
            state.delBreakMarkerCode = val
        },
        // 设置设备认证返回信息
        setEquipmentContent(state, val) {
            state.equipmentInfo = val;

            try {
                state.uavVideoUrl = val.stream_uav_list[0]
            } catch (error) {
                console.error("设备认证-飞机视频流地址出错");
            }

        },
        // 设置飞机流信息
        setUavVideoUrl(state, val) {
            state.uavVideoUrl = val;
        },

        // 设置设备连接认证
        setEquipmentLinkState(state, val) {
            state.equipmentLinkState = val;
        },

        // 设置设备列表
        setEquipmentList(state, val) {
            state.equipmentList = val;
        },

        // 设置视频层级样式
        setVideoScreen(state, val) {
            state.isVideoScreen = val;
            if (val) {
                state.screenZIndex = "";
            } else {
                state.screenZIndex = "zindex-style";
            }
        },

        // 设置状态回传2参数
        setStaveTwoData(state, val) {
            state.staveTwoData = val;
        },

        // 设置状态回传3参数
        setStaveThreeData(state, val) {
            state.staveThreeData = val;
        },

        // 设置设备认证ws
        setEquipmentWS(state, val) {
            state.equipmentWS = val;
        },
        // 设置任务预计时间
        setMissionTime(state, val) {
            state.missionTime = timestampSwitch(Math.ceil(val) * 1000).time;
        },

        // 设置围栏航线信息
        setFenceRoute(state, val) {
            state.flightCourseInfo = val.flightCourse;
            state.railInfo = val.fence;
        },

        setPlayVideo(state, val) {
            state.playVideo = val;
        },
        setTimeDataIverview(state, val) {
            state.timeDataIverview = val;
        },
        setFlightTask(state, val) {
            state.flightTask = val;
        },
        setCloseFlightTrack(state, val) {
            state.closeFlightTrack = val;
        },
        setLaterTook(state, val) {
            state.laterTook = val;
        },
        setWsMmessageFun(state, val) {
            state.wsMmessageFun[val.key] = val.message;
        },
        setMaps(state, val) {
            state.maps = val;
        },
        setLeafletMaps(state, val) {
            state.leafletMaps = val;
        },
        //设置rtk状态
        setRtkState(state, val) {
            state.rtkState = val
        },
        setKeyboardOperate(state, val) {
            state.keyboardOperate = val
        },
        //设置链路模式
        setFormCno(state, val) {
            state.from_cno = val

        },
        setFormCor(state, val) {
            state.form_cor = val
        },
        setZoomMaxHeight(state, val) {
            state.zoomMaxHeight = val
        },
        setElevationcode(state, val) {
            state.elevationcode = val
        }
    },
    actions: {
        //  获取设备列表
        getEquipmentList(content, val = {}) {
            return new Promise((resolve, reject) => {
                let params = {
                    type: 0,
                    search: '',
                    page: 0,
                    size: 200
                }
                params = Object.assign(params, val);
                params.pmd = String(params.page) + String(params.type);
                request("deviceList", params).then((res) => {
                    let data = res.data;
                    content.commit("setEquipmentList", data.list ? data.list : []);
                    resolve(res);
                })
            })
        },
        // 设备认证
        createEquipmentWS(commit, val = {}) {

        },

    }
}

export default equipment;