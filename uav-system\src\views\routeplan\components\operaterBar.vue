<template>
  <div class="operaterBar">
    <el-button
      @click="clickEvent('drawMark')"
      :class="drawend ? 'chooseAcitve' : ''"
    >
      <el-image v-show="drawend" :src="pointImg" fit="contain"></el-image>
      <el-image v-show="!drawend" :src="pointImg_1" fit="contain"></el-image>
      <span style="vertical-align: middle">{{
        routeLanguage.operationBtn.drawPoint
      }}</span>
    </el-button>
    <el-button
      @click="clickEvent('removeMarker')"
      :class="removeClick ? 'chooseAcitve' : ''"
    >
      <el-image
        v-show="removeClick"
        :src="removeImg"
        fit="contain"
        style="width: 23%"
      ></el-image>
      <el-image
        v-show="!removeClick"
        :src="removeImg_1"
        fit="contain"
        style="width: 23%"
      ></el-image>
      <span style="vertical-align: middle">{{
        routeLanguage.operationBtn.delete
      }}</span>
    </el-button>
    <el-button
      @click="clickEvent('delMarker')"
      :class="delClick ? 'chooseAcitve' : ''"
    >
      <el-image v-show="delClick" :src="delImg" fit="contain"></el-image>
      <el-image v-show="!delClick" :src="delImg_1" fit="contain"></el-image>
      <span style="vertical-align: middle">{{
        routeLanguage.operationBtn.remove
      }}</span>
    </el-button>
    <el-button
      @click="clickEvent('recallMarker')"
      :class="recallClick ? 'chooseAcitve' : ''"
    >
      <el-image v-show="recallClick" :src="recallImg" fit="contain"></el-image>
      <el-image
        v-show="!recallClick"
        :src="recallImg_1"
        fit="contain"
      ></el-image>
      <span style="vertical-align: middle">{{
        routeLanguage.operationBtn.recall
      }}</span>
    </el-button>
    <el-button
      @click="clickEvent('importEvent')"
      :class="importClick ? 'chooseAcitve' : ''"
    >
      <el-image v-show="importClick" :src="importImg" fit="contain"></el-image>
      <el-image
        v-show="!importClick"
        :src="importImg_1"
        fit="contain"
      ></el-image>
      <span style="vertical-align: middle">{{
        routeLanguage.operationBtn.import
      }}</span>
    </el-button>
    <el-button
      @click="clickEvent('exportEvent')"
      :class="exportClick ? 'chooseAcitve' : ''"
    >
      <el-image v-show="exportClick" :src="exportImg" fit="contain"></el-image>
      <el-image
        v-show="!exportClick"
        :src="exportImg_1"
        fit="contain"
      ></el-image>
      <span style="vertical-align: middle">{{
        routeLanguage.operationBtn.export
      }}</span>
    </el-button>
  </div>
</template>
<script>
export default {
  name: "operaterBar",
  props: {
    drawend: {
      type: Boolean,
      default: false,
    },
    removeClick: {
      type: Boolean,
      default: false,
    },
    recallClick: {
      type: Boolean,
      default: false,
    },
    importClick: {
      type: Boolean,
      default: false,
    },
    exportClick: {
      type: Boolean,
      default: false,
    },
    delClick: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      pointImg: require("../../../assets/img/routeplan/point.png"),
      removeImg: require("../../../assets/img/routeplan/remove.png"),
      delImg: require("../../../assets/img/routeplan/del.png"),
      recallImg: require("../../../assets/img/routeplan/recall.png"),
      importImg: require("../../../assets/img/routeplan/import.png"),
      exportImg: require("../../../assets/img/routeplan/export.png"),
      pointImg_1: require("../../../assets/img/routeplan/point_1.png"),
      removeImg_1: require("../../../assets/img/routeplan/remove_1.png"),
      delImg_1: require("../../../assets/img/routeplan/del_1.png"),
      recallImg_1: require("../../../assets/img/routeplan/recall_1.png"),
      importImg_1: require("../../../assets/img/routeplan/import_1.png"),
      exportImg_1: require("../../../assets/img/routeplan/export_1.png"),
    };
  },
  methods: {
    clickEvent(e) {
      this.$emit("operateEvent", e);
    },
  },
  computed: {
    routeLanguage() {
      return this.$languagePackage.routes;
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .operaterBar {
    .el-button {
      width: @zoomIndex * 80px !important;
      padding: @zoomIndex * 5px 0 !important;
      font-size: @zoomIndex * 16px !important;
      .el-image {
        margin: 0 @zoomIndex * 5px !important;
      }
    }
  }
}
.operaterBar {
  .el-button {
    width: 80px;
    padding: 5px 0;
    margin: 0;
    text-align: left;
    font-size: 16px;
    .el-image {
      width: 25%;
      float: left;
      margin: 0 5px;
    }
  }
}
</style>