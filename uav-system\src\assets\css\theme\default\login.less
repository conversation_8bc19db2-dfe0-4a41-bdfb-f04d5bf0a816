 // 登录页面样式
 .login {

   #login {

     background-color: #000;

     .login-form {
       color: #ffffff;

       .form-title {
         h1 {
          // rgb(12, 20, 31)
           text-shadow: 2px 1px 0 rgb(12, 20, 31), 1px 1px 0 rgb(12, 20, 31), 3px 3px 0 rgb(12, 20, 31), 3px 3px 0 rgb(12, 20, 31), 3px 3px 0 rgb(12, 20, 31), 3px 3px 0 rgb(12, 20, 31);
         }
       }

       .form-content {
         .content-main {
           border: 2px solid rgba(51, 72, 172, 1);
           background-image: linear-gradient(to top,
               rgba(19, 64, 159, 0.9),
               rgba(17, 57, 144, 0.8),
               rgba(13, 44, 112, 0.6),
               rgba(11, 38, 100, 0.1));

           .rim-line {
             border: 4px solid rgba(116, 137, 249, 1);
           }

           .from-mian-title {
             .title-bg {
               .title-bg-item {
                 background-color: rgba(0, 56, 115, 1);
               }

               .title-bg-left,
               .title-bg-right {
                 background-color: rgba(0, 56, 115, 1);
               }

             }

             .text {
               color: #ffffff;
             }
           }

         }

         .footer-line {
           background-image: linear-gradient(to right,
               rgba(119, 161, 246, 0.1),
               rgba(59, 117, 235, 0.4),
               rgba(255, 255, 255, 1),
               rgba(59, 117, 235, 0.4),
               rgba(119, 161, 246, 0.1));
         }
       }

       .login-footer {
         color: #fff;
       }

       .login-fluxay {
         .fluxay-main {
           .fluxay-cell {
             &::before {
               content: '';
               background-color: #ffff00;
             }
           }
         }
       }
     }

     .el-input__inner {
       background-color: rgba(0, 0, 0, 0);
       border: none;
       border-radius: 0;
       border-bottom: 1px solid #ffffff;
       color: #fff;
     }
   }
 }
