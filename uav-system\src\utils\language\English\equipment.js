const equipment = {
    language: 'en-US',
    //设备信息
    equipInfo: {
        equipStateIn: 'Equipment Online',
        equipStateOut: 'Equipment Offline',
        personalStatus: "Connected user",
        personalStatusNo: "No Connection",
        equipNum: "Equipment Number:",
        belonging: "Belonging To:",
        equipName: "Equipment Name:",
        address: "Address",
        equipStateOut1: 'Equipment Offline',
        taskExecuted: 'Task to be executed',
        unit: '',
        popupLabel: {
            title: 'Connected User',
            nick: 'Nick:',
            account: 'Account:'
        }
    },
    //输入框
    input: {
        placeholder: "Please enter the device name/address"
    },
    //页面按钮
    button: {
        searchBtn: "Search",
        addEquip: "Add Equipment",
        setEquip: "Set",
        delEquip: "Delete",
        previous: "PgUp",
        next: "PgDn",
        firmwareVersion: 'Version Information',
        Upgrade: 'Upgrade',
        calibration: 'Sensor',
        outCabinVCR: 'Extravehicular Recording',
        nestLog: 'Upload Nest Logs'
    },
    //设备天气状态
    equipStatus: {
        temperature: "TEMP",
        weather: "Weather",
        rain: "Rainfall",
        humidity: "Humidity",
        wind: "Wind",
        windDirection: "Wind force"
    },
    //机巢状态
    airportStatus: {
        title: 'Airport Status',
        drone: "Drone",
    },
    operationList: [{
            title: "Hatch",
            id: "Hatch",
            value: false,
            value1: 0,
        },
        {
            title: "Platform",
            id: "Lift",
            value: false,
            value1: 0,
        },
        {
            title: "Horse",
            id: "center",
            value: false,
            value1: 0,
        },
        {
            title: "Charger",
            id: "charger",
            value: false,
            value1: 0,
        },
    ],
    //机巢控制按钮
    airportBtn: {
        onKeyState: 'One click to start',
        starting: 'Starting',
        starting1: 'Opening',
        onKeyClose: 'One click to Close',
        closing: 'Closing',
        removeError: 'Clear errors',
        removeState: 'Clear State',
        crashStop: "Crash-stop",
        stop: 'Pause',
        continue: 'Continue',
        reset: 'Reset',
        fitOn: "Loading aircraft batteries",
        fitOning: 'Loading...',
        unfitOn: "Remove the aircraft battery",
        unfitOning: 'Removing',
    },
    //机巢地图
    airportMap: {
        title: "Airport Location",
        title1: "Uav Location",
        placeholder: 'Search location',
        sure: 'Confirm',
        cancel: 'Cancel',
        reading: 'Reading',
        readUav: 'Reading aircraft position',
        readAirport: 'Reading airport location',
        errorMessage: 'Unable to obtain accurate airport location at the moment',
        errorMessage1: 'Unable to obtain the precise position of the drone at the moment',
        errorMessage2: 'The drone is unlocked and cannot obtain precise position at the moment',
        errorMessage3: 'Drone RTK not entered, unable to obtain precise position at the moment',
        successMessage: 'Successfully read the precise location of the airport',
        successMessage1: 'Successfully read the precise position of the aircraft',

    },
    //飞机状态
    uavStatus: {
        title: "The Plane State",
        lng: "longitude",
        lat: "latitude",
        beaconValue: 'beacon',
        direction: 'direction',
        rtkStatus: "state of RTK",
        satellitesNum: "number of satellites",
        electricity: "electricity",
        voltage: "voltage",
        flightModel: "flightModel",
        uavState: "uavState",
        height: "height(m)",
        yaw: "course",
        roll: "roll angle",
        pitch: "pitch angle",
        hSpeed: "horizontal velocity(m/s)",
        vSpeed: "vertical speed(m/s)"
    },
    //云台操作
    holderOperation: {
        title: "Gimbal Operation",
        holder: "PTZ",
        zoom: "Zoom",
        center: "Correction",
        down: 'Vertical down'

    },
    //相机参数
    cameraParams: {
        title: "Camera Parameters",
        ISO: "ISO",
        shutter: "shutter",
        expModel: "exposure mode",
        expValue: "exposure value",
        awb: "white balance",
        moreSet: "more set >>>",
        // pictureMode: "picture mode",
        // photoFormat: "photo format",
        // photoSize: "photo size",
        // previewResolution: "preview resolution",
        // PreviewBitrate: "Preview bitrate",
        // videoResolution: "video resolution",
        // videoBitrate: "video bitrate"
    },
    selsectList: [{
            title: "Picture Mode",
            placeholder: "Please select the photo mode",
            value: 1,
            id: "photoMode",
            options: [{
                    label: "single photo",
                    value: 1,
                },
                {
                    label: "continuous shooting",
                    value: 2,
                },
                {
                    label: "timer",
                    value: 3,
                },
                {
                    label: "delay shooting",
                    value: 4,
                },
            ],
        },
        {
            title: "Photo Format",
            placeholder: "Please select a photo format",
            value: 1,
            id: "photoFormat",
            options: [{
                    label: "JPG",
                    value: 1,
                },
                {
                    label: "RAW",
                    value: 2,
                },
                {
                    label: "RAW&JPG",
                    value: 3,
                },
            ],
        },
        {
            title: "Photo Size",
            placeholder: "Please select the photo size",
            value: 1,
            id: "photoSize",
            options: [{
                    label: "8Mb",
                    value: 1,
                },
                {
                    label: "12Mb",
                    value: 2,
                },
                {
                    label: "16Mb",
                    value: 3,
                },
                {
                    label: "21Mb",
                    value: 4,
                },
                {
                    label: "32Mb",
                    value: 5,
                },
                {
                    label: "41Mb",
                    value: 6,
                },
            ],
        },
        {
            title: "Preview Resolution",
            placeholder: "Please select a preview resolution",
            value: 0,
            id: "resolving",
            options: [{
                    label: "720p",
                    value: 1,
                },
                {
                    label: "1080p",
                    value: 0,
                },
            ],
        },
        {
            title: "Preview Bitrate",
            placeholder: "Please select the preview bit rate",
            value: 2,
            id: "rateCode",
            options: [{
                    label: "1M",
                    value: 0,
                },
                {
                    label: "2M",
                    value: 1,
                },
                {
                    label: "4M",
                    value: 2,
                }
            ],
        },
        {
            title: "Video Resolution",
            placeholder: "Please select a video resolution",
            value: 3,
            id: "videotapeResolving",
            options: [{
                    label: "4K 25",
                    value: 0,
                },
                {
                    label: "4K 30",
                    value: 1,
                },
                {
                    label: "4K 60",
                    value: 2,
                },
                {
                    label: "6K 25",
                    value: 3,
                },

            ],
        },
        {
            title: "Vvideo Bitrate",
            placeholder: "Please select the preview bit rate",
            value: 2,
            id: "videotapeRateCode",
            options: [{
                    label: "8M",
                    value: 0,
                },
                {
                    label: "16M",
                    value: 1,
                },
                {
                    label: "32M",
                    value: 2,
                },
                {
                    label: "64M",
                    value: 3,
                }
            ],
        },
        {
            title: "White Balance",
            placeholder: "Please select white balance",
            value: 0,
            id: "awb",
            options: [{
                    label: "auto",
                    value: 0,
                },
                {
                    label: "candlelight",
                    value: 1,
                },
                {
                    label: "tungsten lamp",
                    value: 2,
                },
                {
                    label: "fluorescent lamp",
                    value: 3,
                },
                {
                    label: "daylight",
                    value: 4,
                },
                {
                    label: "cloudy",
                    value: 5,
                },
                {
                    label: "overcast",
                    value: 6,
                },
                {
                    label: "sky blue",
                    value: 7,
                },
                {
                    label: "smoke",
                    value: 8,
                }
            ],
        },
    ],
    flightModeList: {
        2: "Altitude mode",
        3: "Automatic mode",
        4: "Follow mode",
        5: "GPS mode",
        6: "Return mode",
        9: "Landing flight mode",
        13: "Motion mode",
    },
    flightStateList: {
        0: "Free",
        1: "Take off",
        2: "In the air",
        3: "Land",
        4: "On the ground",
        8: "Return",
    },
    cellList: [{
        label: 'Battery A',
        seq: 1,
    }, {
        label: 'Battery B',
        seq: 2,
    }, {
        label: 'Battery C',
        seq: 3,
    }, {
        label: 'Battery D',
        seq: 4,
    }],
    rtkStatusList: ["Not Connected", "Not Positioned", "Single Point Positioning", "Floating Solution", "Fixed Solution"],
    awb: ['Auto', 'Candlelight', 'Tungsten Lamp', 'Fluorescent Lamp', 'Daylight', 'Cloudy', 'Overcast', 'Sky Blue', 'Smoke'],
    novideoTip: "The channel video stream is invalid.",
    inCabin: "Cabin Video",
    outCabin: "Extravehicular Video",
    uav: 'UAV Video',
    addeditEquip: {
        addTitle: "New Eequipment",
        editTilte: "Edit Equipment",
        type: "Type",
        linkType: 'Link',
        name: "Automatic airport name",
        uavName: "UAV name",
        num: 'Automatic airport number',
        address: 'Automatic airport deployment address',
        uavModel: 'UAV model',
        uavNum: "UAV number",
        desc: 'Describe',
        fixAirport: "Fixed airport",
        // fixLargeAirport: "Fixed Airport",
        MobileAirport: "Mobile Airport",
        uav: "Single soldier UAV",
        link2: '4G',
        link1: 'fpv',
        link3: 'automatic',
        azimuth: "Automatic airport azimuth",
        placeholder: "Please enter the name of UAV",
        placeholder1: "Please enter the name of the Airport",
        placeholder2: "Please enter the airport Sn code",
        placeholder3: 'Please select the airport location',
        placeholder4: 'Please select the UAV model',
        placeholder5: 'Please enter the SN code of UAV',
        placeholder6: "Please select device type",
        placeholder7: "Please enter the device name",
        placeholder8: "Please enter the device SN code",
        save: 'Save',
        cancel: 'Cancel',
        use: "Use",
        disable: "Disable",
        addSuccess: 'Successfully added equipment',
        noOption: 'The device is disabled, unable to repeat the operation!',
        editSuccess: 'Edit device succeeded',
        equipNo: 'Device disabled',
        landPoint: 'Alternate landing point',
        airportPoint: 'Airport coordinate points',
        lng: 'longitude',
        lat: 'latitude',
        company: 'Select the company/department',
        placeholder9: "Please select the company/department to which the user belongs",
        sendDevice: 'Send to device',
        placeholderBeacon: 'Please click to set beacon parameters',
        beacon: 'Beacon Parameters',
        setBeacon: 'Set beacon parameters',
        warnTip: 'Will it affect the landing accuracy issue? Do you want to continue?',
        tipTitle: 'Tips',
        setDirectionIng: "Setting direction angle",
        setBeaconing: 'Setting beacon parameters',
        deviceInLine: 'The current device is not online!',
        directionEmpty: 'The azimuth angle is invalid, currently -1',
        setTimeOut: 'Azimuth setting timeout!',
        setTimeOut1: 'Beacon parameter setting timeout!',
        setBeaconSuccess: 'Beacon parameter setting successful!',
        setDirectionSuccess: "Azimuth setting successful!",
        noSetDirection: "The direction angle is not set,",
        noSetBeacon: "Beacon parameters not set,",
    },
    onKeyStartError: 'One key start failed!',
    onKeyCloseError: 'One key closing failed!',
    onKeyStartSuccess: 'One click Start succeeded!',
    onKeyCloseSuccess: 'One key closing succeeded!',
    disconnect: "Disconnected!",
    searchTip: "No matching devices were found",
    searchReturn: 'If the input is blank, return to the starting list',
    noset: 'This device has been connected, cannot be temporarily set!',
    delTip: 'This operation cannot be recovered. Are you sure you want to delete the device information?',
    tips: 'Tips',
    delSuccess: 'Delete succeeded!',
    cancelDel: 'Cancelled deletion',
    noDel: 'This device has been connected, and cannot be deleted!',
    noClose: 'The device is starting and cannot be closed!',
    inClosing: 'The device is shutting down!',
    noOpen: 'The device is shutting down and cannot be opened temporarily!',
    inOpening: 'The device is on!',
    upgradeFirmware: {
        dialogTitle: 'Upgrade Firmware',
        fileTitle: 'Firmware files',
        clickChoose: 'Click to select',
        uploadTip: 'Only wkimg suffix files can be uploaded',
        submit: 'Confirm',
        cancel: 'Cancel',
        errorTip: 'Please upload the firmware file',
        loadingText: 'Uploading firmware...'
    },
    versionLabel: {
        nest_version: 'Machine nest',
        nest_mcu_version: 'Machine nest Drive',
        fcs_version: 'Flight control',
        camera_version: 'Camera',
        fcs_slave_version: 'Secondary IC',
        gimbal_version: 'PTZ',
        drone_linux_version: 'Airborne 4G'
    },
    upgradeMsg: {
        noUpgrade: 'The current device is not online and cannot be upgraded at this time!',
        noUpgrade1: 'The plane is currently performing a task and cannot be upgraded at the moment!',
        reStart: 'The upgrade is complete and requires a reboot to take effect. Are you sure you want to reboot?',
        reStartTip: 'Restart Tip',
        submit: 'Confirm'

    },
    noVersionData: "No version information returned temporarily",
    rtkStateTitle: 'RTK State:',
    calibration: {
        title: 'Drone Sensor Settings',
        title1: "Calibration",
        calibrationTitle: 'Calibration',
        startTitle: 'Start Calibration',
        nextStep: 'Next Step',
        list: {
            compass: 'COMPASS',
            gyroscope: 'Gyroscope',
            acceleration: 'Acceleration'
        },
        stateList: {
            0: 'abnormal',
            1: 'normal'
        },
        gyroscope: {
            1: "Place the aircraft horizontally and vertically on a flat platform, keeping it stationary.",
            2: "Place the aircraft horizontally and vertically on a flat platform, and the status indicator light will flash rapidly. When the indicator light is red and green, it indicates successful calibration.",
        },
        compass: {
            1: "Firstly, remove the drone blades",
            2: "Rotate the aircraft nose vertically upwards and horizontally by 720 °.",
            3: "Place the aircraft flat and rotate it 720 degrees horizontally.",
        },
        acceleration: {
            1: 'Place the aircraft horizontally and vertically on a flat platform',
            2: 'Place the back of the aircraft horizontally and vertically on a flat platform',
            3: 'Place the left side of the aircraft horizontally and vertically on a flat platform',
            4: 'Place the right side of the aircraft horizontally and vertically on a flat platform',
            5: 'Place the aircraft camera horizontally and vertically on a flat platform facing downwards',
            6: 'Place the aircraft camera facing upwards horizontally and vertically on a flat platform',
        },
        resultList: {
            success: "Calibration Successful",
            error: "Calibration failed, please recalibrate",
            reStart: "After successful calibration, please restart the aircraft before starting use",
        },
    },
    weatherState: {
        humidity: 'NaN',
        temperature: 'NaN',
        weather: "NaN",
        winddirection: "NaN",
        windpower: "NaN",
    },

}
export default equipment