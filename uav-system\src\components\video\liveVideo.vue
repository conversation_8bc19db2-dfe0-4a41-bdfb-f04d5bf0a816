<!-- 直播视频 -->
<template>
  <vodei-layout
    ref="videoLayout"
    :videoId="videoId"
    :griddingType="griddingType"
    :url="url"
    :title="title"
    :showFooter="showFooter"
    :autoPlay="autoPlay"
    :isPlay.sync="isPlay"
    :isNetworking="isNetworking"
    :linkErrorType="linkErrorType"
    @playVideo="playVideo"
    @clickVideo="clickVideoEvent"
    @dblClickVideo="dblClickVideo"
  >
    <template v-slot:video>
      <!-- 视频-拉流 -->
      <video
        :id="videoId"
        class="video"
        ref="video"
        muted
        :style="videoStyle"
        style="border-radius: 5px"
        v-if="isShowVideo"
      ></video>

      <!-- 不拉流且有图片时显示 -->
      <div class="img-show" v-if="imgUrl">
        <el-image style="width: 100%; height: 100%" :src="imgUrl" />
      </div>
    </template>

    <template v-slot:error v-if="imgUrl">
      <slot name="error" :linkErrorType="linkErrorType">
        <div></div>
      </slot>
    </template>
  </vodei-layout>
</template>

<script>
import FlvjsVideo from "@/utils/flvjsVideo";
import vodeiLayout from "./videoLayout.vue";
export default {
  components: {
    vodeiLayout,
  },
  props: {
    title: String, // 标题
    url: String, // 视频链接
    // 视频id，唯一
    videoId: {
      type: String,
      default: "video-id",
    },
    griddingType: [String, Number],
    // 显示底部操作
    showFooter: {
      type: Boolean,
      default: true,
    },
    autoPlay: Boolean, // 是否自动播放
    isFill: Boolean, // 播放器是否充满
    cover: String, // 封面图片
  },
  data() {
    return {
      isPlay: false, // 是否播放
      isScreen: false, // 是否全屏
      isShowVideo: true, // 是否显示视频

      imgUrl: this.cover, // 暂停时显示的图片连接
      isNetworking: false, // 视频是否连接
      linkErrorType: 1, // 连接错误类型
      flvjsVideo: null, // flv实例
      isPlay: this.autoPlay
    };
  },
  computed: {
    videoStyle() {
      return {
        "object-fit": this.isFill ? "fill" : "",
      };
    },
  },
  beforeDestroy() {
    this.flvjsVideo && this.flvjsVideo.flvjsDestroy();
  },
  watch: {
    cover: function (val) {
      this.imgUrl = val;
    },
    url: function (val) {
      this.$nextTick(() => {
        this.autoPlay && this.initVideo();
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      // 如果自动播放，则初始化
      this.autoPlay && this.initVideo();
    });
  },
  methods: {
    // 更新网格
    updareGridding: function () {
      console.log("this.videoLayout", this.$refs.videoLayout.$el.$refs);
    },
    clickVideoEvent: function (row) {
      this.$emit("clickVideo", row);
    },
    dblClickVideo: function (row) {
      this.$emit("dblClickVideo", row);
    },
    // 重新连接/局部刷新
    reconnection: function (state) {
      this.isPlay = false;
      this.playVideo(state);
    },
    // 播放/暂停
    playVideo: function (state) {
      this.imgUrl = ""; // 清空上一次截屏的图片
      this.isShowVideo = false; // 隐藏视频播放器
      if (state) {
        // 进行局部刷新
        setTimeout(() => {
          this.isShowVideo = true;
          this.$nextTick(() => {
            this.initVideo();
          });
        }, 0);
      } else {
        this.getVideoImg();
        this.linkErrorType = 2;
        this.isNetworking = false;
        this.flvjsVideo && this.flvjsVideo.flvjsDestroy();
      }
    },
    initVideo: function () {
      if (!this.url) {
        this.isPlay = false;
        this.$message({
          type: "error",
          message: `${this.title}连接失败`,
        });
        this.linkErrorType = 3;
        return false;
      }
      this.isPlay = true;

      let video = new FlvjsVideo();
      this.flvjsVideo = video;
      video.createFlvjs({
        videoId: this.videoId,
        videoUrl: this.url,
        autoPlay: true,
        // 视频准备就绪时触发
        onCanplay: () => {
          this.isNetworking = true;
        },
        // 网络卡顿
        onWaiting: () => {
          this.isNetworking = false;
          this.linkErrorType = 2;
          // 手动进行跳帧
          this.flvjsVideo.frameSkip();
        },
        // 网络错误
        httpError: () => {
          this.isNetworking = false;
          this.linkErrorType = 3;
        },
      });
    },

    // 获取视频播放中的某一个页面
    getVideoImg: function () {
      var canvas = document.createElement("canvas");
      let video = document.getElementById(this.videoId);
      if (!video) {
        return false;
      }
      canvas.width = video.clientWidth;
      canvas.height = video.clientHeight;
      canvas
        .getContext("2d")
        .drawImage(video, 0, 0, canvas.width, canvas.height);
      var dataURL = canvas.toDataURL("image/jpeg");
      this.imgUrl = dataURL;
    },
  },
};
</script>

<style lang="less" scoped>
.img-show {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  // height: 100%;
}
</style>