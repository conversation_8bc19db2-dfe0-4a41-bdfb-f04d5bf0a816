/**
 * 成果管理
 */
const achievement = {
    form: {
        placeholder: {
            search: "请输入任务名称/设备id",
            uav: "请选择设备id",
            type: "请选择成果类型",
            startTime: "开始日期",
            endTime: "结束日期",
            searchButText: "搜 索",

        },

        listLabel: {
            sn_id: "设备编号",
            sort_id: "飞行编号",
            sort_name: "任务名称",
            create_time: "创建时间"
        },
        button: {
            search: "搜 索",
            submitDown: '确定下载',
            bulkDown: '批量下载',
            cancelbulkdown: '取消批量下载',
            submitDel: '确定删除',
            bulkDel: '批量删除',
            cancelDel: '取消删除',
            sortiesDown: '架次成果下载',
            down: '下 载',
            del: '删 除',
            sortieList: '架次列表',
            imgLoading: '加载中。。。',
        },
        deleteInfo: {
            deleteTip: '确定删除该成果吗？',
            submitTip: '确认删除',
            cancel: '取消',
            message: '删除成功',
            deleteTip1: '确定删除选中的成果吗？',
            downTip: '确认下载选中的成果吗？',
            tip: '提示',
            submit: '确定'

        }
    },

    flightLog: {
        table: {
            sort_id: "飞行编号",
            mission_name: "任务名称",
            sn_id: "设备ID",
            uav_id: "无人机",
            mission_type: "任务类型",
            user_name: "飞行用户",
            start_time: "起飞时间",
            end_time: "着陆时间",
            count: "成果总数",
            operation: "操作"
        },
        tipMessage: {
            sortieDelTip: "确定删除该架次并删除对应的成果数据？",
            sortieDelTips: "是否删除所选架次并删除对应的成果数据？",
            achieveDelMsg: '确定删除该架次所有成果吗？',
            noChooseMsg: '未选择架次数据！',
            deleteConf: '删除确认',
            confirm: '确定删除',
            cancel: '取消',
            successDel: '删除成功',
        },
        button: {
            search: "搜 索",
            data: "数据分析",
            results: "成果",
            play: "播放",
            del: '删除成果',
            down: '成果下载',
            alternateRecord: '备降记录',
            sortieDel: '删除架次',
            submitDel: '确定删除',
            bulkDel: '批量删除架次',
            cancelDel: '取消删除',
        },
        recordList: [{
                prop: "sn_id",
                label: "设备ID",
                width: '80px'
            },
            // {
            // prop: "timestamp", label: "备降时间戳"
            // },
            {
                prop: "reason",
                label: "备降原因"
            },
            {
                prop: "solution",
                label: "处理方案"
            },
            {
                prop: "timestamp",
                label: '备降时间',
                width: '90px'

            },
            {
                prop: "drone_int",
                label: "无人机坐标"
            },
            {
                prop: "altn_int",
                label: "备降点坐标"
            },
            {
                prop: "place_int",
                label: "机巢坐标"
            },
            {
                prop: "create_time",
                label: "创建时间",
                width: '90px'
            },
        ],
        altnReason: {
            0: "无",
            1: "位置偏差过大",
            2: "机巢一键开舱失败",
            3: "视觉识别降落失败"
        },
        altnSolution: {
            0: "无效",
            1: "仅做备降动作",
            2: "备降完成后尝试复降"
        },
        label: {
            title: '备降记录',
            lat: "经度：",
            lon: "纬度："
        }

    }
}

export default achievement;