<template>
  <div class="outLineMap">
      <el-form ref="form" :model="form" label-width="20%">
        <div class="title">选择下载所需的瓦片</div>
        <el-form-item label="地图类型">
          <el-select v-model="form.mts" style="width:80%;float:left">
            <el-option label="街道地图" :value="10"></el-option>
            <el-option label="卫星地图" :value="12"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="最小纬度">
          <el-input-number v-model="form.min_lat" :controls="false"  :precision="7" style="width:80%;float:left"></el-input-number>
        </el-form-item>
        <el-form-item label="最小经度">
          <el-input-number v-model="form.min_lon" :controls="false"  :precision="7" style="width:80%;float:left"></el-input-number>
        </el-form-item>
        <el-form-item label="最大纬度">
          <el-input-number v-model="form.max_lat" :controls="false"  :precision="7" style="width:80%;float:left"></el-input-number>
        </el-form-item>
        <el-form-item label="最大经度">
          <el-input-number v-model="form.max_lon" :controls="false"  :precision="7" style="width:80%;float:left"></el-input-number>
        </el-form-item>
        <el-form-item label="最小层级">
          <el-input-number v-model="form.from_zoom" :controls="false"  :min="1" :max="form.to_zoom" :precision="0" style="width:80%;float:left"></el-input-number>
        </el-form-item>
        <el-form-item label="最大层级">
          <el-input-number v-model="form.to_zoom" :controls="false"  :min="1" :max="20" :precision="0" style="width:80%;float:left"></el-input-number>
        </el-form-item>
        <el-form-item class="buttonDiv">
          <!-- <el-button class="close" @click="close">清除</el-button> -->
          <el-button :class="code?'close':'sure'" @click="sure">确定</el-button>
        </el-form-item>
        
      </el-form>
    
  </div>
</template>
<script>
import requestHttp from "../utils/api";
import initMaps from "../utils/maps";
export default {
  name: "outLineMap",
  data() {
    return {
      form:{
        mts:10,
        min_lat:113.400322,
        max_lat:113.400322,
        min_lon:22.888973,
        max_lon:22.888973,
        from_zoom:2,
        to_zoom:20
      },
      code:false
    };
  },
  async mounted() {
    // this.getMt()
    // this.initMap();
  },
  methods: {
    //下载瓦片
    sure(){
      if(!this.code){
        this.code=true
        let data={
        mts:this.form.mts,
        min_lat:parseInt(this.form.min_lat*1e7),
        max_lat:parseInt(this.form.max_lat*1e7),
        min_lon:parseInt(this.form.min_lon*1e7),
        max_lon:parseInt(this.form.max_lon*1e7),
        from_zoom:this.form.from_zoom,
        to_zoom:this.form.to_zoom,
      }
      data.pmd=data.mts.toString() + data.max_lat.toString() + data.min_lon.toString()+ data.min_lat.toString() +data.max_lon.toString() + data.from_zoom.toString() + data.to_zoom.toString()
      requestHttp("getMt",data).then(res=>{
        console.log(res)
          if(res.code==2000){
          this.$message.success("后台正在下载。。")
          }else{
            this.$message.error(res.data.msg)
          }
      })
      setTimeout(()=>{
        this.code=false
      },1000)
      }
      
      
    },
    //初始化地图
    initMap() {
      let titleLayer = new AMap.TileLayer({
        getTileUrl: function (x, y, z) {
          return `/api/walkeraMapLayer/${z}/${x}-${y}.png`;
        },
        zIndex: 10,
      });
      this.map = new AMap.Map("mapContainer", {
        center: [113.400322, 22.888973],
        zoom: 18,
        zooms: [3, 18],
        layers: [titleLayer],
        dragEnable: false,
      });

      this.map.on("zoomchange", this.zoomEvent);
      console.log(this.map.getBounds())
    },
    //缩放事件
    zoomEvent(e) {
      this.map.setCenter([113.400322, 22.888973]);
    },
    //获取设备列表数据
    async getDeviceData() {
      let data = {
        page: 0,
        size: 100,
        type: 10,
      };
      data.pmd = data.page.toString() + data.type.toString();
      await requestHttp("deviceList", data).then((res) => {
        this.deviceList = res.data.list?res.data.list:[];
      });
      this.drawDevicePoint();
    },
    //绘画设备坐标
    drawDevicePoint() {
      for (let index = 0; index < this.deviceList.length; index++) {
        let icon = new AMap.Icon({
          // 图标的取图地址
          image: require("../assets/img/outLineHome.png"),
          imageSize: new AMap.Size(50, 50),
        });
        let title = "离线";
        if (this.deviceList[index].is_push_on) {
          icon = new AMap.Icon({
            // 图标的取图地址
            image: require("../assets/img/inLineHome.png"),
            imageSize: new AMap.Size(50, 50),
          });
          title = "在线";
        }
        let marker = new AMap.Marker({
          position: new AMap.LngLat(
            this.deviceList[index].lon_int / 1e7,
            this.deviceList[index].lat_int / 1e7
          ),
          title: this.deviceList[index].name + " \n" + title,
          icon: icon,
          offset: new AMap.Pixel(-15, -30),
          zIndex: 50,
        });
        this.map.add(marker);
        this.deviceMarker.push(marker);
      }
    },
    //获取围栏信息
    async getFenceList() {
      let data = {
        page: 0,
        size: 100,
      };
      data.pmd = data.page.toString();
      await requestHttp("fenceList", data).then((res) => {
        this.fenceList = res.data.list?res.data.list:[];
      });
      this.drawFenceList();
    },
    //绘画围栏
    drawFenceList() {
      if (this.polyponList) {
        this.map.remove(this.polyponList);
        this.polyponList = "";
      }
      this.polyponList = [];
      for (let index = 0; index < this.fenceList.length; index++) {
        let strPoint = [];
        for (let j = 1; j < this.fenceList[index].point_list.length + 1; j++) {
          for (let i = 0; i < this.fenceList[index].point_list.length; i++) {
            if (this.fenceList[index].point_list[i].seq == j) {
              let point = new AMap.LngLat(
                this.fenceList[index].point_list[i].lon_int / 1e7,
                this.fenceList[index].point_list[i].lat_int / 1e7
              );
              strPoint.push(point);
              break;
            }
          }
        }
        let polypon = initMaps.drawPolypon(strPoint);
        polypon.id = this.fenceList[index].f_id;
        this.polyponList.push(polypon);
        polypon.setMap(this.map);
      }
    },
    //获取航线信息
    async getMissionList() {
      for (let index = 0; index < this.fenceList.length; index++) {
        let data = {
          f_id: this.fenceList[index].f_id,
          type: 20,
          page: 0,
          size: 100,
        };
        data.pmd = data.page.toString() + data.f_id + data.type.toString();
        await requestHttp("missionList", data).then((res) => {
          if(res.data.list){
            for (let i = 0; i < res.data.list.length; i++) {
            this.missionList.push(res.data.list[i]);
          }
          }
        });
      }
      this.drawRoute();
    },
    //绘制航线信息
    drawRoute() {
      for (let i = 0; i < this.missionList.length; i++) {
        console.log(this.missionList[i]);
        let routePoints = [];
        for (
          let index = 0;
          index < this.missionList[i].point_list.length;
          index++
        ) {
          if (this.missionList[i].point_list[index].seq == index + 1) {
            let point = new AMap.LngLat(
              this.missionList[i].point_list[index].lon_int / 1e7,
              this.missionList[i].point_list[index].lat_int / 1e7
            );
            routePoints.push(point);
            let params = {
              offset: -17,
              clickable: false,
              draggable: false,
            };
            let marker = initMaps.drawMarker(
              index + 1,
              point,
              "marker-edit",
              params
            );
            marker.setMap(this.map);
          }
        }
        let polyline = initMaps.drawPolyline(routePoints);
        (polyline.id = this.missionList[i].m_id), polyline.setMap(this.map);

        // console.log(routePoints)
      }
    },
  },
};
</script>
<style lang="less" scoped>
.outLineMap {
  width: 100%;
  height: 100%;
  background-color: white;
  .el-form{
    width: 30%;
    margin-left: 35%;
    border: 1px solid #eee;
    margin-top:2%;
    padding: 1% 2%;
    box-shadow: 10px 10px 5px #888888;
    text-align: center;
    .title{
      font-size: 16px;
      letter-spacing: 2px;
      margin-bottom: 2%;

    }
    .buttonDiv{
      text-align: left;
      margin-left:30%;
      .el-button{
        width: 30%;
        letter-spacing: 5px;
        &.close{
          color: #0b58de;
          background-color: white;
          
        }
        &.sure{
          color: white;
          background-color: #0b58de;
        }
      }
    }
  }
}
</style>
<style lang="less">
</style>