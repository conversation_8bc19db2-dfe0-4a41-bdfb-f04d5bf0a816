<template>
  <div class="videoList">
    <custom-table
      :column="column"
      :isShowPage="false"
      :data="tableData"
      ref="videoListTable"
    >
      <template #thumb_url="scope">
        <el-image
          style="width: 100px; height: auto"
          :src="scope.row.thumb_url"
          :preview-src-list="[scope.row.thumb_url]"
        >
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
        </el-image>
      </template>
      <template #vr_type="scope">{{ vr_type[scope.row.vr_type] }}</template>
      <template #start_time="scope">
        {{ formatTime(scope.row.start_time) }}
      </template>
      <template #end_time="scope">
        {{ formatTime(scope.row.end_time) }}
      </template>
      <template #create_time="scope">
        {{ formatTime(scope.row.create_time) }}
      </template>
      <template #modified_time="scope">
        {{ formatTime(scope.row.modified_time) }}
      </template>
      <template #operation="scope">
        <el-button
          type="text"
          @click="uploadVideo(scope.row)"
          v-if="!scope.row.file_url && !scope.row.show"
          >{{ language.upload }}</el-button
        >
        <div class="uploading-box" v-if="!scope.row.file_url && scope.row.show">
          <div class="">
            {{ language.uploading }}({{ scope.row.progress }}%)
          </div>
          <el-progress
            :stroke-width="6"
            :percentage="scope.row.progress"
            :show-text="false"
          ></el-progress>
        </div>
        <el-button
          type="text"
          @click="playVideo(scope.row)"
          v-if="scope.row.file_url"
          >{{ language.play }}</el-button
        >
        <!-- <el-button type="text" @click="delVideo(scope.row)">删除</el-button> -->
      </template>
    </custom-table>
    <!-- 视频弹窗 -->
    <video-dialog ref="videoDialog"></video-dialog>
  </div>
</template>
<script>
import customTable from "@/components/customTable/index.vue";
import videoDialog from "@/views/achievementAdmin/dialog/videoDialog.vue";
import { nowDate } from "@/utils/date";
import { removeCookie } from "@/utils/storage";
export default {
  components: {
    customTable,
    videoDialog,
  },
  data() {
    return {
      tableData: [
        // { sn_id: "11111", vr_type: 1 },
        // { sn_id: "2222", vr_type: 0 },
      ],
      column: [
        { label: "设备ID", prop: "sn_id" },
        { label: "录像视频类型", prop: "vr_type" },
        { label: "状态", prop: "state" },
        { label: "录制开始时间", prop: "start_time" },
        { label: "录制结束时间", prop: "end_time" },
        { label: "文件名", prop: "file_name" },
        { label: "文件大小", prop: "file_size" },
        { label: "创建时间", prop: "create_time" },
        { label: "更新时间", prop: "modified_time" },
        { label: "操作", prop: "operation" },
      ],
      vr_type: {},
      nowTime: new Date().getTime(),
      sn_id: "",
      operationId: "",
      tableData1: [],
    };
  },
  computed: {
    ws() {
      return this.$store.state.websocket.ws;
    },
    language() {
      return this.$languagePackage.videoList;
    },
  },
  created() {
    this.$store.commit("setOpenRouter", this.$route.name);
    let query = this.$route.query;
    this.sn_id = query.sn_id;
    this.column = this.language.column;
    this.vr_type = this.language.vr_type;
  },
  mounted() {
    this.$refs.videoListTable.tableLoading = true;
    this.$store.commit("setMultiMessage", {
      key: "videoList",
      message: this.getMessage,
      error: this.getErrorMsg,
    });
    this.sendws();
    // setTimeout(() => {
    //   this.$refs.videoListTable.tableLoading = false;
    // }, 1000);
  },
  methods: {
    sendws() {
      if (!(this.ws && this.ws.ws.readyState == 1)) {
        let now = new Date().getTime();
        if (now - 1000 * 60 * 10 > this.nowTime) {
          this.$refs.videoListTable.tableLoading = true;
          return false;
        }
        setTimeout(() => {
          this.sendws();
        }, 200);
        return false;
      }
      let data = {
        sn_id: this.sn_id,
      };
      this.ws.manualSend(data, 240);
    },
    getMessage(msg_id, data) {
      if (msg_id == 240) {
        this.$refs.videoListTable.tableLoading = false;
        this.tableData = data.list ? data.list : [];
        this.operationID();
      }
      if (msg_id == 242) {
        if (data) {
          let index = this.tableData.findIndex((item) => {
            return item.id == data.id;
          });
          if (index !== -1) {
            data.show = false;
            data.progress = 100;
            this.tableData.splice(index, 1, data);
          }
          this.operationID(data.id, "del");
        } else {
          let index = this.tableData.findIndex((item) => {
            return item.id == this.operationId;
          });
          if (index !== -1) {
            this.$set(this.tableData[index], "show", true);
            this.$set(this.tableData[index], "progress", 0);
          }
          this.operationID(this.operationId, "save");
        }
      }
      if (msg_id == 244) {
        let index = this.tableData.findIndex((item) => {
          return item.id == data.id;
        });
        if (index !== -1) {
          this.$set(this.tableData[index], "show", true);
          this.$set(this.tableData[index], "progress", data.progress);
        }
      }
    },
    operationID(id, type) {
      let idList = [];
      let ids = sessionStorage.getItem("videoItemId");
      sessionStorage.removeItem("videoItemId");
      try {
        if (ids) {
          idList = JSON.parse(ids);
        }
        if (!type) {
          for (let index = 0; index < idList.length; index++) {
            let index = this.tableData.findIndex((item) => {
              return item.id == idList[index];
            });
            if (index !== -1) {
              if (this.tableData[index].file_url) {
                this.operationID(this.tableData[index].id, "del");
              } else {
                this.$set(this.tableData[index], "show", true);
                this.$set(this.tableData[index], "progress", 0);
              }
            }
          }
          return;
        }
        switch (type) {
          case "save":
            idList.push(id);
            break;
          case "del":
            idList = idList.filter((item) => item != id);
            break;
          default:
            break;
        }
        if (idList.length) {
          sessionStorage.setItem("videoItemId", JSON.stringify(idList));
        }
      } catch (error) {
        console.log("数据解析失败");
      }
    },
    getErrorMsg(res) {
      if (res.msg_id && res.msg_id == 240) {
        this.$refs.videoListTable.tableLoading = false;
      }
      if (res.msg_id == 100 && res.code == "40619") {
        let obj = {
          message: "登录已过期，请重新登录",
          tip: "提示",
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        };
        if (this.$language == "english") {
          obj = {
            message: "Login has expired, please log in again",
            tip: "Tips",
            confirmButtonText: "confirm",
            cancelButtonText: "cancel",
          };
        }
        this.$confirm(obj.message, obj.tip, {
          confirmButtonText: obj.confirmButtonText,
          cancelButtonText: obj.cancelButtonText,
          type: "warning",
          customClass: "login-40263",
        })
          .then(() => {
            this.$store.commit("setUserInfo", {});
            removeCookie("token");
            this.$router.push({
              name: "login",
            });
          })
          .catch(() => {})
          .finally(() => {
            this.$refs.videoListTable.tableLoading = false;
          });
      }
    },
    uploadVideo(item) {
      let data = {
        id: item.id,
        sn_id: this.sn_id,
      };
      this.ws.manualSend(data, 242);
      this.operationId = item.id;
    },
    playVideo(item) {
      let data = {
        o_url: item.file_url,
        p_url: item.thumb_url,
      };
      this.$refs.videoDialog.openDialog(data);
    },
    delVideo(item) {},
    formatTime(time) {
      if (!time) {
        return "";
      }
      return nowDate(time).dateTime;
    },
  },
};
</script>
<style lang="less" scoped>
.videoList {
  background-color: rgba(0, 0, 0, 1);
  padding: 20px;
  padding-bottom: 0;
  box-sizing: border-box;
  height: 100%;
  .uploading-box {
    width: 80%;
    margin: auto;
    color: #66b1ff;
  }
}
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .videoList {
    padding: @zoomIndex * 20px;
    padding-bottom: 0;
  }
}
</style>
<style lang="less">
.videoList {
  .image-slot {
    background-color: #f5f7fa;
    color: #909399;
    height: 50px;
    line-height: 50px;
    font-size: 24px;
  }
}
</style>