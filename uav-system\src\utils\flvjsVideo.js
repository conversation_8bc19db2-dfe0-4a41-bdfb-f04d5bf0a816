// import flvjs from '@/assets/flvjs/index'
import "@/assets/js/flv.min.js"
class FlvjsVideo {

    player = null; // 播放器实例
    videoConfig = null; // 视频一些配置
    videoElement = null;
    /**
     * 创建flv播放
     * @param {videoElement} 挂载的videdom
     * @param { videlUrl } 视频播放地址
     */
    createFlvjs = (videoConfig) => {
        this.videoConfig = videoConfig;

        let {
            type = "flv", // 文件类型，不填默认flv
                videoId, // 挂载video的id
                videoUrl, // 播放的url
                multiple = 1, // 播放倍速
                width = 500, // 播放器宽度
                height = 300, // 播放器高度
                autoPlay = true, // 是否自动播放
                controls = false, // 是否显示插件
                isLive = true, // 是否直播流
                autoCleanupSourceBuffer = true, // 对SourceBuffer进行自动清理
                enableStashBuffer = false, // 启用IO隐藏缓冲区
                stashInitialSize = 50, //指示IO暂存缓冲区的初始大小
                onCanplay, // 视频准备就绪时触发
                onWaiting, // 视频因需要缓存下一帧而暂停时触发
                onPause, // 视频暂停
                onEnded, // 视频播放结束
                onDurationchange, // 视频时长更改时触发
                onError, // 发生错误错误
                httpError, // 网络错误
        } = videoConfig;

        this.videoElement = document.getElementById(videoId);

        if (flvjs.isSupported()) {
            this.player = flvjs.createPlayer({
                type: type, // 文件类型
                url: videoUrl, // 播放路径
                hasAudio: false,
                hasVideo: true,
                isLive: isLive, // 是否直播流
                width: width,
                height: height
            }, {
                enableStashBuffer: enableStashBuffer, // 启用IO隐藏缓冲区。如果您需要实时（最小延迟）来进行实时流播放，则设置为false，但是如果网络抖动，则可能会停顿。
                autoCleanupSourceBuffer: autoCleanupSourceBuffer, // 对SourceBuffer进行自动清理
                stashInitialSize: stashInitialSize, // 指示IO暂存缓冲区的初始大小。默认值为384KB。指出合适的尺寸可以改善视频负载/搜索时间。
                lazyLoadMaxDuration: 1 * 60, // 缓存数据时间
            })

            if (!this.videoElement) {
                return Promise.reject("video标签获取失败，请重试");
            }

            this.player.attachMediaElement(this.videoElement)
            this.player.load()

            if (autoPlay) {
                this.player.play()
                    // 每播放一帧都会触发
                if (isLive) {
                    this.videoElement.addEventListener("timeupdate", this.timeupdate);
                }
            }

            this.videoElement.defaultPlaybackRate = multiple; // 设置倍速
            this.videoElement.controls = controls; // 是否显示播放按钮之类的控件
            this.videoElement.muted = 'muted'; // 静音播放,自动播放必须设置为静音

            // 视频准备就绪时触发
            this.videoElement.oncanplay = (row) => {
                onCanplay && onCanplay(row);
            }

            // 浏览器因需缓存下一帧触发
            this.videoElement.onwaiting = (row) => {
                onWaiting && onWaiting(row);
            }

            // 播放暂停
            this.videoElement.onpause = (row) => {
                onPause && onPause(row);
            }

            // 播放结束触发
            this.videoElement.onended = () => {
                onEnded && onEnded(row);
            }

            this.videoElement.ondurationchange = () => {
                onDurationchange && onDurationchange(row);
            }

            // 监听错误事件
            this.player.on(flvjs.Events.ERROR, (err, errdet) => {
                let errorHint = "";
                if (err == flvjs.ErrorTypes.MEDIA_ERROR) {
                    errorHint = "媒体错误";
                    if (errdet == flvjs.ErrorDetails.MEDIA_FORMAT_UNSUPPORTED) {
                        errorHint = "媒体格式不支持";
                    }
                }
                if (err == flvjs.ErrorTypes.NETWORK_ERROR) {
                    httpError && httpError(err);
                    if (errdet == flvjs.ErrorDetails.NETWORK_STATUS_CODE_INVALID) {
                        errorHint = "http状态码异常";
                    }
                }
                if (err == flvjs.ErrorTypes.OTHER_ERROR) {
                    errorHint = "其他异常：";
                }

                onError && onError(errorHint, err, errdet);
            })

            return Promise.resolve(this.videoElement);

        } else {
            console.error("抱歉，浏览器不支持播放，请切换浏览器");
        }
    }

    timeupdate = () => {
        this.frameSkip();
    }

    frameSkip = () => {
        if (this.player && this.player.buffered && this.player.buffered.length) {
            let end = this.player.buffered.end(0) //获取当前buffered值
            let diff = end - this.player.currentTime //获取buffered与currentTime的差值
            console.log("时间差---->", diff);
            if (diff >= 0.8) {
                this.player.currentTime = end - 0.1 //手动跳帧 
            }
        }
    }

    /** 手动播放 */
    manualPlay = () => {
        this.videoElement.addEventListener("timeupdate", this.timeupdate);
        this.player.play();
    }

    // 暂停
    manuaStop = () => {
        this.player.pause();
        this.videoElement.removeEventListener("timeupdate", this.timeupdate);
    }

    /** flvjs实例销毁 */
    flvjsDestroy = () => {
        if (!this.player) {
            return false;
        }
        this.player.pause()
        this.player.unload()
        this.player.destroy()
        this.videoElement.removeEventListener("timeupdate", this.timeupdate);
        this.player.detachMediaElement();
        this.player = null; // 播放器实例
        this.videoConfig = null; // 视频一些配置
        this.videoElement = null;
    }
}

export default FlvjsVideo;