<!-- 巡检排行榜 -->
<template>
  <bg-layout :title="language.title" class="task-list-data">
    <template v-slot:content>
      <div class="" style="height: 100%; padding: 0 20px">
        <!--tab导航 -->
        <div class="tab-nav">
          <div
            class="tab-row"
            v-for="(item, index) in taskType"
            :key="index"
            @click="cutTab(item)"
          >
            <img
              :src="
                param.task_state !== item.value
                  ? imgList.paihangbTag
                  : imgList.paihangbTagSelect
              "
              alt=""
            />
            <div class="tab-row-title" :style="titleStyle">
              {{ item.label }}
            </div>
          </div>
        </div>

        <!-- 表格数据 -->
        <div class="table-data">
          <custom-table
            v-if="showTabel"
            :column="column"
            urlName="taskList"
            ref="taskList"
            :isShowPage="false"
            :params="param"
            pmd="page"
            :isShowIndex="false"
            :emptyCopy="false"
            class="min-table"
            headerRowClassName="custom-table-header-1"
          >
            <template #task_tms="scope">
              {{ returnTime(scope.row.task_tms) }}
            </template>
          </custom-table>
          <!-- <scroll-list
            class="table-tbody scrollbar-style"
            ref="scrollList"
            urlName="taskList"
            pmd="page"
            :params="params"
            :pageSize="200"
          >
            <template v-slot:content="scope">
              <div
                class="table-tbody-tr"
                style="width: 100%; display: flex"
                :style="tableTrStyle(index, scope)"
                v-for="(item, index) in scope.data"
                :key="index"
              >
                <div style="width: 28px" class="tbody-cell">
                  <div class="table-index">
                    {{ index + 1 }}
                  </div>
                </div>
                <div
                  style="
                    width: 60%;
                    text-align: left;
                    color: rgb(36, 191, 223);
                    letter-spacing: 2px;
                  "
                  class="tbody-cell ml14"
                >
                  {{ item.name }}
                </div>
                <div style="width: 30%" class="tbody-cell">
                  <span v-show="params.sort == 10">
                    {{ item.flight_count }} {{ units.time }}
                  </span>
                  <span v-show="params.sort == 20">
                    {{ item.flight_mileage }} {{ units.m }}
                  </span>
                  <span v-show="params.sort == 30">
                    {{ item.result_count }} {{ units.PCs }}
                  </span>
                </div>
              </div>
            </template>
          </scroll-list> -->
        </div>
      </div>
    </template>
  </bg-layout>
</template>

<script>
import bgLayout from "../components/bgLayout.vue";
import paihangbTag from "@/assets/img/home/<USER>";
import paihangbTagSelect from "@/assets/img/home/<USER>";
import scrollList from "@/components/scrollList/index.vue";
import customTable from "@/components/customTable/index";
export default {
  components: {
    bgLayout,
    scrollList,
    customTable
  },
  data() {
    return {
      imgList: {
        paihangbTag,
        paihangbTagSelect
      },
      param: {
        task_state: 1,
        size: 200,
        page: 0
      },
      isInit: false,
      showTabel: false
    };
  },
  computed: {
    language() {
      return this.$languagePackage.home.taskList;
    },
    taskType() {
      return this.language.taskType;
    },
    column() {
      return this.language.column;
    },

    units() {
      return this.$languagePackage.unit;
    },
    titleStyle() {
      return {
        "letter-spacing": this.$language == "chinese" ? "3px" : 0
      };
    }
  },
  mounted() {
    setTimeout(() => {
      this.showTabel = true;
    }, 3000);
  },
  methods: {
    returnTime(tms) {
      let date = new Date(tms);
      let y = date.getFullYear();
      let m = (date.getMonth() + 1).toString().padStart(2, "0");
      let d = date
        .getDate()
        .toString()
        .padStart(2, "0");
      let h = date
        .getHours()
        .toString()
        .padStart(2, "0");
      let mm = date
        .getMinutes()
        .toString()
        .padStart(2, "0");
      let s = date
        .getSeconds()
        .toString()
        .padStart(2, "0");
      return y + "-" + m + "-" + d + " " + h + ":" + mm + ":" + s;
    },
    tableTrStyle: function(index, scope) {
      let botder = index % 2 == 0 ? "none" : "1px solid #2f6099";
      return {
        background: index % 2 == 0 ? "" : "rgba(5, 21, 45, 0.7)",
        borderTop: botder,
        borderBottom: botder,
        animationDelay:
          this.isInit || scope.current != 1 ? "0s" : 1 + 0.2 * index + "s"
      };
    },
    cutTab: function(item) {
      this.isInit = true;
      this.param.task_state = item.value;
      this.$refs.taskList.refresh();
    }
  }
};
</script>

<style lang="less" scoped>
.task-list-data {
  //   padding: 0 20px;

  .tab-nav {
    display: flex;
    // color: #fff;
    .tab-row {
      flex-grow: 1;
      cursor: pointer;
      height: 34px;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      img {
        width: 100%;
        height: 100%;
      }
      .tab-row-title {
        letter-spacing: 3px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 34px;
      }
    }
  }

  .table-data {
    height: calc(100% - 94px);
    position: relative;
    // margin: 10px;
    margin-top: 10px;

    // .table-tbody {
    //   height: 100%;
    //   overflow-x: hidden;
    //   overflow-y: auto;
    //   //   border: 1px solid #2f6099;
    //   border-bottom: none;
    //   .table-tbody-tr {
    //     animation-name: tbodyTr;
    //     animation-duration: 0.4s;
    //     animation-fill-mode: forwards; // 保留动画最后的状态
    //     opacity: 0;
    //   }
    //   @keyframes tbodyTr {
    //     0% {
    //       transform: rotateX(90deg);
    //     }
    //     50% {
    //       opacity: 1;
    //     }
    //     100% {
    //       transform: rotateX(0deg);
    //       opacity: 1;
    //     }
    //   }
    // }

    // .tbody-cell {
    //   padding: 8px 0;
    //   font-size: 12px;
    //   // color: #fff;
    //   text-align: center;
    // }
  }
}
</style>
<style lang="less">
.min-table {
  .el-table__empty-text {
    line-height: 16px !important;
  }
  .custom-table-header-1 {
    background-color: #0a0a54;
    // color: rgba(151, 151, 151, 0.479);

    .el-table__cell {
      color: #61b3ea;
      background-color: rgba(0, 0, 0, 0);
    }
  }
  .el-table__cell {
    vertical-align: top;
  }
}
</style>
