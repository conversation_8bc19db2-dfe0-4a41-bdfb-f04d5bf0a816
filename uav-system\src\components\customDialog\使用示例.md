## 引入
## import customDialog from "@/components/customDialog/index.vue";

## 使用
<custom-dialog>
    <!-- 如果头部需要自定义，则使用header卡槽 -->
    <template v-slot:header>
    </template>
    <!-- 插入中间内容 -->
    <template v-slot:main>
    </template>
    <!-- 如果底部需要自定义，则使用footer卡槽 -->
    <template v-slot:footer>
    </template>
</custom-dialog>

## 属性
##  title 展示标题
##  visible 是否显示
##  submitLoading 提交按钮loading状态
##  labelWidth  和表单label-width值一直，默认120px

## 自定义事件
##  submit  点击确认按钮触发
##  cancel  点击取消按钮触发
##  close   关闭对话框触发