<template>
  <div class="dialogType">
    <el-dialog
      :title="dialogLanguage.title"
      :visible.sync="chooseType"
      :close-on-click-modal="true"
      :show-close="false"
      width="50%"
      center
      @close="closeEvent"
      :modal="false"
    >
      <div
        class="tipInfo"
        v-if="
          !checkTypes ||
          checkTypes == 20 ||
          checkTypes == 50 ||
          checkTypes == 40
            ? false
            : true
        "
      >
        {{ dialogLanguage.undeveloped }}
      </div>
      <div class="content-type" :class="$loadingEnUI?'content-type-en':''">
        <el-button
          v-for="item in typeList"
          :key="item.value"
          @click="chooseEvent(item.value)"
          :class="
            item.value == 20 || item.value == 50 || item.value == 40
              ? item.value == type
                ? 'active'
                : ''
              : 'noActive'
          "
          @mouseover.native="enter(item.value)"
          @mouseout.native="leave()"
          style="border: 1px solid rgba(0, 0, 0, 0.75)"
        >
          <el-image
            fit="fill"
            :src="item.type_img_1"
            v-show="
              (item.value == 20 || item.value == 50 || item.value == 40) &&
              item.value == type
            "
          ></el-image>
          <el-image
            fit="fill"
            v-show="
              (item.value == 20 || item.value == 50 || item.value == 40) &&
              item.value != type
            "
            :src="item.type_img_2"
          ></el-image>
          <el-image
            fit="fill"
            v-show="!(item.value == 20 || item.value == 50 || item.value == 40)"
            :src="item.type_img_3"
          ></el-image>
          <div>{{ $loadingEnUI ? item.name_en:item.name_cn }}</div>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "dialogType",
  props: {
    dialogLanguage: {
      type: [Object, Array],
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      chooseType: false,
      type: 0,
      checkTypes: 0,
    };
  },
  methods: {
    //打开选择框
    openDialog() {
      this.chooseType = true;
    },
    //点击选择类型
    chooseEvent(e) {
      if (!this.checkTypes) {
        this.checkTypes = e;
        if (
          this.checkTypes == 20 ||
          this.checkTypes == 50 ||
          this.checkTypes == 40
        ) {
          this.chooseType = false;
        } else {
          setTimeout(() => {
            this.checkTypes = 0;
          }, 1000);
        }
      }
    },
    enter(e) {
      this.type = e;
    },
    leave() {
      this.type = 0;
    },
    closeEvent() {
      let typeItem = "";
      for (let index = 0; index < this.typeList.length; index++) {
        if (this.typeList[index].value == this.checkTypes) {
          typeItem = this.typeList[index];
        }
      }
      if (!typeItem) {
        let str = this.$store.state.route.checkType;
        if (parseInt(str) == 0) {
          typeItem = "";
        } else {
          typeItem = 0;
        }
      }
      this.$store.commit("checkType", typeItem);
      this.checkTypes = 0;
    },
  },
  computed: {
    typeList() {
      return this.$store.state.route.typeList;
    },
  },
};
</script>
<style lang="less" scoped>
@media screen and(min-width:1200px) {
  @zoomIndex: 100vw / 1920px;
  .dialogType {
    .el-dialog {
      .tipInfo {
        font-size: @zoomIndex * 12px !important;
        letter-spacing: @zoomIndex * 2px !important;
        border-radius: @zoomIndex * 6px !important;
        margin-bottom: @zoomIndex * 20px !important;
        padding: @zoomIndex * 5px !important;
      }
      .content-type {
        .el-button {
          min-width: @zoomIndex * 80px !important;

          padding: @zoomIndex * 6px @zoomIndex * 12px !important;
          font-size: @zoomIndex * 16px !important;
        }
        &.content-type-en{
          .el-button{
            font-size: @zoomIndex * 14px !important;
          }
        }
      }
    }
  }
}
.dialogType {
  .el-dialog {
    .tipInfo {
      width: 33%;
      margin-left: 33.5%;
      text-align: center;

      font-size: 12px;
      letter-spacing: 2px;
      border-radius: 6px;
      margin-bottom: 20px;
      padding: 5px;
    }
    .content-type {
      width: 70%;
      margin-left: 15%;
      .el-button {
        text-align: center;
        margin: 1%;
        width: 23%;
        min-width: 80px;

        padding: 6px 12px;
        font-size: 16px;
        font-weight: 550;
        .el-image {
          width: 90%;
        }
      }
      &.content-type-en{
        .el-button{
          font-size: 14px;
        }
      }
    }
  }
}
</style>
<style lang="less">
.dialogType {
  .el-dialog {
    top: 15vh !important;
    .el-dialog__header {
      text-align: left !important;
      .el-dialog__title {
        color: white !important;
      }
    }
    .el-dialog__body {
      padding-top: 0 !important;
    }
  }
}
</style>