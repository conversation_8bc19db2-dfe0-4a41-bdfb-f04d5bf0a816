<template>
  <div class="calibrationContent">
    <div class="main-content">
      <el-image
        :src="toList[toStep].imgSrc"
        v-if="!resultCode && toStep"
      ></el-image>
      <el-button v-if="!resultCode && !toStep" @click="startEvent">{{
        language.startTitle
      }}</el-button>
      <div class="main-content-1" v-if="resultCode">
        <el-image :src="resultList[resultCode].imgSrc"></el-image>
        <div :class="'result-' + resultCode">
          {{ resultList[resultCode].label }}
        </div>
      </div>
    </div>
    <div
      class="font-content"
      :class="toStep && !toList[toStep].isNext ? 'content-margin' : ''"
    >
      <div class="" v-if="!resultCode && toStep">
        {{ toList[toStep].label }}
      </div>
      <div class="" v-if="!resultCode && toStep && toList[toStep].isNext">
        <el-button @click="stepPlay">{{ language.nextStep }}</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    calibrationItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    websocket: {
      type: [Object, String],
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      imgList: [],
      list: {
        gyroscope: {
          1: {
            label: "将飞行器水平垂直放置在平坦平台，保持飞行器静止状态",
            isNext: true,
          },
          2: {
            label:
              "将飞行器水平垂直放置在平坦平台，状态指示灯进入快闪，当指示灯为红绿彩灯时，表示校准成功。",
            isNext: false,
          },
        },
        compass: {
          1: {
            label: "首先，取下无人机桨叶",
            isNext: true,
          },
          2: {
            label: "将飞行器机头垂直向上，沿水平方向旋转720°。",
          },
          3: {
            label: "将飞行器放平，在沿水平方向旋转720度。",
          },
        },
        acceleration: {
          1: {
            label: "将飞行器水平垂直放置在平坦平台。",
          },
          2: {
            label: "将飞行器背面水平垂直放置在平坦平台。",
          },
          3: {
            label: "将飞行器左侧面水平垂直放置在平坦平台。",
          },
          4: {
            label: "将飞行器右侧面水平垂直放置在平坦平台。",
          },
          5: {
            label: "将飞行器摄像头朝下水平垂直放置在平坦平台。",
          },
          6: {
            label: "将飞行器摄像头朝上水平垂直放置在平坦平台。",
          },
        },
      },
      toList: {},
      resultList: {
        success: {
          label: "校准成功",
        },
        error: {
          label: "校准失败，请重新校准",
        },
        reStart: {
          label: "校准成功后请重启飞行器再开始使用",
        },
      },
      resultCode: "",
      loopTimeout: "",
      sendSuccess: false,
      getChangeCode: false,
      cmd_type: {
        gyroscope: 20011, //陀螺仪
        compass: 20013, //电子罗盘
        acceleration: 20012, //加速度
        //校准对应的指令参数值
      },
    };
  },
  created() {
    this.toList = this.list[this.calibrationItem.id];
    for (const key in this.toList) {
      this.toList[key].label = this.language[this.calibrationItem.id][key];
    }
    for (const key in this.resultList) {
      this.resultList[key].label = this.language.resultList[key];
    }
    this.toStep = 1;
    this.getImgList();
  },
  computed: {
    language() {
      return this.$languagePackage.equipment.calibration;
    },
  },
  methods: {
    getMessage(msg_id, data) {
      if (!this.sendSuccess) {
        return false;
      }
      if (msg_id == 414) {
        let cmd_type = data.cmd_type;
        if(cmd_type!==this.cmd_type[this.calibrationItem.id]){
          return false
        }
        switch (cmd_type) {
          case 20011:
            this.resultGyroscope(data.state);
            break;
          case 20012:
            this.resultAcceleration(data.state);
            break;
          case 20013:
            this.resultCompass(data.state);
            break;
          default:
            break;
        }
      }
    },
    resultGyroscope(state) {
      switch (state) {
        case 2:
          if (this.resultCode != "reStart") {
            this.resultCode = "success";
            setTimeout(() => {
              this.resultCode = "reStart";
            }, 1000);
          }
          break;
        case 3:
        case 4:
          this.resultCode = "error";
          break;
        default:
          break;
      }
    },
    resultAcceleration(state) {
      this.toStep = state;
      this.$forceUpdate();
      if (this.toStep > 6) {
        if (state == 7) {
          if (this.resultCode != "reStart") {
            this.resultCode = "success";
            setTimeout(() => {
              this.resultCode = "reStart";
            }, 1000);
          }
        } else if (state == 8 || state == 9) {
          this.resultCode = "error";
        }
      }
    },
    resultCompass(state) {
      if (state !== 1 && state !== 2) {
        this.loopTimeout && clearTimeout(this.loopTimeout);
      }
      switch (state) {
        case 1:
          if (this.toStep !== 2 || !this.getChangeCode) {
            this.loopTimeout && clearTimeout(this.loopTimeout);
            this.toStep = 2;
            this.startAnimation(0);
            this.getChangeCode = true;
          }
          break;
        case 2:
          if (this.toStep !== 3) {
            this.loopTimeout && clearTimeout(this.loopTimeout);
            this.toStep = 3;
            this.startAnimation(0);
          }
          break;
        case 3:
          if (this.resultCode != "reStart") {
            this.resultCode = "success";
          }
          setTimeout(() => {
            this.resultCode = "reStart";
          }, 1000);
          break;
        case 4:
        case 5:
          this.resultCode = "error";
          break;
        default:
          break;
      }
    },
    getImgList() {
      let id = this.calibrationItem.id;
      switch (id) {
        case "gyroscope":
          let img = this.resultImg("gyroscope_1");
          for (const key in this.toList) {
            this.toList[key].imgSrc = img;
          }
          break;
        case "compass":
          this.toList[1].imgSrc = this.resultImg("compass_1");
          break;
        case "acceleration":
          for (const key in this.toList) {
            this.toList[key].imgSrc = this.resultImg(id + "_" + key);
          }
          this.toStep = 1;
          let data = {
            cmd_type: this.cmd_type[this.calibrationItem.id],
          };
          this.websocket && this.websocket.manualSend(data, 414);
          setTimeout(() => {
            this.sendSuccess = true;
          }, 300);

          break;
        default:
          break;
      }
      for (const key in this.resultList) {
        if (key == "reStart") {
          this.resultList[key].imgSrc = this.resultImg("success");
        } else {
          this.resultList[key].imgSrc = this.resultImg(key);
        }
      }
    },
    resultImg(name) {
      return require(`@/assets/img/equipment/${name}.png`);
    },
    stepPlay() {
      let id = this.calibrationItem.id;
      switch (id) {
        case "gyroscope":
          this.toStep = "";
          break;
        case "compass":
          this.toStep = 2;
          this.toList[this.toStep].imgSrc = this.resultImg(
            "compass_" + this.toStep + "-1"
          );
          this.$forceUpdate();
          let data = {
            cmd_type: this.cmd_type[this.calibrationItem.id],
          };
          this.websocket && this.websocket.manualSend(data, 414);
          setTimeout(() => {
            this.sendSuccess = true;
          }, 300);
          break;
        case "acceleration":
          break;
        default:
          break;
      }
      this.$forceUpdate();
    },
    startEvent() {
      this.toStep = 2;
      this.$forceUpdate();
      let data = {
        cmd_type: this.cmd_type[this.calibrationItem.id],
      };
      this.websocket && this.websocket.manualSend(data, 414);
      setTimeout(() => {
        this.sendSuccess = true;
      }, 300);
      // setTimeout(() => {
      //   let ra = Math.random();
      //   if (ra > 0.5) {
      //     this.resultCode = "success";
      //     setTimeout(() => {
      //       this.resultCode = "reStart";
      //     }, 1000);
      //   } else {
      //     this.resultCode = "error";
      //   }
      // }, 3000);
    },
    startAnimation(index) {
      this.toList[this.toStep].imgSrc = this.resultImg(
        "compass_" + this.toStep + "-" + (index + 1)
      );
      this.$forceUpdate();
      this.loopTimeout = setTimeout(() => {
        if (index >= 7) {
          this.startAnimation(0);
          return false;
        }
        this.startAnimation(index + 1);
      }, 500);
    },
    resultEvent() {
      setTimeout(() => {
        let ra = Math.random();
        if (ra > 0.5) {
          this.resultCode = "success";
          setTimeout(() => {
            this.resultCode = "reStart";
          }, 2000);
        } else {
          this.resultCode = "error";
        }
      }, 2000);
    },
  },
};
</script>
<style lang="less" scoped>
.calibrationContent {
  background-color: #fff;
  border-radius: 0 0 12px 12px;
  .main-content {
    // padding-bottom: 30px;
    text-align: center;
    height: 400px;
    line-height: 400px;
    .el-image {
      width: 100%;
    }
    .main-content-1 {
      height: 100%;
      width: 100%;
      overflow: hidden;
      line-height: 94%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      .el-image {
        width: 14%;
      }
      .result-success,
      .result-reStart {
        font-size: 24px;
        color: #26ce00;
        margin-top: 20px;
      }
      .result-error {
        font-size: 24px;
        color: #ff554f;
        margin-top: 20px;
      }
    }
    .el-button {
      width: 200px;
      color: #fff8f8;
      background-color: #000;
      font-size: 24px;
      border-radius: 8px;
      border: none;
    }
  }
  .font-content {
    padding: 20px 40px 40px 40px;
    background-color: #2e2b2b;
    border-radius: 0 0 10px 10px;
    font-size: 18px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &.content-margin {
      justify-content: center;
    }

    .el-button {
      padding: 5px 20px;
      color: #1c1e21;
      background-color: #fff;
      border: none;
      font-size: 16px;
    }
  }
}
</style>