<!-- 遥感页面模块页面参数说明布局 -->
<template>
  <div class="instructions-layout">
    <div class="left-top-content" :style="contentStyle">
      <div class="content-title">{{ title }}</div>
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    direction: {
      type: [String, Object],
      default: "rightTop",
    },
  },
  data() {
    return {};
  },
  computed: {
    contentStyle() {
      let type = this.direction;
      if (typeof type === "object") {
        return type;
      }

      let styles = {
        left: "100%",
        top: 0,
        marginLeft: "20px",
      };
      if (type == "leftTop") {
        styles = {
          right: "100%",
          top: 0,
          marginRight: "20px",
        };
      }

      return styles;
    },
  },
};
</script>

<style lang="less" scoped>
@media screen and(min-width: 1920px) {
  @radio: 100vw / 1920px;
  .instructions-layout {
    @size: @radio * 12px;
    width: calc(100% - @size);
    height: calc(100% - @size);
    .left-top-content {
      width: @radio * 120px !important;
    }
    .content-title {
      border-radius: @radio * 10px !important;
      height: @radio * 32px !important;
      padding: 0 @radio * 16px !important;
      font-size: @radio * 12px !important;
      margin-bottom: @radio * 10px !important;
    }
  }
}

.instructions-layout {
  position: relative;
  width: calc(100% - 12px);
  height: calc(100% - 12px);
  .left-top-content {
    position: absolute;
    width: 120px;
  }
  .content-title {
    border-radius: 10px;
    display: flex;
    align-items: center;
    height: 32px;
    font-weight: 700;
    padding: 0 16px;
    font-size: 12px;
    margin-bottom: 10px;
  }
}
</style>