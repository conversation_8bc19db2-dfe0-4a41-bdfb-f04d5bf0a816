import { wgs84_to_gcj02 } from './wgs84_to_gcj02'

function get(x, y) { return x.getElementsByTagName(y); }

function getChildren(x) {
    let value = {}
    if (x.children.length) {
        for (let index = 0; index < x.children.length; index++) {
            if (x.children[index].tagName !== "mis:actions") {
                if (value[x.children[index].tagName]) {
                    if (value[x.children[index].tagName].length) {
                        value[x.children[index].tagName].push(getChildren(x.children[index]))
                    } else {
                        value[x.children[index].tagName] = [value[x.children[index].tagName]]
                        value[x.children[index].tagName].push(getChildren(x.children[index]))
                    }
                } else {
                    value[x.children[index].tagName] = getChildren(x.children[index])
                }
            } else {
                if (value.actions) {
                    value.actions.push(getChildren(x.children[index]))
                } else {
                    value.actions = [getChildren(x.children[index])]
                }
            }
        }
    } else {
        if (x.tagName == "mis:actions") {
            let a = {
                id: x.textContent,
                label: attr(x, 'label')
            }
            if (a.id !== "ShootPhoto") {
                a.param = attr(x, 'param')
            }
            value = a
        } else if (x.tagName == "coordinates") {
            let a = x.textContent.split(" ")
            if (a.length > 1) {
                value = []
                for (let index = 0; index < a.length; index++) {
                    value.push(a[index].split(","))
                }
            } else {
                value = a[0].split(",")
            }
        } else {
            value = x.textContent
        }
    }
    return value
}

function attr(x, y) { return x.getAttribute(y); }

function get1(x, y) {
    var n = get(x, y);
    return n.length ? n[0] : null;
}

function get2(x, y) {
    var n = get_(x, y);
    return n.length ? n[0] : null;

}

function norm(el) {
    if (el.normalize) {
        el.normalize();
    }
    return el;
}

function nodeVal(x) {
    if (x) { norm(x); }
    return (x && x.textContent) || '';
}


function getPlacemark(x) {

    let pointList = {
        name: nodeVal(get1(x, 'name')),
        visibility: nodeVal(get1(x, 'visibility')),
        description: nodeVal(get1(x, 'description')),
        action: [],
    }
    let ExtendedData = get1(x, 'ExtendedData')
    for (let index = 0; index < ExtendedData.children.length; index++) {
        let title = ExtendedData.children[index]
        if (title.tagName == "mis:actions") {
            let a = {
                id: ExtendedData.children[index].textContent,
                label: attr(ExtendedData.children[index], 'label')
            }
            if (a.id !== "ShootPhoto") {
                a.param = attr(ExtendedData.children[index], 'param')
            }
            pointList.action.push(a)
        }
        // pointList.ExtendedData[title] = nodeVal(get1(ExtendedData, ExtendedData.children[index]))

    }
    let points = get1(x, 'Point')
    for (let index = 0; index < points.children.length; index++) {
        let title = points.children[index].tagName
        pointList[title] = points.children[index].textContent
        if (title == "coordinates") {
            pointList[title] = pointList[title].split(',')
        }

    }
    return pointList


}

function getJson(e) {
    let points = {
        name: '',
        points: []
    }
    let pointType = ''
    if (e.Placemark.ExtendedData) {
        if (e.Placemark.ExtendedData.Data && e.Placemark.ExtendedData.Data.value) {
            pointType = e.Placemark.ExtendedData.Data.value
        }
    }
    if (e.Folder && e.Folder.Placemark.length) {
        for (let index = 0; index < e.Folder.Placemark.length; index++) {
            let r = wgs84_to_gcj02(parseFloat(e.Folder.Placemark[index].Point.coordinates[0]), parseFloat(e.Folder.Placemark[index].Point.coordinates[1]))
            let c = {
                lng: r[0],
                lat: r[1],
                height: e.Folder.Placemark[index].Point.coordinates[2],
                altitudeMode: e.Folder.Placemark[index].Point.altitudeMode,
                action: e.Folder.Placemark[index].ExtendedData.actions
            }
            points.points.push(c)
        }
    } else {
        for (let index = 0; index < e.Placemark.LineString.coordinates.length; index++) {
            if (e.Placemark.LineString.coordinates[index][0]) {
                let r = []
                if (pointType == "GCJ-02") {
                    r = [e.Placemark.LineString.coordinates[index][0], e.Placemark.LineString.coordinates[index][1]]
                } else {
                    r = wgs84_to_gcj02(e.Placemark.LineString.coordinates[index][0], e.Placemark.LineString.coordinates[index][1])
                }
                let p = {
                    lng: r[0],
                    lat: r[1],
                    height: e.Placemark.LineString.coordinates[index][2],
                }
                points.points.push(p)

            }

        }
    }
    if (e.name) {
        points.name = e.name
    } else {
        points.name = e.Placemark.name
    }

    return points
}
export function changeKML(xml) {

    let returnData = {}
    let kml = get1(xml, "Document").children
    for (let index = 0; index < kml.length; index++) {
        returnData[kml[index].tagName] = getChildren(kml[index])
    }
    return getJson(returnData)

}