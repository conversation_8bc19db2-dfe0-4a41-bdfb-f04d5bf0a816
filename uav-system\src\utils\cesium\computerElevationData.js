import axios from 'axios'
import { computedMapMethods } from "./computedMapMethods";
import { getLonAndLat } from '../getCoordinate'
let cancel = []
    //计算两点间的高程
export function computerElevationPoints(point1, point2, oneRequestNum, progressFun, distance, map, id) {
    cancel.forEach(x => {
        if (x[id] && typeof x[id] === 'function') {
            x[id]("终止请求")
        }
    })
    let dis = computedMapMethods("computedDistance", { point1, point2, defaultHeight: true })
    let dis1 = parseInt(dis / distance)
    let nums = (dis % distance) >= 1 ? dis1 + 1 : dis1
    let distance1 = nums !== 0 ? (dis / nums) : 0
    return new Promise((resolve, reject) => {
        asyncPools(oneRequestNum, getArrayList(point1, point2, map, distance1),
            getZoomHeight, progressFun, id).then(res => {
            let data = {
                arr: res,
                distance: parseInt(distance1 * 100)
            }
            resolve(data)
        })
    })
}
//请求
function getZoomHeight(aPoints, id) {
    return new Promise((resolve, reject) => {
        axios({
            url: process.env.API_ROOT + "/carpet?points=" + aPoints.ap,
            cancelToken: new axios.CancelToken(c => {
                cancel.push({
                    [id]: c
                })
            })
        }).then(res => {
            if (res.status == 200) {
                let obj = {
                    points: aPoints,
                    height: 0
                }
                obj.height = res.data.data.stats.max * 100
                resolve(obj)
                    // maxHeight.push(res.data.data.stats.max)
            } else {
                reject('失败')
            }
        }).catch(() => {
            reject('失败')
        })
    })
}
//计算外扩点是否需要切分
export function getNewPoint(point1, point2, map) {
    let limitWidth = 30
    let angle1 = computedMapMethods("calcAngle", { points: [point2, point1], map: map, paramsPoint: true })
    let angle2 = computedMapMethods("calcAngle", { points: [point1, point2], map: map, paramsPoint: true })
    angle1 = (angle1 + 45) > 180 ? (angle1 + 45 - 360) : (angle1 + 45);
    angle2 = (angle2 + 45) > 180 ? (angle2 + 45 - 360) : (angle2 + 45);
    let a1 = getLonAndLat(point1.lng, point1.lat, angle1, 2 * limitWidth * Math.sqrt(2))
    let a2 = getLonAndLat(point2.lng, point2.lat, angle2, 2 * limitWidth * Math.sqrt(2))
    let ap = a1.lat + ',' + a1.lng + ',' + a2.lat + ',' + a2.lng
    return ap
}
//计算两点间间隔的坐标
export function getIntervalPoint(point1, point2, map, distance) {
    let dis = computedMapMethods("computedDistance", { point1, point2, defaultHeight: true })
    if (Number(dis.toFixed(2)) <= Number(distance.toFixed(2))) {
        return [point1, point2]
    }
    let angle1 = computedMapMethods("calcAngle", { points: [point1, point2], map: map, paramsPoint: true })
    let a1 = getLonAndLat(point1.lng, point1.lat, angle1, distance)
    a1.height = (point1.height + point2.height) / 2
    let arr = getIntervalPoint(a1, point2, map, distance)
    if (arr[0].lng == a1.lng && arr[0].lat == a1.lat) {
        arr.shift()
    }
    return [a1, ...arr]
}
//获取计算数组列表
export function getArrayList(point1, point2, map, distance) {
    let arr = getIntervalPoint(point1, point2, map, distance)
    arr.unshift(point1)
    let funArray = []
    for (let index = 0; index < arr.length - 1; index++) {
        let item = arr[index + 1]
        let ap = getNewPoint(arr[index], arr[index + 1], map)
        item.ap = ap
        funArray.push(item)
    }
    return funArray;
}

//限制并发数
export function asyncPools(poolLimit, array, fnInter, progressFun, id) {
    let doing = [];
    let i = 0;
    let ret = [];
    let progressIndex = 0;

    function pp() {
        if (i >= array.length) {
            return Promise.resolve(); //最后一个resolve状态，会进入外层返回Promise.then
        }
        let e = fnInter(array[i++], id);
        e.then(() => {
            doing.splice(doing.indexOf(e), 1)
            progressIndex++;
            let progress = parseInt(progressIndex * 100 / array.length)
            progressFun(progress)
        })
        doing.push(e);
        ret.push(e);
        if (doing.length >= poolLimit) {
            return Promise.race(doing).then(pp); //return返回
        } else {
            return Promise.resolve().then(pp); //改写一下保证then链式调用
        }
    }
    return pp().then(() => Promise.all(ret)); //只有当array结束，最后一个resolve才会进入then
}
//计算数组的所有高程数据
export function computerElevationArray(points, oneRequestNum, progressFun, distance, map) {
    if (points.length < 2) {
        return false;
    }
    let doing = [];
    let i = 0;
    let ret = [];
    let progressIndex = 0;

    function progressFun1(progress) {
        return progress
    }

    function pp() {
        if (i >= points.length - 1) {
            return Promise.resolve(); //最后一个resolve状态，会进入外层返回Promise.then
        }
        let e = computerElevationPoints(points[i], points[i + 1], oneRequestNum, progressFun1, distance, map, i + 1);
        i++;
        e.then(() => {
            doing.splice(doing.indexOf(e), 1)
            progressIndex++;
            let progress = parseInt(progressIndex * 100 / (points.length - 1))
            progressFun(progress)
        })
        doing.push(e);
        ret.push(e);
        if (doing.length >= 120) {
            return Promise.race(doing).then(pp); //return返回
        } else {
            return Promise.resolve().then(pp); //改写一下保证then链式调用
        }
    }
    return pp().then(() => Promise.all(ret)); //只有当array结束，最后一个resolve才会进入then

}