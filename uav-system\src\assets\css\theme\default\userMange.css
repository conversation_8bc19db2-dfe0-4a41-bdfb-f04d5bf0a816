.userMange .userManage {
  background-color: #040404;
}
.userMange .userManage .searchDiv .el-button {
  background-color: white;
  color: #0b58de;
  border: none;
}
.userMange .userManage .searchDiv .el-button.active {
  background-color: #0b58de;
  color: white;
}
.userMange .userManage .searchDiv .el-input .el-input__inner {
  background-color: transparent !important;
  color: white !important;
}
.userMange .userManage .custom-table .el-button {
  background-color: transparent;
  border: none;
  color: white;
}
.userMange .userManage .custom-table .el-button.active {
  color: #0b58de;
}
.userMange .userManage .el-dialog .userForm .fromButton .saveBut {
  background-color: #0b58de;
  color: #ffffff;
}
.userMange .userManage .el-dialog .userForm .fromButton .saveBut.checked {
  color: #0b58de;
  background-color: transparent;
}
.userMange .userManage .el-dialog .userForm .fromButton .closeBut {
  color: #0b58de;
  background-color: transparent;
}
.userMange .userManage .el-dialog .userForm .fromButton .closeBut.checked {
  background-color: #0b58de;
  color: white;
}
.userMange .userManage .el-dialog .delButDiv .saveBut {
  background-color: #de0b0b;
  color: #ffffff;
}
.userMange .userManage .el-dialog .delButDiv .saveBut.recoverBtn {
  background-color: #0b58de;
}
.userMange .userManage .el-dialog .delButDiv .saveBut.active {
  background-color: transparent;
  color: #0b58de;
}
.userMange .userManage .el-dialog .delButDiv .closeBut {
  background-color: #7c7e82;
  color: #ffffff;
}
.userMange .userManage .el-dialog .delButDiv .closeBut.active {
  background-color: #0b58de;
  color: white;
}
.userMange .userManage .el-dialog__wrapper .el-dialog__header .el-dialog__headerbtn {
  border: 1px solid #909399 !important;
}
.userMange .userManage .el-dialog__wrapper .el-dialog__body .el-divider {
  background-color: #127ED7 !important;
}
.userMange .userManage .el-dialog__wrapper input::-webkit-input-placeholder {
  color: #cfcfcf !important;
}
.userMange .userManage .el-dialog__wrapper input::-moz-input-placeholder {
  color: #cfcfcf !important;
}
.userMange .userManage .el-dialog__wrapper input::-ms-input-placeholder {
  color: #cfcfcf !important;
}
.userMange .userManage .delDialog .el-dialog__body {
  color: #827f7f !important;
}
.userMange .userManage input::-webkit-input-placeholder {
  color: #5e5e5e !important;
}
.userMange .userManage input::-moz-input-placeholder {
  color: #5e5e5e !important;
}
.userMange .userManage input::-ms-input-placeholder {
  color: #5e5e5e !important;
}
.userMange .userManage::-webkit-scrollbar {
  width: 3px;
}
.userMange .userManage::-webkit-scrollbar-track {
  background-color: #ccc;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}
.userMange .userManage::-webkit-scrollbar-thumb {
  background-color: #777777;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}
