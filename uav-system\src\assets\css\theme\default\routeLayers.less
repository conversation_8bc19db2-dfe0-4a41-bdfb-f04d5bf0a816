.routeLayers {
  .selects {
    background-color: rgb(5, 3, 46, 0.5) !important;
    border: none !important;
    color: #929292 !important;

    .el-select-dropdown__item {
      background-color: transparent !important;

      &.selected {
        color: white !important;
        background-color: #1646a8 !important;

      }

      &:hover {
        color: white !important;
        background-color: rgba(60, 83, 129, 0.8) !important;
      }
    }

    .popper__arrow,
    .popper__arrow:after {
      border-bottom-color: rgb(4, 7, 39, 0.7) !important;
      border-top-color: rgb(4, 7, 39, 0.7) !important;
    }
  }

  .popover-item {
    &.popover-item-1 {
      background-color: rgba(8, 16, 39, 0.9) !important;
      border: none !important;

      .el-autocomplete {
        .el-input {
          .el-input__inner {
            background-color: rgba(8, 16, 39, 0.9) !important;
            color: white !important;
            // border: 1px solid #a5a5a5 !important;
          }
        }
      }

      .el-input-group__append {
        .el-button {
          // border: 1px solid #a5a5a5 !important;

          background-color: rgba(36, 36, 36, 0.9) !important;
          color: white !important;


        }
      }
    }

    &.popover-item-3 {
      background-color: rgba(8, 16, 39, 0.9) !important;

      border: none !important;

    }

    &.popover-item-2 {
      background-color: rgba(8, 16, 39, 0.9) !important;
      border: none !important;

      .content {
        background-color: rgba(8, 16, 39, 0.9) !important;
        // border: 2px solid #000000 !important;

        color: white !important;

        .el-button {

          background-color: transparent !important;

          color: white !important;
          border: none !important;

          &.actived {
            color: #0091f7 !important;
          }
        }
      }
    }

    .el-input {
      input::-webkit-input-placeholder {
        color: #a7a7a7 !important;
      }

      input::-moz-input-placeholder {
        color: #a7a7a7 !important;
      }

      input::-ms-input-placeholder {
        color: #a7a7a7 !important;
      }
    }

    .searchReturn {
      background-color: white;

      border: 1px solid #eee;

      .el-button {

        border: none;
      }
    }
  }

  .autoInput {

    background-color: rgba(8, 16, 39, 0.9) !important;

    .popper__arrow,
    .popper__arrow::after {
      border-bottom-color: rgba(8, 16, 39, 0.9) !important;
    }

    &.el-autocomplete-suggestion {
      border: none !important;
    }
  }

  .setPopover-item,
  .setPopover-item-1 {
    &.el-popover {
      background: rgba(14, 18, 42, 0.8) !important;
      border: none !important;
    }

    .el-button {
      color: white !important;

      &:hover {
        background-color: #0b58de !important;
      }
    }
  }

  #routeplan-layer {
    background-color: rgba(37, 38, 41, 0.9);
    border: 2px solid #000000;

    .el-collapse {
      border: none;

      .el-collapse-item {
        .type-item {
          .type-item-1 {
            background-color: #ff0000;
          }

          .type-item-2 {
            background-color: #5b5656;
          }

          .type-item-3 {
            background-color: #d5ff00;
          }

          .type-item-4 {
            background-color: #ff6600;
          }
        }

        .el-collapse-item__header {
          background-color: rgba(8, 16, 39, 0.9) !important;
          border: none !important;
          color: white !important;
        }

        .el-collapse-item__wrap {
          background-color: transparent !important;
          border: none !important;

          .el-collapse-item__content {
            color: white !important;
          }
        }
      }
    }
  }
  .popover-item-class {
    &.el-popover {
      background: rgba(14, 18, 42, 0.8) !important;
      border: none !important;
      
    }
    .el-button {
      color: white !important;
      &:hover {
        background-color: #0b58de !important;
      }
    }
  }
}
