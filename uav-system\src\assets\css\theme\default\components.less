// 存放组件样式
.components {
  .custom-components-dialog {

    background-color: rgba(0, 0, 0, 0.5);

    .dialog-body input::-webkit-input-placeholder {
      color: #cccccc !important;
    }

    .dialog-body input::-moz-input-placeholder {
      color: #cccccc !important;
    }

    .dialog-body input::-ms-input-placeholder {
      color: #cccccc !important;
    }

    .dialog-body {
      background-color: #fff;

      .body-header {
        .header-main {
          color: #464c57;

          .header-footer-line {
            background-color: #127ed7;
          }
        }

        .header-shut {
          &:hover {
            color: #127ed7;
          }
        }
      }

      .dialog-footer {

        .el-button--default {
          color: #0b58de;
        }

        .el-button--primary {
          background-color: #0b58de;
        }
      }
    }
  }

  .custom-table {

    .el-table,
    .el-table__expanded-cell {
      background-color: rgba(0, 0, 0, 0);
    }

    // 表
    .custom-table-header {
      background-color: #0a0a54;

      .el-table__cell {
        color: #9499a2;
        background-color: rgba(0, 0, 0, 0);
      }
    }

    // 内容
    .el-table .singular-row {
      background: #050e19;
    }

    .el-table .pairage-row {
      background: #0c1a2d;
    }

    .el-table__cell {
      background: rgba(0, 0, 0, 0) !important;
      color: #ffffff;
    }
  }

  .custom-info-tip {
    background-color: rgba(0, 0, 0, 0.2);

    .info-tip-body {
      background-color: rgba(0, 0, 0, 0.9);
      color: #fff;

      .shut-icon {
        border: 1px solid #ccc;
        color: #fff;
      }
    }
  }

  .custom-pages {

    color: #fff;

    .btn-next,
    .btn-prev {
      background: transparent !important;
      color: #fff !important;
    }
  }

  .scroll-confirm {
    background-color: #4ca6ff;

    .show-text {
      color: #fff;
    }

    .scroll-button {
      border: 1px solid #fff;
      background-image: linear-gradient(to right,
          rgba(6, 122, 238, 0.9),
          rgba(76, 166, 255, 0.6));

      .scroll-button-mian {
        .main-item {
          background-color: #fff;
        }
      }
    }

    .scroll-later {
      background-color: #1df50e;
    }

  }

  .custom-scroll-list {
    .load-cell {
      color: #fff;
    }

    .fail-load {
      color: red;
    }

    .scroll-list-no-data {
      color: #fff;
    }

    .return-top {
      color: #fff;
    }
  }

  .custom-tab-scroll {
    .tab-scroll-main {
      .tab-scroll-ul {
        li {
          color: #ffffff;
        }

        .select-li {

          color: #398bcc;

          &::after {
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid #398bcc;

          }
        }
      }
    }

    .table-icon {
      color: #ffffff !important;
    }

    .left-icon {
      border-right: 1px solid #ccc;
    }

    .right-icon {
      border-left: 1px solid #ccc;
    }

  }


  .video-module {
    
    background-color: rgba(61, 67, 72, 1);

    .header-title {
      background-color: rgba(1, 1, 1, 1);
      color: #fff;
    }

    .video-footer {
      background-color: rgba(0, 0, 0, 0.3);
      color: #fff;
    }

    .no-link-show {
      background-color: rgba(61, 67, 72, 0.3);
      color: #fff;
    }
  }
}
